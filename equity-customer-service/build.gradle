plugins {
    id 'java'
}

version = '1.0.1-SNAPSHOT'


group 'com.ddmc.equity'
version '1.0.1-SNAPSHOT'

repositories {
    mavenCentral()
}

dependencies {

    compileOnly('org.projectlombok:lombok:1.18.22')
    testCompileOnly('org.projectlombok:lombok:1.18.22')
    // 测试相关
    // Spring Boot 测试基础依赖
    testImplementation('org.springframework.boot:spring-boot-starter-test')
    // JUnit 依赖
    testImplementation('junit:junit:4.13.2')
    // Mockito 相关依赖
    testImplementation('org.mockito:mockito-core:3.11.2')
    testImplementation('org.mockito:mockito-junit-jupiter:3.11.2')
    // PowerMock 相关依赖
    testImplementation('org.powermock:powermock-core:2.0.9')
    testImplementation('org.powermock:powermock-module-junit4:2.0.9')
    testImplementation('org.powermock:powermock-api-mockito2:2.0.9')

    compile('com.mysql:mysql-connector-j')

    compile project(':equity-domain-service')
}
