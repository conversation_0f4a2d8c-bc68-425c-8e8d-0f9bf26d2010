@startmindmap
* 本地缓存
** 全量-keys
*** 场景缓存 sceneCache
**** 场景号 scene_code
**** 活动ID列表 List<Long> activityIds
*** 权益缓存 benefitCache
**** 权益ID benefitId
**** 权益实体 benefitEntity
** LRU-keys
*** 活动缓存 activityCache
**** 活动ID activityId
**** 活动必要信息 活动开始时间、活动结束时间、发放类型 activityDTO
*** 活动策略缓存 activityStrategyCache
**** 活动ID activityId
**** 策略ID列表 List<Long> strategyIds
*** 策略规则缓存 strategyRuleCache
**** 策略ID strategyId
**** 规则实体列表 List<RuleEntity> ruleEntities
*** 策略权益组权益缓存 strategyBenefitGroupItemCache
**** 策略ID strategyId
**** 权益ID+配置(权重/概率)实体列表 List<BenefitIdWithConf> benefitIdWithConf


* Redis缓存(可能有)
** 活动缓存 activityCache
** 规则缓存 ruleCache
@endmindmap