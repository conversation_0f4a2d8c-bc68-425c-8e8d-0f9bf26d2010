@startuml
'https://plantuml.com/sequence-diagram

autonumber

actor "运营"
participant "权益后台\nequity-business-service"
participant "消息队列\npulsar"
participant "权益脚本机\nequity-job-service"
database "库存变化流水表\ntb_equity_benefit_stock_flow"
participant "XXL脚本"
database "库存表\ntb_equity_benefit_stock"
database "计划库存表\ntb_equity_benefit_stock_plan"
participant "缓存\nRedis"

"运营" -> "权益后台\nequity-business-service": 上线带库存限制规则的活动
"权益后台\nequity-business-service" -> "权益后台\nequity-business-service": 处理
"权益后台\nequity-business-service" -> "消息队列\npulsar": 完成后\n发初始化库存消息
"权益后台\nequity-business-service" --> "运营": 完成
alt 队列 persistent://ddmc/equity/init_stock
"消息队列\npulsar" -> "权益脚本机\nequity-job-service": 推消息消费初始化消息
"权益脚本机\nequity-job-service" -> "库存变化流水表\ntb_equity_benefit_stock_flow": 写一条流水记录
end
alt 定时任务 SyncFlowDBToStockDB
"XXL脚本" -> "库存变化流水表\ntb_equity_benefit_stock_flow": 扫表
"XXL脚本" -> "缓存\nRedis": 写库存、剩余库存初始化缓存
"XXL脚本" -> "库存表\ntb_equity_benefit_stock": 写库存、剩余库存初始化记录
"XXL脚本" -> "计划库存表\ntb_equity_benefit_stock_plan": 写计划库存、计划剩余库存初始化记录
end

@enduml