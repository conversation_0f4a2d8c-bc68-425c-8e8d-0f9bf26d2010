@startuml
'https://plantuml.com/sequence-diagram

autonumber

participant "权益服务依赖端"
participant "权益C端Service"
participant "权益C端本地缓存"
participant "权益C端MySQL"
participant "权益C端策略引擎"

participant "AB服务"
participant "人群服务"
participant "会员服务"


权益服务依赖端 -> 权益C端Service: 查询用户该场景下的有效权益
alt 1.如果本地缓存存在
权益C端Service -> 权益C端本地缓存: 读取场景、活动、策略、规则缓存
return 场景、活动、策略、规则缓存
else 2. 如果本地缓存不存在，穿透
权益C端Service -> 权益C端MySQL: 读取被穿透的场景、活动、策略、规则缓存
return 场景、活动、策略、规则数据库数据
end
权益C端Service -> 权益C端策略引擎: 组装待并发查询数据，查询有效策略
alt 如果存在待并发查询的规则
权益C端策略引擎 -> AB服务: 查询一个uid下多个layerId的AB结果
return AB结果
权益C端策略引擎 -> 人群服务: 查询一个uid下命中的多个分群id结果
return 分群结果
权益C端策略引擎 -> 会员服务: 查询一个uid是否属于会员
return 会员结果
end
权益C端策略引擎 -> 权益C端策略引擎: 根据查询到的数据和现有信息，组装有效策略
note over 权益C端策略引擎
1. 过滤不命中的规则
2. 如果活动存在互斥（类型非全部发放），完成互斥逻辑，过滤被排除的策略
3. 过滤脏数据（状态异常、上下游信息异常等情况）
4. 如存在库存校验，过滤无库存的权益
5. 如存在领取限制，过滤已领取的权益
end note
权益C端策略引擎 --> 权益C端Service: 返回有效策略
权益C端Service -> 权益C端Service: 根据目前有效策略和规则，判断用户命中权益
权益C端Service --> 权益服务依赖端: 返回用户有效权益

@enduml