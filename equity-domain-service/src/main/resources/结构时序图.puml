@startuml
'https://plantuml.com/sequence-diagram

autonumber

participant "Controller"
participant "AppService"
participant "Entity"
participant "Converter"
participant "DomainService"
participant "Mapper"
database "MySQL"

"Controller" -> "AppService": 调用AppService，入参DTO，出参DTO
activate "AppService"
alt 1. 和DomainService交互
"AppService" -> "DomainService" : 调用DomainService获取一个DO
activate "DomainService"
"DomainService" -> "Mapper" : 执行单表查询
"Mapper" -> "MySQL" : 查询
"MySQL"-->"Mapper": 返回查询结果 DO
"Mapper" --> "DomainService" : 返回DO透传
"DomainService" --> "AppService" : 返回DO
deactivate "DomainService"
end

alt 2. 和Entity交互
"AppService" -> "Entity" : 调用Entity把DO转换成DTO
activate "Entity"
"Entity" -> "Converter" : 把DO转换成DTO
"Converter" --> "Entity" : 返回转换结果 DTO
"Entity" --> "AppService" : 返回转换结果 DTO
deactivate "Entity"
end

"AppService" --> "Controller" : 返回DTO

@enduml