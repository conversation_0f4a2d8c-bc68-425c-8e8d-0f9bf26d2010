@startuml
'https://plantuml.com/sequence-diagram

autonumber

actor "用户"
participant "权益C端\nequity-customer-service"
participant "缓存\nRedis"
participant "消息队列\npulsar"
participant "权益脚本机\nequity-job-service"
database "库存变化流水表\ntb_equity_benefit_stock_flow"
participant "XXL脚本"
database "库存表\ntb_equity_benefit_stock"
database "计划库存表\ntb_equity_benefit_stock_plan"

"用户" -> "权益C端\nequity-customer-service": 完成带库存限制规则的活动领取
"权益C端\nequity-customer-service" -> "权益C端\nequity-customer-service": 处理
"权益C端\nequity-customer-service" -> "缓存\nRedis": 更新缓存(lua)
"缓存\nRedis" --> "权益C端\nequity-customer-service": 更新缓存成功
"权益C端\nequity-customer-service" -> "消息队列\npulsar": 完成后\n发活动库存变动消息
"权益C端\nequity-customer-service" --> "用户": 完成
alt 队列 persistent://ddmc/equity/activity_stock_change
"消息队列\npulsar" -> "权益脚本机\nequity-job-service": 推消息消费初始化消息
"权益脚本机\nequity-job-service" -> "库存变化流水表\ntb_equity_benefit_stock_flow": 写一条流水记录
end
alt 定时任务 SyncFlowDBToStockDB
"XXL脚本" -> "库存变化流水表\ntb_equity_benefit_stock_flow": 扫表
"XXL脚本" -> "库存表\ntb_equity_benefit_stock": 聚合、批量更新库存、剩余库存初始化记录
"XXL脚本" -> "计划库存表\ntb_equity_benefit_stock_plan": 聚合、批量更新计划库存、计划剩余库存初始化记录
end

@enduml