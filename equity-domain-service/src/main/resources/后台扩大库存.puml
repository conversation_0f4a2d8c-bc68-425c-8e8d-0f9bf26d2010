@startuml
'https://plantuml.com/sequence-diagram

autonumber

actor "运营"
participant "权益后台\nequity-business-service"
participant "消息队列\npulsar"
participant "权益脚本机\nequity-job-service"
database "库存变化流水表\ntb_equity_benefit_stock_flow"
participant "XXL脚本"
database "库存表\ntb_equity_benefit_stock"
participant "缓存\nRedis"

"运营" -> "权益后台\nequity-business-service": 带库存限制规则的活动扩库存
"权益后台\nequity-business-service" -> "权益后台\nequity-business-service": 处理
"权益后台\nequity-business-service" -> "消息队列\npulsar": 完成后\n发扩大库存消息
"权益后台\nequity-business-service" --> "运营": 完成
alt 队列 persistent://ddmc/equity/expand_stock
"消息队列\npulsar" -> "权益脚本机\nequity-job-service": 推消息扩大库存消息，数量是两者diff
"消息队列\npulsar" -> "权益脚本机\nequity-job-service": 推消息扩大计划库存消息，数量是两者diff
"权益脚本机\nequity-job-service" -> "库存变化流水表\ntb_equity_benefit_stock_flow": 写一条流水记录
end
alt 定时任务 SyncFlowDBToStockDB
"XXL脚本" -> "库存变化流水表\ntb_equity_benefit_stock_flow": 扫表
"XXL脚本" -> "缓存\nRedis": 更新库存和剩余库存(自增)
"XXL脚本" -> "库存表\ntb_equity_benefit_stock": 更新库存和剩余库存记录
end

@enduml