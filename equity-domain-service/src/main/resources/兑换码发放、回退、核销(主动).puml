@startuml
'https://plantuml.com/sequence-diagram

autonumber

participant 其他服务
participant 权益C端
database 兑换码权益账户表
database 兑换码权益账户操作表

alt 兑换码发放(接口)
其他服务 -> 权益C端: 发放用户兑换码接口 (用户id、权益id)
权益C端 ->  兑换码权益账户表: 创建用户兑换码权益账户 \n或新增一条记录
权益C端 ->  兑换码权益账户操作表: 创建\更新\n用户兑换码权益账户操作记录
权益C端 --> 其他服务: 返回发放结果 (权益id、用户id、兑换码)
end

alt 兑换码回退(接口)
其他服务 -> 权益C端: 用户兑换码批量回退接口 (用户id、权益id、兑换码列表)
权益C端 ->  兑换码权益账户表: 读取用户已核销的全部兑换码
权益C端 ->  兑换码权益账户操作表: 更新用户兑换码权益账户操作记录
权益C端 --> 其他服务: 返回回退结果 (权益id、用户id、兑换码列表)
end

alt 兑换码核销(接口)
其他服务 -> 权益C端: 用户兑换码批量核销接口 (用户id、权益id、兑换码列表)
权益C端 ->  兑换码权益账户表: 读取用户已有的全部兑换码
权益C端 ->  兑换码权益账户操作表: 更新用户兑换码权益账户操作记录
权益C端 --> 其他服务: 返回核销结果 (权益id、用户id、兑换码列表)
end
@enduml