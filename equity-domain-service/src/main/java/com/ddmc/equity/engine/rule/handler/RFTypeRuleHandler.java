package com.ddmc.equity.engine.rule.handler;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.dto.rule.UserRfTypeDTO;
import com.ddmc.equity.domain.dto.rule.condition.RfTypeRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 人群标签
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RFTypeRuleHandler extends AbstractRuleHandler {

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.RF_TYPE_RULE.getCode();
    }

    @Override
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {
        if (ruleNotNeedCheck(ruleDTO, engineContextDTO)) {
            return true;
        }
        String userId = engineContextDTO.getUserId();
        RfTypeRuleDTO rfTypeRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleDTO, RfTypeRuleDTO.class);
        List<String> rfFirstTypeNames = Optional.ofNullable(rfTypeRuleDTO).map(RfTypeRuleDTO::getRfFirstTypeNames).orElse(null);
        List<String> rfTypeNames = Optional.ofNullable(rfTypeRuleDTO).map(RfTypeRuleDTO::getRfTypeNames).orElse(null);
        UserRfTypeDTO factoryUserRfTypeDTO = Optional.ofNullable(ruleConditionFactorValueDTO)
                .map(RuleConditionFactorValueDTO::getUserRfTypeDTO).orElse(null);
        String factoryRfFirstTypeName = Optional.ofNullable(factoryUserRfTypeDTO).map(UserRfTypeDTO::getRfFirstTypeName).orElse(null);
        String factoryRfTypeName = Optional.ofNullable(factoryUserRfTypeDTO).map(UserRfTypeDTO::getRfTypeName).orElse(null);
        boolean matchRfFirstTypeName = CollectionUtils.isNotEmpty(rfFirstTypeNames) && rfFirstTypeNames.contains(factoryRfFirstTypeName);
        boolean matchRfTypeName = CollectionUtils.isNotEmpty(rfTypeNames) && rfTypeNames.contains(factoryRfTypeName);
        // 只要 RF 人群一级分类名称或者 RF 人群细分类型名称中任意一个匹配成功，则满足规则
        if (matchRfFirstTypeName || matchRfTypeName) {
            return true;
        }
        actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_RF_TYPE.getCode());
        log.info("[规则过滤] userIsHitRule rFType 命中 RF 人群失败. userId={}, actInfo={}, rfTypeRuleDTO={}, factoryUserRfTypeDTO={}",
                userId, JSON.toJSONString(actInfoDTO), JSON.toJSONString(rfTypeRuleDTO), JSON.toJSONString(factoryUserRfTypeDTO));
        return false;
    }
}
