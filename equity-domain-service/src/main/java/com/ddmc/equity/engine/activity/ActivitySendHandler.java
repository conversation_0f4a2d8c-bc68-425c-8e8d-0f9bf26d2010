package com.ddmc.equity.engine.activity;

import com.ddmc.equity.model.dto.SceneActivityCacheDto;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/18 10:15
 * @description
 */
public interface ActivitySendHandler {

    /**
     * 获取发放类型
     *
     * @return 发放类型
     */
    Integer getSendType();

    /**
     * 根据发放类型选中用户可以领取的活动列表
     *
     * @param activities 传入活动列表
     * @return 用户可以领取的活动列表
     */
    List<SceneActivityCacheDto> chooseActivities(List<SceneActivityCacheDto> activities);
}
