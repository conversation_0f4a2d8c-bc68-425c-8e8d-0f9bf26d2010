package com.ddmc.equity.engine.activity;

import com.ddmc.equity.common.apollo.RuleConstants;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.util.MapUtils;
import com.ddmc.equity.common.util.ThreadsUtils;
import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.engine.ActivityEngineContextDTO;
import com.ddmc.equity.domain.dto.engine.StrategyEngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.entity.common.EngineContextEntity;
import com.ddmc.equity.engine.Engine;
import com.ddmc.equity.engine.rule.RuleContext;
import com.ddmc.equity.engine.strategy.EquityStrategyEngine;
import com.ddmc.equity.model.convert.EngineContextConvert;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ddmc.equity.enums.ActivityFilterTypeEnum.EFFECT_AND_PENDING_ACTIVITY;

/**
 * 活动引擎
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class EquityActivityEngine implements Engine<SceneActivityCacheDto, ActivityEngineContextDTO> {

    @Autowired
    private RuleConstants ruleConstants;
    @Autowired
    private RuleContext ruleContext;
    @Autowired
    private ActivitySendHandlerContext activitySendHandlerContext;
    @Autowired
    private EquityStrategyEngine equityStrategyEngine;
    @Autowired
    private List<EquityStrategyEngine> equityStrategyEngineList;

    private final Map<Integer, EquityStrategyEngine> STRATEGY_ENGINE_HANDLER_MAP = Maps.newHashMap();

    @PostConstruct
    public void init() {
        equityStrategyEngineList.forEach(strategy -> {
            strategy.getActivitySendType().forEach(type -> {
                STRATEGY_ENGINE_HANDLER_MAP.put(type, strategy);
            });
        });
    }

    @Override
    public List<SceneActivityCacheDto> consultSceneBenefit(List<SceneActivityCacheDto> needFilterList,
                                                           ActivityEngineContextDTO engineContextDTO,
                                                           RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        if (CollectionUtils.isEmpty(needFilterList)) {
            return null;
        }

        // 小于异步处理要求数量，则同步处理
        if (CollectionUtils.size(needFilterList) < ruleConstants.getActivityRuleMatchSyncHandleLimitCount()) {
            List<SceneActivityCacheDto> hitActivities = handler(needFilterList, engineContextDTO, ruleConditionFactorValueDTO);
            // 根据发放类型选中用户可以领取的活动列表（sceneSendType、activity.weight）
            return activitySendHandlerContext.chooseActivitiesBySendType(hitActivities, engineContextDTO.getSceneSendType());
        }

        // 异步处理
        List<SceneActivityCacheDto> hitActivities = handlerByAsync(needFilterList, engineContextDTO, ruleConditionFactorValueDTO);
        // 根据发放类型选中用户可以领取的活动列表（sceneSendType、activity.weight）
        return activitySendHandlerContext.chooseActivitiesBySendType(hitActivities, engineContextDTO.getSceneSendType());
    }

    /**
     * 异步处理
     */
    private List<SceneActivityCacheDto> handlerByAsync(List<SceneActivityCacheDto> needFilterActivities,
                                                       ActivityEngineContextDTO engineContextDTO,
                                                       RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        // 分组异步处理
        Map<Integer, List<SceneActivityCacheDto>> resultMap = Maps.newConcurrentMap();
        List<CompletableFuture<Void>> asyncTasks = Lists.newArrayList();
        List<List<SceneActivityCacheDto>> partition = Lists.partition(needFilterActivities, ruleConstants.getActivityRuleMatchSyncHandleGroupCount());
        for (int i = 0; i < partition.size(); i++) {
            final int key = i;
            CompletableFuture<Void> filterFuture = ThreadsUtils.runAsync(() ->
                            handlerAndSetResult(partition.get(key), engineContextDTO, ruleConditionFactorValueDTO, key, resultMap),
                    ThreadsUtils.getCoreThreadPoll(), Constants.RECALL_ALL_SCENE_BENEFIT);
            asyncTasks.add(filterFuture);
        }

        // 同步等待各线程池结果
        ThreadsUtils.getCompletableFutureList(asyncTasks, ruleConstants.getActivityRuleMatchSyncHandleWaitMsTime(), TimeUnit.MILLISECONDS);
        if (MapUtils.isEmpty(resultMap)) {
            return null;
        }
        return resultMap.values().stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 异步过滤出用户可以领取的活动列表
     */
    private void handlerAndSetResult(List<SceneActivityCacheDto> needFilterActivities,
                                     ActivityEngineContextDTO engineContextDTO,
                                     RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                     Integer key, Map<Integer, List<SceneActivityCacheDto>> resultMap) {
        // 并发场景下共用一个 engineContextDTO，会导致对象的属性错乱，所以需要一个 newEngineContextDTO 对象。处理完成后需要 copy 返回结果上下文
        ActivityEngineContextDTO newEngineContextDTO = EngineContextConvert.INSTANCE.convertToActivityEngineContextDTO(engineContextDTO);
        List<SceneActivityCacheDto> hitActivities = handler(needFilterActivities, newEngineContextDTO, ruleConditionFactorValueDTO);
        if (CollectionUtils.isNotEmpty(hitActivities)) {
            resultMap.put(key, hitActivities);
        }
        // 需要把[new 活动引擎]返回结果上下文 copy 到 engineContextDTO
        EngineContextEntity.copyEngineResultContext(newEngineContextDTO.getResultContextDTO(), engineContextDTO.getResultContextDTO());
    }

    private List<SceneActivityCacheDto> handler(List<SceneActivityCacheDto> needFilterActivities,
                                                ActivityEngineContextDTO engineContextDTO,
                                                RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        return needFilterActivities.stream().map(activity ->
                handlerSingle(activity, engineContextDTO, ruleConditionFactorValueDTO)
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private SceneActivityCacheDto handlerSingle(SceneActivityCacheDto activity, ActivityEngineContextDTO engineContextDTO,
                                                RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        // 单个活动过滤，设置当前活动信息到上下文
        fillActivityEngineContextDTO(engineContextDTO, activity);

        // 1 活动规则过滤。如果用户有未命中的规则，则不返回该活动
        RuleFilterActInfoDTO actInfoDTO = convertToRuleFilterActInfoDTO(engineContextDTO);
        if (ruleContext.userHasNotHitActivityRule(activity.getRuleCacheDTOList(), engineContextDTO, ruleConditionFactorValueDTO, actInfoDTO)) {
            // 如果活动不能领取，则设置不能领取的权益列表
            fillUnableReceiveBenefitDTOList(engineContextDTO, actInfoDTO, activity);
            // 在规则过滤的时候已经打印了日志，这里就不再重复打日志
            return null;
        }

        // 2 调用【策略引擎】过滤。如果策略列表为空，则不返回该活动
        StrategyEngineContextDTO strategyEngineContextDTO = convertToStrategyEngineContextDTO(engineContextDTO);
        //todo
        List<StrategyCacheDto> hitStrategies = STRATEGY_ENGINE_HANDLER_MAP.getOrDefault(engineContextDTO.getActivitySendType(), equityStrategyEngine).consultSceneBenefit(activity.getStrategyCacheDtoList(),
                strategyEngineContextDTO, ruleConditionFactorValueDTO);
        // 需要把[策略引擎]返回结果上下文 copy 到 engineContextDTO
        EngineContextEntity.copyEngineResultContext(strategyEngineContextDTO.getResultContextDTO(), engineContextDTO.getResultContextDTO());
        if (CollectionUtils.isEmpty(hitStrategies)) {
            // 在【策略引擎】过滤的时候已经打印了日志，这里就不再重复打日志
            return null;
        }

        // 3 返回活动
        activity.setStrategyCacheDtoList(hitStrategies);
        return activity;
    }

    private StrategyEngineContextDTO convertToStrategyEngineContextDTO(ActivityEngineContextDTO engineContextDTO) {
        return EngineContextConvert.INSTANCE.convertToStrategyEngineContextDTO(engineContextDTO);
    }

    private RuleFilterActInfoDTO convertToRuleFilterActInfoDTO(ActivityEngineContextDTO engineContextDTO) {
        return RuleFilterActInfoDTO.builder()
                .activityId(engineContextDTO.getActivityId())
                .build();
    }

    private void fillActivityEngineContextDTO(ActivityEngineContextDTO engineContextDTO, SceneActivityCacheDto activity) {
        engineContextDTO.setActivityId(activity.getActivityId());
        engineContextDTO.setExternalType(activity.getExternalType());
        engineContextDTO.setExternalId(activity.getExternalId());
        engineContextDTO.setActivitySendType(activity.getSendType());
        if (Objects.equals(engineContextDTO.getActFilterType(), EFFECT_AND_PENDING_ACTIVITY)
                && Objects.nonNull(activity.getStartTime()) && activity.getStartTime().after(new Date())) {
            engineContextDTO.setIgnoreActivityReceiveLimit(true);
        }

    }

    /**
     * 设置不能领取权益列表（活动维度）
     *
     * @param engineContextDTO 上下文
     * @param actInfoDTO       过滤的活动、策略、权益组信息（用于打印日志，以及返回不能领取原因）
     * @param activity         活动
     */
    public void fillUnableReceiveBenefitDTOList(ActivityEngineContextDTO engineContextDTO, RuleFilterActInfoDTO actInfoDTO,
                                                SceneActivityCacheDto activity) {
        // 如果未指定需要不能领取原因，则无需返回不能领取权益列表
        if (!Boolean.TRUE.equals(engineContextDTO.getNeedUnableReceiveReason())) {
            return;
        }
        List<UnableReceiveBenefitDTO> unableReceiveBenefitDTOList = EngineContextEntity.convertToUnableReceiveBenefitDTOList(
                engineContextDTO, actInfoDTO, activity.getStrategyCacheDtoList());
        if (CollectionUtils.isNotEmpty(unableReceiveBenefitDTOList)) {
            engineContextDTO.getResultContextDTO().getUnableReceiveBenefitDTOList().addAll(unableReceiveBenefitDTOList);
        }
    }
}

