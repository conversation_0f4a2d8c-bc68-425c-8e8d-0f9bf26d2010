package com.ddmc.equity.engine.strategy.handler;

import com.ddmc.equity.enums.ActivitySendTypeEnum;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.common.util.lottery.LotteryUtils;
import com.ddmc.equity.common.util.lottery.Gift;
import com.ddmc.equity.engine.strategy.StrategySendHandler;
import com.ddmc.equity.enums.StrategyTypeEnum;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/***
 * <AUTHOR>
 * 概率发放。按照权重取一个策略发放
 */
@Slf4j
@Component
public class ProbabilityStrategySendHandler implements StrategySendHandler {

    @Override
    public Integer getSendType() {
        return ActivitySendTypeEnum.PROBABILITY.getCode();
    }

    @Override
    public List<StrategyCacheDto> chooseStrategies(List<StrategyCacheDto> strategies) {
        Gift gift = drawGift(strategies);
        if (Objects.isNull(gift)) {
            // 如果按概率抽返回 gift 为空（满足条件的策略权重都为 null 或者 0），则返回兜底策略
            log.warn("strategySendHandler chooseStrategies gift is null, then getFallbackStrategies return.");
            return getFallbackStrategies(strategies);
        }
        return strategies.stream().filter(e -> StringUtils.equals(gift.getId(), getGiftId(e))).collect(Collectors.toList());
    }

    private Gift drawGift(List<StrategyCacheDto> strategies) {
        List<Gift> gifts = strategies.stream().map(strategy -> Gift.builder()
                .id(getGiftId(strategy))
                .weight(NumberUtils.convertToBigDecimal(strategy.getActivityWeight()))
                .build()
        ).collect(Collectors.toList());
        return LotteryUtils.drawGiftByWeight(gifts);
    }

    private String getGiftId(StrategyCacheDto strategy) {
        return String.valueOf(strategy.getStrategyId());
    }

    private List<StrategyCacheDto> getFallbackStrategies(List<StrategyCacheDto> strategies) {
        return strategies.stream()
                .filter(s -> Objects.equals(s.getStrategyType(), StrategyTypeEnum.FALLBACK.getCode()))
                .collect(Collectors.toList());
    }
}
