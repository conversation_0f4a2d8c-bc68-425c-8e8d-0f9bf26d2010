package com.ddmc.equity.engine.rule.handler;


import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.ABTestRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * AB 实验
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ABTestRuleHandler extends AbstractRuleHandler {

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.AB_TEST_RULE.getCode();
    }

    @Override
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {
        if (ruleNotNeedCheck(ruleDTO, engineContextDTO)) {
            return true;
        }
        String userId = engineContextDTO.getUserId();
        ABTestRuleDTO abTestRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleDTO, ABTestRuleDTO.class);
        if (Objects.isNull(abTestRuleDTO) || StringUtils.isBlank(abTestRuleDTO.getLayerId())
                || CollectionUtils.isEmpty(abTestRuleDTO.getChoosePutGroup())) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_AB.getCode());
            log.info("[规则过滤] userIsHitRule ab 规则实验组为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        String layerId = abTestRuleDTO.getLayerId();
        List<String> factorUserHitGroups = getFactorUserHitGroups(layerId, ruleConditionFactorValueDTO.getUserHitAbTestRuleMap());
        if (CollectionUtils.isEmpty(factorUserHitGroups)) {
            log.info("[规则过滤] userIsHitRule ab 因子值用户实验组组为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_AB.getCode());
            return false;
        }
        List<String> choosePutGroup = abTestRuleDTO.getChoosePutGroup();
        if (choosePutGroup.stream().noneMatch(factorUserHitGroups::contains)) {
            log.info("[规则过滤] userIsHitRule ab 用户命中实验组失败. userId={}, actInfo={}, layerId={}, choosePutGroup={}, factorUserHitGroups={}",
                    userId, JSON.toJSONString(actInfoDTO), layerId, JSON.toJSONString(choosePutGroup), JSON.toJSONString(factorUserHitGroups));
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_AB.getCode());
            return false;
        }
        return true;
    }

    private List<String> getFactorUserHitGroups(String layerId, Map<String, ABTestRuleDTO> userHitAbTestRuleMap) {
        if (MapUtils.isEmpty(userHitAbTestRuleMap)) {
            return null;
        }
        ABTestRuleDTO abTestRuleDTO = userHitAbTestRuleMap.get(layerId);
        return Optional.ofNullable(abTestRuleDTO).map(ABTestRuleDTO::getUserHitGroup).orElse(null);
    }
}
