package com.ddmc.equity.engine.activity.handler;

import com.ddmc.equity.enums.SceneSendTypeEnum;
import com.ddmc.equity.engine.activity.ActivitySendHandler;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/18 10:16
 * @description
 */
@Slf4j
@Service
public class WholeActivitySendHandler implements ActivitySendHandler {

    @Override
    public Integer getSendType() {
        return SceneSendTypeEnum.WHOLE.getCode();
    }

    @Override
    public List<SceneActivityCacheDto> chooseActivities(List<SceneActivityCacheDto> activities) {
        return activities;
    }
}
