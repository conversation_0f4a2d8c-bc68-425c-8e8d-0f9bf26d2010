package com.ddmc.equity.engine.benefit_group.handler;

import com.ddmc.equity.enums.StrategySendTypeEnum;
import com.ddmc.equity.engine.benefit_group.BenefitSendHandler;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * 全部发放。不过滤
 */
@Slf4j
@Component
public class WholeBenefitSendHandler implements BenefitSendHandler {

    @Override
    public Integer getSendType() {
        return StrategySendTypeEnum.WHOLE.getCode();
    }

    @Override
    public List<BenefitIdWithConfDto> chooseBenefits(List<BenefitIdWithConfDto> benefits) {
        return benefits;
    }
}
