package com.ddmc.equity.engine.benefit_group;

import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.engine.BenefitGroupEngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.entity.common.EngineContextEntity;
import com.ddmc.equity.engine.Engine;
import com.ddmc.equity.engine.rule.RuleContext;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 权益组引擎
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/21 15:50
 */
@Slf4j
@Service
public class EquityBenefitGroupEngine implements Engine<BenefitIdWithConfDto, BenefitGroupEngineContextDTO> {

    @Autowired
    private RuleContext ruleContext;
    @Autowired
    private BenefitSendHandlerContext benefitSendHandlerContext;

    @Override
    public List<BenefitIdWithConfDto> consultSceneBenefit(List<BenefitIdWithConfDto> needFilterList,
                                                          BenefitGroupEngineContextDTO engineContextDTO,
                                                          RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        if (CollectionUtils.isEmpty(needFilterList)) {
            return null;
        }

        // 1 遍历每一个权益：权益规则过滤
        List<BenefitIdWithConfDto> hitBenefits = handler(needFilterList, engineContextDTO, ruleConditionFactorValueDTO);
        if (CollectionUtils.isEmpty(hitBenefits)) {
            // 日志在规则过滤已经打印过了，无需重复打印
            return null;
        }

        // 2 根据发放类型选中用户可以领取的权益列表（strategySendType、benefit.weight）
        hitBenefits = benefitSendHandlerContext.chooseBenefitsBySendType(hitBenefits, engineContextDTO.getStrategySendType());
        if (CollectionUtils.isEmpty(hitBenefits)) {
            log.warn("benefitGroupEngine chooseBenefitsBySendType hitBenefits is null. activityId={}, strategyId={}",
                    engineContextDTO.getActivityId(), engineContextDTO.getStrategyId());
            return null;
        }

        // 3 权益排序，数字越小展示越靠前
        hitBenefits.sort(Comparator.comparing(e -> NumberUtils.isNumber(e.getWeight()) ? NumberUtils.toLong(e.getWeight()) : 0L));
        return hitBenefits;
    }

    private List<BenefitIdWithConfDto> handler(List<BenefitIdWithConfDto> needFilterBenefits,
                                               BenefitGroupEngineContextDTO engineContextDTO,
                                               RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        return needFilterBenefits.stream().map(benefit ->
                handlerSingle(benefit, engineContextDTO, ruleConditionFactorValueDTO)
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private BenefitIdWithConfDto handlerSingle(BenefitIdWithConfDto benefit, BenefitGroupEngineContextDTO engineContextDTO,
                                               RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        // 单个权益过滤，设置当前权益信息到上下文
        fillBenefitGroupEngineContextDTO(engineContextDTO, benefit);

        // 1 权益规则过滤。如果用户有未命中的规则，则不返回该权益
        RuleFilterActInfoDTO benefitActInfoDTO = convertToRuleFilterActInfoDTO(engineContextDTO);
        if (ruleContext.userHasNotHitBenefitRule(benefit.getRuleCacheDTOList(), engineContextDTO, ruleConditionFactorValueDTO, benefitActInfoDTO)) {
            // 如果权益不能领取，则设置不能领取的权益列表
            fillUnableReceiveBenefitDTOList(engineContextDTO, benefitActInfoDTO, benefit);
            // 在规则过滤的时候已经打印了日志，这里就不再重复打日志
            return null;
        }

        // 2 返回权益
        return benefit;
    }

    private RuleFilterActInfoDTO convertToRuleFilterActInfoDTO(BenefitGroupEngineContextDTO engineContextDTO) {
        return RuleFilterActInfoDTO.builder()
                .activityId(engineContextDTO.getActivityId())
                .strategyId(engineContextDTO.getStrategyId())
                .benefitGroupId(engineContextDTO.getBenefitGroupId())
                .benefitId(engineContextDTO.getBenefitId())
                .build();
    }

    private void fillBenefitGroupEngineContextDTO(BenefitGroupEngineContextDTO engineContextDTO, BenefitIdWithConfDto benefit) {
        engineContextDTO.setBenefitGroupId(benefit.getBenefitGroupId());
        engineContextDTO.setBenefitId(benefit.getId());
    }

    /**
     * 设置不能领取的权益列表（权益组维度）
     *
     * @param engineContextDTO  上下文
     * @param benefitActInfoDTO 过滤的活动、策略、权益组信息（用于打印日志，以及返回不能领取原因）
     * @param benefit           权益
     */
    public void fillUnableReceiveBenefitDTOList(BenefitGroupEngineContextDTO engineContextDTO,
                                                RuleFilterActInfoDTO benefitActInfoDTO,
                                                BenefitIdWithConfDto benefit) {
        // 如果未指定需要不能领取原因，则无需返回不能领取权益列表
        if (!Boolean.TRUE.equals(engineContextDTO.getNeedUnableReceiveReason())) {
            return;
        }
        UnableReceiveBenefitDTO unableReceiveBenefitDTO = EngineContextEntity.convertToUnableReceiveBenefitDTO(engineContextDTO,
                benefitActInfoDTO, benefit);
        engineContextDTO.getResultContextDTO().getUnableReceiveBenefitDTOList().add(unableReceiveBenefitDTO);
    }
}
