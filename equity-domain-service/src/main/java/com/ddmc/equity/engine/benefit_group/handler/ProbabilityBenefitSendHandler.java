package com.ddmc.equity.engine.benefit_group.handler;

import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.enums.StrategySendTypeEnum;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.common.util.lottery.LotteryUtils;
import com.ddmc.equity.common.util.lottery.Gift;
import com.ddmc.equity.engine.benefit_group.BenefitSendHandler;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 概率发放。按照权重取一个权益发放
 */
@Slf4j
@Component
public class ProbabilityBenefitSendHandler implements BenefitSendHandler {

    @Override
    public Integer getSendType() {
        return StrategySendTypeEnum.PROBABILITY.getCode();
    }

    @Override
    public List<BenefitIdWithConfDto> chooseBenefits(List<BenefitIdWithConfDto> benefits) {
        Gift gift = drawGift(benefits);
        if (Objects.isNull(gift)) {
            log.warn("benefitSendHandler chooseBenefits gift is null.");
            return null;
        }
        return benefits.stream().filter(e -> StringUtils.equals(gift.getId(), getGiftId(e))).collect(Collectors.toList());
    }

    private Gift drawGift(List<BenefitIdWithConfDto> benefits) {
        List<Gift> gifts = benefits.stream().map(benefit -> Gift.builder()
                .id(getGiftId(benefit))
                .weight(NumberUtils.convertToBigDecimal(benefit.getWeight()))
                .build()
        ).collect(Collectors.toList());
        return LotteryUtils.drawGiftByWeight(gifts);
    }

    private String getGiftId(BenefitIdWithConfDto benefit) {
        return benefit.getBenefitGroupId() + Constants.UNDERLINE + benefit.getId();
    }
}
