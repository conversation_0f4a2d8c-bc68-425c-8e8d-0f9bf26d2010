package com.ddmc.equity.engine;

import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;

import java.util.List;

/**
 * @param <T>
 * <AUTHOR>
 */
public interface Engine<T, E extends EngineContextDTO> {

    /**
     * 权益咨询引擎过滤。过滤活动规则、策略规则、权益组规则，含领取频次、领取库存限制
     * <p>
     * 1 场景引擎
     * 1.1 调用【活动引擎】过滤
     * <p>
     * 2 活动引擎。先过滤规则，再调用【策略引擎】过滤，可减少过滤次数
     * 2.1 活动规则过滤
     * 2.2 调用【策略引擎】过滤
     * <p>
     * 3 策略引擎。先过滤规则，再调用【权益组引擎】过滤，可减少过滤次数
     * 3.1 策略规则过滤
     * 3.2 调用【权益组引擎】过滤
     * 3.3 根据发放方式选中用户可以领取的策略列表
     * <p>
     * 4 权益组引擎
     * 4.1 权益组规则过滤
     * 4.2 根据发放方式选中用户可以领取的权益列表
     *
     * @param needFilterList        活动列表
     * @param engineContextDTO            上下文
     * @param ruleConditionFactorValueDTO 规则因子值
     * @return 命中的活动列表
     */
    List<T> consultSceneBenefit(List<T> needFilterList, E engineContextDTO, RuleConditionFactorValueDTO ruleConditionFactorValueDTO);
}
