package com.ddmc.equity.engine.rule.handler;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.OldSysMaiCaiUserNewOrOldIdentityEnum;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.OldSysMaiCaiNewOrOldUserLimitRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * 老玩法系统的买菜新老用户人群
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OldSysMaiCaiNewOrOldUserRuleHandler extends AbstractRuleHandler {

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.OLD_SYS_MAI_CAI_NEW_OR_OLD_USER_RULE.getCode();
    }

    @Override
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {
        if (ruleNotNeedCheck(ruleDTO, engineContextDTO)) {
            return true;
        }
        String userId = engineContextDTO.getUserId();
        OldSysMaiCaiNewOrOldUserLimitRuleDTO oldSysRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleDTO, OldSysMaiCaiNewOrOldUserLimitRuleDTO.class);
        Integer oldSysUserIdentity = Optional.ofNullable(oldSysRuleDTO)
                .map(OldSysMaiCaiNewOrOldUserLimitRuleDTO::getOldSysUserNewOrOldIdentity).orElse(null);
        if (Objects.isNull(oldSysUserIdentity)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_OLD_SYS_NEW_USER.getCode());
            log.info("[规则过滤] userIsHitRule oldSysIdentity 规则老系统用户身份为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        if (OldSysMaiCaiUserNewOrOldIdentityEnum.ALL.getCode().equals(oldSysUserIdentity)) {
            return true;
        }
        Integer factorOldSysUserIdentity = Optional.ofNullable(ruleConditionFactorValueDTO)
                .map(RuleConditionFactorValueDTO::getOldSysMaiCaiUserNewOrOldIdentity).orElse(null);
        if (Objects.isNull(factorOldSysUserIdentity)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_OLD_SYS_NEW_USER.getCode());
            log.info("[规则过滤] userIsHitRule oldSysIdentity 因子值老系统用户身份为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        if (!oldSysUserIdentity.equals(factorOldSysUserIdentity)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_OLD_SYS_NEW_USER.getCode());
            log.info("[规则过滤] userIsHitRule oldSysIdentity 命中老系统用户身份失败. userId={}, actInfo={}, oldSysUserIdentity={}, factorOldSysUserIdentity={}",
                    userId, JSON.toJSONString(actInfoDTO), oldSysUserIdentity, factorOldSysUserIdentity);
            return false;
        }
        return true;
    }
}
