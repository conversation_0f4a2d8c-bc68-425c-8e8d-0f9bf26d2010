package com.ddmc.equity.engine.strategy;


import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.engine.BenefitGroupEngineContextDTO;
import com.ddmc.equity.domain.dto.engine.StrategyEngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.entity.activity.ActivityCacheRuleConvertEntity;
import com.ddmc.equity.domain.entity.common.EngineContextEntity;
import com.ddmc.equity.engine.Engine;
import com.ddmc.equity.engine.benefit_group.EquityBenefitGroupEngine;
import com.ddmc.equity.engine.rule.RuleContext;
import com.ddmc.equity.enums.ActivitySendTypeEnum;
import com.ddmc.equity.model.convert.EngineContextConvert;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import com.ddmc.equity.model.dto.StrategyRuleCacheDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 策略引擎
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class EquityStrategyEngine implements Engine<StrategyCacheDto, StrategyEngineContextDTO> {

    @Autowired
    private RuleContext ruleContext;
    @Autowired
    private StrategySendHandlerContext strategySendHandlerContext;
    @Autowired
    private EquityBenefitGroupEngine equityBenefitGroupEngine;

    @Override
    public List<StrategyCacheDto> consultSceneBenefit(List<StrategyCacheDto> needFilterList,
                                                      StrategyEngineContextDTO engineContextDTO,
                                                      RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        if (CollectionUtils.isEmpty(needFilterList)) {
            return null;
        }

        // 1 遍历每一个策略：策略规则过滤、调用【权益组引擎】过滤
        List<StrategyCacheDto> hitStrategies = handler(needFilterList, engineContextDTO, ruleConditionFactorValueDTO);
        if (CollectionUtils.isEmpty(hitStrategies)) {
            return null;
        }

        // 2 根据发放类型选中用户可以领取的策略列表（activitySendType、strategy.weight）
        // 如果来源请求参数的 activitySendTypeFromReq 不为空，则使用 activitySendTypeFromReq；为空，则使用活动配置上的 activitySendType
        Integer activitySendType = ObjectUtils.defaultIfNull(engineContextDTO.getActivitySendTypeFromReq(),
                engineContextDTO.getActivitySendType());
        return strategySendHandlerContext.chooseStrategiesBySendType(hitStrategies, activitySendType);
    }

    public List<Integer> getActivitySendType() {
        return Lists.newArrayList(ActivitySendTypeEnum.WHOLE.getCode(), ActivitySendTypeEnum.RANDOM.getCode(), ActivitySendTypeEnum.PROBABILITY.getCode(), ActivitySendTypeEnum.PRIORITY.getCode());
    }

    private List<StrategyCacheDto> handler(List<StrategyCacheDto> needFilterList, StrategyEngineContextDTO engineContextDTO,
                                           RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        return needFilterList.stream().map(strategy ->
                handlerSingle(strategy, engineContextDTO, ruleConditionFactorValueDTO)
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private StrategyCacheDto handlerSingle(StrategyCacheDto strategy, StrategyEngineContextDTO engineContextDTO,
                                           RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        // 单个策略过滤，设置当前策略信息到上下文
        fillStrategyEngineContextDTO(engineContextDTO, strategy);

        Long strategyId = strategy.getStrategyId();
        List<StrategyRuleCacheDTO> ruleCacheDTOList = strategy.getRuleCacheDTOList();
        RuleFilterActInfoDTO strategyActInfoDTO = convertToRuleFilterActInfoDTO(engineContextDTO);
        // 1.0 过滤可以领取的权益列表。如果权益列表为空，则不返回该策略（频次、库存规则需要优先判断）
        // 20230707lry 由于策略规则的频次限制和库存限制规则不属于策略，它应该是属于权益组下面的权益的，所以这个地方分两块写是合理的，主要是因为设计上的缺陷，频次限制和库存限制就应该是在权益上面，不应该在策略上
        List<BenefitIdWithConfDto> benefits = strategy.getStrategyBenefitGroup().get(strategyId);
        List<BenefitIdWithConfDto> canReceiveBenefits = filterCanReceiveBenefits(benefits, engineContextDTO, ruleCacheDTOList,
                ruleConditionFactorValueDTO, strategyActInfoDTO);
        if (CollectionUtils.isEmpty(canReceiveBenefits)) {
            return null;
        }
        // 1 策略规则过滤。如果用户有未命中的规则，则不返回该策略
        if (ruleContext.userHasNotHitStrategyRule(ruleCacheDTOList, engineContextDTO, ruleConditionFactorValueDTO, strategyActInfoDTO)) {
            // 如果策略不能领取，则设置不能领取的权益列表
            fillUnableReceiveBenefitDTOList(engineContextDTO, strategyActInfoDTO, strategy);
            // 在规则过滤的时候已经打印了日志，这里就不再重复打日志
            return null;
        }

        // 2 调用【权益组引擎】过滤。如果权益列表为空，则不返回该策略
        BenefitGroupEngineContextDTO benefitGroupEngineContextDTO = convertToBenefitEngineContextDTO(engineContextDTO);
        List<BenefitIdWithConfDto> hitBenefits = equityBenefitGroupEngine.consultSceneBenefit(canReceiveBenefits,
                benefitGroupEngineContextDTO, ruleConditionFactorValueDTO);
        // 需要把[权益组引擎]返回结果上下文 copy 到 engineContextDTO
        EngineContextEntity.copyEngineResultContext(benefitGroupEngineContextDTO.getResultContextDTO(), engineContextDTO.getResultContextDTO());
        if (CollectionUtils.isEmpty(hitBenefits)) {
            // 在【权益组引擎】过滤的时候已经打印了日志，这里就不再重复打日志
            return null;
        }

        // 3 返回策略
        strategy.getStrategyBenefitGroup().put(strategyId, hitBenefits);
        return strategy;
    }

    /**
     * 过滤可以领取的权益列表
     */
    public List<BenefitIdWithConfDto> filterCanReceiveBenefits(List<BenefitIdWithConfDto> needFilterBenefits,
                                                               StrategyEngineContextDTO engineContextDTO,
                                                               List<StrategyRuleCacheDTO> ruleCacheDTOList,
                                                               RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                                               RuleFilterActInfoDTO actInfoDTO) {
        if (CollectionUtils.isEmpty(needFilterBenefits)) {
            return null;
        }
        // 判断是否有需要区分维度的规则（活动维度、策略维度、权益维度）。如果没有，则无需过滤
        if (!ActivityCacheRuleConvertEntity.hasDimensionRule(ruleCacheDTOList)) {
            return needFilterBenefits;
        }
        //是否忽略库存频次限制
        if (engineContextDTO.isIgnoreReceiveLimit() || engineContextDTO.isIgnoreActivityReceiveLimit()) {
            return needFilterBenefits;
        }
        return needFilterBenefits.stream().filter(benefit -> {
            RuleFilterActInfoDTO benefitActInfoDTO = convertToRuleFilterActInfoDTO(actInfoDTO, benefit);
            boolean canReceiveBenefit = ruleContext.userIsCanReceiveBenefit(ruleCacheDTOList, engineContextDTO,
                    ruleConditionFactorValueDTO, benefitActInfoDTO);
            if (!canReceiveBenefit) {
                // 如果权益不能领取，则设置不能领取的权益列表
                fillUnableReceiveBenefitDTOList(engineContextDTO, benefitActInfoDTO, benefit);
            }
            return canReceiveBenefit;
        }).collect(Collectors.toList());
    }

    private BenefitGroupEngineContextDTO convertToBenefitEngineContextDTO(StrategyEngineContextDTO engineContextDTO) {
        return EngineContextConvert.INSTANCE.convertToBenefitEngineContextDTO(engineContextDTO);
    }

    private RuleFilterActInfoDTO convertToRuleFilterActInfoDTO(StrategyEngineContextDTO engineContextDTO) {
        return RuleFilterActInfoDTO.builder()
                .activityId(engineContextDTO.getActivityId())
                .strategyId(engineContextDTO.getStrategyId())
                .build();
    }

    private RuleFilterActInfoDTO convertToRuleFilterActInfoDTO(RuleFilterActInfoDTO actInfoDTO, BenefitIdWithConfDto benefit) {
        return RuleFilterActInfoDTO.builder()
                .activityId(actInfoDTO.getActivityId())
                .strategyId(actInfoDTO.getStrategyId())
                .benefitGroupId(benefit.getBenefitGroupId())
                .benefitId(benefit.getId())
                .build();
    }

    private void fillStrategyEngineContextDTO(StrategyEngineContextDTO engineContextDTO, StrategyCacheDto strategy) {
        engineContextDTO.setStrategyId(strategy.getStrategyId());
        engineContextDTO.setStrategyExternalId(strategy.getExternalId());
        engineContextDTO.setStrategySendType(strategy.getSendType());
    }

    /**
     * 设置不能领取的权益列表（策略维度）
     *
     * @param engineContextDTO   上下文
     * @param strategyActInfoDTO 过滤的活动、策略、权益组信息（用于打印日志，以及返回不能领取原因）
     * @param strategy           策略
     */
    private void fillUnableReceiveBenefitDTOList(StrategyEngineContextDTO engineContextDTO,
                                                 RuleFilterActInfoDTO strategyActInfoDTO,
                                                 StrategyCacheDto strategy) {
        // 如果未指定需要不能领取原因，则无需返回不能领取权益列表
        if (!Boolean.TRUE.equals(engineContextDTO.getNeedUnableReceiveReason())) {
            return;
        }
        List<UnableReceiveBenefitDTO> unableReceiveBenefitDTOList = EngineContextEntity.convertToUnableReceiveBenefitDTOList(
                engineContextDTO, strategyActInfoDTO, strategy.getStrategyBenefitGroup().get(strategy.getStrategyId()));
        if (CollectionUtils.isNotEmpty(unableReceiveBenefitDTOList)) {
            engineContextDTO.getResultContextDTO().getUnableReceiveBenefitDTOList().addAll(unableReceiveBenefitDTOList);
        }
    }

    /**
     * 设置不能领取的权益列表（策略维度）
     *
     * @param engineContextDTO  上下文
     * @param benefitActInfoDTO 过滤的活动、策略、权益组信息（用于打印日志，以及返回不能领取原因）
     * @param benefit           权益
     */
    private void fillUnableReceiveBenefitDTOList(StrategyEngineContextDTO engineContextDTO,
                                                 RuleFilterActInfoDTO benefitActInfoDTO,
                                                 BenefitIdWithConfDto benefit) {
        // 如果未指定需要不能领取原因，则无需返回不能领取权益列表
        if (!Boolean.TRUE.equals(engineContextDTO.getNeedUnableReceiveReason())) {
            return;
        }
        UnableReceiveBenefitDTO unableReceiveBenefitDTO = EngineContextEntity.convertToUnableReceiveBenefitDTO(
                engineContextDTO, benefitActInfoDTO, benefit);
        engineContextDTO.getResultContextDTO().getUnableReceiveBenefitDTOList().add(unableReceiveBenefitDTO);
    }
}
