package com.ddmc.equity.engine.benefit_group;

import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class BenefitSendHandlerContext {

    @Autowired
    private List<BenefitSendHandler> sendHandlers;

    private final Map<Integer, BenefitSendHandler> HANDLER_MAP = Maps.newHashMap();

    @PostConstruct
    public void init() {
        HANDLER_MAP.putAll(sendHandlers.stream()
                .collect(Collectors.toMap(BenefitSendHandler::getSendType, Function.identity())));
    }

    private BenefitSendHandler getSendHandler(Integer sendType) {
        return HANDLER_MAP.get(sendType);
    }

    public List<BenefitIdWithConfDto> chooseBenefitsBySendType(List<BenefitIdWithConfDto> benefits, Integer strategySendType) {
        // 如果传入权益为空或者只有一个，则直接返回，无需根据 sendType 再做 choose
        if (CollectionUtils.isEmpty(benefits) || CollectionUtils.size(benefits) == 1) {
            return benefits;
        }
        BenefitSendHandler sendHandler = this.getSendHandler(strategySendType);
        if (Objects.isNull(sendHandler)) {
            log.warn("chooseBenefitsBySendType handler is null. strategySendType={}", strategySendType);
            return null;
        }
        return sendHandler.chooseBenefits(benefits);
    }
}
