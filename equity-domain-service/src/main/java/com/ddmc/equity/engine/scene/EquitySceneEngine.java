package com.ddmc.equity.engine.scene;

import com.csoss.monitor.api.trace.Span;
import com.csoss.monitor.api.trace.Traces;
import com.ddmc.equity.domain.dto.engine.ActivityEngineContextDTO;
import com.ddmc.equity.domain.dto.engine.SceneEngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.entity.common.EngineContextEntity;
import com.ddmc.equity.engine.Engine;
import com.ddmc.equity.engine.activity.EquityActivityEngine;
import com.ddmc.equity.model.convert.EngineContextConvert;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 场景引擎
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class EquitySceneEngine implements Engine<SceneActivityCacheDto, SceneEngineContextDTO> {

    @Resource
    private EquityActivityEngine equityActivityEngine;

    @Override
    public List<SceneActivityCacheDto> consultSceneBenefit(List<SceneActivityCacheDto> needFilterList,
                                                           SceneEngineContextDTO engineContextDTO,
                                                           RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        Span span = Traces.spanBuilder("SceneEngineUserHitBenefit").startSpan();
        try {
            if (Objects.isNull(engineContextDTO)) {
                span.addEvent("invalidInput");
                return null;
            }
            span.setAttribute("sceneCode", engineContextDTO.getSceneCode());
            span.setAttribute("filterType", Objects.isNull(engineContextDTO.getActFilterType()) ?
                    StringUtils.EMPTY : engineContextDTO.getActFilterType().getName());

            // 1 调用【活动引擎】过滤
            ActivityEngineContextDTO activityEngineContextDTO = convertToActivityEngineContextDTO(engineContextDTO);
            List<SceneActivityCacheDto> hitActivities = equityActivityEngine.consultSceneBenefit(needFilterList,
                    activityEngineContextDTO, ruleConditionFactorValueDTO);
            // 需要把[活动引擎]返回结果上下文 copy 到 engineContextDTO
            EngineContextEntity.copyEngineResultContext(activityEngineContextDTO.getResultContextDTO(), engineContextDTO.getResultContextDTO());

            Set<Long> hitActivityIds = CollectionUtils.isEmpty(hitActivities) ? Sets.newHashSet() :
                    hitActivities.stream().map(SceneActivityCacheDto::getActivityId).collect(Collectors.toSet());
            span.setAttribute("hitActivities.size", CollectionUtils.size(hitActivities));
            span.setAttribute("hitActivities.ids", StringUtils.join(hitActivityIds, ","));
            return hitActivities;
        } catch (Exception e) {
            span.recordException(e);
            throw e;
        } finally {
            span.end();
        }
    }

    private ActivityEngineContextDTO convertToActivityEngineContextDTO(SceneEngineContextDTO engineContextDTO) {
        return EngineContextConvert.INSTANCE.convertToActivityEngineContextDTO(engineContextDTO);
    }
}
