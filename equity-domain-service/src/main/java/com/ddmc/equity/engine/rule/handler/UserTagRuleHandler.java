package com.ddmc.equity.engine.rule.handler;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.UserTagRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 人群标签
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserTagRuleHandler extends AbstractRuleHandler {

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.USER_TAG_RULE.getCode();
    }

    @Override
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {
        if (ruleNotNeedCheck(ruleDTO, engineContextDTO)) {
            return true;
        }
        String userId = engineContextDTO.getUserId();
        UserTagRuleDTO userTagRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleDTO, UserTagRuleDTO.class);
        List<String> ruleIds = Optional.ofNullable(userTagRuleDTO).map(UserTagRuleDTO::getRuleIds).orElse(null);
        if (CollectionUtils.isEmpty(ruleIds)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_USER_TAG.getCode());
            log.info("[规则过滤] userIsHitRule userTag 规则人群标签为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        List<String> factoryRuleIds = Optional.ofNullable(ruleConditionFactorValueDTO)
                .map(RuleConditionFactorValueDTO::getUserMatchTagRuleIds).orElse(null);
        if (CollectionUtils.isEmpty(factoryRuleIds)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_USER_TAG.getCode());
            log.info("[规则过滤] userIsHitRule userTag 因子值人群标签为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        if (ruleIds.stream().noneMatch(factoryRuleIds::contains)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_USER_TAG.getCode());
            log.info("[规则过滤] userIsHitRule userTag 命中人群标签失败. userId={}, actInfo={}, ruleIds={}, factoryRuleIds={}",
                    userId, JSON.toJSONString(actInfoDTO), JSON.toJSONString(ruleIds), JSON.toJSONString(factoryRuleIds));
            return false;
        }
        return true;
    }
}
