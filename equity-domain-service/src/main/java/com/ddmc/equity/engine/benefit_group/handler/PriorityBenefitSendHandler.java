package com.ddmc.equity.engine.benefit_group.handler;

import com.ddmc.equity.enums.StrategySendTypeEnum;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.engine.benefit_group.BenefitSendHandler;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * 优先级发放。取优先级最高的权益发放
 */
@Slf4j
@Component
public class PriorityBenefitSendHandler implements BenefitSendHandler {

    @Override
    public Integer getSendType() {
        return StrategySendTypeEnum.PRIORITY.getCode();
    }

    @Override
    public List<BenefitIdWithConfDto> chooseBenefits(List<BenefitIdWithConfDto> benefits) {
        benefits.sort((o1, o2) -> {
            BigDecimal o1Weight = NumberUtils.convertToBigDecimal(o1.getWeight());
            BigDecimal o2Weight = NumberUtils.convertToBigDecimal(o2.getWeight());
            return o2Weight.compareTo(o1Weight);
        });
        return Lists.newArrayList(benefits.get(0));
    }
}
