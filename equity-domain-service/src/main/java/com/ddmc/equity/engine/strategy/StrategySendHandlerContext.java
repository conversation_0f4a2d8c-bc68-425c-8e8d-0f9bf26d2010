package com.ddmc.equity.engine.strategy;

import com.ddmc.equity.model.dto.StrategyCacheDto;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/18 11:17
 * @description
 */
@Slf4j
@Component
public class StrategySendHandlerContext {

    @Autowired
    private List<StrategySendHandler> sendHandlers;

    private final Map<Integer, StrategySendHandler> HANDLER_MAP = Maps.newHashMap();

    @PostConstruct
    public void init() {
        HANDLER_MAP.putAll(sendHandlers.stream()
                .collect(Collectors.toMap(StrategySendHandler::getSendType, Function.identity())));
    }

    private StrategySendHandler getSendHandler(Integer sendType) {
        return HANDLER_MAP.get(sendType);
    }

    public List<StrategyCacheDto> chooseStrategiesBySendType(List<StrategyCacheDto> strategies, Integer activitySendType) {
        // 如果传入策略为空或者只有一个，则直接返回，无需根据 sendType 再做 choose
        if (CollectionUtils.isEmpty(strategies) || CollectionUtils.size(strategies) == 1) {
            return strategies;
        }
        StrategySendHandler sendHandler = this.getSendHandler(activitySendType);
        if (Objects.isNull(sendHandler)) {
            log.warn("chooseStrategiesBySendType handler is null. activitySendType={}", activitySendType);
            return null;
        }
        return sendHandler.chooseStrategies(strategies);
    }
}
