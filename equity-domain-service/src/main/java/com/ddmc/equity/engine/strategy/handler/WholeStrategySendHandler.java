package com.ddmc.equity.engine.strategy.handler;

import com.ddmc.equity.enums.ActivitySendTypeEnum;
import com.ddmc.equity.engine.strategy.StrategySendHandler;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/***
 * <AUTHOR>
 * 全部发放。不过滤
 */
@Slf4j
@Component
public class WholeStrategySendHandler implements StrategySendHandler {

    @Override
    public Integer getSendType() {
        return ActivitySendTypeEnum.WHOLE.getCode();
    }

    @Override
    public List<StrategyCacheDto> chooseStrategies(List<StrategyCacheDto> strategies) {
        return strategies;
    }
}
