package com.ddmc.equity.engine.activity.handler;

import com.ddmc.equity.enums.SceneSendTypeEnum;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.common.util.lottery.LotteryUtils;
import com.ddmc.equity.common.util.lottery.Gift;
import com.ddmc.equity.engine.activity.ActivitySendHandler;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/18 11:16
 * @description
 */
@Slf4j
@Service
public class ProbabilityActivitySendHandler implements ActivitySendHandler {

    @Override
    public Integer getSendType() {
        return SceneSendTypeEnum.PROBABILITY.getCode();
    }

    @Override
    public List<SceneActivityCacheDto> chooseActivities(List<SceneActivityCacheDto> activities) {
        Gift gift = drawGift(activities);
        if (Objects.isNull(gift)) {
            log.warn("activitySendHandler chooseActivities gift is null.");
            return null;
        }
        return activities.stream().filter(e -> StringUtils.equals(gift.getId(), getGiftId(e))).collect(Collectors.toList());
    }

    private Gift drawGift(List<SceneActivityCacheDto> activities) {
        List<Gift> gifts = activities.stream().map(activity -> Gift.builder()
                .id(getGiftId(activity))
                .weight(NumberUtils.convertToBigDecimal(activity.getWeight()))
                .build()
        ).collect(Collectors.toList());
        return LotteryUtils.drawGiftByWeight(gifts);
    }

    private String getGiftId(SceneActivityCacheDto activity) {
        return String.valueOf(activity.getActivityId());
    }
}
