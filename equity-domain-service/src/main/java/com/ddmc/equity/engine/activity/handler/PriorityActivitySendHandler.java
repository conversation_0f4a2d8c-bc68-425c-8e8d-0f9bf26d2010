package com.ddmc.equity.engine.activity.handler;

import com.ddmc.equity.enums.SceneSendTypeEnum;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.engine.activity.ActivitySendHandler;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/18 11:16
 * @description
 */
@Slf4j
@Service
public class PriorityActivitySendHandler implements ActivitySendHandler {

    @Override
    public Integer getSendType() {
        return SceneSendTypeEnum.PRIORITY.getCode();
    }

    @Override
    public List<SceneActivityCacheDto> chooseActivities(List<SceneActivityCacheDto> activities) {
        activities.sort((o1, o2) -> {
            BigDecimal o1Weight = NumberUtils.convertToBigDecimal(o1.getWeight());
            BigDecimal o2Weight = NumberUtils.convertToBigDecimal(o2.getWeight());
            return o2Weight.compareTo(o1Weight);
        });
        return Lists.newArrayList(activities.get(0));
    }
}
