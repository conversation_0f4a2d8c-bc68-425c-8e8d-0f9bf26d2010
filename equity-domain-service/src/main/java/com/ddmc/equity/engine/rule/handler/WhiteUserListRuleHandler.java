package com.ddmc.equity.engine.rule.handler;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.WhiteUserListRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

/**
 * 白名单
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WhiteUserListRuleHandler extends AbstractRuleHandler {

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.WHITE_USER_LIST_RULE.getCode();
    }

    @Override
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {
        if (ruleNotNeedCheck(ruleDTO, engineContextDTO)) {
            return true;
        }
        String userId = engineContextDTO.getUserId();
        WhiteUserListRuleDTO whiteUserListRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleDTO, WhiteUserListRuleDTO.class);
        List<String> whiteUserIds = Optional.ofNullable(whiteUserListRuleDTO).map(WhiteUserListRuleDTO::getWhiteUserList).orElse(null);
        if (CollectionUtils.isEmpty(whiteUserIds)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_WHITE_USER.getCode());
            log.info("[规则过滤] userIsHitRule whiteUser 规则白名单用户为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        if (!whiteUserIds.contains(userId)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_WHITE_USER.getCode());
            log.info("[规则过滤] userIsHitRule whiteUser 命中白名单用户失败. userId={}, actInfo={}, whiteUserIds={}",
                    userId, JSON.toJSONString(actInfoDTO), JSON.toJSONString(whiteUserIds));
            return false;
        }
        return true;
    }
}
