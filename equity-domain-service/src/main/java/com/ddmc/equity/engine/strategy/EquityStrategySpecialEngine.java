package com.ddmc.equity.engine.strategy;


import com.ddmc.equity.domain.dto.engine.StrategyEngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.engine.benefit_group.EquityBenefitGroupEngine;
import com.ddmc.equity.engine.rule.RuleContext;
import com.ddmc.equity.enums.ActivitySendTypeEnum;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 特殊策略引擎，针对中国银行人群
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class EquityStrategySpecialEngine extends EquityStrategyEngine {

    @Autowired
    private RuleContext ruleContext;
    @Autowired
    private StrategySendHandlerContext strategySendHandlerContext;
    @Autowired
    private EquityBenefitGroupEngine equityBenefitGroupEngine;

    @Override
    public List<StrategyCacheDto> consultSceneBenefit(List<StrategyCacheDto> needFilterList,
                                                      StrategyEngineContextDTO engineContextDTO,
                                                      RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {

        //忽略库存和频次限制咨询一遍
        engineContextDTO.setIgnoreReceiveLimit(true);
        List<StrategyCacheDto> firstChooseStrategies = super.consultSceneBenefit(needFilterList, engineContextDTO, ruleConditionFactorValueDTO);
        if (CollectionUtils.isEmpty(firstChooseStrategies)) {
            return null;
        }
        //复原,恢复库存和频次限制再咨询一遍
        engineContextDTO.setIgnoreReceiveLimit(false);
        return super.consultSceneBenefit(firstChooseStrategies, engineContextDTO, ruleConditionFactorValueDTO);


    }

    @Override
    public List<Integer> getActivitySendType() {
        return Lists.newArrayList(ActivitySendTypeEnum.SPECIAL_PRIORITY.getCode());
    }

}
