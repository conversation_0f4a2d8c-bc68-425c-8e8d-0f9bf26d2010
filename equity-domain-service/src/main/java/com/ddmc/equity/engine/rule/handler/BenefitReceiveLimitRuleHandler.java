package com.ddmc.equity.engine.rule.handler;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.common.util.MapUtils;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.BenefitReceiveLimitFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 领取频次限制，权益规则
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BenefitReceiveLimitRuleHandler extends AbstractRuleHandler {

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.BENEFIT.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode();
    }


    @Override
    public boolean isCanReceiveBenefit(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                       RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                       RuleFilterActInfoDTO actInfoDTO) {
        String userId = engineContextDTO.getUserId();
        ReceiveLimitRuleDTO receiveLimitRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleDTO, ReceiveLimitRuleDTO.class);
        if (Objects.isNull(receiveLimitRuleDTO)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.FREQUENCY_NOT_ENOUGH.getCode());
            log.info("[规则过滤] isCanReceiveBenefit receiveLimit 规则为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        Map<String, BenefitReceiveLimitFactorValueDTO> factorValueMap = ruleConditionFactorValueDTO.getBenefitReceiveLimitFactorValueMap();
        String key = actInfoDTO.getStrategyId() + Constants.COLON + actInfoDTO.getBenefitId();
        BenefitReceiveLimitFactorValueDTO benefitReceiveLimitFactorValueDTO = MapUtils.isEmpty(factorValueMap) ? null :
                factorValueMap.get(key);
        // 如果频次限制因子值不存在，则表示无频次限制；或者用户首次咨询时，对应的频次限制还未初始化到 redis 中
        if (Objects.nonNull(benefitReceiveLimitFactorValueDTO) && !benefitReceiveLimitFactorValueDTO.isCanReceive()) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.FREQUENCY_NOT_ENOUGH.getCode());
            log.info("[规则过滤] isCanReceiveBenefit receiveLimit can not receive. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        return true;
    }

    @Override
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {

        return isCanReceiveBenefit(ruleDTO, engineContextDTO,
                ruleConditionFactorValueDTO,
                actInfoDTO);
    }
}
