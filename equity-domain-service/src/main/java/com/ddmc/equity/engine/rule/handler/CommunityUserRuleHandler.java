package com.ddmc.equity.engine.rule.handler;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.CommunityUserRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * 社群用户规则处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CommunityUserRuleHandler extends AbstractRuleHandler {

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.COMMUNITY_USER.getCode();
    }

    @Override
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {
        if (ruleNotNeedCheck(ruleDTO, engineContextDTO)) {
            return true;
        }

        String userId = engineContextDTO.getUserId();
        CommunityUserRuleDTO communityUserRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleDTO,
                CommunityUserRuleDTO.class);
        if (communityUserRuleDTO == null) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_COMMUNITY_USER.getCode());
            log.info("[规则过滤] userIsHitRule communityUser 规则为空. userId={}, actInfo={}",
                    userId, JSON.toJSONString(actInfoDTO));
            return false;
        }

        Integer communityUserStatus = communityUserRuleDTO.getCommunityUserStatus();
        Boolean isCommunityUser = Optional.ofNullable(ruleConditionFactorValueDTO)
                .map(RuleConditionFactorValueDTO::getIsCommunityUser)
                .orElse(false);
        // 命中社群用户规则（全部社群用户）
        if (Objects.equals(communityUserStatus, Constants.ONE) && Boolean.TRUE.equals(isCommunityUser)) {
            return true;
        }

        actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_COMMUNITY_USER.getCode());
        log.info("[规则过滤] userIsHitRule communityUser 命中社群用户规则失败. userId={}, actInfo={}" +
                        ", communityUserRuleDTO={}, isCommunityUser={}",
                userId, JSON.toJSONString(actInfoDTO), JSON.toJSONString(communityUserRuleDTO), isCommunityUser);
        return false;
    }
} 