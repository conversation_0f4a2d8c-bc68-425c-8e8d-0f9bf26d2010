package com.ddmc.equity.engine.activity.handler;

import com.ddmc.equity.enums.SceneSendTypeEnum;
import com.ddmc.equity.common.util.lottery.LotteryUtils;
import com.ddmc.equity.engine.activity.ActivitySendHandler;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/18 11:17
 * @description
 */
@Slf4j
@Service
public class RandomActivitySendHandler implements ActivitySendHandler {

    @Override
    public Integer getSendType() {
        return SceneSendTypeEnum.RANDOM.getCode();
    }

    @Override
    public List<SceneActivityCacheDto> chooseActivities(List<SceneActivityCacheDto> activities) {
        int index = LotteryUtils.drawIndexByRandom(CollectionUtils.size(activities));
        return Lists.newArrayList(activities.get(index));
    }
}
