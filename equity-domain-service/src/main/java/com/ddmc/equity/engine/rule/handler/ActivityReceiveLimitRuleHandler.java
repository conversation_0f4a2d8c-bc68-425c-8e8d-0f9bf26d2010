package com.ddmc.equity.engine.rule.handler;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.common.util.MapUtils;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.ActivityReceiveLimitFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 领取频次限制，活动规则
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ActivityReceiveLimitRuleHandler extends AbstractRuleHandler {

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.ACTIVITY.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode();
    }

    @Override
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {
        if (ruleNotNeedCheck(ruleDTO, engineContextDTO)) {
            return true;
        }
        String userId = engineContextDTO.getUserId();
        ReceiveLimitRuleDTO receiveLimitRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleDTO, ReceiveLimitRuleDTO.class);
        if (Objects.isNull(receiveLimitRuleDTO)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.FREQUENCY_NOT_ENOUGH.getCode());
            log.info("[规则过滤] activityReceiveLimitRule receiveLimit 规则为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        Map<Long, ActivityReceiveLimitFactorValueDTO> factorValueMap = ruleConditionFactorValueDTO.getActivityReceiveLimitFactorValueMap();
        ActivityReceiveLimitFactorValueDTO activityReceiveLimitFactorValueDTO = MapUtils.isEmpty(factorValueMap) ? null :
                factorValueMap.get(actInfoDTO.getActivityId());
        // 如果频次限制因子值不存在，则表示无频次限制；或者用户首次咨询时，对应的频次限制还未初始化到 redis 中
        if (Objects.nonNull(activityReceiveLimitFactorValueDTO) && !activityReceiveLimitFactorValueDTO.isCanReceive()) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.FREQUENCY_NOT_ENOUGH.getCode());
            log.info("[规则过滤] activityReceiveLimitRule receiveLimit can not receive. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        return true;
    }
}
