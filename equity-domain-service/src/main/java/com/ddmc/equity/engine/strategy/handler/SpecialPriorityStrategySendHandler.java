package com.ddmc.equity.engine.strategy.handler;

import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.engine.strategy.StrategySendHandler;
import com.ddmc.equity.enums.ActivitySendTypeEnum;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/***
 * <AUTHOR>
 * 优先级发放。取优先级最高的策略发放
 */
@Slf4j
@Component
public class SpecialPriorityStrategySendHandler implements StrategySendHandler {

    @Override
    public Integer getSendType() {
        return ActivitySendTypeEnum.SPECIAL_PRIORITY.getCode();
    }

    //返回权重最大的，如果权重最大的有多个，就都返回
    @Override
    public List<StrategyCacheDto> chooseStrategies(List<StrategyCacheDto> strategies) {
        strategies.sort((o1, o2) -> {
            BigDecimal o1Weight = NumberUtils.convertToBigDecimal(o1.getActivityWeight());
            BigDecimal o2Weight = NumberUtils.convertToBigDecimal(o2.getActivityWeight());
            return o2Weight.compareTo(o1Weight);
        });
        //第一个就是权重值最大的
        StrategyCacheDto dto = strategies.get(0);
        //跟第一个 权重值 一样的都返回
        return strategies.stream().filter(s -> s.getActivityWeight().equals(dto.getActivityWeight())).collect(Collectors.toList());
    }
}
