package com.ddmc.equity.engine.rule.handler;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.OldSysBOCUserRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.infra.rpc.user.UserProxy;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * 老玩法系统的中国银行渠道用户人群，暂时未用到（之前讨论这种不通用的规则，还是放在玩法侧）
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OldSysBOCUserRuleHandler extends AbstractRuleHandler {

    @Resource
    private UserProxy userProxy;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.OLD_SYS_BANK_BOC_USER_RULE.getCode();
    }

    @Override
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {
        if (ruleNotNeedCheck(ruleDTO, engineContextDTO)) {
            return true;
        }
        String userId = engineContextDTO.getUserId();
        OldSysBOCUserRuleDTO bocUserRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleDTO, OldSysBOCUserRuleDTO.class);
        //当前奖品(策略规则)填写的银联号
        String bankCode = Optional.ofNullable(bocUserRuleDTO)
                .map(OldSysBOCUserRuleDTO::getBankCode).orElse(null);

        //本人的银联号
        String factorBankCode = Optional.ofNullable(ruleConditionFactorValueDTO).map(RuleConditionFactorValueDTO::getBankCode).orElse(null);

        if (StringUtils.isNotBlank(factorBankCode) && factorBankCode.equals(bankCode)) {
            return true;
        }
        if (StringUtils.isNotBlank(factorBankCode) && StringUtils.isBlank(bankCode)) {
            return true;
        }


        log.info("[规则过滤] userIsHitRule bocUser 命中中国银行银行号失败. userId={}, actInfo={}, bankCode={}, factorBankCode={}",
                userId, JSON.toJSONString(actInfoDTO), bankCode, factorBankCode);
        return false;
    }

}
