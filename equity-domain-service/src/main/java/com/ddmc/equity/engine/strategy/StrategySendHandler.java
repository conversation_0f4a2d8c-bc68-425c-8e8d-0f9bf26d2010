package com.ddmc.equity.engine.strategy;

import com.ddmc.equity.model.dto.StrategyCacheDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface StrategySendHandler {

    /**
     * 获取发放类型
     *
     * @return 发放类型
     */
    Integer getSendType();

    /**
     * 根据发放类型选中用户可以领取的策略列表
     *
     * @param strategies 传入策略列表
     * @return 用户可以领取的策略列表
     */
    List<StrategyCacheDto> chooseStrategies(List<StrategyCacheDto> strategies);
}
