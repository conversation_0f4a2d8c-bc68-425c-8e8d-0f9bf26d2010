package com.ddmc.equity.engine.rule.handler;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.AppVersionCompareTypeEnum;
import com.ddmc.equity.common.enums.AppVersionJoinTypeEnum;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.common.util.VersionStringUtils;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.AppVersionRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.AppVersionRuleValueDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * App 版本
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AppVersionRuleHandler extends AbstractRuleHandler {

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.APP_VERSION_RULE.getCode();
    }

    @Override
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {
        if (ruleNotNeedCheck(ruleDTO, engineContextDTO)) {
            return true;
        }
        String userId = engineContextDTO.getUserId();
        AppVersionRuleDTO appVersionRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleDTO, AppVersionRuleDTO.class);
        List<AppVersionRuleValueDTO> appVersionRuleValues = Optional.ofNullable(appVersionRuleDTO).map(AppVersionRuleDTO::getAppVersionList).orElse(null);
        if (CollectionUtils.isEmpty(appVersionRuleValues)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_APP_VERSION.getCode());
            log.info("[规则过滤] userIsHitRule appVersion 规则 App 版本为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        String reqNativeVersion = engineContextDTO.getNativeVersion();
        boolean isHit = false;
        Integer appVersionJoinType = appVersionRuleDTO.getJoinType();
        if (AppVersionJoinTypeEnum.OR.getCode().equals(appVersionJoinType)) {
            isHit = appVersionRuleValues.stream().anyMatch(appVersionRuleValue ->
                    appVersionCompare(reqNativeVersion, appVersionRuleValue.getCompareType(), appVersionRuleValue.getNativeVersion())
            );
        } else if (AppVersionJoinTypeEnum.AND.getCode().equals(appVersionJoinType)) {
            isHit = appVersionRuleValues.stream().allMatch(appVersionRuleValue ->
                    appVersionCompare(reqNativeVersion, appVersionRuleValue.getCompareType(), appVersionRuleValue.getNativeVersion())
            );
        }
        if (!isHit) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_APP_VERSION.getCode());
            log.info("[规则过滤] userIsHitRule appVersion 用户命中 App 版本失败. userId={}, actInfo={}, appVersionRuleValues={}",
                    userId, JSON.toJSONString(actInfoDTO), JSON.toJSONString(appVersionRuleValues));
        }
        return isHit;
    }

    /**
     * 版本比较
     *
     * @param currentNativeVersion 当前版本
     * @param compareType          比较类型
     * @param nativeVersions       投放版本列表
     * @return 是否匹配
     */
    private boolean appVersionCompare(String currentNativeVersion, Integer compareType, List<String> nativeVersions) {
        if (CollectionUtils.isEmpty(nativeVersions) || StringUtils.isBlank(currentNativeVersion)) {
            return false;
        }
        if (!AppVersionCompareTypeEnum.isContain(compareType)) {
            return false;
        }
        String nativeVersion = nativeVersions.get(0);
        // 大于
        if (AppVersionCompareTypeEnum.GT.getCode().equals(compareType)) {
            return VersionStringUtils.versionCompare(currentNativeVersion, nativeVersion) == 1;
        }
        // 大于等于
        if (AppVersionCompareTypeEnum.GTE.getCode().equals(compareType)) {
            return VersionStringUtils.versionCompare(currentNativeVersion, nativeVersion) >= 0;
        }
        // 小于
        if (AppVersionCompareTypeEnum.LT.getCode().equals(compareType)) {
            return VersionStringUtils.versionCompare(currentNativeVersion, nativeVersion) == -1;
        }
        // 小于等于
        if (AppVersionCompareTypeEnum.LTE.getCode().equals(compareType)) {
            int result = VersionStringUtils.versionCompare(currentNativeVersion, nativeVersion);
            return result <= 0;
        }
        // 介于
        if (AppVersionCompareTypeEnum.BETWEEN.getCode().equals(compareType)) {
            if (CollectionUtils.size(nativeVersions) != 2) {
                return false;
            }
            String startVersion = nativeVersions.get(0);
            String endVersion = nativeVersions.get(1);
            return VersionStringUtils.versionCompare(currentNativeVersion, startVersion) >= 0 &&
                    VersionStringUtils.versionCompare(currentNativeVersion, endVersion) <= 0;
        }
        return false;
    }
}
