package com.ddmc.equity.engine.rule.handler;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.WeComUserRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * 企微用户身份
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WeComUserRuleHandler extends AbstractRuleHandler {

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.WE_COM_USER_RULE.getCode();
    }

    @Override
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {
        if (ruleNotNeedCheck(ruleDTO, engineContextDTO)) {
            return true;
        }
        String userId = engineContextDTO.getUserId();
        WeComUserRuleDTO weComUserRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleDTO, WeComUserRuleDTO.class);
        Integer userStatus = Optional.ofNullable(weComUserRuleDTO).map(WeComUserRuleDTO::getWeComUserStatus).orElse(null);
        if (Objects.isNull(userStatus)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_WECOM_USER.getCode());
            log.info("[规则过滤] userIsHitRule WECOM_USER 规则用户身份为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        //全部，暂时没有
        if (userStatus == 0) {
            return true;
        }
        Integer factorUserStatus = Optional.ofNullable(ruleConditionFactorValueDTO).map(RuleConditionFactorValueDTO::getWeComUserStatus).orElse(null);
        if (Objects.isNull(factorUserStatus)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_WECOM_USER.getCode());
            log.info("[规则过滤] userIsHitRule WECOM_USER 因子值用户身份为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        if (!userStatus.equals(factorUserStatus)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_WECOM_USER.getCode());
            log.info("[规则过滤] userIsHitRule WECOM_USER 命中用户身份失败. userId={}, actInfo={}, userStatus={}, factorUserStatus={}",
                    userId, JSON.toJSONString(actInfoDTO), userStatus, factorUserStatus);
            return false;
        }
        return true;
    }
}
