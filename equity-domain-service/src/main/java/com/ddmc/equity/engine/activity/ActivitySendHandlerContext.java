package com.ddmc.equity.engine.activity;

import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/18 10:22
 * @description
 */
@Slf4j
@Component
public class ActivitySendHandlerContext {

    @Autowired
    private List<ActivitySendHandler> sendHandlers;

    private final Map<Integer, ActivitySendHandler> HANDLER_MAP = Maps.newHashMap();

    @PostConstruct
    public void init() {
        HANDLER_MAP.putAll(sendHandlers.stream()
                .collect(Collectors.toMap(ActivitySendHandler::getSendType, Function.identity())));
    }

    private ActivitySendHandler getSendHandler(Integer sendType) {
        return HANDLER_MAP.get(sendType);
    }

    public List<SceneActivityCacheDto> chooseActivitiesBySendType(List<SceneActivityCacheDto> activities, Integer sceneSendType) {
        // 如果传入活动为空或者只有一个，则直接返回，无需根据 sendType 再做 choose
        if (CollectionUtils.isEmpty(activities) || CollectionUtils.size(activities) == 1) {
            return activities;
        }
        ActivitySendHandler sendHandler = this.getSendHandler(sceneSendType);
        if (Objects.isNull(sendHandler)) {
            log.warn("chooseActivitiesBySendType handler is null. sceneSendType={}", sceneSendType);
            return null;
        }
        return sendHandler.chooseActivities(activities);
    }
}
