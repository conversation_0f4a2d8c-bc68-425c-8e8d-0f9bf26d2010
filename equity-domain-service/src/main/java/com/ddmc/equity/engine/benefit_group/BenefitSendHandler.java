package com.ddmc.equity.engine.benefit_group;

import com.ddmc.equity.model.dto.BenefitIdWithConfDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BenefitSendHandler {

    /**
     * 获取发放类型
     *
     * @return 发放类型
     */
    Integer getSendType();

    /**
     * 根据发放类型选中用户可以领取的权益列表
     *
     * @param benefits 传入权益列表
     * @return 用户可以领取的权益列表
     */
    List<BenefitIdWithConfDto> chooseBenefits(List<BenefitIdWithConfDto> benefits);
}
