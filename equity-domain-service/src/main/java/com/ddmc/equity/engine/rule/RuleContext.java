package com.ddmc.equity.engine.rule;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.entity.activity.ActivityCacheRuleConvertEntity;
import com.ddmc.equity.model.dto.ActivityRuleCacheDTO;
import com.ddmc.equity.model.dto.BenefitRuleCacheDTO;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import com.ddmc.equity.model.dto.StrategyRuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 规则处理
 */
@Slf4j
@Component
public class RuleContext {

    @Autowired
    List<AbstractRuleHandler> abstractRuleHandlers;

    private final Map<Pair<Integer /* ruleDimension */, Integer /* ruleType */>, AbstractRuleHandler> HANDLER_MAP = new HashMap<>(16);

    @PostConstruct
    public void afterPropertiesSet() {
        HANDLER_MAP.putAll(abstractRuleHandlers.stream().collect(Collectors.toMap(handler ->
                Pair.of(handler.getRuleDimension(), handler.getRuleType()), Function.identity(), (v1, v2) -> v1)));
    }

    public boolean userHasNotHitActivityRule(List<ActivityRuleCacheDTO> ruleCacheDTOList, EngineContextDTO engineContextDTO,
                                             RuleConditionFactorValueDTO ruleConditionFactorValueDTO, RuleFilterActInfoDTO actInfoDTO) {
        Map<Pair<Integer, Integer>, ActivityRuleCacheDTO> rulesMap = ActivityCacheRuleConvertEntity.getActivityRulesMap(ruleCacheDTOList);
        return userHasNotHitRule(rulesMap, engineContextDTO, ruleConditionFactorValueDTO, actInfoDTO);
    }

    public boolean userHasNotHitStrategyRule(List<StrategyRuleCacheDTO> ruleCacheDTOList, EngineContextDTO engineContextDTO,
                                             RuleConditionFactorValueDTO ruleConditionFactorValueDTO, RuleFilterActInfoDTO actInfoDTO) {
        Map<Pair<Integer, Integer>, StrategyRuleCacheDTO> rulesMap = ActivityCacheRuleConvertEntity.getStrategyRulesMap(ruleCacheDTOList);
        return userHasNotHitRule(rulesMap, engineContextDTO, ruleConditionFactorValueDTO, actInfoDTO);
    }

    public boolean userHasNotHitBenefitRule(List<BenefitRuleCacheDTO> ruleCacheDTOList, EngineContextDTO engineContextDTO,
                                            RuleConditionFactorValueDTO ruleConditionFactorValueDTO, RuleFilterActInfoDTO actInfoDTO) {
        Map<Pair<Integer, Integer>, BenefitRuleCacheDTO> rulesMap = ActivityCacheRuleConvertEntity.getBenefitRulesMap(ruleCacheDTOList);
        return userHasNotHitRule(rulesMap, engineContextDTO, ruleConditionFactorValueDTO, actInfoDTO);
    }

    /**
     * 判断用户是否有未命中的规则
     */
    public boolean userHasNotHitRule(Map<Pair<Integer/* ruleDimension */, Integer /* ruleType */>, ? extends RuleCacheDTO> ruleMap,
                                     EngineContextDTO engineContextDTO, RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                     RuleFilterActInfoDTO actInfoDTO) {
        List<Map.Entry<Pair<Integer, Integer>, ? extends RuleCacheDTO>> ruleEntries = ActivityCacheRuleConvertEntity.getSortRuleEntries(ruleMap);
        if (CollectionUtils.isEmpty(ruleEntries)) {
            return false;
        }
        return ruleEntries.stream().anyMatch(entry -> {
            // 如果规则处理类未找到，则规则相当于未命中
            Pair<Integer, Integer> key = entry.getKey();
            AbstractRuleHandler handler = HANDLER_MAP.get(key);
            if (Objects.isNull(handler)) {
                log.warn("ruleHasNotHitRule handler is null. ruleDimension={}, ruleType={}, actInfoDTO={}",
                        key.getFirst(), key.getSecond(), JSON.toJSONString(actInfoDTO));
                return true;
            }
            return !handler.userIsHitRule(entry.getValue(), engineContextDTO, ruleConditionFactorValueDTO, actInfoDTO);
        });
    }

    /**
     * 判断用户是否不可以领取权益
     */
    public boolean userIsCanReceiveBenefit(List<StrategyRuleCacheDTO> ruleCacheDTOList,
                                           EngineContextDTO engineContextDTO,
                                           RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                           RuleFilterActInfoDTO actInfoDTO) {
        Map<Pair<Integer, Integer>, StrategyRuleCacheDTO> rulesMap = ActivityCacheRuleConvertEntity.getStrategyRulesMap(ruleCacheDTOList);
        List<Map.Entry<Pair<Integer, Integer>, ? extends RuleCacheDTO>> ruleEntries = ActivityCacheRuleConvertEntity.getSortRuleEntries(rulesMap);
        if (CollectionUtils.isEmpty(ruleEntries)) {
            return true;
        }
        boolean hasAnyNotHit = ruleEntries.stream().anyMatch(entry -> {
            RuleCacheDTO rule = entry.getValue();
            if (!RuleTypeEnum.hasDimension(rule.getRuleType())) {
                return false;
            }
            // 如果规则处理类未找到，则规则相当于未命中
            Pair<Integer, Integer> key = entry.getKey();
            AbstractRuleHandler handler = HANDLER_MAP.get(key);
            if (Objects.isNull(handler)) {
                log.warn("userIsCanNotReceiveBenefit handler is null. ruleDimension={}, ruleType={}, actInfoDTO={}",
                        key.getFirst(), key.getSecond(), JSON.toJSONString(actInfoDTO));
                return true;
            }
            // 判断权益是否可以领取。权益维度的频次限制、库存限制过滤
            return !handler.isCanReceiveBenefit(rule, engineContextDTO, ruleConditionFactorValueDTO, actInfoDTO);
        });
        return !hasAnyNotHit;
    }
}
