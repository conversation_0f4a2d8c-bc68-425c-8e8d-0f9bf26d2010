package com.ddmc.equity.engine.rule;

import com.ddmc.equity.enums.RuleScopeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.model.dto.RuleCacheDTO;

import java.util.Objects;

/**
 * <AUTHOR>
 */
public abstract class AbstractRuleHandler {

    /**
     * 获取规则维度
     *
     * @return 规则维度
     */
    public abstract Integer getRuleDimension();

    /**
     * 获取规则类型
     *
     * @return 规则类型
     */
    public abstract Integer getRuleType();

    /**
     * 判断规则是否不需要校验，此次咨询、领取是否需要校验该规则
     *
     * @param ruleDTO          规则
     * @param engineContextDTO 上下文
     * @return 此次咨询、领取是否需要校验该规则
     */
    protected boolean ruleNotNeedCheck(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO) {
        return !ruleNeedCheck(ruleDTO, engineContextDTO);
    }

    private boolean ruleNeedCheck(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO) {
        Integer ruleScope = ruleDTO.getRuleScope();
        // 咨询和领券都需要校验
        if (Objects.isNull(ruleScope) || RuleScopeEnum.CONSULT_AND_RECEIVE.getCode().equals(ruleScope)) {
            return true;
        }
        // 咨询需要校验
        boolean isReceive = Boolean.TRUE.equals(engineContextDTO.getIsReceive());
        if (RuleScopeEnum.CONSULT.getCode().equals(ruleScope) && !isReceive) {
            return true;
        }
        // 领取需要校验
        return RuleScopeEnum.RECEIVE.getCode().equals(ruleScope) && isReceive;
    }

    /**
     * 用户是否命中规则，默认命中
     *
     * @param ruleDTO                     规则
     * @param engineContextDTO            上下文
     * @param ruleConditionFactorValueDTO 规则因子值
     * @param actInfoDTO                  过滤的活动、策略、权益组信息（用于打印日志，以及返回不能领取原因）
     * @return 是否命中
     */
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {
        return true;
    }

    /**
     * 权益是否可以领取，默认可以领取。过滤领取频次限制、领取库存限制
     * <p>
     * 20230707lry 因为历史原因领券活动把权益维度的频次、库存规则保存策略上了，所一需要特出处理策略频次、库存规则 handler
     *
     * @param ruleDTO                     规则
     * @param engineContextDTO            上下文
     * @param ruleConditionFactorValueDTO 规则因子值
     * @param actInfoDTO                  过滤的活动、策略、权益组信息（用于打印日志，以及返回不能领取原因）
     * @return 可以领取
     */
    public boolean isCanReceiveBenefit(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                       RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                       RuleFilterActInfoDTO actInfoDTO) {
        return true;
    }
}
