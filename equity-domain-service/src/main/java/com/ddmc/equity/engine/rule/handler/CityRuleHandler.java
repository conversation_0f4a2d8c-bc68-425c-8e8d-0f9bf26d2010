package com.ddmc.equity.engine.rule.handler;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.CityRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 城市
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CityRuleHandler extends AbstractRuleHandler {

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.CITY_RULE.getCode();
    }

    @Override
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {
        if (ruleNotNeedCheck(ruleDTO, engineContextDTO)) {
            return true;
        }
        String userId = engineContextDTO.getUserId();
        CityRuleDTO cityRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleDTO, CityRuleDTO.class);
        List<String> cityCodes = Optional.ofNullable(cityRuleDTO).map(CityRuleDTO::getCityCodeList).orElse(null);
        if (CollectionUtils.isEmpty(cityCodes)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_CITY.getCode());
            log.info("[规则过滤] userIsHitRule city 规则城市为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        if ("equity-job-service".equals(engineContextDTO.getAppId())) {
            log.info("[规则过滤] userIsHitRule city binlog 同步临时豁免城市规则. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return true;
        }
        String reqCityNumber = engineContextDTO.getCityNumber();
        if (!cityCodes.contains(reqCityNumber)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.NOT_HIT_CITY.getCode());
            log.info("[规则过滤] userIsHitRule city 命中城市失败. userId={}, actInfo={}, cityCodes={}, reqCityNumber={}",
                    userId, JSON.toJSONString(actInfoDTO), JSON.toJSONString(cityCodes), reqCityNumber);
            return false;
        }
        return true;
    }


}
