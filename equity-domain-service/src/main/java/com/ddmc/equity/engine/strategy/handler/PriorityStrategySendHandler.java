package com.ddmc.equity.engine.strategy.handler;

import com.ddmc.equity.enums.ActivitySendTypeEnum;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.engine.strategy.StrategySendHandler;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/***
 * <AUTHOR>
 * 优先级发放。取优先级最高的策略发放
 */
@Slf4j
@Component
public class PriorityStrategySendHandler implements StrategySendHandler {

    @Override
    public Integer getSendType() {
        return ActivitySendTypeEnum.PRIORITY.getCode();
    }

    @Override
    public List<StrategyCacheDto> chooseStrategies(List<StrategyCacheDto> strategies) {
        strategies.sort((o1, o2) -> {
            BigDecimal o1Weight = NumberUtils.convertToBigDecimal(o1.getActivityWeight());
            BigDecimal o2Weight = NumberUtils.convertToBigDecimal(o2.getActivityWeight());
            return o2Weight.compareTo(o1Weight);
        });
        return Lists.newArrayList(strategies.get(0));
    }
}
