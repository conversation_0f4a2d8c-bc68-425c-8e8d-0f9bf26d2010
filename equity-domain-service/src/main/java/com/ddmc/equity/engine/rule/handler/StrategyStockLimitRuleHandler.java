package com.ddmc.equity.engine.rule.handler;

import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 领取库存限制，策略规则
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class StrategyStockLimitRuleHandler extends AbstractRuleHandler {

    @Autowired
    private BenefitStockLimitRuleHandler benefitStockLimitRuleHandler;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.STRATEGY.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.STOCK_LIMIT_RULE.getCode();
    }

    @Override
    public boolean isCanReceiveBenefit(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                       RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                       RuleFilterActInfoDTO actInfoDTO) {
        return benefitStockLimitRuleHandler.userIsHitRule(ruleDTO, engineContextDTO, ruleConditionFactorValueDTO,
                actInfoDTO);
    }
}
