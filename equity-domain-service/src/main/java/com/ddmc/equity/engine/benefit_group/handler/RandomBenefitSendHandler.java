package com.ddmc.equity.engine.benefit_group.handler;

import com.ddmc.equity.enums.StrategySendTypeEnum;
import com.ddmc.equity.common.util.lottery.LotteryUtils;
import com.ddmc.equity.engine.benefit_group.BenefitSendHandler;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * 随机发放。随机取一个权益发放
 */
@Slf4j
@Component
public class RandomBenefitSendHandler implements BenefitSendHandler {

    @Override
    public Integer getSendType() {
        return StrategySendTypeEnum.RANDOM.getCode();
    }

    @Override
    public List<BenefitIdWithConfDto> chooseBenefits(List<BenefitIdWithConfDto> benefits) {
        int index = LotteryUtils.drawIndexByRandom(CollectionUtils.size(benefits));
        return Lists.newArrayList(benefits.get(index));
    }
}
