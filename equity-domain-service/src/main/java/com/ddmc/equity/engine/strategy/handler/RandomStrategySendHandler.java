package com.ddmc.equity.engine.strategy.handler;

import com.ddmc.equity.enums.ActivitySendTypeEnum;
import com.ddmc.equity.common.util.lottery.LotteryUtils;
import com.ddmc.equity.engine.strategy.StrategySendHandler;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/***
 * <AUTHOR>
 * 随机发放。随机取一个策略发放
 */
@Slf4j
@Component
public class RandomStrategySendHandler implements StrategySendHandler {

    @Override
    public Integer getSendType() {
        return ActivitySendTypeEnum.RANDOM.getCode();
    }

    @Override
    public List<StrategyCacheDto> chooseStrategies(List<StrategyCacheDto> strategies) {
        int index = LotteryUtils.drawIndexByRandom(CollectionUtils.size(strategies));
        return Lists.newArrayList(strategies.get(index));
    }
}
