package com.ddmc.equity.engine.rule.handler;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.BenefitStockFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.engine.rule.AbstractRuleHandler;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 领取库存限制，权益规则
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BenefitStockLimitRuleHandler extends AbstractRuleHandler {

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.BENEFIT.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.STOCK_LIMIT_RULE.getCode();
    }

    @Override
    public boolean userIsHitRule(RuleCacheDTO ruleDTO, EngineContextDTO engineContextDTO,
                                 RuleConditionFactorValueDTO ruleConditionFactorValueDTO,
                                 RuleFilterActInfoDTO actInfoDTO) {
        if (ruleNotNeedCheck(ruleDTO, engineContextDTO)) {
            return true;
        }
        String userId = engineContextDTO.getUserId();
        StockLimitRuleDTO stockLimitRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleDTO, StockLimitRuleDTO.class);
        if (Objects.isNull(stockLimitRuleDTO)) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.STOCK_NOT_ENOUGH.getCode());
            log.info("[规则过滤] benefitStockLimitRule stockLimit 规则为空. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        Map<String, BenefitStockFactorValueDTO> factorValueMap = ruleConditionFactorValueDTO.getBenefitStockLimitFactorValueMap();
        String key = actInfoDTO.getStrategyId() + Constants.COLON + actInfoDTO.getBenefitId();
        BenefitStockFactorValueDTO benefitStockFactorValueDTO = MapUtils.isEmpty(factorValueMap) ? null : factorValueMap.get(key);
        if (Objects.isNull(benefitStockFactorValueDTO) || !benefitStockFactorValueDTO.isCanReceive()) {
            actInfoDTO.setUnableReceiveReasonCode(BenefitUnableReceiveReasonType.STOCK_NOT_ENOUGH.getCode());
            log.info("[规则过滤] benefitStockLimitRule stockLimit can not receive. userId={}, actInfo={}", userId, JSON.toJSONString(actInfoDTO));
            return false;
        }
        return true;
    }
}
