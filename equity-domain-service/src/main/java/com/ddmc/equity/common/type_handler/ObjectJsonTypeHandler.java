package com.ddmc.equity.common.type_handler;


import com.ddmc.utils.json.JsonUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ObjectJsonTypeHandler<T extends Object> extends BaseTypeHandler<T> {
    private final Class<T> clazz;

    public ObjectJsonTypeHandler(Class<T> clazz) {
        if (Objects.isNull(clazz)) {
            throw new IllegalArgumentException("Type argument cannot be null");
        }
        this.clazz = clazz;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JsonUtil.beanToJSON(parameter));
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return this.toObject(rs.getObject(columnName), clazz);
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return this.toObject(rs.getObject(columnIndex), clazz);
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return this.toObject(cs.getObject(columnIndex), clazz);
    }

    private T toObject(Object content, Class<T> clazz) {
        if (Objects.isNull(content)) {
            return null;
        }
        return JsonUtil.jsonToBean(content.toString(), clazz);
    }
}

