package com.ddmc.equity.common.exception;


import com.ddmc.equity.common.constant.Constants;

/**
 * 系统异常规则 错误码规则：前4位固定1201，后6位，每2位为一个业务模块
 *
 * <AUTHOR>
 */
public enum BaseExceptionChecker implements ExceptionChecker {

	//系统异常
	SYS_ERROR			("010000", "叮咚去邻居家串门了，马上回来哈～"),
	SYS_EXCEPTION		("010001", "叮咚去邻居家串门了，马上回来哦～"),

	//基本错误
	CAN_NOT_INSTANCE_ERROR	("010100", "该类不能实例化"),
	BIZ_NOT_EXIST			("010101", "此记录已存在"),
	BIZ_ERROR				("010102", "系统未知错误"),

	//数据库错误
	DB_ERROR      ("010200", "数据库操作错误"),
	DB_EXCEPTION  ("010201", "数据库操作异常"),
	DB_UPDATE_FAIL("010202", "数据库更新失败，请重试"),

	//缓存操作错误
	REDIS_ERROR     				("010300", "Redis操作错误"),
	REDIS_EXCEPTION					("010301", "Redis操作异常"),
	REDIS_ASSEMBLE_KEY_EXCEPTION	("010302", "RedisKey组装异常"),
	REDIS_FACTORY_ERROR     				("010303", "Redis连接工厂错误"),

	//定时任务操作错误
	XXL_EXCEPTION	("010400", "定时任务执行异常"),

	//数据签名异常
	ENCRYPT_ERROR("010500", "数据签名异常"),

	//数据校验异常
	REQUEST_CHECK_ERROR	("010600", "参数错误"),
	API_VERSION_ERROR	("010601", "apiVersion无法比较"),
	REQUEST_CHECK_V2_ERROR	("010602", "参数错误: {}"),

	//feign调用异常
	FEIGN_ERROR("010700", "feign调用异常{}"),

	//restTemplate调用异常
	REST_TEMPLATE_ERROR("010800", "restTemplate调用异常"),

	//jackson序列化异常
	JACKSON_ERROR("010900", "jackson序列化异常"),

	//消息队列异常
	MQ_ERROR("011000", "mq消息处理异常"),

	//入参校验错误
	NOT_NULL			("011100","入参数对象为空"),
	NOT_NULL_UID		("011101","uid不能为空"),
	APP_NOT_EXIST		("011102","应用id不存在"),
	CATEGORY_ID_ERROR	("011103","无效的分类标识"),
	STATION_ID_ERROR	("011104","该地址不在配送范围内"),
	PARAM_ERROR			("011105","参数异常"),

	GZIP_ERROR("011200", "字符串压缩错误"),
	THREAD_POOL_ERROR("011301", "线程池异常"),
	THREAD_ERROR("011302", "子线程异常"),

	LOCK_EXIST_ERROR("011303","操作太频繁"),
	NEED_LOGIN_ERROR("1111","您的访问已过期, 请重新登录"),

	PAGE_MISSING_TOTAL_ERROR("011400","分页计算缺失总条目"),
	PAGE_TOTAL_ILLEGAL_ERROR("011401","分页计算总条目范围错误"),

	KAFKA_SERVER_BROKER_LIST_EXCEPTION	("010402", "kafkaBrokerList获取异常"),
	;

	private final String code;
	private final String message;

	BaseExceptionChecker(String code, String message) {
		if(code.equals("1111")){
			this.code = code;
		} else {
			this.code = Constants.ERROR_CODE_PRE +code;
		}
		this.message = message;
	}

	@Override
	public String getCode() {
		return code;
	}

	@Override
	public String getMessage() {
		return message;
	}

}
