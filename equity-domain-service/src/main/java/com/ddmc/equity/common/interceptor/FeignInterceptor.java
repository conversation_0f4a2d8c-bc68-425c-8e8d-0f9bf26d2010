package com.ddmc.equity.common.interceptor;

import com.ddmc.equity.common.config.CommonApolloConfig;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.CsossEventEnum;
import com.ddmc.equity.common.util.CsossUtils;
import feign.Request;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * Feign调用前置处理 收集请求参数打点
 *
 * <AUTHOR>
 */
@Component
public class FeignInterceptor implements RequestInterceptor {

    @Resource
    private CommonApolloConfig commonApolloConfig;

    @Override
    public void apply(RequestTemplate template) {

        // 启用FeignResInterceptor收集feign日志，则不处理
        if (Constants.ONE.equals(commonApolloConfig.getFeignResInterceptorSwitch())) {
            return ;
        }

        StringBuilder sb = new StringBuilder();
        sb.append(template.method());
        sb.append(Constants.WHITE_SPACE);
        sb.append(template.url());
        if (Constants.HTTP_METHOD_POST.equals(template.method())) {
            String reqBody = Optional.ofNullable(template.requestBody()).
                    map(Request.Body::asString).
                    orElse(Strings.EMPTY);
            sb.append(Constants.WHITE_SPACE);
            sb.append(reqBody);
        }
        CsossUtils.sendCatEvent(CsossEventEnum.FEIGN_PARAMS, sb.toString());
    }
}
