package com.ddmc.equity.domain.entity.scene_action;

import cn.hutool.core.bean.BeanUtil;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/7/2 20:42
 * @description
 */
@Slf4j
public class SceneActionProcessConvertEntity {

    public static List<UnableReceiveBenefitDTO> buildUnableReceiveBenefitDTOList(List<FullBenefitInfoDTO> fullBenefitInfoDTOList,
                                                                                 String unableReceiveReasonCode) {
        if (CollectionUtils.isEmpty(fullBenefitInfoDTOList)) {
            return null;
        }

        return fullBenefitInfoDTOList.stream()
                .map(e -> buildUnableReceiveBenefitDTO(e, unableReceiveReasonCode))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public static List<UnableReceiveBenefitDTO> buildUnableReceiveBenefitDTOList(FullBenefitInfoDTO receiveBenefit,
                                                                                 String unableReceiveReasonCode) {
        UnableReceiveBenefitDTO unableReceiveBenefitDTO = buildUnableReceiveBenefitDTO(
                receiveBenefit, unableReceiveReasonCode);
        return unableReceiveBenefitDTO == null ? null : Lists.newArrayList(unableReceiveBenefitDTO);
    }

    private static UnableReceiveBenefitDTO buildUnableReceiveBenefitDTO(FullBenefitInfoDTO receiveBenefit,
                                                                        String unableReceiveReasonCode) {
        if (receiveBenefit == null) {
            return null;
        }

        UnableReceiveBenefitDTO unableReceiveBenefitDTO = BeanUtil.copyProperties(receiveBenefit,
                UnableReceiveBenefitDTO.class);
        unableReceiveBenefitDTO.setUnableReceiveReasonCode(unableReceiveReasonCode);
        return unableReceiveBenefitDTO;
    }
}
