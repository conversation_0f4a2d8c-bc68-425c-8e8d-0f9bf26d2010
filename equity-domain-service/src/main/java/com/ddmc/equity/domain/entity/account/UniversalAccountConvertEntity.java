package com.ddmc.equity.domain.entity.account;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.common.util.business.BenefitUtil;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.QueryUniversalAccountDetailsReqDTO;
import com.ddmc.equity.domain.dto.QueryUniversalAccountRecordsReqDTO;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountDetailInnerExtDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRecordInnerExtDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcRequestExtDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcResponseExtDTO;
import com.ddmc.equity.dto.customer.ReceiveExternalInfoDTO;
import com.ddmc.equity.enums.BenefitReceiveTypeEnum;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;
import com.ddmc.equity.model.vo.BenefitReceiveSuccessMsgVO;
import com.ddmc.ugc.commons.util.UUIDUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/26 16:07
 * @description
 */
public class UniversalAccountConvertEntity {

    public static UniversalAccountRecordDO convertToAccountRecordDO(EquityAccountContext equityAccountContext,
                                                                    Integer operateType) {
        UniversalAccountRecordDO recordDO = new UniversalAccountRecordDO();
        recordDO.setUserId(equityAccountContext.getUid());
        recordDO.setSceneCode(equityAccountContext.getSceneCode());
        recordDO.setActivityId(equityAccountContext.getActivityId());
        recordDO.setStrategyId(equityAccountContext.getStrategyId());
        recordDO.setBenefitGroupId(equityAccountContext.getBenefitGroupId());
        recordDO.setBenefitId(equityAccountContext.getBenefitId());
        recordDO.setBenefitType(equityAccountContext.getBenefitType());
        recordDO.setBenefitValue(equityAccountContext.getEquityValue());
        recordDO.setUseActivityId(equityAccountContext.getUseActivityId());

        recordDO.setOperateType(operateType);
        recordDO.setStatus(StatusEnum.INIT.getCode());
        recordDO.setOperateCount(getReceivedCount(equityAccountContext));
        recordDO.setReqNo(equityAccountContext.getSerialNumber());
        recordDO.setAuxKey(equityAccountContext.getSerialNumber());
        // 避免扣减库存、频次失败后，无法再插入新的记录
        recordDO.setInnerSerialNumber(UUIDUtils.getUUID("pre_"));

        recordDO.setAppId(equityAccountContext.getAppId());
        recordDO.setPageId(equityAccountContext.getPageId());
        recordDO.setSource(equityAccountContext.getSource());
        recordDO.setInnerExt(JsonUtil.toString(convertToRecordInnerExtDTO(equityAccountContext)));
        recordDO.setRpcRequestExt(JsonUtil.toString(convertToRpcRequestExtDTO(equityAccountContext)));
        recordDO.setRpcResponseExt(null);
        recordDO.setRpcCode(null);
        recordDO.setRpcMsg(null);
        recordDO.setFreezeReceiveLimitId(null);
        recordDO.setRelatedReqNo(null);

        return recordDO;
    }

    public static UniversalAccountDetailDO convertToAccountDetailDO(EquityAccountContext equityAccountContext) {
        UniversalAccountDetailDO detailDO = new UniversalAccountDetailDO();
        detailDO.setUserId(equityAccountContext.getUid());
        detailDO.setSceneCode(equityAccountContext.getSceneCode());
        detailDO.setActivityId(equityAccountContext.getActivityId());
        detailDO.setStrategyId(equityAccountContext.getStrategyId());
        detailDO.setBenefitGroupId(equityAccountContext.getBenefitGroupId());
        detailDO.setBenefitId(equityAccountContext.getBenefitId());
        detailDO.setBenefitType(equityAccountContext.getBenefitType());
        detailDO.setBenefitValue(equityAccountContext.getEquityValue());
        detailDO.setUseActivityId(equityAccountContext.getUseActivityId());

        Integer receivedCount = getReceivedCount(equityAccountContext);
        detailDO.setTotalCount(receivedCount);
        detailDO.setAvailableCount(receivedCount);
        detailDO.setExpireCount(0);
        detailDO.setExpireTime(BenefitUtil.calculateBenefitExpireTime(equityAccountContext.getBenefitId()));

        detailDO.setSource(equityAccountContext.getSource());
        detailDO.setInnerExt(JsonUtil.toString(convertToDetailInnerExtDTO(equityAccountContext)));
        detailDO.setRpcRequestExt(JsonUtil.toString(convertToRpcRequestExtDTO(equityAccountContext)));
        detailDO.setRpcResponseExt(Optional.ofNullable(equityAccountContext.getEquityRpcDto())
                .map(EquityRpcDto::getRpcResponseExtDTO).map(JsonUtil::toString).orElse(null));

        return detailDO;
    }

    public static UniversalAccountDO convertToAccountDO(UniversalAccountDetailDO detailDO) {
        UniversalAccountDO accountDO = new UniversalAccountDO();
        accountDO.setUserId(detailDO.getUserId());
        accountDO.setBenefitType(detailDO.getBenefitType());
        accountDO.setUseActivityId(detailDO.getUseActivityId());
        accountDO.setTotalCount(detailDO.getTotalCount());
        accountDO.setAvailableCount(detailDO.getAvailableCount());
        accountDO.setExpireCount(detailDO.getExpireCount());
        return accountDO;
    }

    private static Integer getReceivedCount(EquityAccountContext equityAccountContext) {
        Integer benefitType = equityAccountContext.getBenefitType();
        String compatibleOldRealSendAmount = UniversalAccountAmountCalEntity.getCompatibleOldRealSendAmount(
                equityAccountContext.getSendAmount(), equityAccountContext.getBalanceMoney(),
                benefitType, equityAccountContext.getEquityValue());
        return UniversalAccountAmountCalEntity.getReceivedCount(benefitType, compatibleOldRealSendAmount);
    }

    private static UniversalAccountRecordInnerExtDTO convertToRecordInnerExtDTO(EquityAccountContext equityAccountContext) {
        return UniversalAccountRecordInnerExtDTO.builder()
                .cityCode(equityAccountContext.getCityCode())
                .stationId(equityAccountContext.getStationId())
                .externalType(equityAccountContext.getExternalType())
                .externalId(equityAccountContext.getExternalId())
                .strategyExternalId(equityAccountContext.getStrategyExternalId())
                .consumeBenefitFreezeLogId(equityAccountContext.getConsumeBenefitFreezeLogId())
                .build();
    }

    private static UniversalAccountDetailInnerExtDTO convertToDetailInnerExtDTO(EquityAccountContext equityAccountContext) {
        return UniversalAccountDetailInnerExtDTO.builder()
                .lastReqNo(equityAccountContext.getSerialNumber())
                .cityCode(equityAccountContext.getCityCode())
                .stationId(equityAccountContext.getStationId())
                .externalType(equityAccountContext.getExternalType())
                .externalId(equityAccountContext.getExternalId())
                .strategyExternalId(equityAccountContext.getStrategyExternalId())
                .build();
    }

    private static UniversalAccountRpcRequestExtDTO convertToRpcRequestExtDTO(EquityAccountContext equityAccountContext) {
        ReceiveExternalInfoDTO receiveExternalInfoDTO = equityAccountContext.getReceiveExternalInfoDTO();
        Integer benefitType = equityAccountContext.getBenefitType();
        BigDecimal rechargeBalanceMoney = UniversalAccountAmountCalEntity.getDoRpcBalanceMoneyAmountJudgedType(equityAccountContext);
        UniversalAccountRpcRequestExtDTO rpcRequestExtDTO = UniversalAccountRpcRequestExtDTO.builder()
                .sendAmount(equityAccountContext.getSendAmount())
                .rpcReqCustomMap(equityAccountContext.getRpcReqCustomMap())
                .receiveExternalInfoDTO(receiveExternalInfoDTO)
                .build();

        if (Objects.equals(benefitType, BenefitTypeEnum.POINT.getId())) {
            rpcRequestExtDTO.setSendPointScene(equityAccountContext.getSendPointScene());
            rpcRequestExtDTO.setSendPointDesc(equityAccountContext.getSendPointDesc());
            rpcRequestExtDTO.setSendPointSource(equityAccountContext.getSendPointSource());
        }
        if (Objects.equals(benefitType, BenefitTypeEnum.BALANCE.getId())) {
            rpcRequestExtDTO.setBalanceMoney(rechargeBalanceMoney);
            rpcRequestExtDTO.setSendBalanceScene(equityAccountContext.getSendBalanceScene());
            rpcRequestExtDTO.setSendBalanceDesc(equityAccountContext.getSendBalanceDesc());
        }
        if (Objects.equals(benefitType, BenefitTypeEnum.TICKET.getId()) ||
                Objects.equals(benefitType, BenefitTypeEnum.TICKET_PACKAGE.getId())) {
            rpcRequestExtDTO.setSendTicketScene(equityAccountContext.getSendTicketScene());
            rpcRequestExtDTO.setSendTicketActivity(equityAccountContext.getSendTicketActivity());
            rpcRequestExtDTO.setSendTicketPrize(equityAccountContext.getSendTicketPrize());
        }
        if (Objects.equals(benefitType, BenefitTypeEnum.RANDOM_BALANCE.getId())) {
            rpcRequestExtDTO.setBalanceMoney(rechargeBalanceMoney);
            rpcRequestExtDTO.setSendBalanceScene(equityAccountContext.getSendBalanceScene());
            rpcRequestExtDTO.setSendBalanceDesc(equityAccountContext.getSendBalanceDesc());
        }
        if (Objects.equals(benefitType, BenefitTypeEnum.VIP_DAYS.getId())) {
            rpcRequestExtDTO.setSendVipDaysScene(equityAccountContext.getSendVipDaysScene());
        }
        return rpcRequestExtDTO;
    }

    public static void changeEquityAccountContextWhileRetry(EquityAccountContext equityAccountContext,
                                                            UniversalAccountRecordDO existRecord) {
        equityAccountContext.setAccountRecordId(existRecord.getId());
        equityAccountContext.setSceneCode(existRecord.getSceneCode());
        equityAccountContext.setActivityId(existRecord.getActivityId());
        equityAccountContext.setStrategyId(existRecord.getStrategyId());
        equityAccountContext.setBenefitGroupId(existRecord.getBenefitGroupId());
        equityAccountContext.setBenefitId(existRecord.getBenefitId());
        equityAccountContext.setBenefitType(existRecord.getBenefitType());
        equityAccountContext.setEquityValue(existRecord.getBenefitValue());
        equityAccountContext.setUseActivityId(existRecord.getUseActivityId());
        UniversalAccountRpcRequestExtDTO rpcRequestExtDTO = JsonUtil.parseObject(existRecord.getRpcRequestExt(), UniversalAccountRpcRequestExtDTO.class);
        equityAccountContext.setSendAmount(Objects.isNull(rpcRequestExtDTO) ? null : rpcRequestExtDTO.getSendAmount());
        equityAccountContext.setRpcReqCustomMap(Objects.isNull(rpcRequestExtDTO) ? null : rpcRequestExtDTO.getRpcReqCustomMap());
        equityAccountContext.setBalanceMoney(Objects.isNull(rpcRequestExtDTO) ? null : rpcRequestExtDTO.getBalanceMoney());
        equityAccountContext.setSendTicketScene(Objects.isNull(rpcRequestExtDTO) ? null : rpcRequestExtDTO.getSendTicketScene());
        equityAccountContext.setSendTicketActivity(Objects.isNull(rpcRequestExtDTO) ? null : rpcRequestExtDTO.getSendTicketActivity());
        equityAccountContext.setSendTicketPrize(Objects.isNull(rpcRequestExtDTO) ? null : rpcRequestExtDTO.getSendTicketPrize());
        equityAccountContext.setSendBalanceScene(Objects.isNull(rpcRequestExtDTO) ? null : rpcRequestExtDTO.getSendBalanceScene());
        equityAccountContext.setSendBalanceDesc(Objects.isNull(rpcRequestExtDTO) ? null : rpcRequestExtDTO.getSendBalanceDesc());
        equityAccountContext.setSendPointScene(Objects.isNull(rpcRequestExtDTO) ? null : rpcRequestExtDTO.getSendPointScene());
        equityAccountContext.setSendPointDesc(Objects.isNull(rpcRequestExtDTO) ? null : rpcRequestExtDTO.getSendPointDesc());
        equityAccountContext.setSendVipDaysScene(Objects.isNull(rpcRequestExtDTO) ? null : rpcRequestExtDTO.getSendVipDaysScene());
        equityAccountContext.setSerialNumber(existRecord.getReqNo());
        equityAccountContext.setDoRpcUniqueSerialNumber(existRecord.getInnerSerialNumber());

        // 消耗权益相关
        UniversalAccountRecordInnerExtDTO innerExtDTO = JsonUtil.parseObject(existRecord.getInnerExt(), UniversalAccountRecordInnerExtDTO.class);
        if (innerExtDTO != null && innerExtDTO.getConsumeBenefitFreezeLogId() != null) {
            // 需要从内部拓展字段中获取原来的冻结记录 ID，用于后续确认扣减和释放
            equityAccountContext.setConsumeBenefitFreezeLogId(innerExtDTO.getConsumeBenefitFreezeLogId());
        }
    }

    public static Wrapper<UniversalAccountDetailDO> convertToUniversalAccountDetailDOWrapper(QueryUniversalAccountDetailsReqDTO req) {
        return Wrappers.<UniversalAccountDetailDO>lambdaQuery()
                .and(Boolean.TRUE.equals(req.getBindMaster()), w -> w.apply("'bind_master' = 'bind_master'"))
                .eq(UniversalAccountDetailDO::getUserId, req.getUserId())
                .eq(StringUtils.isNotBlank(req.getSceneCode()), UniversalAccountDetailDO::getSceneCode, req.getSceneCode())
                .in(CollectionUtils.isNotEmpty(req.getActivityIds()), UniversalAccountDetailDO::getActivityId, req.getActivityIds())
                .in(CollectionUtils.isNotEmpty(req.getStrategyIds()), UniversalAccountDetailDO::getStrategyId, req.getStrategyIds())
                .in(CollectionUtils.isNotEmpty(req.getBenefitGroupIds()), UniversalAccountDetailDO::getBenefitGroupId, req.getBenefitGroupIds())
                .in(CollectionUtils.isNotEmpty(req.getBenefitIds()), UniversalAccountDetailDO::getBenefitId, req.getBenefitIds())
                .in(CollectionUtils.isNotEmpty(req.getBenefitTypes()), UniversalAccountDetailDO::getBenefitType, req.getBenefitTypes())
                .in(CollectionUtils.isNotEmpty(req.getBenefitValues()), UniversalAccountDetailDO::getBenefitValue, req.getBenefitValues())
                .ge(Objects.nonNull(req.getStartDate()), UniversalAccountDetailDO::getCreateTime, req.getStartDate())
                .le(Objects.nonNull(req.getEndDate()), UniversalAccountDetailDO::getCreateTime, req.getEndDate())
                .orderByDesc(UniversalAccountDetailDO::getId);
    }

    public static Wrapper<UniversalAccountRecordDO> convertToUniversalAccountRecordDOWrapper(QueryUniversalAccountRecordsReqDTO req) {
        return Wrappers.<UniversalAccountRecordDO>lambdaQuery()
                .and(Boolean.TRUE.equals(req.getBindMaster()), w -> w.apply("'bind_master' = 'bind_master'"))
                .eq(UniversalAccountRecordDO::getUserId, req.getUserId())
                .eq(StringUtils.isNotBlank(req.getSceneCode()), UniversalAccountRecordDO::getSceneCode, req.getSceneCode())
                .in(CollectionUtils.isNotEmpty(req.getActivityIds()), UniversalAccountRecordDO::getActivityId, req.getActivityIds())
                .in(CollectionUtils.isNotEmpty(req.getStrategyIds()), UniversalAccountRecordDO::getStrategyId, req.getStrategyIds())
                .in(CollectionUtils.isNotEmpty(req.getBenefitGroupIds()), UniversalAccountRecordDO::getBenefitGroupId, req.getBenefitGroupIds())
                .in(CollectionUtils.isNotEmpty(req.getBenefitIds()), UniversalAccountRecordDO::getBenefitId, req.getBenefitIds())
                .in(CollectionUtils.isNotEmpty(req.getBenefitTypes()), UniversalAccountRecordDO::getBenefitType, req.getBenefitTypes())
                .in(CollectionUtils.isNotEmpty(req.getBenefitValues()), UniversalAccountRecordDO::getBenefitValue, req.getBenefitValues())
                .in(CollectionUtils.isNotEmpty(req.getReqNos()), UniversalAccountRecordDO::getReqNo, req.getReqNos())
                .in(CollectionUtils.isNotEmpty(req.getOperateTypes()), UniversalAccountRecordDO::getOperateType, req.getOperateTypes())
                .in(CollectionUtils.isNotEmpty(req.getStatuses()), UniversalAccountRecordDO::getStatus, req.getStatuses())
                .ge(Objects.nonNull(req.getStartDate()), UniversalAccountRecordDO::getCreateTime, req.getStartDate())
                .le(Objects.nonNull(req.getEndDate()), UniversalAccountRecordDO::getCreateTime, req.getEndDate())
                .orderByDesc(UniversalAccountRecordDO::getId);
    }

    public static BenefitReceiveSuccessMsgVO convertToSceneBenefitReceiveSuccessMsgVO(EquityAccountContext equityAccountContext) {
        EquityRpcDto equityRpc = equityAccountContext.getEquityRpcDto();
        return BenefitReceiveSuccessMsgVO.builder()
                .receiveType(BenefitReceiveTypeEnum.SCENE_RECEIVE.getCode())
                .reqNo(equityAccountContext.getSerialNumber())
                .userId(equityAccountContext.getUid())
                .accountDetailId(equityAccountContext.getAccountDetailId())
                .useActivityId(equityAccountContext.getUseActivityId())
                .sceneCode(equityAccountContext.getSceneCode())
                .activityId(equityAccountContext.getActivityId())
                .externalType(equityAccountContext.getExternalType())
                .externalId(equityAccountContext.getExternalId())
                .activityName(equityAccountContext.getActivityName())
                .strategyId(equityAccountContext.getStrategyId())
                .strategyExternalId(equityAccountContext.getStrategyExternalId())
                .benefitGroupId(equityAccountContext.getBenefitGroupId())
                .benefitId(equityAccountContext.getBenefitId())
                .benefitType(equityAccountContext.getBenefitType())
                .benefitValue(equityAccountContext.getEquityValue())
                .benefitName(equityAccountContext.getBenefitName())
                .sendAmount(equityAccountContext.getSendAmount())
                .balanceMoney(UniversalAccountAmountCalEntity.getDoRpcBalanceMoneyAmountJudgedType(equityAccountContext))
                .rpcRespNo(Optional.ofNullable(equityRpc).map(EquityRpcDto::getValue).orElse(null))
                .receiveTime(System.currentTimeMillis())
                .build();
    }

    /**
     * 已存在成功的权益账户操作记录，转换成领取结果
     *
     * @param existRecord 已存在成功的权益账户操作记录
     * @return 领取结果
     */
    public static ReceiveBenefitResDTO convertToExistSuccessReceiveResp(UniversalAccountRecordDO existRecord) {
        // 重试时 existRecord 状态为成功，返回的 receiveRes 中 accountDetailId 字段为空（existRecord 中没有存储 accountDetailId）
        UniversalAccountRpcResponseExtDTO rpcResponseExtDTO = JsonUtil.parseObject(existRecord.getRpcResponseExt(),
                UniversalAccountRpcResponseExtDTO.class);
        EquityRpcDto equityRpcDTO = EquityRpcDto.success(getRpcRespNo(existRecord), rpcResponseExtDTO);
        return ReceiveBenefitResDTO.success(equityRpcDTO, existRecord.getId(), null);
    }

    /**
     * 获取已存在的权益账户记录中的三方返回编号（如果权益类型为优惠券，则返回 userTicketId）
     *
     * @param existRecord 已存在的权益账户记录
     * @return 三方返回编号
     */
    private static String getRpcRespNo(UniversalAccountRecordDO existRecord) {
        UniversalAccountRpcResponseExtDTO rpcResponseExtDTO = JsonUtil.parseObject(existRecord.getRpcResponseExt(),
                UniversalAccountRpcResponseExtDTO.class);
        if (Objects.isNull(rpcResponseExtDTO)) {
            return null;
        }
        String userTicketId = rpcResponseExtDTO.getUserTicketId();
        if (StringUtils.isNotBlank(userTicketId)) {
            return userTicketId;
        }
        String userTicketPackageId = rpcResponseExtDTO.getUserTicketPackageId();
        if (StringUtils.isNotBlank(userTicketPackageId)) {
            return userTicketPackageId;
        }
        return null;
    }
}
