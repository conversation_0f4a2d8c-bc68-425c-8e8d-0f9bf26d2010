package com.ddmc.equity.domain.dto.benefit_stock;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BenefitStockLimitReq extends BenefitStockLimitInfo {

    /**
     * 计划库存类型
     */
    private Integer planDateType;
}