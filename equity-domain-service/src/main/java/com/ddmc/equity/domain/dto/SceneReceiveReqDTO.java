package com.ddmc.equity.domain.dto;

import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.ddmc.equity.dto.customer.CommonBaseRequestDTO;
import com.ddmc.equity.dto.customer.ReceiveExternalInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/4/23 10:29
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("指定活动 + 策略 + 权益领取（先咨询后领取） req")
public class SceneReceiveReqDTO extends CommonBaseRequestDTO {

    @ApiModelProperty(value = "基础请求")
    private BaseRequestDTO baseRequestDTO;

    @ApiModelProperty(value = "场景 code（场景接入时由权益中心分配）")
    private String sceneCode;

    @ApiModelProperty(value = "活动 id")
    private Long activityId;

    @ApiModelProperty(value = "使用活动 id。具体权益发送到哪个活动上使用")
    private Long useActivityId;

    @ApiModelProperty(value = "外部策略 id。不指定则在所有策略里面选择一个发放；")
    private String externalStrategyId;

    @ApiModelProperty(value = "权益 id")
    private Long benefitId;

    @ApiModelProperty(value = "业务流水号")
    private String reqNo;

    @ApiModelProperty("发放不固定数量权益时，指定的数量（积分数、余额金额、活动次数、步数、会员天数、金豆数等）。如果发放权益类型是抽签码，则为发放抽签码数量")
    private String sendAmount;

    @ApiModelProperty(value = "领取时关联的外部信息")
    private ReceiveExternalInfoDTO receiveExternalInfoDTO;

    @ApiModelProperty(value = "调用风控 sceneCode（领取前）")
    private String riskSceneCode;

    @ApiModelProperty(value = "调用风控 sceneCode（领取后）")
    private String riskSceneCodeAfter;

    @ApiModelProperty(value = "调用风控场景特殊参数")
    private Map<String, Object> riskSceneStrategyDataMap;

    @ApiModelProperty("发放权益时调用下游透传的自定义参数")
    private Map<String, String> rpcReqCustomMap;

    @ApiModelProperty(value = "发放积分时的场景")
    private Integer sendPointScene;

    @ApiModelProperty(value = "发放积分时的描述")
    private String sendPointDesc;

    @ApiModelProperty(value = "发放积分时的来源")
    private Integer sendPointSource;

    @ApiModelProperty(value = "发放余额时的场景")
    private String sendBalanceScene;

    @ApiModelProperty(value = "发放余额时的描述")
    private String sendBalanceDesc;

    @ApiModelProperty(value = "发放优惠券时的场景（发放优惠券包时的场景也用该字段）")
    private Integer sendTicketScene;

    @ApiModelProperty(value = "发放会员天数时的场景")
    private Integer sendVipDaysScene;
}
