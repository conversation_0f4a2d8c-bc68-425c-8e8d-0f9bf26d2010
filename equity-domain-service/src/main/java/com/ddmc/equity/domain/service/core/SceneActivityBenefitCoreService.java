package com.ddmc.equity.domain.service.core;

import com.ddmc.equity.domain.dto.ReceiveSceneBenefitDTO;
import com.ddmc.equity.domain.dto.ReqReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.dto.customer.QueryReceivedBenefitsReqDTO;
import com.ddmc.equity.enums.ActivityFilterTypeEnum;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.equity.model.dto.SceneBenefitDataDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SceneActivityBenefitCoreService {

    /**
     * 按活动场景咨询
     *
     * @param sceneCode        场景 code
     * @param engineContextDTO 上下文
     * @return 活动列表
     */
    List<SceneActivityCacheDto> consultSceneBenefit(String sceneCode, EngineContextDTO engineContextDTO);

    /**
     * 权益召回
     *
     * @param engineContextDTO 上下文
     * @return 召回权益列表
     */
    List<SceneBenefitDataDto> recallBenefit(EngineContextDTO engineContextDTO);

    /**
     * 指定领取场景领取权益
     *
     * @param reqNo                    业务流水号
     * @param sceneCode                场景 code
     * @param engineContextDTO         上下文
     * @param reqReceiveBenefitDTOList 领取的权益列表
     */
    ReceiveSceneBenefitDTO receiveSceneBenefits(String reqNo, String sceneCode, EngineContextDTO engineContextDTO,
                                                List<ReqReceiveBenefitDTO> reqReceiveBenefitDTOList);

    /**
     * 幂等用，用户UID+流水号查领取成功的权益
     *
     * @param queryReceivedBenefitsReqDTO QueryReceivedBenefitsReqDTO
     * @return ReceiveSceneBenefitDTO
     */
    ReceiveSceneBenefitDTO queryReceivedBenefits(QueryReceivedBenefitsReqDTO queryReceivedBenefitsReqDTO);

    /**
     * 过滤生效中的活动
     * <p>
     * 1 判断当前活动是否生效（活动状态、活动时间）
     * 2 判断过滤策略状态（暂无）
     * 3 判断过滤权益是否生效（权益状态、权益有效期）
     * 4 如果策略关联的权益列表为空，则活动对应策略过滤不返回
     *
     * @param activityCacheList 活动集合
     * @param actFilterType     活动过滤类型
     * @return 生效中的活动集合
     */
    List<SceneActivityCacheDto> filterActiveActivityCacheList(List<SceneActivityCacheDto> activityCacheList,
                                                              ActivityFilterTypeEnum actFilterType);
    void initActivityCount(EngineContextDTO engineContextDTO,List<SceneActivityCacheDto> activities);
}
