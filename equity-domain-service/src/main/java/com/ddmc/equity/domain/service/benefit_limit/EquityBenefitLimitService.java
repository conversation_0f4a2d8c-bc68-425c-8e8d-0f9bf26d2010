package com.ddmc.equity.domain.service.benefit_limit;

import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.domain.valueobject.benefit_limit.DeductBenefitLimitResp;
import com.ddmc.equity.domain.valueobject.benefit_limit.FreezeBenefitLimitResp;
import com.ddmc.equity.domain.valueobject.benefit_limit.BenefitIdInfoDTO;
import com.ddmc.equity.domain.valueobject.benefit_limit.GetBenefitLimitCacheInfoResp;
import com.ddmc.equity.domain.valueobject.benefit_limit.ReleaseBenefitLimitResp;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EquityBenefitLimitService {

    /**
     * 冻结频次，权益组维度（具体到权益组下的权益维度）
     * <p>
     * 1 查询 redis 中频次，并通过 endTime 判断是否过期
     * 2 如果 redis 为空，穿透查询 DB 中频次
     * 3 如果 DB 中没有频次，把规则中的频次设置到 DB
     */
    FreezeBenefitLimitResp freezeBenefitLimit(@NotNull String userId, @NotNull Long activityId,
                                              @NotNull Long strategyId, @NotNull Long benefitId,
                                              @NotNull ReceiveLimitRuleDTO receiveLimitRuleDTO);

    /**
     * 扣减频次，权益组维度（具体到权益组下的权益维度）
     * <p>
     * null 表示异常报错，频次扣减失败，建议针对这个权益不发直接失败
     */
    @Nullable
    DeductBenefitLimitResp deductBenefitLimit(@NotNull String userId, @NotNull Long activityId,
                                              @NotNull Long strategyId, @NotNull Long benefitId,
                                              @NotNull ReceiveLimitRuleDTO receiveLimitRuleDTO,
                                              @Nullable Long freezeId);

    /**
     * 频次回滚，权益组维度（具体到权益组下的权益维度）
     * <p>
     * 其中 lockKey 会保证释放频次期间没有第二次重入即重复释放频次
     */
    @Nullable
    ReleaseBenefitLimitResp releaseBenefitLimit(@NotNull String userId, @NotNull Long activityId,
                                                @NotNull Long strategyId, @NotNull Long benefitId,
                                                @NotNull ReceiveLimitRuleDTO receiveLimitRuleDTO,
                                                @Nullable Long freezeId);

    /**
     * 用户频次查询，null表示未查询到相关次数或出错
     * 从缓存中取，仅供参考，实际操作用benefitLimitCheck函数
     *
     * @param userId     String
     * @param activityId Long
     * @param strategyId Long
     * @param benefitId  Long
     * @return GetBenefitLimitCacheInfoResp
     */
    @Nullable
    GetBenefitLimitCacheInfoResp getBenefitLimitCache(
            @NotNull String userId, @NotNull Long activityId,
            @NotNull Long strategyId, @NotNull Long benefitId);

    /**
     * 用户领取频次批量查询
     *
     * @param userId         用户 id
     * @param benefitIdInfos 领取频次查询 benefitIdInfos
     */
    List<GetBenefitLimitCacheInfoResp> getBenefitLimitCacheMul(String userId, List<BenefitIdInfoDTO> benefitIdInfos);
}
