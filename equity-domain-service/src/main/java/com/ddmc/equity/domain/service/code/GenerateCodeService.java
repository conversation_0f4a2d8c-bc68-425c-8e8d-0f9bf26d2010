package com.ddmc.equity.domain.service.code;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ddmc.equity.infra.repository.dao.GenerateCodeDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/6 16:20
 * @description
 */
public interface GenerateCodeService extends IService<GenerateCodeDO> {

    /**
     * 批量生成抽签码
     *
     * @param count 生成数量
     * @return 生成的抽签码列表
     */
    List<String> batchGenerateAndInsertDrawCode(Integer count);
}
