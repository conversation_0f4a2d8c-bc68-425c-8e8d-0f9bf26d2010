package com.ddmc.equity.domain.service.account;

import com.ddmc.equity.account.DirectAccountContext;
import com.ddmc.equity.domain.dto.account_expire.ExpireOperateAccountDetailDTO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/7/16 15:48
 * @description
 */
public interface OperateAccountCoreService {

    /**
     * 扣减权益账户可用数量
     *
     * @param accountContext 上下文
     * @return 是否扣减成功
     */
    Boolean deductAccount(DirectAccountContext accountContext);

    /**
     * 校验并过期权益通用账户
     *
     * @param detailDO 权益通用账户明细
     */
    void expireAccount(UniversalAccountDetailDO detailDO);

    /**
     * 指定 userId 上下文合并过期权益通用账户
     *
     * @param accountContext        上下文
     * @param operateAccountDetails 需要过期的账户明细
     * @return 是否过期成功
     */
    Boolean expireAccount(DirectAccountContext accountContext, List<ExpireOperateAccountDetailDTO> operateAccountDetails);
}
