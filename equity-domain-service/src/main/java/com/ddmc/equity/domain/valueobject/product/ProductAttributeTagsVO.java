package com.ddmc.equity.domain.valueobject.product;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created on 2022/10/13.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductAttributeTagsVO implements Serializable {

    private static final long serialVersionUID = 3128060619819898225L;

    private Integer type;

    private String name;
}

