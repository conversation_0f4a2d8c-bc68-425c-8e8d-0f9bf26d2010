package com.ddmc.equity.domain.dto.benefit;

import com.ddmc.equity.common.enums.StockOperationEnum;
import com.ddmc.equity.domain.dto.rule.UserTagInfoSaveDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class StrategyBenefitMultiCreateDTO {
    /**
     * 活动ID
     */
    @ApiModelProperty("活动ID")
    private Long activityId;

    /**
     * 主券信息
     */

    @ApiModelProperty("主券信息")
    private List<MasterCouponDTO> masterCoupons;


    /**
     * 主优惠券
     */
    @Data
    public static class MasterCouponDTO {
        /**
         * 券ID
         */
        @ApiModelProperty("券ID")
        private String ticketId;

        /**
         * 券名称
         */
        @ApiModelProperty("券名称，可为空")
        private String name;

        /**
         * 参与城市id列表
         */
        @ApiModelProperty("城市列表，前端保持为空")
        private List<String> cityIds;

        /**
         * 策略ID
         */
        @ApiModelProperty("策略ID，修改是必填")
        private Long strategyId;

        /**
         * 权益组ID
         */
        @ApiModelProperty("权益组ID，修改时必填")
        private Long benefitGroupId;

        /**
         * 主券下挂的膨胀券列表
         */
        @ApiModelProperty("子券列表")
        private List<InflationCouponDTO> inflationCoupons;
    }


    /**
     * 膨胀券
     */
    @Data
    public static class InflationCouponDTO {
        /**
         * 券ID
         */
        @ApiModelProperty("券ID")
        private String ticketId;

        /**
         * 券名称
         */
        @ApiModelProperty("券名称，可为空")
        private String name;

        /**
         * 膨胀券库存
         */
        @ApiModelProperty("券库存")
        private Integer stock;

        /**
         * 膨胀券库存变更
         */
        @ApiModelProperty("券库存变更")
        private Integer stockChange;

        /**
         * 库存操作方式
         */
        private StockOperationEnum stockOperationType;
        
        /**
         * 膨胀券展示顺序
         */
        @ApiModelProperty("券的展示顺序")
        private Integer sort;

        /**
         * 参与城市id列表
         */
        @ApiModelProperty("城市列表，前端保持为空")
        private List<String> cityIds;

        @ApiModelProperty("人群标签列表")
        private List<UserTagInfoSaveDTO> userTags;

        /**
         * 当前状态开启状态
         */
        @ApiModelProperty("是否开启，true开启，false关闭")
        private Boolean enable;

        /**
         * 权益ID
         */
        @ApiModelProperty("权益ID，修改时必填")
        private Long benefitId;


        /**
         * 过期时间
         */
        @ApiModelProperty("过期时间，前端保持为空")
        private Date expiryTime;
    }
}
