package com.ddmc.equity.domain.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class EquityToCReqDTO {

    private static final long serialVersionUID = 4310587173464191920L;

    @ApiModelProperty(value = "微信小程序参数")
    private String wx;

    /**
     * 应用id(1:ios,2:安卓,3:微信,4:小程序,支付宝小程序:10)
     */
//    @Valid
    @ApiModelProperty(name = "app_client_id", value = "应用id 1:ios,2:安卓,3:微信,4:小程序,支付宝小程序:10", required = true)
    @JsonProperty("app_client_id")
    @JsonAlias("appClientId")
    private String appClientId;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String latitude;

    /**
     * 城市code
     */
    @JsonAlias("cityNumber")
    @ApiModelProperty(name = "city_number", value = "城市code")
    @JsonProperty("city_number")
    private String cityNumber;

    /**
     * 站点ID
     * 注意这个类不加Validator，如果有需要可以在切面里定制 参考
     */
    @JsonAlias("stationId")
    @ApiModelProperty(name = "station_id", value = "站点ID", required = true)
    @JsonProperty("station_id")
    @NotNull
    private String stationId;

    /**
     * api版本
     */
    @ApiModelProperty(name = "api_version", value = "api版本", required = true)
    @JsonProperty("api_version")
    @JsonAlias("apiVersion")
    private String apiVersion;

    /**
     * app版本号 h5使用
     */
    @JsonAlias("nativeVersion")
    @ApiModelProperty(name = "native_version", value = "app版本号 h5使用")
    @JsonProperty("native_version")
    private String nativeVersion;

    /**
     * 用户ID
     */
    @ApiModelProperty(name = "uid", value = "用户id", example = "5b80cf90d765e03597236fc2")
    private String uid;

    /**
     * h5版本号
     */
    @ApiModelProperty(name = "h5_source", value = "h5版本号 h5使用")
    @JsonProperty("h5_source")
    @JsonAlias("h5Source")
    private String h5Source;

    /**
     * 设备编号
     */
    @ApiModelProperty(name = "deviceId", value = "设备编号")
    @JsonProperty("device_id")
    @JsonAlias("deviceId")
    private String deviceId;

    /**
     * 预览token
     */
    @ApiModelProperty(name = "preview_token", value = "预览token")
    @JsonProperty("preview_token")
    @JsonAlias("previewToken")
    private String previewToken;

    /**
     * 长辈版标识
     */
    @JsonAlias("appType")
    @JsonProperty("app_type")
    private String appType;


    @ApiModelProperty(name = "rn_version")
    @JsonProperty("rn_version")
    @JsonAlias("rnVersion")
    private String rnVersion;

    @JsonProperty("source_page_id")
    @JsonAlias("sourcePageId")
    private String sourcePageId;

    @JsonProperty("source_module_id")
    @JsonAlias("sourceModuleId")
    private String sourceModuleId;


    @JsonProperty("os_version")
    @JsonAlias("osVersion")
    private String osVersion;

    private String sign;

    @JsonProperty("seq_id")
    @JsonAlias("seqId")
    private String seqId;

    private String channel;
    private String lfp;
    private String time;

    @JsonProperty("device_model")
    @JsonAlias("deviceModel")
    private String deviceModel;

    @JsonProperty("device_name")
    @JsonAlias("deviceName")
    private String deviceName;

    @JsonProperty("device_token")
    @JsonAlias("deviceToken")
    private String deviceToken;

    @JsonIgnore
    private String summaryUid;

    @ApiModelProperty("应用ID")
    @JsonProperty("app_id")
    @JsonAlias("appId")
    private String appId;

    @ApiModelProperty("来源")
    private String source;

}
