package com.ddmc.equity.domain.converter.rule;

import com.ddmc.equity.domain.entity.rule.EquityRuleEntity;
import com.ddmc.equity.infra.repository.dao.EquityRuleDO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface RuleConverter {
    RuleConverter INSTANCE = Mappers.getMapper(RuleConverter.class);

    EquityRuleEntity ruleDoToEntity(EquityRuleDO target);

    List<EquityRuleEntity> ruleDoListToEntityList(List<EquityRuleDO> target);

}
