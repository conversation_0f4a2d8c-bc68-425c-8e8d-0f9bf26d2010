package com.ddmc.equity.domain.dto.benefit;

import com.ddmc.equity.domain.dto.rule.UserTagInfoSaveDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class StrategyBenefitMultiCreateInDTO {
    /**
     * 活动ID
     */
    @ApiModelProperty("活动ID")
    private Long activityId;

    /**
     * 主券信息
     */

    @ApiModelProperty("主券信息")
    private List<MasterCouponInDTO> masterCoupons;


    /**
     * 主优惠券
     */
    @Data
    public static class MasterCouponInDTO {
        /**
         * 券ID
         */
        @ApiModelProperty("券ID")
        private String ticketId;

        /**
         * 策略ID
         */
        @ApiModelProperty("策略ID，修改是必填")
        private Long strategyId;

        /**
         * 权益组ID
         */
        @ApiModelProperty("权益组ID，修改时必填")
        private Long benefitGroupId;

        /**
         * 主券下挂的膨胀券列表
         */
        @ApiModelProperty("子券列表")
        private List<InflationCouponInDTO> inflationCoupons;
    }


    /**
     * 膨胀券
     */
    @Data
    public static class InflationCouponInDTO {
        /**
         * 券ID
         */
        @ApiModelProperty("券ID")
        private String ticketId;

        /**
         * 膨胀券库存
         */
        @ApiModelProperty("券库存")
        private Integer stock;

        /**
         * 膨胀券展示顺序
         */
        @ApiModelProperty("券的展示顺序")
        private Integer sort;

        /**
         * 当前状态开启状态
         */
        @ApiModelProperty("是否开启，true开启，false关闭")
        private Boolean enable;

        /**
         * 权益ID
         */
        @ApiModelProperty("权益ID，修改时必填")
        private Long benefitId;

        @ApiModelProperty("人群标签列表")
        private List<UserTagInfoSaveDTO> userTags;
    }
}
