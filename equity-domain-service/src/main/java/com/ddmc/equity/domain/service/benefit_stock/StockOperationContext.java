package com.ddmc.equity.domain.service.benefit_stock;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.StockOperationEnum;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.common.util.LongUtils;
import com.ddmc.equity.domain.dto.benefit_stock.StockOperationBaseParam;
import com.ddmc.equity.domain.entity.benefit_stock.EquityBenefitStockFlowEntity;
import com.ddmc.equity.domain.service.equityBenefitStockFlow.EquityBenefitStockFlowService;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockFlowDO;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.SetMultimap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class StockOperationContext {

    @Autowired
    private EquityBenefitStockFlowService equityBenefitStockFlowService;
    @Autowired
    List<AbstractStockFlowFactorHandler> abstractStockHandlers;
    private final Map<Integer, AbstractStockFlowFactorHandler> HANDLER_MAP = new HashMap<>(16);

    public static final Map<Integer, Integer> HANDLER_TYPE_WEIGHT = Maps.newHashMap();

    // 初始化>扩容总库存>扩容计划库存>扣减>释放
    static {
        HANDLER_TYPE_WEIGHT.put(StockOperationEnum.INIT.getOperation(), Constants.ZERO);
        HANDLER_TYPE_WEIGHT.put(StockOperationEnum.EXPEND.getOperation(), Constants.ONE);
        HANDLER_TYPE_WEIGHT.put(StockOperationEnum.EXPEND_PLAN.getOperation(), Constants.TWO);
        HANDLER_TYPE_WEIGHT.put(StockOperationEnum.DEDUCT.getOperation(), Constants.THREE);
        HANDLER_TYPE_WEIGHT.put(StockOperationEnum.RELEASE.getOperation(), Constants.FOUR);
    }

    public static final ImmutableSet<Integer> AGGREGATION_TYPES = ImmutableSet.<Integer>builder()
            .add(StockOperationEnum.DEDUCT.getOperation(), StockOperationEnum.RELEASE.getOperation())
            .build();


    /**
     * 处理类放到map中
     */
    @PostConstruct
    public void afterPropertiesSet() {
        HANDLER_MAP.putAll(abstractStockHandlers.stream().collect(Collectors.toMap(AbstractStockFlowFactorHandler::getStockOperationType, Function.identity())));
        log.info("[StockOperationContext][afterPropertiesSet][处理类放到map中]map=" + HANDLER_MAP);
    }

    /**
     * 扣减和释放库存 flow 每次更新成功 stockDB 后，直接修改 flow 为已处理；
     * 初始化&扩充库存 flow 暂时保持原有逻辑，最后统一修改处理成功的 flow 为已处理；
     *
     * @param list 待处理的 flowList
     * @return 处理失败的 flowIds
     */
    public List<Long /* mightFailedIds */> handleFlowList(List<EquityBenefitStockFlowDO> list) {
        List<Long> mightFailedIds = Lists.newArrayList();
        Map<Integer, List<EquityBenefitStockFlowDO>> maps = list.stream().collect(Collectors.groupingBy(EquityBenefitStockFlowDO::getOperationType));
        List<Integer> operationTypes = Lists.newArrayList(maps.keySet());
        // 按照权重排序
        // 1. 初始化的信息拎出来初始化掉
        // 2. 非初始化的信息，先做扩容的 在做扩容计划库存
        // ---
        // 3. 做扣减的
        // 4. 做释放的
        operationTypes.sort((t1, t2) -> {
            Integer w1 = HANDLER_TYPE_WEIGHT.getOrDefault(t1, Constants.HUNDRED);
            Integer w2 = HANDLER_TYPE_WEIGHT.getOrDefault(t2, Constants.HUNDRED);
            return w1.compareTo(w2);
        });
        // 扣减和释放要聚合，其他的循环就行了
        for (Integer stockOperationType : operationTypes) {
            if (!AGGREGATION_TYPES.contains(stockOperationType)) {
                mightFailedIds.addAll(handleSingleRow(maps, stockOperationType));
            } else {
                mightFailedIds.addAll(handleAggregateRow(maps, stockOperationType));
            }
        }
        return mightFailedIds;
    }

    private List<Long /* failedFlowIds */> handleAggregateRow(Map<Integer, List<EquityBenefitStockFlowDO>> maps,
                                                              Integer stockOperationType) {
        // 本次需要处理的库存流水
        List<EquityBenefitStockFlowDO> needHandleFlows = maps.get(stockOperationType);
        if (CollectionUtils.isEmpty(needHandleFlows)) {
            log.info("handleAggregateRow needHandleFlows is null. stockOperationType={}", stockOperationType);
            return Lists.newArrayList();
        }

        // 按照 activityId、strategyId、benefitId 聚合，同时把 Map 填充好
        // 权益关联的库存操作流水 id map
        SetMultimap<Triple<Long /* activityId */, Long /* strategyId */, Long /* benefitId */>,
                Long /* 关联的 flowId */> flowIdMap = HashMultimap.create();
        // 非计划库存操作数量 map
        ListMultimap<Triple<Long /* activityId */, Long /* strategyId */, Long /* benefitId */>,
                Long /* operateNum */> stockOperateNumMap = ArrayListMultimap.create();

        // 权益关联的计划库存 id
        SetMultimap<Triple<Long /* activityId */, Long /* strategyId */, Long /* benefitId */>,
                Long  /* 关联的 planStockId */> planStockIdMap = HashMultimap.create();
        // 计划库存操作数量 map
        ListMultimap<String /* planStockUniqueId */, Long /* operateNum */> planStockOperateNumMap = ArrayListMultimap.create();
        // 计划库存类型 map
        Map<Triple<Long /* activityId */, Long /* strategyId */, Long /* benefitId */>,
                Integer /* planDateType */> planDateTypeMap = Maps.newHashMap();

        for (EquityBenefitStockFlowDO equityBenefitStockFlowDO : needHandleFlows) {
            Long activityId = equityBenefitStockFlowDO.getActivityId();
            Long strategyId = equityBenefitStockFlowDO.getStrategyId();
            Long benefitId = equityBenefitStockFlowDO.getBenefitId();
            Triple<Long, Long, Long> key = Triple.of(activityId, strategyId, benefitId);
            flowIdMap.put(key, equityBenefitStockFlowDO.getId());
            stockOperateNumMap.put(key, equityBenefitStockFlowDO.getOperationNum());
            // 存在计划库存
            planDateTypeMap.putIfAbsent(key, equityBenefitStockFlowDO.getPlanDateType());
            Long planStockId = equityBenefitStockFlowDO.getPlanStockId();
            if (LongUtils.isTrue(planStockId)) {
                planStockIdMap.put(key, planStockId);
                String planStockUniqueId = getPlanStockUniqueId(activityId, strategyId, benefitId, planStockId);
                planStockOperateNumMap.put(planStockUniqueId, equityBenefitStockFlowDO.getPlanOperationNum());
            }
        }

        // 处理失败的 flowIds
        List<Long> failedFlowIds = Lists.newArrayList();
        stockOperateNumMap.keySet().forEach(key -> {
            List<Long> currFailedFlowIds = handleDeductAndReleaseFlow(stockOperationType, key, flowIdMap, stockOperateNumMap,
                    planStockOperateNumMap, planStockIdMap, planDateTypeMap);
            if (CollectionUtils.isNotEmpty(currFailedFlowIds)) {
                failedFlowIds.addAll(currFailedFlowIds);
            }
        });
        return failedFlowIds;
    }

    private List<Long> handleDeductAndReleaseFlow(Integer stockOperationType, Triple<Long, Long, Long> key,
                                                  SetMultimap<Triple<Long, Long, Long>, Long> flowIdMap,
                                                  ListMultimap<Triple<Long, Long, Long>, Long> stockOperateNumMap,
                                                  ListMultimap<String, Long> planStockOperateNumMap,
                                                  SetMultimap<Triple<Long, Long, Long>, Long> planStockIdMap,
                                                  Map<Triple<Long, Long, Long>, Integer> planDateTypeMap) {
        List<Long> flowIds = Lists.newArrayList(flowIdMap.get(key));
        // 更新 flow 为已处理成功，用于标记最终决定是否需要回滚 flow 为未处理
        boolean updateFlowHandled = false;
        // 更新库存 DB 成功，用于标记最终决定是否需要回滚 flow 为未处理
        boolean updateStockDb = false;
        try {
            // 1 首先使用乐观锁更新 flow 为已处理。并发场景下，同样的 flowIds 只能更新成功一次，防止同一条流水被执行多次
            updateFlowHandled = equityBenefitStockFlowService.updateFlowHandledByFlowIds(flowIds);
            if (!updateFlowHandled) {
                log.warn("handleDeductAndReleaseFlow updateFlowHandledByFlowIds fail. stockOperationType={}, key={}, flowIds={}",
                        stockOperationType, key, flowIds);
                CsossUtils.logEvent(MonitorConstants.HANDLE_DEDUCT_AND_RELEASE_FLOW, "update_flow_handled_fail");
                return flowIds;
            }

            // 2 根据聚合后的扣减或释放库存数，更新非计划库存和计划库存 DB
            // param.dateStr 在扣减或释放库存时没用到。此处需要设置只是因为 param.dateStr 不能为空，所以随便设置了一个值
            String nowDateStr = DateUtil.format_yyyyMMdd(new Date());
            StockOperationBaseParam param = StockOperationBaseParam.builder()
                    .activityId(key.getLeft())
                    .strategyId(key.getMiddle())
                    .benefitId(key.getRight())
                    .planDateType(planDateTypeMap.get(key))
                    .dateStr(nowDateStr)
                    .stockOperation(getTotalOperateNum(stockOperateNumMap.get(key)))
                    .stockOperationType(stockOperationType)
                    .planOperationMap(getPlanStockIdOperateNumMap(key, planStockIdMap, planStockOperateNumMap))
                    .build();
            AbstractStockFlowFactorHandler handler = HANDLER_MAP.get(stockOperationType);
            updateStockDb = Boolean.TRUE.equals(handler.handleCacheUpdate(param)) &&
                    Boolean.TRUE.equals(handler.consumerStockOperationMsg(param));
            if (!updateStockDb) {
                log.warn("handleDeductAndReleaseFlow fail. stockOperationType={}, key={}, flowIds={}", stockOperationType, key, flowIds);
                CsossUtils.logEvent(MonitorConstants.HANDLE_DEDUCT_AND_RELEASE_FLOW, "update_stock_db_fail");
                return flowIds;
            }
            return null;
        } catch (Exception e) {
            log.error("handleDeductAndReleaseFlow exception. stockOperationType={}, key={}, flowIds={}", stockOperationType, key, flowIds, e);
            CsossUtils.logEvent(MonitorConstants.HANDLE_DEDUCT_AND_RELEASE_FLOW, "execute_exception");
            return flowIds;
        } finally {
            // 3 更新 flow 为已处理成功，且更新库存 DB 失败，需要回滚 flow 为未处理，以便下一次继续处理
            if (updateFlowHandled && !updateStockDb) {
                rollbackFlowIsHandle(key, flowIds);
            }
        }
    }

    private void rollbackFlowIsHandle(Triple<Long, Long, Long> key, List<Long> flowIds) {
        boolean updateFlowUnHandled = equityBenefitStockFlowService.updateFlowUnHandledByFlowIds(flowIds);
        if (!updateFlowUnHandled) {
            log.warn("handleDeductAndReleaseFlow rollbackFlowIsHandle fail. key={}, flowIds={}", key, flowIds);
            CsossUtils.logEvent(MonitorConstants.HANDLE_DEDUCT_AND_RELEASE_FLOW, "update_flow_un_handled_fail");
        }
    }

    private List<Long /* mightFailedIds */> handleSingleRow(Map<Integer, List<EquityBenefitStockFlowDO>> maps, Integer stockOperationType) {
        List<Long> mightFailedIds = Lists.newArrayList();
        //TODO 考虑下分布式事务问题
        for (EquityBenefitStockFlowDO equityBenefitStockFlowDO : maps.get(stockOperationType)) {
            StockOperationBaseParam params = (EquityBenefitStockFlowEntity.getByDO(equityBenefitStockFlowDO)).toBaseParam();
            boolean updateCache = false;
            try {
                if (params.getLoadCache() == 0) {
                    updateCache = HANDLER_MAP.get(stockOperationType).handleCacheUpdate(params);
                }
            } catch (Exception e) {
                log.error("handleSingleRow handleCacheUpdate fail. stockOperationType={}, param={}", stockOperationType, JSON.toJSONString(params), e);
            }
            boolean updateDB = false;
            try {
                if (params.getLoadDb() == 0) {
                    updateDB = HANDLER_MAP.get(stockOperationType).consumerStockOperationMsg(params);
                }
            } catch (Exception e) {
                log.error("handleSingleRow consumerStockOperationMsg fail. stockOperationType={}, param={}", stockOperationType, JSON.toJSONString(params), e);
            }
            if (!updateCache || !updateDB) {
                mightFailedIds.add(equityBenefitStockFlowDO.getId());
            }
        }
        return mightFailedIds;
    }

    private Map<Long, Long> getPlanStockIdOperateNumMap(Triple<Long, Long, Long> key,
                                                        SetMultimap<Triple<Long, Long, Long>, Long> planStockIdMap,
                                                        ListMultimap<String, Long> planStockOperateNumMap) {
        Map<Long /* 关联的 planStockId */, Long /* operateNum */> planStockIdOperateNumMap = Maps.newHashMap();
        Set<Long> planStockIds = planStockIdMap.get(key);
        if (CollectionUtils.isEmpty(planStockIds)) {
            return planStockIdOperateNumMap;
        }
        planStockIds.forEach(planStockId -> {
            String planStockUniqueId = getPlanStockUniqueId(key.getLeft(), key.getMiddle(), key.getRight(), planStockId);
            planStockIdOperateNumMap.put(planStockId, getTotalOperateNum(planStockOperateNumMap.get(planStockUniqueId)));
        });
        return planStockIdOperateNumMap;
    }

    private Long getTotalOperateNum(List<Long> operateNums) {
        return CollectionUtils.isEmpty(operateNums) ? 0L : operateNums.stream().mapToLong(operationNum -> operationNum).sum();
    }

    private String getPlanStockUniqueId(Long activityId, Long strategyId, Long benefitId, Long planStockId) {
        return activityId + Constants.UNDERLINE + strategyId + Constants.UNDERLINE + benefitId + Constants.UNDERLINE + planStockId;
    }
}
