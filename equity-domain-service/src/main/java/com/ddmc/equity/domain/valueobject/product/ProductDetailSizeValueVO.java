package com.ddmc.equity.domain.valueobject.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created on 2022/10/13.
 *
 * <AUTHOR>
 */
@Data
public class ProductDetailSizeValueVO implements Serializable {

    private static final long serialVersionUID = -4299224906992924231L;

    @JsonProperty("name")
    private String name;

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("_id")
    private String mongoId;


    @JsonProperty("price")
    private String price;


    @JsonProperty("is_irreversible")
    private Integer isIrreversible;

    @JsonProperty("sort")
    private Integer sort;
}