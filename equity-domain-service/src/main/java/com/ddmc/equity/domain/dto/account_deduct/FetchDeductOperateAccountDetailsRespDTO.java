package com.ddmc.equity.domain.dto.account_deduct;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/7/16 17:03
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("查询扣减需要操作的账户明细列表 resp")
public class FetchDeductOperateAccountDetailsRespDTO {

    @ApiModelProperty("扣减需要操作的账户明细列表")
    private List<DeductOperateAccountDetailDTO> operateAccountDetails;

    @ApiModelProperty("是否超过最大循环次数。如果超过 maxLoopTimes，则不再继续查询，扣减只进行总账户扣减")
    private Boolean exceedMaxLoopTimes;
}
