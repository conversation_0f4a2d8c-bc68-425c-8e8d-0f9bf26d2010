package com.ddmc.equity.domain.entity.account;

import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.farm.request.api.task.FarmTaskRewardRequestVo;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/5/30 14:49
 * @description
 */
public class FishPondFeedAccountConvertEntity {

    public static FarmTaskRewardRequestVo convertToFarmSendRewardReq(EquityAccountContext equityAccountContext,
                                                                     String applicationName) {
        FarmTaskRewardRequestVo req = new FarmTaskRewardRequestVo();
        req.setStationId(equityAccountContext.getStationId());
        req.setUserId(equityAccountContext.getUid());
        // 发放鱼食（正整数）
        req.setRewardAmount(String.valueOf(UniversalAccountAmountCalEntity.getDoRpcSendAmountInteger(equityAccountContext)));
        // 来源 appId
        req.setSource(applicationName);
        // 请求唯一流水号
        req.setBizId(equityAccountContext.getSerialNumber());
        // 游透传的自定义参数
        req.setCustomMap(equityAccountContext.getRpcReqCustomMap());
        return req;
    }
}
