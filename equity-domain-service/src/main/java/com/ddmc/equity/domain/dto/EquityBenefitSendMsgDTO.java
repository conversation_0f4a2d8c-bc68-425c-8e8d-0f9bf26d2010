package com.ddmc.equity.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/8/28 15:01
 * @description 老账户权益领取成功 MQ 消息体，用于核对
 */
@Data
@Builder
public class EquityBenefitSendMsgDTO {

    @ApiModelProperty(value = "业务流水号")
    private String reqNo;

    @ApiModelProperty(value = "用户 id")
    private String userId;

    /**
     * @see com.ddmc.equity.enums.SceneCodeEnum
     */
    @ApiModelProperty(value = "活动场景 code，场景接入时由权益平台分配")
    private String sceneCode;

    @ApiModelProperty(value = "活动 id")
    private Long activityId;

    @ApiModelProperty(value = "策略 id")
    private Long strategyId;

    @ApiModelProperty(value = "权益组 id")
    private Long benefitGroupId;

    @ApiModelProperty("权益 id")
    private Long benefitId;

    /**
     * @see com.ddmc.equity.enums.BenefitTypeEnum
     */
    @ApiModelProperty("权益类型")
    private Integer benefitType;

    @ApiModelProperty("权益值")
    private String benefitValue;

    @ApiModelProperty("余额金额，保留两位小数。如果权益类型为余额（固定余额、随机余额），才有该字段")
    private BigDecimal balanceMoney;
}
