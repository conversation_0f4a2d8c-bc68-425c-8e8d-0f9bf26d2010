package com.ddmc.equity.domain.dto.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/26 19:54
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("活动扩展信息")
public class ActivityExtInfoDTO {

    /**
     * 初始化活动次数，对应的权益 ID。消耗活动次数的活动才有该字段
     */
    @ApiModelProperty("初始化活动次数，对应的权益 ID。消耗活动次数的活动才有该字段")
    private Long benefitId;

    /**
     * 初始化活动次数。消耗活动次数的活动才有该字段
     */
    @ApiModelProperty("初始化活动次数。消耗活动次数的活动才有该字段")
    private String initActivityCount;

    /**
     * 消耗权益类型。需要消耗指定数量权益才能发放权益的活动才有该字段
     * <p>
     * 目前支持消耗积分、活动次数、金豆
     *
     * @see com.ddmc.equity.enums.BenefitTypeEnum
     */
    @ApiModelProperty(value = "消耗权益类型。需要消耗指定数量权益才能发放权益的活动才有该字段")
    private Integer consumeBenefitType;

    /**
     * 消耗权益数量。需要消耗指定数量权益才能发放权益的活动才有该字段
     */
    @ApiModelProperty(value = "消耗权益数量。需要消耗指定数量权益才能发放权益的活动才有该字段")
    private String consumeBenefitAmount;
}
