package com.ddmc.equity.domain.valueobject.rule;

import com.ddmc.equity.domain.valueobject.rule.condition.*;
import lombok.Data;

@Data
public class RuleReqVO {
    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * ab实验
     */
    private AbTestRuleVO abTestRuleVO;
    /**
     * app版本
     */
    private AppVersionRuleVO appVersionRuleVO;
    /**
     * 城市规则
     */
    private CityRuleVO cityRuleVO;
    /**
     * 人群标签
     */
    private UserTagRuleVO userTagRuleVO;

    /**
     * 会员身份
     */
    private VipIdentityRuleVO vipIdentityRuleVO;

    /**
     * 白名单
     */
    private WhiteUserListRuleVO whiteUserListRuleVO;

    private String opAdminId;

    private String opAdminName;

}
