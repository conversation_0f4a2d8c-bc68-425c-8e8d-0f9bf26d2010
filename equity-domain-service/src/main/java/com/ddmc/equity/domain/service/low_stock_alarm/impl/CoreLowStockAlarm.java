package com.ddmc.equity.domain.service.low_stock_alarm.impl;

import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.util.business.BenefitUtil;
import com.ddmc.equity.domain.dto.benefit_stock.LowStockAlarmContextDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/8/12 13:37
 * @description
 */
@Slf4j
@Component("coreLowStockAlarm")
public class CoreLowStockAlarm extends AbstractLowStockAlarm {

    @Override
    protected String getLowStockAlarmTimesKey(LowStockAlarmContextDTO contextDTO, String hitLowStockAlarmType) {
        String benefitUni = BenefitUtil.getBenefitUni(contextDTO.getActivityId(), contextDTO.getStrategyId(),
                contextDTO.getBenefitId());
        return String.format(CacheKeyConstants.LOW_STOCK_ALARM_TIMES_KEY, "core", benefitUni, hitLowStockAlarmType);
    }

    @Override
    protected String buildAlarmContent(LowStockAlarmContextDTO contextDTO) {
        return String.format("**[%s] core 库存不足提醒**\n" +
                        "> 活动 id& 外部活动 id& 名称：<font color=\"comment\">%d/ %s/ %s</font>\n" +
                        "> 策略 id& 外部策略 id& 名称：<font color=\"comment\">%d/ %s/ %s</font>\n" +
                        "> 权益 id& 值& 名称：<font color=\"comment\">%d /%s / %s</font>\n" +
                        "%s" +
                        "> 活动时间：<font color=\"comment\">%s - %s</font>\n" +
                        "%s%s" +
                        "> 请及时补充！！！",
                contextDTO.buildDisplaySceneCode(),
                contextDTO.getActivityId(), contextDTO.buildExternal(), contextDTO.getActivityName(),
                contextDTO.getStrategyId(), contextDTO.buildStrategyExternal(), contextDTO.getStrategyName(),
                contextDTO.getBenefitId(), contextDTO.getBenefitValue(), contextDTO.getBenefitName(),
                contextDTO.buildStrategyTypeContentStr(),
                contextDTO.buildStartDateStr(), contextDTO.buildEndDateStr(),
                contextDTO.buildStockContentStr(), contextDTO.buildPlanStockContentStr());
    }
}
