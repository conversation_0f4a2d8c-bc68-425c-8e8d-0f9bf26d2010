package com.ddmc.equity.domain.dto.benefit_stock;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class RedisMulQueryStockKey {

    /**
     * 策略 id
     */
    @NotNull
    private Long strategyId;
    /**
     * 权益组 id
     */
    private Long benefitGroupId;
    /**
     * 权益 id
     */
    @NotNull
    private Long benefitId;
    /**
     * 计划库存类型
     */
    @NotNull
    private Integer planDateType;
    /**
     * 日期，格式：yyyyMMdd
     */
    @NotNull
    private String dateYmd;
}
