package com.ddmc.equity.domain.service.ticketPackage.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.service.ticketPackage.TicketPackageAccountDomainService;
import com.ddmc.equity.infra.repository.dao.TicketPackageAccountDO;
import com.ddmc.equity.infra.repository.dao.mapper.TicketPackageAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
public class TicketPackageAccountDomainServiceImpl extends ServiceImpl<TicketPackageAccountMapper, TicketPackageAccountDO>
        implements TicketPackageAccountDomainService {

    @Override
    public void insertAccountDO(TicketPackageAccountDO accountDO) {
        Assert.mustTrue(StringUtils.isNotBlank(accountDO.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "余额权益子账户 userId 不能为空");
        this.save(accountDO);
    }

    @Override
    public List<TicketPackageAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds,
                                                              List<Integer> statuses, Date startDate, Date endDate) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询权益子账户 userId 不能为空");

        Wrapper<TicketPackageAccountDO> wrapper = Wrappers.<TicketPackageAccountDO>lambdaQuery()
                .eq(TicketPackageAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(TicketPackageAccountDO::getUserId, userId)
                .in(CollectionUtils.isNotEmpty(activityIds), TicketPackageAccountDO::getActivityId, activityIds)
                .in(CollectionUtils.isNotEmpty(statuses), TicketPackageAccountDO::getStatus, statuses)
                .ge(Objects.nonNull(startDate), TicketPackageAccountDO::getCreateTime, startDate)
                .le(Objects.nonNull(endDate), TicketPackageAccountDO::getCreateTime, endDate)
                .orderByDesc(TicketPackageAccountDO::getId);
        return this.list(wrapper);
    }
}
