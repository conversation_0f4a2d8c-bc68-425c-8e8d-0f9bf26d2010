package com.ddmc.equity.domain.dto;

import com.ddmc.equity.common.enums.ExceptionEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/25 14:58
 * @description
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ServiceResult<T> {

    @ApiModelProperty(value = "返回码")
    private String code;

    @ApiModelProperty(value = "返回提示信息")
    private String msg;

    @ApiModelProperty(value = "是否成功")
    private boolean success;

    @ApiModelProperty(value = "返回数据")
    private T data;

    public static <T> ServiceResult<T> buildResult(ExceptionEnum exceptionEnum, String msg, T data) {
        exceptionEnum = ObjectUtils.defaultIfNull(exceptionEnum, ExceptionEnum.COMMON_ERROR);
        ServiceResult<T> serviceResult = new ServiceResult<>();
        serviceResult.setSuccess(exceptionEnum == ExceptionEnum.SUCCESS);
        serviceResult.setCode(exceptionEnum.getCode());
        serviceResult.setMsg(StringUtils.defaultIfBlank(msg, exceptionEnum.getMessage()));
        serviceResult.setData(data);
        return serviceResult;
    }
}
