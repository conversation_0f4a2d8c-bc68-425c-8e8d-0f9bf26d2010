package com.ddmc.equity.domain.entity.common;


import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.util.MapUtils;
import com.ddmc.equity.common.util.business.BenefitUtil;
import com.ddmc.equity.domain.converter.common.ReceiveSceneBenefitConverter;
import com.ddmc.equity.domain.dto.*;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.dto.customer.*;
import com.ddmc.equity.enums.AccountType;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.TicketAccountRecordDO;
import com.ddmc.equity.model.dto.*;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@Builder
public class BenefitOperateEntity {

    public static List<ReceiveBenefitDTO> convertToUserReceiveBenefits(List<SceneActivityCacheDto> sceneActivityCacheDtoList,
                                                                       List<ReqReceiveBenefitDTO> reqReceiveBenefitDTOList) {
        if (CollectionUtils.isEmpty(sceneActivityCacheDtoList)) {
            return null;
        }
        return sceneActivityCacheDtoList.stream().map(sceneActivityCacheDto -> {
            List<StrategyCacheDto> strategyCacheDtoList = sceneActivityCacheDto.getStrategyCacheDtoList();
            if (CollectionUtils.isEmpty(strategyCacheDtoList)) {
                return null;
            }
            return strategyCacheDtoList.stream().map(strategyCacheDto ->
                    convertToUserReceiveBenefits(sceneActivityCacheDto, strategyCacheDto, reqReceiveBenefitDTOList)
            ).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private static List<ReceiveBenefitDTO> convertToUserReceiveBenefits(SceneActivityCacheDto sceneActivityCacheDto,
                                                                        StrategyCacheDto strategyCacheDto,
                                                                        List<ReqReceiveBenefitDTO> reqReceiveBenefitDTOList) {
        Map<Long, List<BenefitIdWithConfDto>> strategyBenefitGroup = strategyCacheDto.getStrategyBenefitGroup();
        if (MapUtils.isEmpty(strategyBenefitGroup)) {
            return null;
        }
        List<BenefitIdWithConfDto> benefitIdWithConfDtoList = strategyBenefitGroup.get(strategyCacheDto.getStrategyId());
        if (CollectionUtils.isEmpty(benefitIdWithConfDtoList)) {
            return null;
        }
        return benefitIdWithConfDtoList.stream().map(benefitIdWithConfDto ->
                convertToUserReceiveBenefit(sceneActivityCacheDto, strategyCacheDto, benefitIdWithConfDto, reqReceiveBenefitDTOList)
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private static ReceiveBenefitDTO convertToUserReceiveBenefit(SceneActivityCacheDto sceneActivityCacheDto,
                                                                 StrategyCacheDto strategyCacheDto,
                                                                 BenefitIdWithConfDto benefitIdWithConfDto,
                                                                 List<ReqReceiveBenefitDTO> reqReceiveBenefitDTOList) {
        ReqReceiveBenefitDTO reqReceiveBenefitDTO = reqReceiveBenefitDTOList.stream()
                .filter(req -> isSameBenefitByReq(req, strategyCacheDto.getStrategyId(), benefitIdWithConfDto.getBenefitGroupId(), benefitIdWithConfDto.getId()))
                .findFirst().orElse(null);
        if (Objects.isNull(reqReceiveBenefitDTO)) {
            return null;
        }
        EquityBenefitDO benefitDO = benefitIdWithConfDto.getBenefitDO();
        if (Objects.isNull(benefitDO)) {
            return null;
        }
        return ReceiveBenefitDTO.builder()
                .activityId(sceneActivityCacheDto.getActivityId())
                .activityName(sceneActivityCacheDto.getActivityName())
                .externalType(sceneActivityCacheDto.getExternalType())
                .externalId(sceneActivityCacheDto.getExternalId())
                .strategyId(strategyCacheDto.getStrategyId())
                .strategyExternalId(strategyCacheDto.getExternalId())
                .strategyType(strategyCacheDto.getStrategyType())
                .masterUserTicketId(reqReceiveBenefitDTO.getMasterUserTicketId())
                .benefitGroupId(benefitIdWithConfDto.getBenefitGroupId())
                .benefitId(benefitIdWithConfDto.getId())
                .benefitName(benefitDO.getName())
                .benefitType(benefitDO.getBenefitType())
                .benefitValue(benefitDO.getBenefitValue())
                .build();
    }

    public static EquityAccountContext convertToEquityAccountContext(String reqNo, String sceneCode,
                                                                     EngineContextDTO engineContextDTO,
                                                                     ReceiveBenefitDTO receiveBenefitDTO) {
        Integer accountType = Optional.ofNullable(AccountType.getByBenefitType(receiveBenefitDTO.getBenefitType()))
                .map(AccountType::getAccountType).orElse(null);
        EquityAccountContext equityAccountContext = new EquityAccountContext();
        equityAccountContext.setAppId(engineContextDTO.getAppId());
        equityAccountContext.setPageId(engineContextDTO.getPageId());
        equityAccountContext.setCityCode(engineContextDTO.getCityNumber());
        equityAccountContext.setSerialNumber(reqNo);
        equityAccountContext.setSceneCode(sceneCode);
        equityAccountContext.setUid(engineContextDTO.getUserId());
        equityAccountContext.setAccountType(accountType);
        equityAccountContext.setActivityId(receiveBenefitDTO.getActivityId());
        equityAccountContext.setActivityName(receiveBenefitDTO.getActivityName());
        equityAccountContext.setExternalType(receiveBenefitDTO.getExternalType());
        equityAccountContext.setExternalId(receiveBenefitDTO.getExternalId());
        equityAccountContext.setStrategyId(receiveBenefitDTO.getStrategyId());
        equityAccountContext.setStrategyExternalId(receiveBenefitDTO.getStrategyExternalId());
        equityAccountContext.setBenefitGroupId(receiveBenefitDTO.getBenefitGroupId());
        equityAccountContext.setBenefitId(receiveBenefitDTO.getBenefitId());
        equityAccountContext.setBenefitName(receiveBenefitDTO.getBenefitName());
        equityAccountContext.setBenefitType(receiveBenefitDTO.getBenefitType());
        equityAccountContext.setEquityValue(receiveBenefitDTO.getBenefitValue());
        equityAccountContext.setMasterUserTicketId(receiveBenefitDTO.getMasterUserTicketId());
        equityAccountContext.setSendTicketScene(engineContextDTO.getSendTicketScene());
        equityAccountContext.setIsTicketIsRead(engineContextDTO.getIsTicketIsRead());
        equityAccountContext.setIsUseSerialNumberSave(engineContextDTO.getIsUseSerialNumberSave());
        equityAccountContext.setOpenId(engineContextDTO.getOpenId());
        return equityAccountContext;
    }

    @NotNull
    public ReceiveSceneBenefitResDTO convertToReceiveSceneBenefitResDTO(ReceiveSceneBenefitDTO receiveSceneBenefitDTO) {
        return ReceiveSceneBenefitConverter.INSTANCE.convertToReceiveSceneBenefitResDTO(receiveSceneBenefitDTO);
    }

    public List<ReqReceiveBenefitDTO> convertToReceiveBenefitDTOList(List<ReceiveBenefitReqDTO> receiveBenefitReqDTOS) {
        if (CollectionUtils.isEmpty(receiveBenefitReqDTOS)) {
            return Lists.newArrayList();
        }
        List<ReqReceiveBenefitDTO> receiveBenefitDTOList = Lists.newArrayList();
        for (ReceiveBenefitReqDTO receiveBenefitReqDTO : receiveBenefitReqDTOS) {
            ReqReceiveBenefitDTO receiveBenefitDTO = new ReqReceiveBenefitDTO();
            receiveBenefitDTO.setStrategyId(receiveBenefitReqDTO.getStrategyId());
            receiveBenefitDTO.setBenefitId(receiveBenefitReqDTO.getBenefitId());
            receiveBenefitDTOList.add(receiveBenefitDTO);
        }
        return receiveBenefitDTOList;
    }

    public EquityAccountContext convertOperateBenefitReqToEquityAccountContext(OperateBenefitReqDTO operateBenefitReqDTO) {
        EquityAccountContext equityAccountContext = new EquityAccountContext();
        equityAccountContext.setAccountType(Objects.requireNonNull(AccountType.getByBenefitType(operateBenefitReqDTO.getBenefitType())).getAccountType());
        equityAccountContext.setOperateType(operateBenefitReqDTO.getOperateType());
        equityAccountContext.setOperateCount(operateBenefitReqDTO.getOperateCount());
        equityAccountContext.setUid(operateBenefitReqDTO.getBaseRequestDTO().getUserId());
        equityAccountContext.setSerialNumber(operateBenefitReqDTO.getReqNo());
        equityAccountContext.setPageId(operateBenefitReqDTO.getPageId());
        equityAccountContext.setAppId(operateBenefitReqDTO.getAppId());
        equityAccountContext.setBenefitId(operateBenefitReqDTO.getBenefitId());
        equityAccountContext.setEquityId(operateBenefitReqDTO.getBenefitId());
        return equityAccountContext;
    }

    public OperateBenefitResDTO convertToOperateBenefitResDTO(OperateBenefitResultDTO operateBenefitResultDTO) {
        OperateBenefitResDTO operateBenefitResDTO = new OperateBenefitResDTO();
        operateBenefitResDTO.setResult(operateBenefitResultDTO.getResult());
        operateBenefitResDTO.setSuccess(operateBenefitResultDTO.isSuccess());
        operateBenefitResDTO.setCode(operateBenefitResultDTO.getCode());
        operateBenefitResDTO.setMsg(operateBenefitResultDTO.getMsg());
        UserBenefitAccountStatusRspDTO userBenefitAccountStatusRspDTO = new UserBenefitAccountStatusRspDTO();
        if (Objects.nonNull(operateBenefitResultDTO.getEquityAccountInfoDto())) {
            userBenefitAccountStatusRspDTO.setStatus(operateBenefitResultDTO.getEquityAccountInfoDto().getStatus());
            userBenefitAccountStatusRspDTO.setAccountId(operateBenefitResultDTO.getEquityAccountInfoDto().getBenefitId());
        }
        return operateBenefitResDTO;
    }

    public EquityAccountContext convertQueryBenefitReqToEquityAccountContext(String userId, AccountType accountType) {
        EquityAccountContext accountContext = new EquityAccountContext();
        accountContext.setUid(userId);
        accountContext.setAccountType(accountType.getAccountType());
        return accountContext;
    }

    public UserBenefitInfoDTO convertQueryEquityDtoToUserBenefitInfoDTO(Integer benefitType, QueryEquityDto queryEquityDto) {
        UserBenefitInfoDTO userBenefitInfoDTO = new UserBenefitInfoDTO();
        userBenefitInfoDTO.setBenefitType(benefitType);
        BenefitTypeEnum benefitTypeEnum = BenefitTypeEnum.getById(benefitType);
        if (!Objects.isNull(benefitTypeEnum)) {
            userBenefitInfoDTO.setBenefitName(benefitTypeEnum.getName());
        }
        userBenefitInfoDTO.setActiveCount(queryEquityDto.getEquityCount());
        return userBenefitInfoDTO;
    }

    public UserBenefitInfoResDTO convertUserBenefitInfoDTOListToUserBenefitInfoResDTO(List<UserBenefitInfoDTO> userBenefitInfoDTOS, String userId) {
        UserBenefitInfoResDTO userBenefitInfoResDTO = new UserBenefitInfoResDTO();
        userBenefitInfoResDTO.setUserId(userId);
        if (!CollectionUtils.isEmpty(userBenefitInfoDTOS)) {
            List<BenefitInfoDTO> benefitInfoDTOList = Lists.newArrayList();
            for (UserBenefitInfoDTO userBenefitInfoDTO : userBenefitInfoDTOS) {
                BenefitInfoDTO benefitInfoDTO = new BenefitInfoDTO();
                benefitInfoDTO.setBenefitName(userBenefitInfoDTO.getBenefitName());
                benefitInfoDTO.setBenefitType(userBenefitInfoDTO.getBenefitType());
                benefitInfoDTO.setActiveCount(new BigDecimal(userBenefitInfoDTO.getActiveCount()));
                benefitInfoDTOList.add(benefitInfoDTO);
            }
            userBenefitInfoResDTO.setBenefitInfoDTOList(benefitInfoDTOList);
        }
        return userBenefitInfoResDTO;
    }

    public UserAccountStatusReqDTO convertToUserAccountStatusReqDTO(UserBenefitAccountStatusReqDTO queryUserBenefitReqDTO) {
        UserAccountStatusReqDTO userAccountStatusReqDTO = new UserAccountStatusReqDTO();
        userAccountStatusReqDTO.setAccountId(queryUserBenefitReqDTO.getAccountId());
        userAccountStatusReqDTO.setAccountType(queryUserBenefitReqDTO.getAccountType());
        userAccountStatusReqDTO.setUserId(queryUserBenefitReqDTO.getUserId());
        return userAccountStatusReqDTO;
    }

    public EquityAccountContext convertUserAccountStatusReqDTOToEquityAccountContext(UserAccountStatusReqDTO userAccountStatusReqDTO) {
        EquityAccountContext equityAccountContext = new EquityAccountContext();
        equityAccountContext.setAccountId(userAccountStatusReqDTO.getAccountId());
        equityAccountContext.setAccountType(userAccountStatusReqDTO.getAccountType());
        equityAccountContext.setUid(userAccountStatusReqDTO.getUserId());
        return equityAccountContext;
    }

    public UserBenefitAccountStatusRspDTO convertToUserBenefitAccountStatusRspDTO(AccountInfoDTO accountInfoDTO) {
        if (Objects.isNull(accountInfoDTO)) {
            return null;
        }
        UserBenefitAccountStatusRspDTO rspDTO = new UserBenefitAccountStatusRspDTO();
        rspDTO.setAccountId(accountInfoDTO.getAccountId());
        rspDTO.setStatus(accountInfoDTO.getStatus());
        return rspDTO;
    }

    public EquityAccountContext convertToTicketEquityAccountContext(TicketAccountRecordDO ticketAccountRecordDO,
                                                                    String sceneCode) {
        EquityAccountContext equityAccountContext = new EquityAccountContext();
        equityAccountContext.setSceneCode(sceneCode);
        equityAccountContext.setAccountRecordId(ticketAccountRecordDO.getId());
        equityAccountContext.setAccountId(ticketAccountRecordDO.getAccountId());
        equityAccountContext.setBenefitId(ticketAccountRecordDO.getBenefitId());
        equityAccountContext.setEquityId(ticketAccountRecordDO.getBenefitId());
        equityAccountContext.setBenefitGroupId(ticketAccountRecordDO.getBenefitGroupId());
        equityAccountContext.setAppId(ticketAccountRecordDO.getAppId());
        equityAccountContext.setStrategyId(ticketAccountRecordDO.getStrategyId());
        equityAccountContext.setActivityId(ticketAccountRecordDO.getActivityId());
        equityAccountContext.setUid(ticketAccountRecordDO.getUserId());
        equityAccountContext.setDoRpcUniqueSerialNumber(ticketAccountRecordDO.getInnerSerialNumber());
        equityAccountContext.setEquityValue(ticketAccountRecordDO.getTicketTemplateId());
        equityAccountContext.setAccountType(AccountType.TICKET.getAccountType());
        equityAccountContext.setSerialNumber(ticketAccountRecordDO.getReqNo());
        equityAccountContext.setFreezeReceiveLimitResultId(ticketAccountRecordDO.getFreezeReceiveLimitId());
        equityAccountContext.setSendTicketScene(ticketAccountRecordDO.getSendTicketScene());

        // 特殊逻辑：如果是特定场景配置的券，不需要天降
        if (BenefitUtil.isSpecSceneSendTicket(sceneCode)) {
            // 如果是异常兜底情况，默认券已读
            equityAccountContext.setIsTicketIsRead(true);
            equityAccountContext.setIsUseSerialNumberSave(CommonEnum.INTEGER_BOOL.YES.getCode());
        } else {
            equityAccountContext.setIsTicketIsRead(false);
            equityAccountContext.setIsUseSerialNumberSave(CommonEnum.INTEGER_BOOL.NO.getCode());
        }
        return equityAccountContext;
    }

    public static ReceiveBenefitDTO convertToUserUnableReceiveBenefit(ReqReceiveBenefitDTO reqReceiveBenefitDTO,
                                                                      List<UnableReceiveBenefitDTO> unableReceiveBenefitDTOList) {
        UnableReceiveBenefitDTO unableReceiveBenefitDTO = unableReceiveBenefitDTOList.stream()
                .filter(e -> isSameBenefitByReq(reqReceiveBenefitDTO, e.getStrategyId(), e.getBenefitGroupId(), e.getBenefitId()))
                .findFirst().orElse(null);
        if (Objects.isNull(unableReceiveBenefitDTO)) {
            return null;
        }
        return ReceiveBenefitDTO.builder()
                .activityId(unableReceiveBenefitDTO.getActivityId())
                .externalType(unableReceiveBenefitDTO.getExternalType())
                .externalId(unableReceiveBenefitDTO.getExternalId())
                .strategyId(unableReceiveBenefitDTO.getStrategyId())
                .strategyExternalId(unableReceiveBenefitDTO.getStrategyExternalId())
                .masterUserTicketId(reqReceiveBenefitDTO.getMasterUserTicketId())
                .benefitGroupId(unableReceiveBenefitDTO.getBenefitGroupId())
                .benefitId(unableReceiveBenefitDTO.getBenefitId())
                .benefitType(unableReceiveBenefitDTO.getBenefitType())
                .benefitValue(unableReceiveBenefitDTO.getBenefitValue())
                .unableReceiveReasonCode(unableReceiveBenefitDTO.getUnableReceiveReasonCode())
                .build();
    }

    private static boolean isSameBenefitByReq(ReqReceiveBenefitDTO reqReceiveBenefitDTO, Long consultStrategyId,
                                              Long consultBenefitGroupId, Long consultBenefitId) {
        Long reqBenefitGroupId = reqReceiveBenefitDTO.getBenefitGroupId();
        Long reqStrategyId = reqReceiveBenefitDTO.getStrategyId();
        Long reqBenefitId = reqReceiveBenefitDTO.getBenefitId();
        if (Objects.isNull(reqBenefitGroupId)) {
            // 如果请求参数未带 benefitGroupId，则只需要通过 strategyId + benefitId 确定领取的权益；
            String reqBenefitUni = BenefitUtil.getBenefitUni(reqStrategyId, reqBenefitId);
            String consultBenefitUni = BenefitUtil.getBenefitUni(consultStrategyId, consultBenefitId);
            return StringUtils.equals(reqBenefitUni, consultBenefitUni);
        }
        // 如果请求参数带了 benefitGroupId，则需要通过 strategyId + benefitGroupId + benefitId 确定领取的权益；
        String reqBenefitUni = BenefitUtil.getBenefitUni(reqStrategyId, reqBenefitGroupId, reqBenefitId);
        String consultBenefitUni = BenefitUtil.getBenefitUni(consultStrategyId, consultBenefitGroupId, consultBenefitId);
        return StringUtils.equals(reqBenefitUni, consultBenefitUni);
    }

    public UniversalReceiveSceneBenefitResDTO convertToUniversalReceiveSceneBenefitResDTO(ReceiveSceneBenefitDTO receiveSceneBenefitDTO) {
        return ReceiveSceneBenefitConverter.INSTANCE.convertToUniversalReceiveSceneBenefitResDTO(receiveSceneBenefitDTO);
    }

    public List<ReqReceiveBenefitDTO> convertToUniversalReceiveBenefitDTOList(List<UniversalReceiveBenefitReqDTO> receiveBenefitReqDTOS) {
        if (CollectionUtils.isEmpty(receiveBenefitReqDTOS)) {
            return Lists.newArrayList();
        }
        List<ReqReceiveBenefitDTO> receiveBenefitDTOList = Lists.newArrayList();
        for (UniversalReceiveBenefitReqDTO receiveBenefitReqDTO : receiveBenefitReqDTOS) {
            ReqReceiveBenefitDTO receiveBenefitDTO = new ReqReceiveBenefitDTO();
            receiveBenefitDTO.setStrategyId(receiveBenefitReqDTO.getStrategyId());
            receiveBenefitDTO.setBenefitId(receiveBenefitReqDTO.getBenefitId());
            receiveBenefitDTOList.add(receiveBenefitDTO);
        }
        return receiveBenefitDTOList;
    }
}
