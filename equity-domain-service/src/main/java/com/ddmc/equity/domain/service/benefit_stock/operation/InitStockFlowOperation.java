package com.ddmc.equity.domain.service.benefit_stock.operation;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.PlanDateTypeEnum;
import com.ddmc.equity.common.enums.StockOperationEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.domain.dto.benefit_stock.StockOperationBaseParam;
import com.ddmc.equity.domain.service.activity.EquityActivityDomainService;
import com.ddmc.equity.domain.service.benefit_stock.AbstractStockFlowFactorHandler;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.repository.dao.EquityActivityDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockPlanDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityActivityMapper;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitStockMapper;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitStockPlanMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Date;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 初始化总库存和剩余库存和计划库存和剩余计划库存
 * 场景：活动新建、每日触发计划库存填充
 */
@Component
@Slf4j
public class InitStockFlowOperation extends AbstractStockFlowFactorHandler {

    @Resource
    private EquityBenefitStockMapper equityBenefitStockMapper;

    @Resource
    private EquityBenefitStockPlanMapper equityBenefitStockPlanMapper;

    @Resource
    private EquityActivityMapper equityActivityMapper;

    @Autowired
    private EquityActivityDomainService equityActivityDomainService;

    private static final Random RANDOM = new Random();

    @Resource
    private RedisCache redisCache;

    @Override
    public Integer getStockOperationType() {
        return StockOperationEnum.INIT.getOperation();
    }

    @Override
    public Boolean handleCacheUpdate(StockOperationBaseParam msg) {
        Long strategyId = msg.getStrategyId();
        Long benefitId = msg.getBenefitId();
        Long activityId = msg.getActivityId();
        Integer planDateType = msg.getPlanDateType();
        Long operationNum = msg.getStockOperation();
        Duration duration = Duration.ofDays(180);

        EquityActivityDO equityActivityDO = equityActivityDomainService.queryEquityActivityDOById(activityId);
        if (Objects.isNull(equityActivityDO)) {
            log.error("handleCacheUpdate equityActivityDO isNull. msg={}", JSON.toJSONString(msg));
            return false;
        }

        // 初始化场景下，都是trySet
        String stockKey = String.format(CacheKeyConstants.STOCK_COUNT_KEY, strategyId, benefitId);
        String balanceKey = String.format(CacheKeyConstants.BALANCE_COUNT_KEY, strategyId, benefitId);
        redisCache.insertNewCache(stockKey, balanceKey, operationNum, duration.toDays(), TimeUnit.DAYS);
        if (PlanDateTypeEnum.DAY.getType().equals(msg.getPlanDateType())) {
            // 今日
            Long supplierCount = msg.getPlanOperationNum();
            String nowStr = DateUtil.format_yyyyMMdd(equityActivityDO.getStartTime());
            int offSet = offSetDate(equityActivityDO.getStartTime());
            insertNewPlanCache(nowStr, strategyId, benefitId, planDateType, supplierCount, 2 + offSet);
            // 明日
            Date tomorrow = DateUtil.getTomorrow(equityActivityDO.getStartTime());
            String tomorrowStr = DateUtil.format_yyyyMMdd(tomorrow);
            insertNewPlanCache(tomorrowStr, strategyId, benefitId, planDateType, supplierCount, 3 + offSet);
        }
        return true;
    }

    /****
     * 计算时间偏差
     * @param date 需要计算的时间
     * @return 返回时间偏差
     */
    private int offSetDate(Date date){
        Date now = new Date();
        if (date.after(now)) {
            long count = cn.hutool.core.date.DateUtil.betweenDay(new Date(),date,true);
            return Integer.parseInt(count+"") ;
        }
        return 0;
    }

    private void insertNewPlanCache(String dateStr, Long strategyId, Long benefitId,
                                    Integer planDateType,
                                    Long supplierCount,
                                    long expireDayOffset) {
        if (supplierCount > 0) {
            String planBalanceCacheKey = String.format(
                    CacheKeyConstants.PLAN_BALANCE_COUNT_KEY, strategyId, benefitId, planDateType, dateStr);
            String planStockCacheKey = String.format(
                    CacheKeyConstants.PLAN_STOCK_COUNT_KEY, strategyId, benefitId, planDateType, dateStr);
            // 初始化场景下，都是trySet
            expireDayOffset = expireDayOffset > 0 ? expireDayOffset : 2;
            int timeToLive = (int) (Duration.ofDays(expireDayOffset).getSeconds() + RANDOM.nextInt(300));
            redisCache.insertNewCache(planBalanceCacheKey,planStockCacheKey,supplierCount,timeToLive, TimeUnit.SECONDS);
        }
    }

    @Override
    public Boolean consumerStockOperationMsg(StockOperationBaseParam msg) {
        // 初始化场景下，都是insert
        // 插库存表
        EquityBenefitStockDO equityBenefitStockDO = new EquityBenefitStockDO();
        equityBenefitStockDO.setStockCount(msg.getStockOperation());
        equityBenefitStockDO.setBalanceCount(msg.getStockOperation());
        equityBenefitStockDO.setActivityId(msg.getActivityId());
        equityBenefitStockDO.setBenefitId(msg.getBenefitId());
        equityBenefitStockDO.setStrategyId(msg.getStrategyId());
        equityBenefitStockDO.setUseCount(0L);
        equityBenefitStockDO.setVersion(0L);
        int stockRows;
        try {
            stockRows = equityBenefitStockMapper.insert(equityBenefitStockDO);
        } catch (DuplicateKeyException e) {
            log.error("出现重复插入的主键，疑似已经处理过了，确认后可无视 equityBenefitStockDO:{}", equityBenefitStockDO, e);
            stockRows = 1;
        }
        if (stockRows == 0) {
            log.error("插入权益库存信息失败 msg:{} equityBenefitStockDO:{}", msg, equityBenefitStockDO);
            return false;
        }
        if (Constants.ONE.equals(msg.getPlanDateType())) {
            // 取活动的开始时间为计划库存当天时间
            EquityActivityDO equityActivityDO = equityActivityMapper.selectById(msg.getActivityId());
            Assert.notNull(equityActivityDO, ExceptionEnum.ACTIVITY_IS_NOT_EXIST);
            Date startTime = equityActivityDO.getStartTime();
            if (null == startTime || startTime.before(new Date())) {
                log.info("创建了已开始的活动 startTime={} equityActivityDO:{}", startTime, equityActivityDO);
                startTime = new Date();
            }

            long supplierCount = msg.getPlanOperationNum();
            String nowStr = DateUtil.format_yyyyMMdd(startTime);
            Boolean tPlan = insertNewPlan(msg, supplierCount, nowStr);

            Date tomorrow = DateUtil.getTomorrow(startTime);
            String tomorrowStr = DateUtil.format_yyyyMMdd(tomorrow);
            Boolean tPlusOnePlan = insertNewPlan(msg, supplierCount, tomorrowStr);

            return tPlan && tPlusOnePlan;
        }
        return true;
    }

    private Boolean insertNewPlan(StockOperationBaseParam msg, Long supplierCount, String nowStr) {
        if (supplierCount > 0) {
            EquityBenefitStockPlanDO mightExist = equityBenefitStockPlanMapper.selectOne(new LambdaQueryWrapper<EquityBenefitStockPlanDO>()
                    .eq(EquityBenefitStockPlanDO::getStrategyId, msg.getStrategyId())
                    .eq(EquityBenefitStockPlanDO::getBenefitId, msg.getBenefitId())
                    .eq(EquityBenefitStockPlanDO::getPlanDateType, msg.getPlanDateType())
                    .eq(EquityBenefitStockPlanDO::getPlanDate, nowStr)
                    .eq(EquityBenefitStockPlanDO::getActivityId, msg.getActivityId())
                    .last("limit 1")
            );
            if (null != mightExist) {
                // 初始化的行为不更新
                return true;
            }
            // 插plan表
            EquityBenefitStockPlanDO equityBenefitStockPlanDO = new EquityBenefitStockPlanDO();
            equityBenefitStockPlanDO.setActivityId(msg.getActivityId());
            equityBenefitStockPlanDO.setStrategyId(msg.getStrategyId());
            equityBenefitStockPlanDO.setBenefitId(msg.getBenefitId());
            equityBenefitStockPlanDO.setStockCount(supplierCount);
            equityBenefitStockPlanDO.setBalanceCount(supplierCount);
            equityBenefitStockPlanDO.setPlanDateType(msg.getPlanDateType());
            equityBenefitStockPlanDO.setPlanDate(nowStr);
            equityBenefitStockPlanDO.setUseCount(0L);
            equityBenefitStockPlanDO.setVersion(0L);
            int stockPlanRow;
            try {
                stockPlanRow = equityBenefitStockPlanMapper.insert(equityBenefitStockPlanDO);
            } catch (DuplicateKeyException e) {
                log.error("出现重复插入的主键，疑似已经处理过了，确认后可无视 equityBenefitStockPlanDO:{}", equityBenefitStockPlanDO, e);
                stockPlanRow = 1;
            }
            if (stockPlanRow <= 0) {
                log.error("插入权益库存计划信息失败 msg:{} equityBenefitStockPlanDO:{}", msg, equityBenefitStockPlanDO);
                return false;
            }

        }
        return true;
    }
}
