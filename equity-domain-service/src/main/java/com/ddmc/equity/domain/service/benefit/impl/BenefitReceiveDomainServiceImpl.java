package com.ddmc.equity.domain.service.benefit.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ddmc.equity.domain.service.benefit.BenefitReceiveDomainService;
import com.ddmc.equity.dto.business.BenefitReceiveReqDTO;
import com.ddmc.equity.infra.repository.dao.BenefitReceiveStatisticsDO;
import com.ddmc.equity.infra.repository.dao.mapper.BenefitReceiveStatisticsMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/28
 */
@Slf4j
@Service
public class BenefitReceiveDomainServiceImpl implements BenefitReceiveDomainService {

    @Resource
    private BenefitReceiveStatisticsMapper benefitReceiveStatisticsMapper;

    @Override
    public List<BenefitReceiveStatisticsDO> queryBenefitReceiveDetail(BenefitReceiveReqDTO benefitReceiveReqDTO) {
        QueryWrapper<BenefitReceiveStatisticsDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("external_type", benefitReceiveReqDTO.getExternalType());
        queryWrapper.in("external_id", benefitReceiveReqDTO.getExternalIds());
        if (!CollectionUtils.isEmpty(benefitReceiveReqDTO.getStrategyExternalIds())) {
            queryWrapper.in("strategy_external_id", benefitReceiveReqDTO.getStrategyExternalIds());
        }
        log.info("queryBenefitReceiveDetail queryWrapper = {}", JSON.toJSONString(queryWrapper));
        return benefitReceiveStatisticsMapper.selectList(queryWrapper);
    }
}
