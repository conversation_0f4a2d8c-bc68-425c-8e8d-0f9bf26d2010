package com.ddmc.equity.domain.dto.benefit_stock;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class QueryStockStatusByRedisMulResp {

    @NotNull
    @Builder.Default
    private Map<BenefitStockLimitInfo, Boolean /* true: has stock */> stockInfo = Maps.newHashMap();
}
