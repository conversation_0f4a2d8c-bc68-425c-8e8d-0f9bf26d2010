package com.ddmc.equity.domain.service.equityAccount;

import com.ddmc.equity.domain.dto.QueryUniversalAccountDetailsReqDTO;
import com.ddmc.equity.domain.dto.account_deduct.DeductOperateAccountDetailDTO;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;

import java.util.Date;
import java.util.List;

public interface UniversalAccountDetailDomainService {

    void add(UniversalAccountDetailDO universalAccountDetailDO);

    /**
     * 查询用户权益通用账户明细列表
     *
     * @param req 请求参数
     * @return 用户权益通用账户明细列表
     */
    List<UniversalAccountDetailDO> queryUniversalAccountDetails(QueryUniversalAccountDetailsReqDTO req);

    /**
     * 分页查询用户权益通用账户明细列表
     *
     * @param req 请求参数
     * @return 用户权益通用账户明细列表
     */
    PageListRespDTO<UniversalAccountDetailDO> pageQueryUniversalAccountDetails(QueryUniversalAccountDetailsReqDTO req);

    /**
     * 查询用户指定的权益通用账户明细
     *
     * @param userId          用户 id
     * @param accountDetailId 账户明细 id
     * @return 权益通用账户明细
     */
    UniversalAccountDetailDO queryById(String userId, Long accountDetailId);

    List<UniversalAccountDetailDO> queryAvailableAccountDetails(String userId, List<Long> useActivityIds,
                                                                Integer benefitType);

    int deductDetailAvailableCount(String userId, Long id, Integer deductCount, Integer version);

    /**
     * 查询需要过期的用户权益通用账户明细列表
     *
     * @param shardingIndex   DAL shardingIndex
     * @param userIds         指定用户 ids。不指定则查询所有
     * @param benefitTypes    指定权益类型。不指定则查询所有
     * @param expireStartTime 过期时间范围。expireTime >= expireStartTime
     * @param expireEndTime   过期时间范围。expireTime <= expireEndTime
     * @param lastId          指定 id > lastId
     * @param limit           限制查询条数
     * @return 需要过期的用户权益通用账户明细列表
     */
    List<UniversalAccountDetailDO> queryNeedExpireAccountDetails(Integer shardingIndex, List<String> userIds,
                                                                 List<Integer> benefitTypes,
                                                                 Date expireStartTime, Date expireEndTime,
                                                                 Long lastId, Integer limit);

    /**
     * 指定 userId + useActivityIds + benefitType 分页查询扣减需要操作的账户明细列表
     * <p>
     * expireTime >= now()
     * availableCount > 0
     * <p>
     * expireTime 小的优先返回，优先扣减早过期的；
     * 过期时间相同的，id 小的优先返回，优先扣减早创建的；
     *
     * @param userId         用户 id，必传
     * @param useActivityIds 使用活动 id 列表，必传。如果扣减账户时未指定使用活动 id，则 useActivityIds in (0)
     * @param benefitType    权益类型，必传。指定扣减哪种类型的权益账户
     * @param limit          每页限制数量
     * @param offset         偏移量
     * @return 查询扣减需要操作的账户明细列表
     */
    List<DeductOperateAccountDetailDTO> fetchDeductOperateAccountDetailsByPage(String userId, List<Long> useActivityIds,
                                                                               Integer benefitType, Integer limit,
                                                                               Long offset);

    /**
     * 扣减账户明细 availableCount -= operateCount
     *
     * @param userId          用户 id
     * @param accountDetailId 账户明细 id
     * @param operateCount    操作扣减数量
     * @return 是否扣减成功
     */
    boolean deductDetailCount(String userId, Long accountDetailId, Integer operateCount);

    /**
     * 扣减账户明细列表 availableCount = 0
     *
     * @param userId           用户 id
     * @param accountDetailIds 账户明细 ids
     * @return 是否扣减成功。只有影响行数 = accountDetailIds.size 才算更新成功
     */
    boolean deductDetailZero(String userId, List<Long> accountDetailIds);

    /**
     * 过期账户明细 expireCount += operateCount、availableCount -= operateCount
     *
     * @param userId          用户 id
     * @param accountDetailId 账户明细 id
     * @param operateCount    操作过期数量
     * @param version         版本号
     * @return 是否过期成功
     */
    boolean expireDetailCount(String userId, Long accountDetailId, Integer operateCount, Integer version);

    /**
     * 过期账户明细列表 expireCount += availableCount、availableCount = 0
     *
     * @param userId           用户 id
     * @param accountDetailIds 账户明细 ids
     * @return 是否过期成功。只有影响行数 = accountDetailIds.size 才算更新成功
     */
    boolean expireDetailZero(String userId, List<Long> accountDetailIds);
}
