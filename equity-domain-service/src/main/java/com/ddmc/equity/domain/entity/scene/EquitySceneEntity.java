package com.ddmc.equity.domain.entity.scene;


import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.SceneStatusEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.converter.scene.SceneConverter;
import com.ddmc.equity.domain.valueobject.scene.SceneListFilterReqVO;
import com.ddmc.equity.dto.business.SceneBusinessDTO;
import com.ddmc.equity.dto.business.SceneListReqDTO;
import com.ddmc.equity.dto.business.SceneSaveReqDTO;
import com.ddmc.equity.infra.repository.dao.EquitySceneDO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.List;


@Getter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class EquitySceneEntity extends EquitySceneDO implements Serializable {
    private static final long serialVersionUID = 7343534293846592691L;

    @NotNull
    public static EquitySceneDO getSceneDOBySaveReq(@NotNull SceneSaveReqDTO saveReq) {
        return SceneConverter.INSTANCE.srd2e(saveReq);
    }

    @NotNull
    public static SceneListFilterReqVO getSceneFilterByListReq(@NotNull SceneListReqDTO req) {
        return SceneConverter.INSTANCE.d2v(req);
    }

    @NotNull
    public static List<SceneBusinessDTO> listSceneBusinessDTO(@NotNull List<EquitySceneDO> list) {
        return SceneConverter.INSTANCE.v2ds(list);
    }

    public static SceneSaveReqDTO getSceneSaveReqBySceneDO(EquitySceneDO sceneDO) {
        return SceneConverter.INSTANCE.d2d(sceneDO);
    }

    /**
     * 校验场景是否存在，是否已发布
     * @param equitySceneDO 场景
     */
    public void checkSceneIsExistAndIsAlreadyPublish(EquitySceneDO equitySceneDO) {
        Assert.notNull(equitySceneDO, ExceptionEnum.SCENE_IS_NOT_EXIST_FAILED);
        Assert.mustTrue(SceneStatusEnum.PUBLISHED.getId().equals(equitySceneDO.getStatus()),ExceptionEnum.SCENE_IS_NOT_PUBLISH);
    }
}
