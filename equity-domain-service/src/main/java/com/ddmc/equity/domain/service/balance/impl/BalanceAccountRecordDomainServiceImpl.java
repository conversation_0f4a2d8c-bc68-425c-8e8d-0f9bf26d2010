package com.ddmc.equity.domain.service.balance.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.entity.account.BalanceAccountConvertEntity;
import com.ddmc.equity.domain.service.balance.BalanceAccountRecordDomainService;
import com.ddmc.equity.infra.repository.dao.BalanceAccountRecordDO;
import com.ddmc.equity.infra.repository.dao.mapper.BalanceAccountRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/16 11:25
 * @description
 */
@Slf4j
@Service
public class BalanceAccountRecordDomainServiceImpl extends ServiceImpl<BalanceAccountRecordMapper, BalanceAccountRecordDO>
        implements BalanceAccountRecordDomainService {

    @Override
    public void insertAccountRecordDO(BalanceAccountRecordDO accountRecordDO) {
        Assert.notNull(accountRecordDO, ExceptionEnum.ILLEGAL_ARGS.getCode(), "余额权益子账户操作流水不能为空");
        Assert.mustTrue(StringUtils.isNotBlank(accountRecordDO.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "余额权益子账户操作流水 userId 不能为空");
        this.save(accountRecordDO);
    }

    @Override
    public boolean updateAccountRecordStatusAndRpcResult(String userId, Long accountRecordId, Long accountId, Integer status,
                                                         EquityRpcDto equityRpcDto, Map<String, Object> ruleLimitInfoMap) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "余额权益子账户操作流水 userId 不能为空");
        Assert.notNull(accountRecordId, ExceptionEnum.ILLEGAL_ARGS.getCode(), "余额权益子账户操作流水 accountRecordId 不能为空");
        Assert.mustTrue(StatusEnum.isContain(status), ExceptionEnum.ILLEGAL_ARGS.getCode(), "余额权益子账户操作流水状态异常");

        BalanceAccountRecordDO update = BalanceAccountConvertEntity.createUpdateAccountRecordDO(accountRecordId, accountId,
                status, equityRpcDto, ruleLimitInfoMap);
        return this.update(update, Wrappers.<BalanceAccountRecordDO>lambdaUpdate()
                .eq(BalanceAccountRecordDO::getUserId, userId)
                .eq(BalanceAccountRecordDO::getId, accountRecordId));
    }

    @Override
    public BalanceAccountRecordDO queryAccountRecordByUniqueKeyAndStatuses(String userId, Integer operateType,
                                                                           String reqNo, List<Integer> statuses) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "余额权益子账户操作流水 userId 不能为空");
        Assert.mustTrue(OperateTypeEnum.contains(operateType), ExceptionEnum.ILLEGAL_ARGS.getCode(), "余额权益子账户操作流水操作类型异常");
        Assert.mustTrue(StringUtils.isNotBlank(reqNo), ExceptionEnum.ILLEGAL_ARGS.getCode(), "余额权益子账户操作流水 reqNo 不能为空");

        Wrapper<BalanceAccountRecordDO> wrapper = Wrappers.<BalanceAccountRecordDO>lambdaQuery()
                .eq(BalanceAccountRecordDO::getUserId, userId)
                .eq(BalanceAccountRecordDO::getOperateType, operateType)
                .eq(BalanceAccountRecordDO::getReqNo, reqNo)
                .in(CollectionUtils.isNotEmpty(statuses), BalanceAccountRecordDO::getStatus, statuses);
        return this.getOne(wrapper);
    }

    @Override
    public List<BalanceAccountRecordDO> queryBalanceAccountRecords(String userId, String sendBalanceScene,
                                                                   List<Integer> operateTypes, List<Integer> statuses,
                                                                   Date startDate, Date endDate) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询权益子账户操作流水列表 userId 不能为空");

        Wrapper<BalanceAccountRecordDO> wrapper = Wrappers.<BalanceAccountRecordDO>lambdaQuery()
                .eq(BalanceAccountRecordDO::getUserId, userId)
                .eq(StringUtils.isNotBlank(sendBalanceScene), BalanceAccountRecordDO::getSendBalanceScene, sendBalanceScene)
                .in(CollectionUtils.isNotEmpty(operateTypes), BalanceAccountRecordDO::getOperateType, operateTypes)
                .in(CollectionUtils.isNotEmpty(statuses), BalanceAccountRecordDO::getStatus, statuses)
                .ge(Objects.nonNull(startDate), BalanceAccountRecordDO::getCreateTime, startDate)
                .le(Objects.nonNull(endDate), BalanceAccountRecordDO::getCreateTime, endDate);
        return this.list(wrapper);
    }
}
