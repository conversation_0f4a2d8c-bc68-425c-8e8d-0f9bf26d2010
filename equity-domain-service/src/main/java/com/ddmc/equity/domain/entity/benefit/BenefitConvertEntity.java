package com.ddmc.equity.domain.entity.benefit;

import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.domain.dto.benefit.BenefitExtInfoDTO;
import com.ddmc.equity.dto.customer.SaveAndGetBenefitDTO;
import com.ddmc.equity.dto.customer.benefit.EquityBenefitDTO;
import com.ddmc.equity.enums.BenefitAmountTypeEnum;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.enums.ExpireTimeUnitEnum;
import com.ddmc.equity.enums.ExpireTypeEnum;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/19 15:25
 * @description
 */
public class BenefitConvertEntity {

    public static List<FullBenefitInfoDTO> convertToFullBenefitInfoDTOList(List<SceneActivityCacheDto> activities) {
        if (CollectionUtils.isEmpty(activities)) {
            return null;
        }
        return activities.stream().map(BenefitConvertEntity::convertToFullBenefitInfoDTOList)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public static List<FullBenefitInfoDTO> convertToFullBenefitInfoDTOList(SceneActivityCacheDto activity) {
        List<StrategyCacheDto> strategies = activity.getStrategyCacheDtoList();
        if (CollectionUtils.isEmpty(strategies)) {
            return null;
        }
        return strategies.stream().map(strategy ->
                BenefitConvertEntity.convertToFullBenefitInfoDTOList(activity, strategy)
        ).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private static List<FullBenefitInfoDTO> convertToFullBenefitInfoDTOList(SceneActivityCacheDto activity,
                                                                            StrategyCacheDto strategy) {
        List<BenefitIdWithConfDto> benefits = strategy.getStrategyBenefitGroup().get(strategy.getStrategyId());
        if (CollectionUtils.isEmpty(benefits)) {
            return null;
        }
        return benefits.stream().map(benefit ->
                convertToFullBenefitInfoDTO(activity, strategy, benefit)
        ).collect(Collectors.toList());
    }

    public static FullBenefitInfoDTO convertToFullBenefitInfoDTO(SceneActivityCacheDto activity, StrategyCacheDto strategy,
                                                                 BenefitIdWithConfDto benefit) {
        EquityBenefitDO benefitDO = benefit.getBenefitDO();
        BenefitExtInfoDTO benefitExtInfoDTO = Optional.ofNullable(benefitDO).map(EquityBenefitDO::getExtInfo)
                .map(e -> JsonUtil.parseObject(e, BenefitExtInfoDTO.class)).orElse(null);
        return FullBenefitInfoDTO.builder()
                .activityId(activity.getActivityId())
                .activityName(activity.getActivityName())
                .externalType(activity.getExternalType())
                .externalId(activity.getExternalId())
                .strategyId(strategy.getStrategyId())
                .strategyName(strategy.getStrategyName())
                .strategyExternalId(strategy.getExternalId())
                .strategyType(strategy.getStrategyType())
                .benefitGroupId(benefit.getBenefitGroupId())
                .benefitId(benefit.getId())
                .benefitName(Optional.ofNullable(benefitDO).map(EquityBenefitDO::getName).orElse(null))
                .benefitType(Optional.ofNullable(benefitDO).map(EquityBenefitDO::getBenefitType).orElse(null))
                .benefitValue(Optional.ofNullable(benefitDO).map(EquityBenefitDO::getBenefitValue).orElse(null))
                .benefitAmountType(Optional.ofNullable(benefitDO).map(EquityBenefitDO::getBenefitAmountType).orElse(null))
                .maxAmount(Optional.ofNullable(benefitDO).map(EquityBenefitDO::getMaxAmount).orElse(null))
                .benefitExtInfoDTO(benefitExtInfoDTO)
                .build();
    }

    public static List<EquityBenefitDTO> convertEquityBenefitDTOList(List<EquityBenefitDO> doList) {
        List<EquityBenefitDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(doList)) {
            return dtoList;
        }
        for (EquityBenefitDO benefitDO : doList) {
            EquityBenefitDTO dto = new EquityBenefitDTO();
            dto.setBenefitType(benefitDO.getBenefitType());
            dto.setId(benefitDO.getId());
            dto.setBenefitValue(benefitDO.getBenefitValue());
            dto.setName(benefitDO.getName());
            dto.setExtInfo(benefitDO.getExtInfo());
            dto.setExpireType(benefitDO.getExpireType());
            dto.setExpireTimeUnit(benefitDO.getExpireTimeUnit());
            dto.setExpireTime(benefitDO.getExpireTime());
            dtoList.add(dto);
        }
        return dtoList;
    }

    public static Date convertToExpireTime(Date startTime, Integer expireType, Integer expireTimeUnit, Long expireTime) {
        if (Objects.equals(expireType, ExpireTypeEnum.FIX_EXPIRE.getStatus()) && Objects.nonNull(expireTime)) {
            // expireTime 时间戳是秒，需要转化成毫秒
            return new Date(expireTime * 1000L);
        }
        if (Objects.equals(expireType, ExpireTypeEnum.OPPOSITE_EXPIRE.getStatus())) {
            return convertToExpireTime(startTime, expireTimeUnit, Math.toIntExact(expireTime));
        }
        return DateUtil.getDefaultPermanentTime();
    }

    private static Date convertToExpireTime(Date startTime, Integer expireTimeUnit, Integer expireTime) {
        if (Objects.equals(expireTimeUnit, ExpireTimeUnitEnum.YEAR_UNIT.getStatus()) && Objects.nonNull(expireTime)) {
            return DateUtils.addYears(startTime, expireTime);
        }
        if (Objects.equals(expireTimeUnit, ExpireTimeUnitEnum.MONTH_UNIT.getStatus()) && Objects.nonNull(expireTime)) {
            return DateUtils.addMonths(startTime, expireTime);
        }
        if (Objects.equals(expireTimeUnit, ExpireTimeUnitEnum.DAY_UNIT.getStatus()) && Objects.nonNull(expireTime)) {
            return DateUtils.addDays(startTime, expireTime);
        }
        return DateUtil.getDefaultPermanentTime();
    }

    public static EquityBenefitDO convertToEquityBenefitDO(SaveAndGetBenefitDTO saveAndGetBenefitDTO) {
        String benefitName = saveAndGetBenefitDTO.getBenefitName();
        Integer benefitType = saveAndGetBenefitDTO.getBenefitType();
        String benefitValue = saveAndGetBenefitDTO.getBenefitValue();

        EquityBenefitDO benefitDO = new EquityBenefitDO();
        benefitDO.setName(convertToBenefitName(benefitName, benefitType, benefitValue));
        benefitDO.setBenefitType(benefitType);
        benefitDO.setBenefitValue(benefitValue);
        benefitDO.setExternalId(StringUtils.EMPTY);
        benefitDO.setBenefitAmountType(BenefitAmountTypeEnum.FIXED_AMOUNT.getCode());
        benefitDO.setExpireType(ObjectUtils.defaultIfNull(saveAndGetBenefitDTO.getExpireType(), ExpireTypeEnum.NONE.getStatus()));
        benefitDO.setExpireTimeUnit(ObjectUtils.defaultIfNull(saveAndGetBenefitDTO.getExpireTimeUnit(), ExpireTimeUnitEnum.NONE.getStatus()));
        benefitDO.setExpireTime(ObjectUtils.defaultIfNull(saveAndGetBenefitDTO.getExpireTime(), 0L));
        benefitDO.setExtInfo(JsonUtil.toString(saveAndGetBenefitDTO.getExtInfo()));
        benefitDO.setStatus(Constants.STATUS_YES);
        return benefitDO;
    }

    private static String convertToBenefitName(String benefitName, Integer benefitType, String benefitValue) {
        if (StringUtils.isNotBlank(benefitName)) {
            return benefitName;
        }
        String benefitTypeValue = Optional.of(benefitType).map(BenefitTypeEnum::getById).map(BenefitTypeEnum::getName).orElse(null);
        return benefitTypeValue + "_" + benefitValue;
    }
}
