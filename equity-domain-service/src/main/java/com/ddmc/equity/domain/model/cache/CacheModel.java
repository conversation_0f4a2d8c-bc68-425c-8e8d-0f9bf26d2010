package com.ddmc.equity.domain.model.cache;

import org.jetbrains.annotations.Nullable;

import java.util.concurrent.TimeUnit;

public interface CacheModel<T> {
    /**
     * 根据传入参数生成redis key
     *
     * @return String
     */
    @Nullable
    String genKey();

    /**
     * 从redis中加载数据
     *
     * @return T|null
     */
    boolean setValueToRedis();

    /**
     * 从redis中加载数据
     *
     * @return T|null
     */
    @Nullable
    T getValueByRedis();

    /**
     * 从redis中加载超时时间
     *
     * @return Long|null
     */
    @Nullable
    default Long getTTL() {
        return 0L;
    }

    default void setTTL(Long ttl, TimeUnit unit) {
    }
}
