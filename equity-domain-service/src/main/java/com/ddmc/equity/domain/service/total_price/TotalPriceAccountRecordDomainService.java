package com.ddmc.equity.domain.service.total_price;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.infra.repository.dao.TotalPriceAccountRecordDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/16 11:30
 * @description
 */
public interface TotalPriceAccountRecordDomainService extends IService<TotalPriceAccountRecordDO> {

    /**
     * 创建权益子账户操作流水
     *
     * @param accountRecordDO DB_DO
     */
    void insertAccountRecordDO(TotalPriceAccountRecordDO accountRecordDO);

    /**
     * 通过 userId、accountRecordId 更新权益子账户操作流水 accountId、状态、rpc 调用结果
     *
     * @param userId           用户 id
     * @param accountRecordId  子账户操作流水 accountId
     * @param accountId        子账户 id
     * @param status           目标流水状态
     * @param equityRpcDto     rpc 调用结果
     * @param ruleLimitInfoMap 频次、库存规则信息。对应操作记录扣减的频次 id、库存 id
     * @return 更新结果
     */
    boolean updateAccountRecordStatusAndRpcResult(String userId, Long accountRecordId, Long accountId, Integer status,
                                                  EquityRpcDto equityRpcDto, Map<String, Object> ruleLimitInfoMap);

    /**
     * 通过 userId、操作类型、业务流水号、流水状态列表查询权益子账户操作流水
     *
     * @param userId      用户 id
     * @param operateType 操作类型
     * @param reqNo       业务流水号
     * @param statuses    流水状态列表
     * @return 权益子账户操作流水信息
     */
    TotalPriceAccountRecordDO queryAccountRecordByUniqueKeyAndStatuses(String userId, Integer operateType,
                                                                    String reqNo, List<Integer> statuses);
}
