package com.ddmc.equity.domain.service.cash.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.entity.account.CashCouponAccountConvertEntity;
import com.ddmc.equity.domain.service.cash.CashCouponAccountRecordDomainService;
import com.ddmc.equity.infra.repository.dao.CashCouponAccountRecordDO;
import com.ddmc.equity.infra.repository.dao.mapper.CashCouponAccountRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Slf4j
@Service
public class CashCouponAccountRecordDomainServiceImpl extends ServiceImpl<CashCouponAccountRecordMapper, CashCouponAccountRecordDO>
        implements CashCouponAccountRecordDomainService {

    @Override
    public void insertAccountRecordDO(CashCouponAccountRecordDO accountRecordDO) {
        this.save(accountRecordDO);
    }

    @Override
    public boolean updateAccountRecordStatusAndRpcResult(String userId, Long accountRecordId, Long accountId, Integer status,
                                                         EquityRpcDto equityRpcDto, Map<String, Object> ruleLimitInfoMap) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "权益子账户操作流水 userId 不能为空");
        Assert.notNull(accountRecordId, ExceptionEnum.ILLEGAL_ARGS.getCode(), "权益子账户操作流水 accountRecordId 不能为空");
        Assert.mustTrue(StatusEnum.isContain(status), ExceptionEnum.ILLEGAL_ARGS.getCode(), "权益子账户操作流水状态异常");

        CashCouponAccountRecordDO update = CashCouponAccountConvertEntity.createUpdateAccountRecordDO(accountRecordId, accountId,
                status, equityRpcDto, ruleLimitInfoMap);
        return this.update(update, Wrappers.<CashCouponAccountRecordDO>lambdaUpdate()
                .eq(CashCouponAccountRecordDO::getUserId, userId)
                .eq(CashCouponAccountRecordDO::getId, accountRecordId));
    }

    @Override
    public CashCouponAccountRecordDO queryAccountRecordByUniqueKeyAndStatuses(String userId, Integer operateType,
                                                                              String reqNo, List<Integer> statuses) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "权益子账户操作流水 userId 不能为空");
        Assert.mustTrue(OperateTypeEnum.contains(operateType), ExceptionEnum.ILLEGAL_ARGS.getCode(), "权益子账户操作流水操作类型异常");
        Assert.mustTrue(StringUtils.isNotBlank(reqNo), ExceptionEnum.ILLEGAL_ARGS.getCode(), "权益子账户操作流水 reqNo 不能为空");

        Wrapper<CashCouponAccountRecordDO> wrapper = Wrappers.<CashCouponAccountRecordDO>lambdaQuery()
                .eq(CashCouponAccountRecordDO::getUserId, userId)
                .eq(CashCouponAccountRecordDO::getOperateType, operateType)
                .eq(CashCouponAccountRecordDO::getReqNo, reqNo)
                .in(CollectionUtils.isNotEmpty(statuses), CashCouponAccountRecordDO::getStatus, statuses);
        return this.getOne(wrapper);
    }

    @Override
    public List<CashCouponAccountRecordDO> queryAccountRecordByStrategyIdAndStatus(String userId, Long strategyId, Long benefitId, Integer operateType, Long freezeId, Integer status) {
        Wrapper<CashCouponAccountRecordDO> wrapper = Wrappers.<CashCouponAccountRecordDO>lambdaQuery()
                .eq(CashCouponAccountRecordDO::getUserId, userId)
                .eq(CashCouponAccountRecordDO::getStrategyId, strategyId)
                .eq(CashCouponAccountRecordDO::getBenefitId, benefitId)
                .eq(CashCouponAccountRecordDO::getOperateType, operateType)
                .eq(CashCouponAccountRecordDO::getFreezeReceiveLimitId, freezeId)
                .eq(CashCouponAccountRecordDO::getStatus, status)
                .orderByDesc(CashCouponAccountRecordDO::getCreateTime);
        return this.list(wrapper);
    }

    @Override
    public boolean updateAccountRecordFreezeId(String userId, Long id, Long freezeId) {
        CashCouponAccountRecordDO update = new CashCouponAccountRecordDO();
        update.setId(id);
        update.setFreezeReceiveLimitId(freezeId);
        update.setUserId(userId);
        return this.update(update, Wrappers.<CashCouponAccountRecordDO>lambdaUpdate()
                .eq(CashCouponAccountRecordDO::getUserId, userId)
                .eq(CashCouponAccountRecordDO::getId, id));
    }

    @Override
    public List<CashCouponAccountRecordDO> queryTicketPackageAccountRecords(String userId, List<Long> activityIds,
                                                                            List<String> ticketIds,
                                                                            List<Integer> operateTypes,
                                                                            List<Integer> statuses,
                                                                            Date startDate, Date endDate) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询权益子账户操作流水列表 userId 不能为空");

        Wrapper<CashCouponAccountRecordDO> wrapper = Wrappers.<CashCouponAccountRecordDO>lambdaQuery()
                .eq(CashCouponAccountRecordDO::getUserId, userId)
                .in(CollectionUtils.isNotEmpty(activityIds), CashCouponAccountRecordDO::getActivityId, activityIds)
                .in(CollectionUtils.isNotEmpty(ticketIds), CashCouponAccountRecordDO::getTicketId, ticketIds)
                .in(CollectionUtils.isNotEmpty(operateTypes), CashCouponAccountRecordDO::getOperateType, operateTypes)
                .in(CollectionUtils.isNotEmpty(statuses), CashCouponAccountRecordDO::getStatus, statuses)
                .ge(Objects.nonNull(startDate), CashCouponAccountRecordDO::getCreateTime, startDate)
                .le(Objects.nonNull(endDate), CashCouponAccountRecordDO::getCreateTime, endDate);
        return this.list(wrapper);
    }
}
