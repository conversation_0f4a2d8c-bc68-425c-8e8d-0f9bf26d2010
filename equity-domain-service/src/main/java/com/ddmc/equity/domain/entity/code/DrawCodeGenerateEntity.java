package com.ddmc.equity.domain.entity.code;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.util.DateUtil;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Random;
import java.util.Set;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/5 10:22
 * @description
 */
@Slf4j
public class DrawCodeGenerateEntity {

    private static final String LETTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    private static final String NUMBERS = "0123456789";
    private static final Random RANDOM = new Random();
    /**
     * 重试次数
     */
    private final static int RETRY_COUNT = 5;

    /**
     * 批量生成抽签码
     *
     * @return 抽签码列表
     */
    public static List<String> batchGenerateDrawCode(Integer count) {
        List<String> drawCodes = Lists.newArrayList();
        // 最少生成一个抽签码。如果 count 不指定，默认生成一个
        count = Math.max(ObjectUtils.defaultIfNull(count, 1), 1);
        for (int i = 0; i < count; i++) {
            generateDrawCodeAndSetResult(drawCodes);
        }
        return drawCodes;
    }

    /**
     * 生成抽签码，并加入到抽签码结果中
     *
     * @param drawCodes 抽签码结果
     */
    private static void generateDrawCodeAndSetResult(List<String> drawCodes) {
        String drawCode;
        int times = 0;
        do {
            // 防止重试死循环，超出次数强行中断，抛出异常
            if (times > RETRY_COUNT) {
                log.warn("generateDrawCodeAndSetResult retryOverLimit. drawCodes={}", JSON.toJSONString(drawCodes));
                throw AdminExceptionBuilder.build(ExceptionEnum.GENERATE_CODE_RETRY_OVER_LIMIT);
            }
            drawCode = generateDrawCode();
            times++;
        } while (drawCodes.contains(drawCode));
        drawCodes.add(drawCode);
    }

    /**
     * 生成抽签码
     *
     * @return 抽签码
     */
    private static String generateDrawCode() {
        // 3 个英文字母（不重复） + 1 个数字 + 3 个英文字母（不重复） + 1 个数字
        return generateLetters() + NUMBERS.charAt(RANDOM.nextInt(10)) + generateLetters() + NUMBERS.charAt(RANDOM.nextInt(10));
    }

    /**
     * 生成 3 个不重复的英文字母（大和小写分别为两个意义）
     *
     * @return 3 个不重复的英文字母
     */
    private static String generateLetters() {
        StringBuilder letters = new StringBuilder();
        for (int i = 0; i < 3; i++) {
            generateLetterAndSetResult(letters);
        }
        return letters.toString();
    }

    private static void generateLetterAndSetResult(StringBuilder letters) {
        char letter;
        int times = 0;
        do {
            // 防止重试死循环，超出次数强行中断，抛出异常
            if (times > RETRY_COUNT) {
                log.warn("generateLetterAndSetResult retryOverLimit. letters={}", letters);
                throw AdminExceptionBuilder.build(ExceptionEnum.GENERATE_CODE_RETRY_OVER_LIMIT);
            }
            letter = LETTERS.charAt(RANDOM.nextInt(LETTERS.length()));
            times++;
        } while (letters.toString().indexOf(letter) != -1);
        letters.append(letter);
    }

    /**
     * 批量生成抽签码
     *
     * @return 抽签码列表
     */
    public static List<String> batchGenerateDrawCodesV1(Integer count) {
        // 最少生成一个抽签码。如果 count 不指定，默认生成一个
        count = Math.max(ObjectUtils.defaultIfNull(count, 1), 1);
        List<String> drawCodes = Lists.newArrayList();
        for (int i = 0; i < count; i++) {
            batchGenerateDrawCodesV1(drawCodes);
        }
        return drawCodes;
    }

    /**
     * 生成抽签码，并加入到抽签码结果中
     *
     * @param drawCodes 抽签码结果
     */
    private static void batchGenerateDrawCodesV1(List<String> drawCodes) {
        String drawCode;
        int times = 0;
        do {
            // 防止重试死循环，超出次数强行中断，抛出异常
            if (times > RETRY_COUNT) {
                log.warn("batchGenerateDrawCodesV1 retryOverLimit drawCodes={}", JSON.toJSONString(drawCodes));
                throw AdminExceptionBuilder.build(ExceptionEnum.GENERATE_CODE_RETRY_OVER_LIMIT);
            }
            drawCode = generateDrawCodeV1();
            times++;
        } while (containsIgnoreCase(drawCodes, drawCode));
        drawCodes.add(drawCode);
    }

    private static boolean containsIgnoreCase(List<String> list, String str) {
        return CollectionUtils.isNotEmpty(list) && list.stream().anyMatch(s -> StringUtils.equalsIgnoreCase(s, str));
    }

    /**
     * 生成抽签码
     *
     * @return 抽签码
     */
    private static String generateDrawCodeV1() {
        // 6 个英文字母 + 4 个数字（年份+月份）随机组合，但是字母和数字的顺序不变
        int lLength = 6;
        String letters = generateNLetters(lLength);
        String numbers = DateUtil.getYYMm();
        return generateCombination(letters, numbers);
    }

    /**
     * 生成 n 个英文字母（大和小写分别为两个意义）
     *
     * @return 生成 n 个英文字母
     */
    private static String generateNLetters(int n) {
        StringBuilder letters = new StringBuilder();
        for (int i = 0; i < n; i++) {
            letters.append(randomLetter());
        }
        return letters.toString();
    }

    private static char randomLetter() {
        return LETTERS.charAt(RANDOM.nextInt(LETTERS.length()));
    }

    private static String generateCombination(String letters, String numbers) {
        StringBuilder result = new StringBuilder();
        int lIndex = 0, nIndex = 0;
        for (int i = 0; i < letters.length() + numbers.length(); i++) {
            // Decide whether to take a letter or a number
            if (lIndex < letters.length() && (RANDOM.nextBoolean() || nIndex >= numbers.length())) {
                result.append(letters.charAt(lIndex++));
            } else if (nIndex < numbers.length()) {
                result.append(numbers.charAt(nIndex++));
            }
        }
        return result.toString();
    }

    public static void main(String[] args) {
        List<String> drawCodes = batchGenerateDrawCodesV1(10);
        System.out.println("drawCodes.size=" + drawCodes.size());

        String letters = generateNLetters(6);
        String numbers = DateUtil.getYYMm();
        Set<String> results = Sets.newHashSet();
        for (int i = 0; i < 10000; i++) {
            results.add(generateCombination(letters, numbers));
        }
        System.out.println("results.size=" + results.size());
    }
}
