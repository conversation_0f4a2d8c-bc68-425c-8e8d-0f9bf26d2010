package com.ddmc.equity.domain.dto;
import lombok.Data;

import java.util.List;

@Data
public class ReceiveSceneBenefitResultDTO {
    /**
     * 场景
     */
    private String sceneCode;
    /**
     * 领取成功的权益
     */
    private List<ReceiveBenefitDTO> receiveSuccessBenefitDTOS;
    /**
     * 领取失败的权益
     */
    private List<ReceiveBenefitDTO> receiveFailBenefitDTOS;
    /**
     * 处理中的权益
     */
    private List<ReceiveBenefitDTO> receiveProcessBenefitDTOS;
}
