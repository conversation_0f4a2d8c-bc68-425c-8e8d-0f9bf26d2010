package com.ddmc.equity.domain.service.benefit_group;

import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitGroupRuleDO;

import java.util.List;

public interface EquityBenefitGroupRuleDomainService {

    /**
     * 保存
     *
     * @param needSaveDO
     */
    void save(EquityBenefitGroupRuleDO needSaveDO);

    /**
     * 新建
     *
     * @param equityBenefitGroupRuleDO
     */
    void insert(EquityBenefitGroupRuleDO equityBenefitGroupRuleDO);


    /**
     * 批量新建
     *
     * @param list
     */
    void batchInsertOrUpdate(List<EquityBenefitGroupRuleDO> list);

    List<EquityBenefitGroupRuleDO> batchInsert(List<EquityBenefitGroupRuleDO> equityBenefitGroupRuleDOList);

    List<EquityBenefitGroupRuleDO> batchUpdate(List<EquityBenefitGroupRuleDO> equityBenefitGroupRuleDOList);

    /**
     * 通过策略 id、权益组 id、权益 id 查询权益组规则列表
     *
     * @param strategyId     策略 id
     * @param benefitGroupId 权益组 id
     * @param benefitId      权益 id
     * @return 权益组规则列表
     */
    List<EquityBenefitGroupRuleDO> queryByBenefitGroupIdAndBenefitId(Long strategyId, Long benefitGroupId, Long benefitId);

    List<EquityBenefitGroupRuleDO> queryByBenefitGroupRuleByStrategyIdAndBenefitGroupId(Long strategyId, Long benefitGroupId);

    /**
     * 通过权益组 ids 查询权益组规则列表
     *
     * @param benefitGroupIds 权益组 ids
     * @return 权益组规则列表
     */
    List<EquityBenefitGroupRuleDO> queryByBenefitGroupIds(List<Long> benefitGroupIds);


    /****
     * 根据策略id和规则类型获取规则信息
     * @param strategyId 策略id
     * @param ruleType 规则类型
     * @return 所有规则
     */
    List<EquityBenefitGroupRuleDO> queryEquityBenefitGroupRuleDOListByStrategyIdAndRuleType(Long strategyId, Integer ruleType);


    void batchDelete(List<EquityBenefitGroupRuleDO> equityBenefitGroupRuleDOList);

    /**
     * 通过 strategyId + benefitId 查询库存规则
     *
     * @param strategyId 策略 id
     * @param benefitId  权益 id
     * @return 库存规则
     */
    StockLimitRuleDTO getBenefitStockLimitRule(Long strategyId, Long benefitId);
}
