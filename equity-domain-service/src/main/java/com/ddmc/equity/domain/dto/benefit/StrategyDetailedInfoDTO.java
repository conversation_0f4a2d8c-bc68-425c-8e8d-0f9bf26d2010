package com.ddmc.equity.domain.dto.benefit;

import com.ddmc.equity.domain.dto.rule.UserTagInfoSaveDTO;
import com.ddmc.vouchercore.client.dto.coupon.CouponTicketDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class StrategyDetailedInfoDTO {
    /**
     * 策略id
     */
    @ApiModelProperty("策略ID")
    private Long strategyId;

    /**
     * 权益组ID
     */
    @ApiModelProperty("权益组ID")
    private Long benefitGroupId;

    /**
     * 名称
     */
    @ApiModelProperty("策略名称-主券的名称")
    private String name;

    /**
     * 城市
     */
    @ApiModelProperty("城市Id列表")
    private List<String> cityIds;

    /**
     * 权益列表
     */
    @ApiModelProperty("权益列表")
    private List<BenefitDetailedInfoDTO> benefits;

    /**
     * 券
     */
    @ApiModelProperty("券对象")
    private CouponTicketDTO ticket;

    /**
     * 权益信息
     */
    @Data
    public static class BenefitDetailedInfoDTO {
        /**
         * 权益ID
         */
        @ApiModelProperty("权益ID")
        private Long benefitId;

        /**
         * 名称
         */
        @ApiModelProperty("权益名称-子券名称")
        private String name;

        /**
         * 库存
         */
        @ApiModelProperty("券的库存")
        private Integer stock;

        /**
         * 顺序
         */
        @ApiModelProperty("券的顺序")
        private Integer sort;


        /**
         * 开启状态
         */
        @ApiModelProperty("开启状态，true开启，false关闭")
        private Boolean enable;

        /**
         * 券ID
         */
        @ApiModelProperty("权益ID-券的ID")
        private String benefitValue;

        /**
         * 城市
         */
        @ApiModelProperty("城市列表")
        private List<String> cityIds;

        @ApiModelProperty("人群标签列表")
        private List<UserTagInfoSaveDTO> userTags;

        /**
         * 券
         */
        @ApiModelProperty("券的对象")
        private CouponTicketDTO ticket;
    }
}
