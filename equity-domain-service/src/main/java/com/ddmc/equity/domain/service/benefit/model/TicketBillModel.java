package com.ddmc.equity.domain.service.benefit.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TicketBillModel {
    /**
     * 活动id
     */
    private String activityId;
    /**
     * 奖品id
     */
    private String prizeId;
    /**
     * 优惠券id
     */
    private String ticketId;
    /**
     * 类型：领取、核销
     */
    private String type;
    /**
     * 统计时间类型
     */
    private String timeUnit;
    /**
     * 统计时间具体值
     */
    private String timeValue;
    /**
     * 次数
     */
    private Long count;
}
