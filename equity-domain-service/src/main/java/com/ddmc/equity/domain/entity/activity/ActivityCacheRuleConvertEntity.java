package com.ddmc.equity.domain.entity.activity;

import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.common.util.MapUtils;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.UserTagRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.model.convert.GeneralRuleCacheConvert;
import com.ddmc.equity.model.dto.ActivityRuleCacheDTO;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import com.ddmc.equity.model.dto.BenefitRuleCacheDTO;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import com.ddmc.equity.model.dto.StrategyRuleCacheDTO;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.util.Pair;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/19 19:51
 * @description
 */
public class ActivityCacheRuleConvertEntity {

    public static ListMultimap<Pair<Integer, Integer>, GeneralRuleCacheDTO> getGeneralRulesMap(List<SceneActivityCacheDto> activities) {
        ListMultimap<Pair<Integer /* ruleDimension */, Integer /* ruleType */>, GeneralRuleCacheDTO> generalRulesMap = ArrayListMultimap.create();
        fillGeneralRulesMapByActivity(generalRulesMap, activities);
        return generalRulesMap;
    }

    private static void fillGeneralRulesMapByActivity(ListMultimap<Pair<Integer, Integer>, GeneralRuleCacheDTO> generalRulesMap,
                                                      List<SceneActivityCacheDto> activities) {
        if (CollectionUtils.isEmpty(activities)) {
            return;
        }
        activities.forEach(activity -> {
            // 设置活动规则列表
            fillActivityRules(generalRulesMap, activity.getRuleCacheDTOList());
            fillGeneralRulesMapByStrategy(generalRulesMap, activity.getActivityId(), activity.getStrategyCacheDtoList());
        });
    }

    private static void fillActivityRules(ListMultimap<Pair<Integer, Integer>, GeneralRuleCacheDTO> generalRulesMap,
                                          List<ActivityRuleCacheDTO> ruleCacheDTOList) {
        if (CollectionUtils.isEmpty(ruleCacheDTOList)) {
            return;
        }
        ruleCacheDTOList.forEach(rule -> {
            GeneralRuleCacheDTO generalRuleCacheDTO = GeneralRuleCacheConvert.INSTANCE.convertToActivityGeneralRuleCacheDTO(rule);
            generalRulesMap.put(getRuleKey(rule, RuleDimensionEnum.ACTIVITY), generalRuleCacheDTO);
        });
    }

    private static void fillGeneralRulesMapByStrategy(ListMultimap<Pair<Integer, Integer>, GeneralRuleCacheDTO> generalRulesMap,
                                                      Long activityId, List<StrategyCacheDto> strategies) {
        if (CollectionUtils.isEmpty(strategies)) {
            return;
        }
        strategies.forEach(strategy -> {
            // 设置策略规则列表
            fillStrategyRules(generalRulesMap, activityId, strategy.getRuleCacheDTOList());
            Long strategyId = strategy.getStrategyId();
            fillGeneralRulesMapByBenefit(generalRulesMap, activityId, strategyId, strategy.getStrategyBenefitGroup().get(strategyId));
        });
    }

    private static void fillStrategyRules(ListMultimap<Pair<Integer, Integer>, GeneralRuleCacheDTO> generalRulesMap,
                                          Long activityId, List<StrategyRuleCacheDTO> ruleCacheDTOList) {
        if (CollectionUtils.isEmpty(ruleCacheDTOList)) {
            return;
        }
        ruleCacheDTOList.forEach(rule -> {
            GeneralRuleCacheDTO generalRuleCacheDTO = GeneralRuleCacheConvert.INSTANCE.convertToStrategyGeneralRuleCacheDTO(rule);
            generalRuleCacheDTO.setActivityId(activityId);
            generalRulesMap.put(getRuleKey(rule, RuleDimensionEnum.STRATEGY), generalRuleCacheDTO);
        });
    }

    private static void fillGeneralRulesMapByBenefit(ListMultimap<Pair<Integer, Integer>, GeneralRuleCacheDTO> generalRulesMap,
                                                     Long activityId, Long strategyId, List<BenefitIdWithConfDto> benefits) {
        if (CollectionUtils.isEmpty(benefits)) {
            return;
        }
        // 设置权益规则列表
        List<BenefitRuleCacheDTO> allBenefitRules = benefits.stream().map(BenefitIdWithConfDto::getRuleCacheDTOList)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
        fillBenefitRules(generalRulesMap, activityId, strategyId, allBenefitRules);
    }

    private static void fillBenefitRules(ListMultimap<Pair<Integer, Integer>, GeneralRuleCacheDTO> generalRulesMap,
                                         Long activityId, Long strategyId, List<BenefitRuleCacheDTO> ruleCacheDTOList) {
        if (CollectionUtils.isEmpty(ruleCacheDTOList)) {
            return;
        }
        ruleCacheDTOList.forEach(rule -> {
            GeneralRuleCacheDTO generalRuleCacheDTO = GeneralRuleCacheConvert.INSTANCE.convertToBenefitGeneralRuleCacheDTO(rule);
            generalRuleCacheDTO.setActivityId(activityId);
            generalRuleCacheDTO.setStrategyId(strategyId);
            generalRulesMap.put(getRuleKey(rule, RuleDimensionEnum.BENEFIT), generalRuleCacheDTO);
        });
    }

    public static Map<Pair<Integer, Integer>, ActivityRuleCacheDTO> getActivityRulesMap(List<ActivityRuleCacheDTO> ruleCacheDTOList) {
        Map<Pair<Integer /* ruleDimension */, Integer /* ruleType */>, ActivityRuleCacheDTO> rulesMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(ruleCacheDTOList)) {
            return rulesMap;
        }
        return ruleCacheDTOList.stream().collect(
                Collectors.toMap(rule -> getRuleKey(rule, RuleDimensionEnum.ACTIVITY), Function.identity(), (v1, v2) -> v1)
        );
    }

    public static Map<Pair<Integer, Integer>, StrategyRuleCacheDTO> getStrategyRulesMap(List<StrategyRuleCacheDTO> ruleCacheDTOList) {
        Map<Pair<Integer /* ruleDimension */, Integer /* ruleType */>, StrategyRuleCacheDTO> rulesMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(ruleCacheDTOList)) {
            return rulesMap;
        }
        return ruleCacheDTOList.stream().collect(
                Collectors.toMap(rule -> getRuleKey(rule, RuleDimensionEnum.STRATEGY), Function.identity(), (v1, v2) -> v1)
        );
    }

    public static Map<Pair<Integer, Integer>, BenefitRuleCacheDTO> getBenefitRulesMap(List<BenefitRuleCacheDTO> ruleCacheDTOList) {
        Map<Pair<Integer /* ruleDimension */, Integer /* ruleType */>, BenefitRuleCacheDTO> rulesMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(ruleCacheDTOList)) {
            return rulesMap;
        }
        return ruleCacheDTOList.stream().collect(
                Collectors.toMap(rule -> getRuleKey(rule, RuleDimensionEnum.BENEFIT), Function.identity(), (v1, v2) -> v1)
        );
    }

    private static Pair<Integer, Integer> getRuleKey(RuleCacheDTO rule, RuleDimensionEnum ruleDimensionEnum) {
        Integer ruleType = rule.getRuleType();
        if (RuleTypeEnum.hasDimension(ruleType)) {
            return Pair.of(ruleDimensionEnum.getCode(), ruleType);
        }
        return Pair.of(RuleDimensionEnum.COMMON.getCode(), ruleType);
    }

    /**
     * 规则列表中是否有需要区分维度的规则（活动维度、策略维度、权益维度）
     *
     * @param ruleList 规则列表
     * @return true-有需要区分维度的规则；
     */
    public static boolean hasDimensionRule(List<? extends RuleCacheDTO> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return false;
        }
        return ruleList.stream().anyMatch(rule -> RuleTypeEnum.hasDimension(rule.getRuleType()));
    }

    public static List<StrategyRuleCacheDTO> getStrategyRules(List<SceneActivityCacheDto> activityList) {
        if (CollectionUtils.isEmpty(activityList)) {
            return null;
        }
        return activityList.stream().map(ActivityCacheRuleConvertEntity::getStrategyRules)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private static List<StrategyRuleCacheDTO> getStrategyRules(SceneActivityCacheDto activity) {
        List<StrategyCacheDto> strategyCacheDtoList = activity.getStrategyCacheDtoList();
        if (CollectionUtils.isEmpty(strategyCacheDtoList)) {
            return null;
        }
        return strategyCacheDtoList.stream().map(StrategyCacheDto::getRuleCacheDTOList)
                .filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public static StockLimitRuleDTO getStockLimitRuleDTO(List<? extends RuleCacheDTO> ruleCacheDTOList) {
        if (CollectionUtils.isEmpty(ruleCacheDTOList)) {
            return null;
        }
        RuleCacheDTO ruleCacheDTO = ruleCacheDTOList.stream()
                .filter(rule -> Objects.equals(RuleTypeEnum.STOCK_LIMIT_RULE.getCode(), rule.getRuleType()))
                .findFirst().orElse(null);
        if (Objects.isNull(ruleCacheDTO)) {
            return null;
        }
        return RuleConvertEntity.convertToFormatRuleDTO(ruleCacheDTO, StockLimitRuleDTO.class);
    }

    public static List<GeneralRuleCacheDTO> getGeneralRules(List<SceneActivityCacheDto> sceneActivityDTOList,
                                                            Pair<Integer, Integer> key) {
        if (Objects.isNull(key)) {
            return null;
        }
        ListMultimap<Pair<Integer /* ruleDimension */, Integer /* ruleType */>, GeneralRuleCacheDTO> generalRulesMap =
                getGeneralRulesMap(sceneActivityDTOList);
        return MapUtils.isEmpty(generalRulesMap) ? null : generalRulesMap.get(key);
    }

    public static List<String> getTagRuleIds(List<SceneActivityCacheDto> sceneActivityDTOList) {
        Pair<Integer, Integer> key = Pair.of(RuleDimensionEnum.COMMON.getCode(), RuleTypeEnum.USER_TAG_RULE.getCode());
        List<GeneralRuleCacheDTO> generalRules = getGeneralRules(sceneActivityDTOList, key);
        if (CollectionUtils.isEmpty(generalRules)) {
            return null;
        }
        return generalRules.stream()
                .map(e -> RuleConvertEntity.convertToFormatRuleDTO(e, UserTagRuleDTO.class))
                .filter(Objects::nonNull)
                .map(UserTagRuleDTO::getRuleIds)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    public static List<Map.Entry<Pair<Integer /* ruleDimension */, Integer /* ruleType */>, ? extends RuleCacheDTO>> getSortRuleEntries(
            Map<Pair<Integer /* ruleDimension */, Integer /* ruleType */>, ? extends RuleCacheDTO> rulesMap) {
        if (MapUtils.isEmpty(rulesMap)) {
            return Lists.newArrayList();
        }
        // 按照 ruleType 的 sortNo 升序
        List<Map.Entry<Pair<Integer, Integer>, ? extends RuleCacheDTO>> ruleEntries = Lists.newArrayList(rulesMap.entrySet());
        ruleEntries.sort(Comparator.comparing(e -> getSortNo(e.getKey())));
        return ruleEntries;
    }

    private static Integer getSortNo(Pair<Integer /* ruleDimension */, Integer /* ruleType */> key) {
        return Optional.of(key.getSecond())
                .map(RuleTypeEnum::getRuleTypeEnumByRuleType)
                .map(RuleTypeEnum::getSortNo)
                .orElse(0);
    }
}
