package com.ddmc.equity.domain.service.equityAccount;

import com.ddmc.equity.domain.dto.account_deduct.DeductOperateAccountDTO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDO;

import java.util.List;

public interface UniversalAccountDomainService {

    void addOrUpdate(UniversalAccountDO universalAccountDO);

    /**
     * 查询用户权益通用账户列表
     *
     * @param userId         用户 id
     * @param useActivityIds 使用时的活动 id。不指定则查询所有
     * @param benefitTypes   权益类型。不指定则查询所有
     * @return 用户权益通用账户列表
     */
    List<UniversalAccountDO> queryUniversalAccounts(String userId, List<Long> useActivityIds, List<Integer> benefitTypes);

    List<UniversalAccountDO> queryAvailableUniversalAccounts(String userId, List<Long> useActivityIds, Integer benefitType);

    int deductAvailableCount(String userId, Long id, Integer deductCount, Integer version);

    UniversalAccountDO queryById(String userId, Long id);

    /**
     * 通过 userId + accountIds 查询权益账户列表
     *
     * @param userId     用户 id
     * @param accountIds 账户 ids
     * @return 权益账户列表
     */
    List<UniversalAccountDO> queryAccounts(String userId, List<Long> accountIds);

    /**
     * 查询扣减可以操作的账户列表
     *
     * @param userId         用户 id，必传
     * @param useActivityIds 使用活动 id 列表，必传。如果扣减账户时未指定使用活动 id，则 useActivityIds in (0)
     * @param benefitType    权益类型，必传。指定扣减哪种类型的权益账户
     * @return 扣减可以操作的账户列表
     */
    List<DeductOperateAccountDTO> fetchDeductOperateAccounts(String userId, List<Long> useActivityIds,
                                                             Integer benefitType);

    /**
     * 扣减权益账户可用数量 availableCount -= operateCount
     *
     * @param userId       用户 id
     * @param accountId    账户 id
     * @param operateCount 操作扣减数量
     * @param version      版本号
     * @return 是否扣减成功
     */
    boolean deductAccountCount(String userId, Long accountId, Integer operateCount, Integer version);

    /**
     * 过期权益账户可用数量  expireCount += operateCount、availableCount -= operateCount
     *
     * @param userId       用户 id
     * @param accountId    账户 id
     * @param operateCount 操作过期数量
     * @param version      版本号
     * @return 是否过期成功
     */
    boolean expireAccountCount(String userId, Long accountId, Integer operateCount, Integer version);
}
