package com.ddmc.equity.domain.dto.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/7/18 15:29
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("账户操作记录对应操作的账户明细")
public class OperateAccountDetailDTO {

    @ApiModelProperty("账户明细 id")
    private Long accountDetailId;

    @ApiModelProperty("操作数量")
    private Integer operateCount;
}
