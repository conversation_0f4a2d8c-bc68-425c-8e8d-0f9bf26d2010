package com.ddmc.equity.domain.service.core.impl.extra;

import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.common.util.ThreadsUtils;
import com.ddmc.equity.domain.service.core.ThirdBenefitHandler;
import com.ddmc.equity.dto.customer.*;
import com.ddmc.equity.infra.rpc.vip.EquityUserVipProxy;
import com.ddmc.vip.app.request.ticket.ExclusiveTicketReceiveRequestDTO;
import com.ddmc.vip.app.response.ticket.FixedExclusiveTicketDTO;
import com.ddmc.vip.app.response.ticket.ReceiveExclusiveTicketResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class VipExclusiveCouponBenefitHandler implements ThirdBenefitHandler {
    @Resource
    private EquityUserVipProxy equityUserVipProxy;

    public static final Long VIRTUAL_BENEFIT_ID = 0L;

    @Value("${need.extra.vip.benefit.maxTimeoutMS:2000}")
    private Integer needExtraVipMaxTimeoutMS;

    @Override
    public @NotNull Set<String> getSceneCode() {
        return Sets.newHashSet(Lists.newArrayList("RECEIVE_TICKET_ACTIVITY"));
    }

    @Override
    public @NotNull List<ReceiveBenefitReqDTO> getThirdReqList(ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO) {
        List<ReceiveBenefitReqDTO> thirdReqList = Lists.newArrayList();
        for (ReceiveBenefitReqDTO receiveBenefitReqDTO : receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS()) {
            if (VIRTUAL_BENEFIT_ID.equals(receiveBenefitReqDTO.getActivityId())
                    && StringUtils.isNotBlank(receiveBenefitReqDTO.getThirdBenefitId())) {
                thirdReqList.add(receiveBenefitReqDTO);
            }
        }
        return thirdReqList;
    }

    @NotNull
    @Override
    public Pair<List<BenefitDTO> /* success */,
            List<BenefitDTO> /* fail */> receive(String userId, List<ReceiveBenefitReqDTO> receiveBenefitReqDTOS) {
        List<BenefitDTO> success = Lists.newArrayList();
        List<BenefitDTO> fail = Lists.newArrayList();
        // 防止重复查询
        Map<String /* thirdBenefitId */, Boolean> hasQuery = Maps.newHashMap();
        Map<Future<ReceiveExclusiveTicketResponse>, BenefitDTO> futures = Maps.newHashMap();
        for (ReceiveBenefitReqDTO thirdReceiveReqDTO : receiveBenefitReqDTOS) {
            if (VIRTUAL_BENEFIT_ID.equals(thirdReceiveReqDTO.getActivityId())
                    && StringUtils.isNotBlank(thirdReceiveReqDTO.getThirdBenefitId())
                    && !hasQuery.containsKey(thirdReceiveReqDTO.getThirdBenefitId())) {
                hasQuery.put(thirdReceiveReqDTO.getThirdBenefitId(), true);
                ExclusiveTicketReceiveRequestDTO req = new ExclusiveTicketReceiveRequestDTO();
                req.setUid(userId);
                req.setId(thirdReceiveReqDTO.getThirdBenefitId());
                Future<ReceiveExclusiveTicketResponse> rawRespFuture = ThreadsUtils.getCoreThreadPoll().submit(() ->
                        equityUserVipProxy.receiveTicket(req));
                BenefitDTO benefitDTO = new BenefitDTO();
                benefitDTO.setActivityId(thirdReceiveReqDTO.getActivityId());
                benefitDTO.setExternalId(Strings.EMPTY);
                benefitDTO.setBenefitType(BenefitTypeEnum.TEMP_VIP_TICKET.getId());
                benefitDTO.setBenefitName(Strings.EMPTY);
                benefitDTO.setBenefitId(thirdReceiveReqDTO.getBenefitId());
                benefitDTO.setThirdBenefitId(thirdReceiveReqDTO.getThirdBenefitId());
                benefitDTO.setBenefitValue(Strings.EMPTY); //领取的时候不需要ticket_id @卢永党 20230224 15:21
                benefitDTO.setThirdResNo(Strings.EMPTY);
                futures.put(rawRespFuture, benefitDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(futures.keySet())) {
            for (Future<ReceiveExclusiveTicketResponse> future : futures.keySet()) {
                ReceiveExclusiveTicketResponse rawResp = null;
                try {
                    rawResp = future.get(needExtraVipMaxTimeoutMS, TimeUnit.MILLISECONDS);
                } catch (InterruptedException e) {
                    log.error("中断异常 可能发放的权益信息 BenefitDTO:{}", futures.get(future), e);
                } catch (Exception e) {
                    log.error("执行异常 可能发放的权益信息 BenefitDTO:{}", futures.get(future), e);
                }
                if (null != rawResp && rawResp.isReceiveSuccess()) {
                    futures.get(future).setThirdResNo(rawResp.getUserTicketId());
                    futures.get(future).setBenefitValue(rawResp.getTicketId());
                    // 20230314 要新增流水号透传返回 @卢永党
                    futures.get(future).setThirdSerialNumber(rawResp.getIdempotentNumber());
                    success.add(futures.get(future));
                } else {
                    log.error("发放权益异常 rawResp:{} 可能发放的权益信息 BenefitDTO:{}", rawResp, futures.get(future));
                    fail.add(futures.get(future));
                }
            }
        }
        return Pair.of(success, fail);
    }

    @Override
    public SceneActivityDTO getBenefitActivity(ConsultSceneBenefitReqDTO consultSceneBenefitReqDTO) {
        List<FixedExclusiveTicketDTO> fixedExclusiveTicketDTOList =
                equityUserVipProxy.listExclusiveTicket(consultSceneBenefitReqDTO.getBaseRequestDTO().getUserId(),
                        consultSceneBenefitReqDTO.getBaseRequestDTO().getCityNumber());
        if (CollectionUtils.isEmpty(fixedExclusiveTicketDTOList)) {
            return null;
        }
        // 要过滤掉已领取的，咨询不展示已领取的 11:06
        fixedExclusiveTicketDTOList = fixedExclusiveTicketDTOList.stream()
                .filter(v -> CommonEnum.INTEGER_BOOL.NO.getCode().equals(v.getIsReceive()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fixedExclusiveTicketDTOList)) {
            return null;
        }

        // @陈振飞 领取活动已领取的券目前在玩法都存， 会员专享券也要存

        SceneActivityDTO activityDTO = new SceneActivityDTO();
        activityDTO.setActivityId(VIRTUAL_BENEFIT_ID);// 会员专享券活动
        activityDTO.setExternalId(Strings.EMPTY);
        activityDTO.setStartTime(DateUtil.parse_yyyyMMddAcross(Constants.NEVER_EXPIRED_START_TIME));
        activityDTO.setEndTime(DateUtil.parse_yyyyMMddAcross(Constants.NEVER_EXPIRED_END_TIME));
        ActivityStrategyDTO strategyDTO = new ActivityStrategyDTO();
        strategyDTO.setStrategyId(VIRTUAL_BENEFIT_ID); // 会员专享券策略
        List<BenefitDTO> benefitDTOList = Lists.newArrayList();
        for (FixedExclusiveTicketDTO vipTicket : fixedExclusiveTicketDTOList) {
            BenefitDTO benefitDTO = new BenefitDTO();
            benefitDTO.setBenefitId(VIRTUAL_BENEFIT_ID);
            benefitDTO.setThirdBenefitId(vipTicket.getId());
            benefitDTO.setActivityId(activityDTO.getActivityId());
            benefitDTO.setExternalId(Strings.EMPTY);
            benefitDTO.setBenefitName(vipTicket.getName());
            benefitDTO.setBenefitValue(vipTicket.getTicketId());
            benefitDTO.setBenefitType(BenefitTypeEnum.TEMP_VIP_TICKET.getId());
            benefitDTO.setThirdResNo(Strings.EMPTY);
            benefitDTOList.add(benefitDTO);
        }
        strategyDTO.setBenefitDTOList(benefitDTOList);
        List<ActivityStrategyDTO> strategyDTOList = Lists.newArrayList(strategyDTO);
        activityDTO.setStrategyDTOList(strategyDTOList);

        return activityDTO;
    }
}
