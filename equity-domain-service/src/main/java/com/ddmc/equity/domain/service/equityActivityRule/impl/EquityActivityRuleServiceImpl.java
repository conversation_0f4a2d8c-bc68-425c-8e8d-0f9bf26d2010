package com.ddmc.equity.domain.service.equityActivityRule.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.service.equityActivityRule.EquityActivityRuleService;
import com.ddmc.equity.infra.repository.dao.EquityActivityRuleDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityActivityRuleMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EquityActivityRuleServiceImpl implements EquityActivityRuleService {

    @Resource
    private EquityActivityRuleMapper equityActivityRuleMapper;

    @Override
    public List<EquityActivityRuleDO> queryEquityActivityRuleByActivityId(Long activityId) {
        QueryWrapper<EquityActivityRuleDO> queryWrapper = new QueryWrapper();
        queryWrapper.eq("activity_id", activityId);
        return equityActivityRuleMapper.selectList(queryWrapper);
    }

    @Override
    public List<EquityActivityRuleDO> queryByActivityIds(List<Long> activityIds) {
        if (CollectionUtils.isEmpty(activityIds)) {
            return null;
        }
        LambdaQueryWrapper<EquityActivityRuleDO> wrapper = Wrappers.<EquityActivityRuleDO>lambdaQuery()
                .in(EquityActivityRuleDO::getActivityId, activityIds);
        return equityActivityRuleMapper.selectList(wrapper);
    }

    @Override
    public int addEquityActivityRuleDOList(List<EquityActivityRuleDO> equityActivityRuleDOList) {
        if (CollectionUtils.isEmpty(equityActivityRuleDOList)) {
            return 0;
        }
        return equityActivityRuleMapper.insertBatchSomeColumn(equityActivityRuleDOList);
    }

    @Override
    public int updateEquityActivityRuleDOList(List<EquityActivityRuleDO> equityActivityRuleDOList) {
        if (CollectionUtils.isEmpty(equityActivityRuleDOList)) {
            return 0;
        }
        return equityActivityRuleMapper.updateBatch(equityActivityRuleDOList);
    }

    /**
     * 查询活动指定规则
     *
     * @param activityId
     * @param type
     * @return
     */
    public EquityActivityRuleDO getActivityRule(Long activityId, RuleTypeEnum type) {
        QueryWrapper<EquityActivityRuleDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("activity_id", activityId);
        queryWrapper.eq("rule_type", type.getCode());
        return equityActivityRuleMapper.selectOne(queryWrapper);
    }

    @Override
    public List<EquityActivityRuleDO> getActivityRule(Long activityId, List<RuleTypeEnum> types) {
        QueryWrapper<EquityActivityRuleDO> queryWrapper = new QueryWrapper<>();
        List<Integer> typeList = types.stream().map(RuleTypeEnum::getCode).collect(Collectors.toList());
        queryWrapper.eq("activity_id", activityId);
        queryWrapper.in("rule_type", typeList);
        return equityActivityRuleMapper.selectList(queryWrapper);
    }

    @Override
    public void batchDelete(List<EquityActivityRuleDO> equityActivityRuleDOList) {
        if (CollectionUtils.isEmpty(equityActivityRuleDOList)) {
            return;
        }
        for (EquityActivityRuleDO ruleDO : equityActivityRuleDOList) {
            UpdateWrapper<EquityActivityRuleDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", ruleDO.getId()).eq("is_delete", 0).set("is_delete", 1).set("aux_key", ruleDO.getId());
            ruleDO.setIsDelete(1);
            ruleDO.setAuxKey(ruleDO.getId());
            equityActivityRuleMapper.update(ruleDO, updateWrapper);

        }
    }


}
