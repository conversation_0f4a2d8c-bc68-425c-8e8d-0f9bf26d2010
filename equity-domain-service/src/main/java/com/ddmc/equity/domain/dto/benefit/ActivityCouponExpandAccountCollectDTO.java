package com.ddmc.equity.domain.dto.benefit;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

/**
 * 明细数据
 */
@Data
public class ActivityCouponExpandAccountCollectDTO {
    /**
     * 明细ID
     */
    @ExcelProperty(value = "明细ID", index = 0)
    private Long id;
    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID", index = 1)
    private String userId;
    /**
     * 用户手机号码
     */
    @ExcelProperty(value = "手机号码", index = 2)
    private String phone;
    /**
     * 主券ID
     */
    @ExcelProperty(value = "主券ID", index = 3)
    private String masterTicketId;
    /**
     * 主券名称
     */
    @ExcelProperty(value = "主券名称", index = 4)
    private String masterTicketName;
    /**
     * 膨胀券ID
     */
    @ExcelProperty(value = "膨胀券ID", index = 5)
    private String expandTicketId;
    /**
     * 膨胀券名称
     */
    @ExcelProperty(value = "膨胀券名称", index = 6)
    private String expandTicketName;
    /**
     * 创建时间
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "膨胀时间", index = 7)
    private Date createTime;
}
