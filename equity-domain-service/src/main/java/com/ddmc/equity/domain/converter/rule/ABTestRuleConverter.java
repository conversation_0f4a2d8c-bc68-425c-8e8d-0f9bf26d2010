package com.ddmc.equity.domain.converter.rule;

import com.ddmc.equity.domain.dto.rule.condition.ABTestRuleDTO;
import com.ddmc.equity.domain.valueobject.rule.condition.AbTestRuleVO;
import com.ddmc.equity.dto.business.rule.ABTestDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface ABTestRuleConverter {
    ABTestRuleConverter INSTANCE = Mappers.getMapper(ABTestRuleConverter.class);

    AbTestRuleVO convertDTOToVO(ABTestDTO abTestDTO);

    ABTestRuleDTO convertDTOToRuleDTO(ABTestDTO abTestDTO);
}
