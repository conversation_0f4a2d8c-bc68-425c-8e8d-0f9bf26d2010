package com.ddmc.equity.domain.service.benefitMapping;

import com.ddmc.equity.dto.business.provide.ProvideBenefitDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitMappingDO;

import java.util.List;

public interface BenefitMappingService {

    List<EquityBenefitMappingDO> queryAllBenefitMapping();

    EquityBenefitMappingDO queryAllBenefitMappingByMappingId(String mappingId,Long strategyId);

    EquityBenefitMappingDO queryAllBenefitMappingByBenefitIdAndStrategyId(Long benefitId,Long strategyId);

    int addMapping(List<EquityBenefitDO> needCreateBenefitList, Long strategyId, List<ProvideBenefitDTO> benefitDTOList);
}
