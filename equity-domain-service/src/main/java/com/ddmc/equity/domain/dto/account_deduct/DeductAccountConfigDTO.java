package com.ddmc.equity.domain.dto.account_deduct;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/7/18 16:31
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class DeductAccountConfigDTO {

    @ApiModelProperty(value = "查询账户明细每页执行数量限制")
    private Integer fetchPageLimit;

    @ApiModelProperty(value = "查询账户明细最大循环次数。如果超过 maxLoopTimes，则不再继续查询，扣减只进行总账户扣减")
    private Integer fetchMaxLoopTimes;

    @ApiModelProperty(value = "更新账户明细每页执行数量限制")
    private Integer updatePageLimit;
}
