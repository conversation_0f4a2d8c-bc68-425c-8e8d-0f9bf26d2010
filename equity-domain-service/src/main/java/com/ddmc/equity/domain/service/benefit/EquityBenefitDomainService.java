package com.ddmc.equity.domain.service.benefit;


import com.ddmc.equity.dto.customer.SaveAndGetBenefitDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;

import java.util.Collection;
import java.util.List;

public interface EquityBenefitDomainService {

    /**
     * 插入或者更新权益信息。如果权益不存在，则插入；如果唯一建冲突，则更新；
     *
     * @param needSaveDO 需要插入的权益
     */
    void insertOrUpdate(EquityBenefitDO needSaveDO);

    /**
     * 批量插入或者更新权益信息列表。如果权益不存在，则插入；如果唯一建冲突，则更新；
     * <p>
     * 因为要获取到权益 id，所以循环插入（批量插入获取不到权益 id）
     *
     * @param needCreateBenefitList 需要插入的权益列表
     */
    void batchInsert(List<EquityBenefitDO> needCreateBenefitList);

    /**
     * 通过主键查询权益信息
     *
     * @param id 权益主键
     * @return 权益信息
     */
    EquityBenefitDO queryById(Long id);

    /**
     * 通过主键列表查询权益信息列表
     *
     * @param ids 权益主键列表
     * @return 权益信息列表
     */
    List<EquityBenefitDO> getBenefitsByIds(Collection<Long> ids);

    /**
     * 通过唯一 id 查询权益信息，走从库
     *
     * @param benefitType  权益类型
     * @param benefitValue 权益值
     * @param externalId   外部权益 id
     * @return 权益信息
     */
    EquityBenefitDO getBenefitByUni(Integer benefitType, String benefitValue, String externalId);

    /**
     * 通过唯一 id 查询权益信息，走主库
     *
     * @param benefitType  权益类型
     * @param benefitValue 权益值
     * @param externalId   外部权益 id
     * @return 权益信息
     */
    EquityBenefitDO getBenefitByUniAndMaster(Integer benefitType, String benefitValue, String externalId);

    /**
     * 通过权益类型查询权益信息列表
     *
     * @param benefitType 权益类型
     * @return 权益信息列表
     */
    List<EquityBenefitDO> queryEquityBenefitsByType(Integer benefitType);

    /**
     * 保存并获取权益
     *
     * @param saveAndGetBenefitDTO 入参
     * @return 权益
     */
    EquityBenefitDO saveAndGetBenefitDO(SaveAndGetBenefitDTO saveAndGetBenefitDTO);
}
