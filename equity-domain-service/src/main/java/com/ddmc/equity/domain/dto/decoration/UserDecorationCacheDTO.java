package com.ddmc.equity.domain.dto.decoration;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * 用户挂件缓存信息
 *
 * <AUTHOR>
 * @date 2025/03/20
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("用户挂件缓存信息")
public class UserDecorationCacheDTO implements Serializable {

    /**
     * uid
     */
    private String uid;

    /**
     * 装饰名字
     */
    private String name;

    /**
     * 装饰图片
     */
    private String image;

    /**
     * linux时间戳, 精确到秒级
     */
    private Long expireTime;
}
