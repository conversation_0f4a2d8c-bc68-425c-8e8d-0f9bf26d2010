package com.ddmc.equity.domain.service.equityAccount.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ddmc.equity.account.UniversalAccountRecordContext;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.QueryUniversalAccountRecordsReqDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRecordInnerExtDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcResponseExtDTO;
import com.ddmc.equity.domain.entity.account.UniversalAccountConvertEntity;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountRecordDomainService;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;
import com.ddmc.equity.infra.repository.dao.mapper.UniversalAccountRecordMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class UniversalAccountRecordDomainServiceImpl implements UniversalAccountRecordDomainService {

    @Autowired
    private UniversalAccountRecordMapper universalAccountRecordMapper;

    @Override
    public void add(UniversalAccountRecordDO universalAccountRecordDO) {
        universalAccountRecordMapper.insert(universalAccountRecordDO);
    }

    @Override
    public void update(UniversalAccountRecordDO universalAccountRecordDO) {
        universalAccountRecordMapper.update(universalAccountRecordDO, Wrappers.<UniversalAccountRecordDO>lambdaUpdate()
                .eq(UniversalAccountRecordDO::getUserId, universalAccountRecordDO.getUserId())
                .eq(UniversalAccountRecordDO::getId, universalAccountRecordDO.getId()));
    }

    @Override
    public UniversalAccountRecordDO getOneByUniqueKey(UniversalAccountRecordDO recordDO) {
        return queryOneByUniqueKey(recordDO.getUserId(), recordDO.getSceneCode(), recordDO.getOperateType(),
                recordDO.getReqNo(), recordDO.getAuxKey());
    }

    @Override
    public UniversalAccountRecordDO queryOneByUniqueKey(String userId, String sceneCode, Integer operateType,
                                                        String reqNo, String auxKey) {
        if (StringUtils.isBlank(userId) || Objects.isNull(operateType) || StringUtils.isBlank(reqNo)
                || StringUtils.isBlank(auxKey)) {
            log.warn("queryOneByUniqueKey params is null");
            return null;
        }
        Wrapper<UniversalAccountRecordDO> wrapper = Wrappers.<UniversalAccountRecordDO>lambdaQuery()
                .eq(UniversalAccountRecordDO::getUserId, userId)
                // 如果为空，则查询 sceneCode = '' 的操作记录（直塞的场景下 sceneCode 为空）
                .eq(UniversalAccountRecordDO::getSceneCode, StringUtils.defaultIfBlank(sceneCode, StringUtils.EMPTY))
                .eq(UniversalAccountRecordDO::getOperateType, operateType)
                .eq(UniversalAccountRecordDO::getReqNo, reqNo)
                .eq(UniversalAccountRecordDO::getAuxKey, auxKey);
        return universalAccountRecordMapper.selectOne(wrapper);
    }

    @Override
    public List<UniversalAccountRecordDO> queryExistAccountRecords(String userId, Long strategyId, Long benefitId,
                                                                   Integer operateType, String reqNo, String auxKey,
                                                                   List<Integer> statuses) {
        if (StringUtils.isBlank(userId) || Objects.isNull(benefitId) || Objects.isNull(operateType)) {
            log.warn("queryExistAccountRecords params is null");
            return null;
        }
        Wrapper<UniversalAccountRecordDO> wrapper = Wrappers.<UniversalAccountRecordDO>lambdaQuery()
                .eq(UniversalAccountRecordDO::getUserId, userId)
                // 如果为空，则查询 strategyId = 0L 的操作记录（直塞的场景下 strategyId 为空）
                .eq(UniversalAccountRecordDO::getStrategyId, ObjectUtils.defaultIfNull(strategyId, 0L))
                .eq(UniversalAccountRecordDO::getBenefitId, benefitId)
                .eq(UniversalAccountRecordDO::getOperateType, operateType)
                .eq(StringUtils.isNotBlank(reqNo), UniversalAccountRecordDO::getReqNo, reqNo)
                .eq(StringUtils.isNotBlank(auxKey), UniversalAccountRecordDO::getAuxKey, auxKey)
                .in(CollectionUtils.isNotEmpty(statuses), UniversalAccountRecordDO::getStatus, statuses);
        return universalAccountRecordMapper.selectList(wrapper);
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    public boolean updateStatusAndRpcResult(String userId, Long accountRecordId, Integer status, Long accountId,
                                            EquityRpcDto equityRpc) {
        if (StringUtils.isBlank(userId) || Objects.isNull(accountRecordId) || Objects.isNull(status)) {
            log.warn("updateStatusAndRpcResult params is null");
            return false;
        }
        UniversalAccountRecordDO update = new UniversalAccountRecordDO();
        update.setStatus(status);
        update.setAuxKey(Objects.equals(StatusEnum.FAIL.getCode(), status) ? String.valueOf(accountRecordId) : null);
        update.setAccountId(Objects.nonNull(accountId) ? accountId : null);
        if (Objects.nonNull(equityRpc)) {
            UniversalAccountRpcResponseExtDTO rpcResponseExtDTO = equityRpc.getRpcResponseExtDTO();
            update.setRpcCode(NumberUtils.convertToInteger(equityRpc.getCode()));
            update.setRpcMsg(equityRpc.getMessage());
            update.setRpcResponseExt(Objects.nonNull(rpcResponseExtDTO) ? JSON.toJSONString(rpcResponseExtDTO) : null);
        }
        LambdaUpdateWrapper<UniversalAccountRecordDO> updateWrapper = Wrappers.<UniversalAccountRecordDO>lambdaUpdate()
                .eq(UniversalAccountRecordDO::getUserId, userId)
                .eq(UniversalAccountRecordDO::getId, accountRecordId);
        return universalAccountRecordMapper.update(update, updateWrapper) > 0;
    }

    @SuppressWarnings("DuplicatedCode")
    @Override
    public boolean updateRecordStatusAndRpcResult(String userId, Long accountRecordId, Integer status, Long accountId,
                                                  EquityRpcDto equityRpc, String relatedReqNo) {
        if (StringUtils.isBlank(userId) || Objects.isNull(accountRecordId) || Objects.isNull(status)) {
            log.warn("updateRecordStatusAndRpcResult params is null");
            return false;
        }
        UniversalAccountRecordDO update = new UniversalAccountRecordDO();
        update.setStatus(status);
        update.setAuxKey(Objects.equals(StatusEnum.FAIL.getCode(), status) ? String.valueOf(accountRecordId) : null);
        update.setAccountId(Objects.nonNull(accountId) ? accountId : null);
        if (Objects.nonNull(equityRpc)) {
            update.setRelatedReqNo(relatedReqNo);
        }
        if (Objects.nonNull(equityRpc)) {
            UniversalAccountRpcResponseExtDTO rpcResponseExtDTO = equityRpc.getRpcResponseExtDTO();
            update.setRpcCode(NumberUtils.convertToInteger(equityRpc.getCode()));
            update.setRpcMsg(equityRpc.getMessage());
            update.setRpcResponseExt(Objects.nonNull(rpcResponseExtDTO) ? JSON.toJSONString(rpcResponseExtDTO) : null);
        }
        LambdaUpdateWrapper<UniversalAccountRecordDO> updateWrapper = Wrappers.<UniversalAccountRecordDO>lambdaUpdate()
                .eq(UniversalAccountRecordDO::getUserId, userId)
                .eq(UniversalAccountRecordDO::getId, accountRecordId);
        return universalAccountRecordMapper.update(update, updateWrapper) > 0;
    }

    @Override
    public boolean updateInnerReqNoAndRuleLimitInfo(String userId, Long accountRecordId, String innerReqNo,
                                                    Long activityReceiveLimitId, Long benefitReceiveLimitId) {
        if (StringUtils.isBlank(userId) || Objects.isNull(accountRecordId) || StringUtils.isBlank(innerReqNo)) {
            log.warn("updateInnerReqNoAndRuleLimitInfo params is null");
            return false;
        }
        LambdaUpdateWrapper<UniversalAccountRecordDO> updateWrapper = Wrappers.<UniversalAccountRecordDO>lambdaUpdate()
                .set(UniversalAccountRecordDO::getInnerSerialNumber, innerReqNo)
                .set(Objects.nonNull(benefitReceiveLimitId), UniversalAccountRecordDO::getFreezeReceiveLimitId, benefitReceiveLimitId)
                .eq(UniversalAccountRecordDO::getUserId, userId)
                .eq(UniversalAccountRecordDO::getId, accountRecordId);
        return universalAccountRecordMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public boolean updateInnerExt(String userId, Long accountRecordId, UniversalAccountRecordInnerExtDTO innerExtDTO) {
        if (StringUtils.isBlank(userId) || Objects.isNull(accountRecordId) || Objects.isNull(innerExtDTO)) {
            log.warn("updateInnerExt params is null");
            return false;
        }

        LambdaUpdateWrapper<UniversalAccountRecordDO> updateWrapper = Wrappers.<UniversalAccountRecordDO>lambdaUpdate()
                .set(UniversalAccountRecordDO::getInnerExt, JsonUtil.toString(innerExtDTO))
                .eq(UniversalAccountRecordDO::getUserId, userId)
                .eq(UniversalAccountRecordDO::getId, accountRecordId);
        return universalAccountRecordMapper.update(null, updateWrapper) > 0;
    }

    @Override
    public List<UniversalAccountRecordDO> queryUniversalAccountRecords(QueryUniversalAccountRecordsReqDTO req) {
        if (Objects.isNull(req) || StringUtils.isBlank(req.getUserId())) {
            log.warn("queryUniversalAccountRecords userId is null");
            return null;
        }

        Wrapper<UniversalAccountRecordDO> wrapper = UniversalAccountConvertEntity.convertToUniversalAccountRecordDOWrapper(req);
        return universalAccountRecordMapper.selectList(wrapper);
    }

    @Override
    public PageListRespDTO<UniversalAccountRecordDO> pageQueryUniversalAccountRecords(QueryUniversalAccountRecordsReqDTO req) {
        if (Objects.isNull(req) || StringUtils.isBlank(req.getUserId())) {
            log.warn("pageQueryUniversalAccountRecords userId is null");
            return PageListRespDTO.<UniversalAccountRecordDO>builder().build();
        }

        IPage<UniversalAccountRecordDO> page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(req.getPage(), req.getPageSize());
        Wrapper<UniversalAccountRecordDO> wrapper = UniversalAccountConvertEntity.convertToUniversalAccountRecordDOWrapper(req);
        IPage<UniversalAccountRecordDO> pageResult = universalAccountRecordMapper.selectPage(page, wrapper);
        if (Objects.isNull(pageResult)) {
            log.warn("pageQueryUniversalAccountRecords pageResult is null req={}", JSON.toJSONString(req));
            return PageListRespDTO.<UniversalAccountRecordDO>builder().build();
        }
        return PageListRespDTO.<UniversalAccountRecordDO>builder()
                .list(pageResult.getRecords())
                .total(page.getTotal())
                .build();
    }

    @Override
    public PageListRespDTO<UniversalAccountRecordDO> queryUniversalAccountRecordsForPage(UniversalAccountRecordContext context) {
        PageListRespDTO<UniversalAccountRecordDO> recordDOPage = new PageListRespDTO<>();
        Page<UniversalAccountRecordDO> pager = PageHelper.startPage(context.getPage(), context.getPageSize(), true);
        Wrapper<UniversalAccountRecordDO> wrapper = Wrappers.<UniversalAccountRecordDO>lambdaQuery()
                .eq(UniversalAccountRecordDO::getUserId, context.getUserId())
                .eq(Objects.nonNull(context.getActivityId()), UniversalAccountRecordDO::getActivityId, context.getActivityId())
                .in(CollectionUtils.isNotEmpty(context.getBenefitTypes()), UniversalAccountRecordDO::getBenefitType, context.getBenefitTypes())
                .eq(Objects.nonNull(context.getStatus()), UniversalAccountRecordDO::getStatus, context.getStatus())
                .in(CollectionUtils.isNotEmpty(context.getOperateTypes()), UniversalAccountRecordDO::getOperateType, context.getOperateTypes())
                .ge(Objects.nonNull(context.getStartTime()), UniversalAccountRecordDO::getCreateTime, context.getStartTime())
                .le(Objects.nonNull(context.getEndTime()), UniversalAccountRecordDO::getCreateTime, context.getEndTime())
                .orderByDesc(UniversalAccountRecordDO::getCreateTime);
        List<UniversalAccountRecordDO> recordDOList = universalAccountRecordMapper.selectList(wrapper);
        recordDOPage.setList(recordDOList);
        recordDOPage.setTotal(pager.getTotal());
        pager.close();
        return recordDOPage;
    }
}
