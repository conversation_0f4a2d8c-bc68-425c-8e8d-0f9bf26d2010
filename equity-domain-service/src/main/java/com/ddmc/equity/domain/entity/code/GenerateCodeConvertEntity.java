package com.ddmc.equity.domain.entity.code;

import com.ddmc.equity.infra.repository.dao.GenerateCodeDO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/21 10:11
 * @description
 */
public class GenerateCodeConvertEntity {

    public static List<GenerateCodeDO> createGenerateCodeDOList(String codeType, List<String> codes) {
        return codes.stream().map(code -> {
            GenerateCodeDO generateCodeDO = new GenerateCodeDO();
            generateCodeDO.setCodeType(codeType);
            generateCodeDO.setCode(code);
            return generateCodeDO;
        }).collect(Collectors.toList());
    }
}
