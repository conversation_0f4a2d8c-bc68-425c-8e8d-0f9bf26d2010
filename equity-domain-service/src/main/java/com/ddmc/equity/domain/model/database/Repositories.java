package com.ddmc.equity.domain.model.database;

import cn.hutool.extra.spring.SpringUtil;
import com.ddmc.equity.infra.repository.dao.mapper.EquityActivityMapper;
import com.ddmc.equity.infra.repository.dao.mapper.EquityRuleMapper;
import com.ddmc.equity.infra.repository.dao.mapper.EquitySceneMapper;
import com.ddmc.equity.infra.repository.dao.mapper.EquityStrategyMapper;


/**
 * <AUTHOR>
 */
@Deprecated
@SuppressWarnings("AlibabaConstantFieldShouldBeUpperCase")
public class Repositories extends SpringUtil {

    static {
        equityRuleMapper = getBean(EquityRuleMapper.class);
    }

    @Deprecated
    public static final EquityRuleMapper equityRuleMapper;
}