package com.ddmc.equity.domain.entity.benefit_stock;

import cn.hutool.core.lang.UUID;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.PlanDateTypeEnum;
import com.ddmc.equity.common.enums.StockOperationEnum;
import com.ddmc.equity.domain.converter.benefit.BenefitConverter;
import com.ddmc.equity.domain.dto.benefit_stock.StockOperationBaseParam;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockFlowDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockPlanDO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Getter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class EquityBenefitStockFlowEntity extends EquityBenefitStockFlowDO implements Serializable {

    private static final long serialVersionUID = -6811006671519601529L;

    public StockOperationBaseParam toBaseParam() {
        return BenefitConverter.INSTANCE.d2b(this);
    }

    public static EquityBenefitStockFlowEntity getByDO(EquityBenefitStockFlowDO flowDO) {
        return BenefitConverter.INSTANCE.d2e(flowDO);
    }

    public static EquityBenefitStockFlowDO buildInitEquityBenefitStockFlowDO(Long activityId,
                                                                             Long strategyId,
                                                                             Long benefitId,
                                                                             StockLimitRuleDTO stockLimitRuleDTO) {
        Integer totalStock = stockLimitRuleDTO.getTotalStock();
        Integer planDateType = stockLimitRuleDTO.getPlanDateType();
        Long dayStock = stockLimitRuleDTO.getDayStock();

        EquityBenefitStockFlowDO equityBenefitStockFlowDO = new EquityBenefitStockFlowDO();
        equityBenefitStockFlowDO.setActivityId(activityId);
        equityBenefitStockFlowDO.setStrategyId(strategyId);
        equityBenefitStockFlowDO.setBenefitId(benefitId);
        equityBenefitStockFlowDO.setReqNo(UUID.fastUUID().toString());
        equityBenefitStockFlowDO.setOperationType(StockOperationEnum.INIT.getOperation());
        // 设置计划库存
        equityBenefitStockFlowDO.setPlanStockId(0L); // 初始化时不需要这个字段
        equityBenefitStockFlowDO.setPlanDateType(Objects.isNull(planDateType) ? PlanDateTypeEnum.UNDEFINED.getType() : planDateType);
        equityBenefitStockFlowDO.setPlanOperationNum(Objects.isNull(dayStock) ? 0L : dayStock);
        // 设置非计划库存
        equityBenefitStockFlowDO.setOperationNum(Objects.isNull(totalStock) ? 0L : Long.valueOf(totalStock));
        equityBenefitStockFlowDO.setIsHandle(CommonEnum.INTEGER_BOOL.NO.getCode());

        equityBenefitStockFlowDO.setCreateTime(new Date());
        equityBenefitStockFlowDO.setUpdateTime(new Date());
        equityBenefitStockFlowDO.setIsDelete(CommonEnum.INTEGER_BOOL.NO.getCode());
        equityBenefitStockFlowDO.setLoadDb(0);
        equityBenefitStockFlowDO.setLoadCache(0);

        return equityBenefitStockFlowDO;
    }

    public static EquityBenefitStockFlowDO buildExpandBenefitStockFlowDO(Long activityId,
                                                                         Long strategyId,
                                                                         Long benefitId,
                                                                         Long operationNum) {
        EquityBenefitStockFlowDO flowDO = new EquityBenefitStockFlowDO();
        flowDO.setActivityId(activityId);
        flowDO.setStrategyId(strategyId);
        flowDO.setBenefitId(benefitId);
        flowDO.setReqNo(UUID.fastUUID().toString());
        flowDO.setOperationType(StockOperationEnum.EXPEND.getOperation());
        flowDO.setOperationNum(operationNum);
        // 扩充非计划库存时，无须设置计划库存
        flowDO.setPlanStockId(0L);
        flowDO.setPlanDateType(PlanDateTypeEnum.UNDEFINED.getType());
        flowDO.setPlanOperationNum(0L);
        flowDO.setIsHandle(CommonEnum.INTEGER_BOOL.NO.getCode());
        flowDO.setLoadCache(CommonEnum.INTEGER_BOOL.NO.getCode());
        flowDO.setLoadDb(CommonEnum.INTEGER_BOOL.NO.getCode());
        return flowDO;
    }

    private static EquityBenefitStockFlowDO buildExpandPlanBenefitStockFlowDO(Long activityId,
                                                                              Long strategyId,
                                                                              Long benefitId,
                                                                              Long planStockId,
                                                                              Integer planDateType,
                                                                              Long planOperationNum) {
        EquityBenefitStockFlowDO flowDO = new EquityBenefitStockFlowDO();
        flowDO.setActivityId(activityId);
        flowDO.setStrategyId(strategyId);
        flowDO.setBenefitId(benefitId);
        flowDO.setReqNo(UUID.fastUUID().toString());
        flowDO.setOperationType(StockOperationEnum.EXPEND_PLAN.getOperation());
        // 扩充计划库存时，也是用 operationNum，不是 planOperationNum
        flowDO.setOperationNum(planOperationNum);
        flowDO.setPlanStockId(planStockId);
        flowDO.setPlanDateType(Objects.isNull(planDateType) ? PlanDateTypeEnum.UNDEFINED.getType() : planDateType);
        flowDO.setPlanOperationNum(0L);
        flowDO.setIsHandle(CommonEnum.INTEGER_BOOL.NO.getCode());
        flowDO.setLoadCache(CommonEnum.INTEGER_BOOL.NO.getCode());
        flowDO.setLoadDb(CommonEnum.INTEGER_BOOL.NO.getCode());
        return flowDO;
    }

    public static List<EquityBenefitStockFlowDO> buildExpandPlanBenefitStockFlowDOList(
            List<EquityBenefitStockPlanDO> effectiveBenefitStockPlans,
            Long activityId, Long strategyId, Long benefitId, Integer planDateType, Long diffDayStock
    ) {
        return effectiveBenefitStockPlans.stream()
                .map(planDO ->
                        buildExpandPlanBenefitStockFlowDO(activityId, strategyId, benefitId, planDO.getId(),
                                planDateType, diffDayStock)
                )
                .collect(Collectors.toList());
    }
}
