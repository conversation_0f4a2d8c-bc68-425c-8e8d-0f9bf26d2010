package com.ddmc.equity.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class ReceiveBenefitDTO {

    /**
     * 活动 id
     */
    private Long activityId;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 外部关联类型。1-玩法；
     */
    private Integer externalType;
    /**
     * 外部关联 id（一般是外部关联方的活动 id，如 promo.activity.activityId）
     */
    private String externalId;
    /**
     * 策略 id
     */
    private Long strategyId;
    /**
     * 策略外部关联 id。如果是来源玩法的活动，则为活动 prizeId；如果是膨胀券活动，则为母券券模板 id；
     */
    private String strategyExternalId;
    /**
     * 策略类型。0-普通策略；1-兜底策略；2-黑灰产策略；
     *
     * @see com.ddmc.equity.enums.StrategyTypeEnum
     */
    private Integer strategyType;
    /**
     * 主券用户券 id，为用户膨胀前的券 userTicketId
     */
    private String masterUserTicketId;
    /**
     * 权益组 id
     */
    private Long benefitGroupId;
    /**
     * 权益 id
     */
    private Long benefitId;
    /**
     * 权益名称
     */
    private String benefitName;
    /**
     * 权益类型
     */
    private Integer benefitType;
    /**
     * 权益值
     */
    private String benefitValue;
    /**
     * 余额金额，保留两位小数。如果权益类型为余额（固定余额、随机余额），才有该字段
     */
    private BigDecimal balanceMoney;

    /**
     * ******** 新增老权益ID，临时用
     *
     * @deprecated 以后会废掉
     */
    @Deprecated
    private String oldBenefitId;
    /**
     * 权益子账户操作流水 id
     */
    private Long accountRecordId;
    /**
     * 三方返回值。领券活动返回用户领取后的用户券 id；券膨胀返回用户膨胀后的券 userTicketId
     */
    private String thirdResNo;
    /**
     * 不能领取原因类型
     *
     * @see com.ddmc.equity.enums.BenefitUnableReceiveReasonType
     */
    private String unableReceiveReasonCode;
}
