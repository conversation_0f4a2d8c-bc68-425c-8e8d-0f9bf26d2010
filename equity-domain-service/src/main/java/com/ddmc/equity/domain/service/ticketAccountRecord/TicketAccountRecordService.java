package com.ddmc.equity.domain.service.ticketAccountRecord;

import com.ddmc.equity.infra.repository.dao.TicketAccountRecordDO;
import org.jetbrains.annotations.Nullable;

import java.util.List;

public interface TicketAccountRecordService {
    /**
     * 记录保存
     *
     * @param recordDO 记录
     * @return 结果
     */
    int save(TicketAccountRecordDO recordDO);

    /**
     * 更新操作记录
     *
     * @param recordId                   操作记录id
     * @param uid                        用户id
     * @param status                     状态
     * @param accountId                  账户记录id
     * @param innerSerialNumber          内部流水号
     * @param userTicketId               用户券id
     * @param serialNumber               请求流水号
     * @param code                       错误码
     * @param msg                        错误描述
     * @param freezeReceiveLimitResultId 冻结频次id
     * @return 更新结果
     */
    boolean updateRecord(Long recordId, String uid, Integer status, Long accountId, String innerSerialNumber,
                         String userTicketId, String serialNumber, String code, String msg, Long freezeReceiveLimitResultId,
                         Integer sendTicketScene);

    /**
     * 根据主键查询记录
     *
     * @param uid          用户id
     * @param serialNumber 请求流水号
     * @param strategyId   策略id
     * @param equityId     权益id
     * @param operateType  操作类型
     * @param auxKey       辅助健
     * @return 操作记录
     */
    TicketAccountRecordDO selectByUk(String uid, String serialNumber, Long strategyId,
                                     Long equityId, int operateType, String auxKey);

    /**
     * 按照状态查询权益操作记录
     *
     * @param uid                  用户id
     * @param activityId           活动id
     * @param strategyId           策略id
     * @param benefitId            权益id
     * @param statusList           状态集合
     * @param operateType          操作类型
     * @param freezeReceiveLimitId 冻结频次id
     * @return 操作记录
     */
    List<TicketAccountRecordDO> select(String uid, Long activityId, Long strategyId, Long benefitId, List<Integer> statusList, int operateType, Long freezeReceiveLimitId);

    /**
     * 更新记录状态
     *
     * @param recordId     记录id
     * @param uid          用户id
     * @param status       状态
     * @param serialNumber 流水号
     * @param code         错误码
     * @param message      错误信息
     */
    boolean updateRecordStatus(Long recordId, String uid, Integer status, String serialNumber, String code, String message,
                               @Nullable Long freezeReceiveLimitId);

    /**
     * 更新字段
     *
     * @param uid          用户id
     * @param recordId     记录id
     * @param serialNumber 流水号
     * @param userTicketId 用户券id
     * @param isRelated    是否关联
     * @param relatedReqNo 关联单号
     * @param status       状态
     * @param code         错误码
     * @param msg          错误信息
     */
    boolean updateRecordColumn(String uid, Long recordId, String serialNumber, String userTicketId, Integer isRelated, String relatedReqNo, Integer status,
                               String code, String msg, Integer sendTicketScene);

    /****
     * 根据TicketAccountRecordDO 查询满足条件的DO数据
     * @param ticketAccountRecordDO TicketAccountRecordDO
     * @return List
     */
    List<TicketAccountRecordDO> queryTicketAccountRecordDOListByTicketAccountRecordDO(TicketAccountRecordDO ticketAccountRecordDO);

    /**
     * 更新频次冻结ID到记录表里
     *
     * @param recordId             Long
     * @param freezeReceiveLimitId Long
     */
    void updateRecordFreezeReceiveLimitId(Long recordId, Long freezeReceiveLimitId, String userId);
}
