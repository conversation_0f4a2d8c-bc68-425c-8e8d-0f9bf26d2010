package com.ddmc.equity.domain.service.receive_limit;

import com.ddmc.equity.infra.repository.dao.UserActivityReceiveLimitDO;

import java.util.Date;

/**
 * 用户活动领取频次限制表 Mapper 代理接口
 * 用于新老表切换过程中的数据操作代理
 */
public interface UserActivityReceiveLimitMapperProxy {

    /**
     * 查询单条记录
     *
     * @param userId     用户 ID
     * @param activityId 活动 ID
     * @param date       查询时间
     * @return 领取限制记录
     */
    UserActivityReceiveLimitDO queryByDate(String userId, Long activityId, Date date);

    /**
     * 查询单条记录
     *
     * @param id         记录 ID
     * @param userId     用户 ID
     * @param activityId 活动 ID
     * @return 领取限制记录
     */
    UserActivityReceiveLimitDO queryByReceiveLimitId(Long id, String userId, Long activityId);

    /**
     * 插入记录
     *
     * @param receiveLimitDO 领取限制记录
     * @return 是否成功
     */
    int insert(UserActivityReceiveLimitDO receiveLimitDO);

    /**
     * 扣减领取次数
     *
     * @param id         记录 ID
     * @param userId     用户 ID
     * @param activityId 活动 ID
     * @param count      释放数量
     * @param version    版本号
     * @return 影响行数
     */
    int deduct(Long id, String userId, Long activityId, Long count, Long version);

    /**
     * 释放领取次数
     *
     * @param id         记录 ID
     * @param userId     用户 ID
     * @param activityId 活动 ID
     * @param count      释放数量
     * @param version    版本号
     * @return 影响行数
     */
    int release(Long id, String userId, Long activityId, Long count, Long version);
}