package com.ddmc.equity.domain.dto.engine;

import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/9/18 14:52
 * @description 权益咨询引擎过滤返回结果上下文
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class EngineResultContextDTO {

    /**
     * 用户身份
     *
     * @see com.ddmc.equity.common.enums.VipIdentityEnum
     */
    private Integer userStatus;
    /**
     * 不能领取权益列表（包含不能领取原因）。当请求参数指定需要不能领取原因才返回，否则为空
     */
    @Builder.Default
    private List<UnableReceiveBenefitDTO> unableReceiveBenefitDTOList = Lists.newCopyOnWriteArrayList();
}
