package com.ddmc.equity.domain.converter.rule;

import com.ddmc.equity.domain.dto.rule.condition.VipIdentityRuleDTO;
import com.ddmc.equity.domain.valueobject.rule.condition.VipIdentityRuleVO;
import com.ddmc.equity.dto.business.rule.VipIdentityDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface VipIdentityRuleConverter {
    VipIdentityRuleConverter INSTANCE = Mappers.getMapper(VipIdentityRuleConverter.class);

    VipIdentityRuleVO convertDTOToVO(VipIdentityDTO abTestDTO);

    VipIdentityRuleDTO convertToVipIdentityRuleDTO(VipIdentityDTO vipIdentityDTO);
}
