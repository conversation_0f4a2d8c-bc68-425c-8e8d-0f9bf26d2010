package com.ddmc.equity.domain.converter.rule;

import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.dto.business.rule.StockLimitDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface StockLimitRuleConverter {
    StockLimitRuleConverter INSTANCE = Mappers.getMapper(StockLimitRuleConverter.class);

    StockLimitRuleDTO convertToStockLimitRuleDTO(StockLimitDTO stockLimitDTO);
}
