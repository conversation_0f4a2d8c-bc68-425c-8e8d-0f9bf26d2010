package com.ddmc.equity.domain.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CommonProductInfoDTO {
    @ApiModelProperty(value = "商品id")
    private Long productId;

    /**
     * 商品mongoId
     * 注意: mongoId会逐步弃用,请尽量使用数字id,避免后续二次改动
     */
    @ApiModelProperty(value = "mongoId")
    private String mongoId;

    @ApiModelProperty(value = "商品售卖名")
    private String name;

    @ApiModelProperty(value = "商品名")
    private String baseName;

    @ApiModelProperty(value = "别名")
    private String searchTags;

    /**
     * 商品类型
     * 正常商品: 0
     * 组合商品: 2
     */
    @ApiModelProperty(value = "商品类型 (0:默认 2:组合)")
    private Integer type;


    /**
     * 状态说明：
     * "1": 显示
     * "2": 删除
     * "3": 草稿
     * "4": 新品草稿
     */
    @ApiModelProperty(value = "商品状态  (1:显示  2:删除  3:草稿  4:新品草稿)")
    private Integer status;


    @ApiModelProperty(value = "商品图片")
    private String imageList;

    /**
     * 商品净含量
     */
    @ApiModelProperty(value = "商品净含量")
    private Integer netWeight;

    /**
     * 商品净含量单位
     */
    @ApiModelProperty(value = "商品净含量单位")
    private String netWeightUnit;

    /**
     * 储存方式
     * 1: 常温
     * 2: 冷藏
     * 3: 冷冻
     * 4: 常温避光
     * 5: 冷藏避光
     * 6: 冰鲜
     * 7: 热链
     */
    @ApiModelProperty(value = "储存方式(1:常温 2:冷藏 3:冷冻 4:常温避光 5:冷藏避光 6:冰鲜 7:热链)")
    private Integer storageValueId;

    /**
     * 是否为原料
     */
    @ApiModelProperty(value = "是否原料 1是 0否")
    private Integer isMaterial;

    /**
     * 温层
     * 1: -18℃以下
     * 2: 0-4℃
     * 3: 4-8℃
     * 4: 8℃以上
     */
    @ApiModelProperty(value = "温层 1、-18℃以下 2、0-4℃ 3、4-8℃ 4、8℃以上")
    private Integer temperatureLayer;

    /**
     * 是否可售
     */
    @ApiModelProperty(value = "是否可交易(1:是 0:否)")
    private Integer isSale;

    /**
     * 是否是非卖品
     */
    @ApiModelProperty(value = "是否为非卖品(1:是 0:否)")
    private Integer isGift;

    /**
     * 是否同步库存
     */
    @ApiModelProperty(value = "是否同步库存(1:是 0:否)")
    private Integer isSync;

    /**
     * 是否废弃
     */
    @ApiModelProperty(value = "是否失效(1:是 0:否)")
    private Integer isDiscard;

    /**
     * 体积: 长, 宽, 高
     */
    @ApiModelProperty(value = "体积")
    private String volume;

    /**
     * 后台分类ID
     */
    @ApiModelProperty(value = "后台分类ID")
    private Long backendCategoryId;

    @ApiModelProperty(value = "后台分类名称")
    private String backendCategoryName;

    @ApiModelProperty(value = "后台分类ID路径")
    private List<Long> backendCategoryIdPath;

    @ApiModelProperty(value = "后台分类路径名称")
    private List<String> backendCategoryPathName;

    /**
     * 负责组Id
     */
    @ApiModelProperty(value = "负责组ID")
    private String responsibleGroupId;

    @ApiModelProperty(value = "商品价格(分)")
    private Integer price;

    /**
     * PHP接口返回   确定是不是同一个price
     */
    @ApiModelProperty(value = "采购分类id")
    private Long manageCategory;

    @ApiModelProperty(value = "采购分类路径")
    private List<String> manageCategoryPath;

    /**
     * 入参中的needQueryManageCategoryPathName字段为true时候才会返回采购分类路径名称
     */
    @ApiModelProperty(value = "采购分类路径名称")
    private List<String> manageCategoryPathName;

    @ApiModelProperty(value = "上架数")
    private Integer onShelfCount;

    /**
     * 是否启用批次 1是 0否
     */
    @ApiModelProperty(value = "是否启用批次 1是 0否")
    private Integer isBatch;
}
