package com.ddmc.equity.domain.service.firstOrderAccountRecord;

import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.infra.repository.dao.FirstOrderAccountRecordDO;

import java.util.List;

public interface FirstOrderAccountRecordService {

     int addFirstOrderAccountRecordDO(FirstOrderAccountRecordDO firstOrderAccountRecordDO);

     int updateFirstOrderAccountRecordDOStatusByIdAndUserId(FirstOrderAccountRecordDO firstOrderAccountRecordDO);

     FirstOrderAccountRecordDO queryFirstOrderAccountRecordDOById(Long id);

     FirstOrderAccountRecordDO queryFirstOrderAccountRecordDOByDO(FirstOrderAccountRecordDO firstOrderAccountRecordDO);

     FirstOrderAccountRecordDO queryFirstOrderAccountRecordDOByDO(FirstOrderAccountRecordDO firstOrderAccountRecordDO, List<Integer> statusList);


     int updateRecord(String uid, Long id, int status, String serialNumber, EquityRpcDto equityRpcDto);


     boolean updateRecordStatus(String uid, Long accountRecordId, Long accountId, Integer status, String serialNumber, EquityRpcDto equityRpcDto);

     boolean updateRecordByIdAndUserId(Long recordId, String uid, String serialNumber, Long accountId, Integer status, String code, String message);


     FirstOrderAccountRecordDO queryFirstOrderAccountRecordDOByUserIdAndOperateTypeAndReqNo(FirstOrderAccountRecordDO firstOrderAccountRecordDO);

     boolean updateFirstOrderAccountRecordAuxKeyByTagAndOperateType(String userId,Integer operateType);

     FirstOrderAccountRecordDO queryFirstOrderAccountRecordDOByUserIdAndReqNo(String userId, String reqNo,Integer operateType);
}
