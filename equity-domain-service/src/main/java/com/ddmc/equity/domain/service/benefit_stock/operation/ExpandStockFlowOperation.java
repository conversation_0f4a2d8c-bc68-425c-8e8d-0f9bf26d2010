package com.ddmc.equity.domain.service.benefit_stock.operation;

import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.enums.StockOperationEnum;
import com.ddmc.equity.domain.dto.benefit_stock.StockOperationBaseParam;
import com.ddmc.equity.domain.service.benefit_stock.AbstractStockFlowFactorHandler;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitStockMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Random;

/**
 * 扩大总库存和剩余库存
 */
@Component
@Slf4j
public class ExpandStockFlowOperation extends AbstractStockFlowFactorHandler {
    private static final Random RANDOM = new Random();

    @Resource
    private EquityBenefitStockMapper equityBenefitStockMapper;

    @Resource
    private RedisCache redisCache;

    @Override
    public Integer getStockOperationType() {
        return StockOperationEnum.EXPEND.getOperation();
    }

    @Override
    public Boolean handleCacheUpdate(StockOperationBaseParam msg) {
        Duration duration = Duration.ofDays(180);
        duration = duration.plusSeconds(RANDOM.nextInt(86400));

        // @吴文蓝，直接原子增加redis容量就行了，正常情况不会重发
        Long strategyId = msg.getStrategyId();
        Long benefitId = msg.getBenefitId();
        Long operationNum = msg.getStockOperation();
        // 扩容场景下，原子增加
        String stockKey = String.format(CacheKeyConstants.STOCK_COUNT_KEY, strategyId, benefitId);
        String balanceKey = String.format(CacheKeyConstants.BALANCE_COUNT_KEY, strategyId, benefitId);
        if (!redisCache.exist(stockKey) || !redisCache.exist(balanceKey)) {
            log.error("当前库存或剩余库存未经过初始化 stockKey={}, balanceKey={}", stockKey, balanceKey);
            return false;
        }
        return redisCache.incrRedis(stockKey,balanceKey,operationNum,duration);
    }

    @Override
    public Boolean consumerStockOperationMsg(StockOperationBaseParam msg) {
//        int row = equityBenefitStockMapper.update(null, new LambdaUpdateWrapper<EquityBenefitStockDO>()
//                .set(EquityBenefitStockDO::getStockCount, "`stock_count` +" + msg.getStockOperation())
//                .set(EquityBenefitStockDO::getBalanceCount, "`balance_count` +" + msg.getStockOperation())
//                .set(EquityBenefitStockDO::getVersion, "`version` + 1")
//                .eq(EquityBenefitStockDO::getStrategyId, msg.getStrategyId())
//                .eq(EquityBenefitStockDO::getBenefitId, msg.getBenefitId())
//                .eq(EquityBenefitStockDO::getActivityId, msg.getActivityId())
//        );
        int row = equityBenefitStockMapper.expandStock( msg.getStockOperation(),msg.getStrategyId(),msg.getActivityId(),msg.getBenefitId());
        return row > 0;
    }


}
