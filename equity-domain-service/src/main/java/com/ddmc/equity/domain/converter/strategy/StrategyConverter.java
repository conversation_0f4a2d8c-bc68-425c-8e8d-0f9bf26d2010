package com.ddmc.equity.domain.converter.strategy;

import com.ddmc.equity.domain.entity.strategy.EquityStrategyEntity;
import com.ddmc.equity.domain.valueobject.strategy.StrategyListFilterReqVO;
import com.ddmc.equity.dto.business.StrategyBusinessDTO;
import com.ddmc.equity.dto.business.StrategyListReqDTO;
import com.ddmc.equity.dto.business.StrategySaveReqDTO;
import com.ddmc.equity.dto.business.UniversalStrategyDTO;
import com.ddmc.equity.infra.repository.dao.EquityStrategyDO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface StrategyConverter {
    StrategyConverter INSTANCE = Mappers.getMapper(StrategyConverter.class);

    EquityStrategyEntity strategyD2E(EquityStrategyDO target);

    List<EquityStrategyEntity> strategyD2Es(List<EquityStrategyDO> target);

    @Mapping(target = "name", source = "idOrName")
    @Mapping(target = "id", expression = "java(com.ddmc.equity.common.util.NumStrUtil.getId(req.getIdOrName()))")
    StrategyListFilterReqVO d2v(StrategyListReqDTO req);

    @Mapping(target = "externalId", expression = "java(java.util.UUID.randomUUID().toString().trim().replace(\"-\", \"\"))")
    @Mapping(target = "opAdminName", expression = "java(com.ddmc.equity.common.util.LongUtils.isTrue(req.getId()) ? null : req.getAdminName())")
    @Mapping(target = "opAdminId", expression = "java(com.ddmc.equity.common.util.LongUtils.isTrue(req.getId()) ? null : req.getAdminId())")
    @Mapping(target = "editAdminId", source = "adminId")
    @Mapping(target = "editAdminName", source = "adminName")
    @Mapping(target = "isDelete", constant = "0")
    @Mapping(target = "createTime", expression = "java(new java.util.Date())")
    @Mapping(target = "updateTime", expression = "java(new java.util.Date())")
    EquityStrategyDO srd2e(StrategySaveReqDTO req);

    List<StrategyBusinessDTO> v2ds(List<EquityStrategyDO> list);

    @Mapping(target = "activityWeight", source = "weight")
    List<EquityStrategyDO> dto2dos(List<UniversalStrategyDTO> list);

    @Mapping(target = "activityWeight", source = "weight")
    EquityStrategyDO dto2do(UniversalStrategyDTO dto);
}
