package com.ddmc.equity.domain.service.empty_prize.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.entity.account.EmptyPrizeAccountConvertEntity;
import com.ddmc.equity.domain.service.empty_prize.EmptyPrizeAccountRecordDomainService;
import com.ddmc.equity.infra.repository.dao.EmptyPrizeAccountRecordDO;
import com.ddmc.equity.infra.repository.dao.mapper.EmptyPrizeAccountRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/16 11:32
 * @description
 */
@Slf4j
@Service
public class EmptyPrizeAccountRecordDomainServiceImpl extends ServiceImpl<EmptyPrizeAccountRecordMapper, EmptyPrizeAccountRecordDO>
        implements EmptyPrizeAccountRecordDomainService {

    @Override
    public void insertAccountRecordDO(EmptyPrizeAccountRecordDO accountRecordDO) {
        Assert.notNull(accountRecordDO, ExceptionEnum.ILLEGAL_ARGS.getCode(), "空奖权益子账户操作流水不能为空");
        Assert.mustTrue(StringUtils.isNotBlank(accountRecordDO.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "空奖权益子账户操作流水 userId 不能为空");
        this.save(accountRecordDO);
    }

    @Override
    public boolean updateAccountRecordStatusAndRpcResult(String userId, Long accountRecordId, Long accountId, Integer status,
                                                         EquityRpcDto equityRpcDto, Map<String, Object> ruleLimitInfoMap) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "空奖权益子账户操作流水 userId 不能为空");
        Assert.notNull(accountRecordId, ExceptionEnum.ILLEGAL_ARGS.getCode(), "空奖权益子账户操作流水 accountRecordId 不能为空");
        Assert.mustTrue(StatusEnum.isContain(status), ExceptionEnum.ILLEGAL_ARGS.getCode(), "空奖权益子账户操作流水状态异常");

        EmptyPrizeAccountRecordDO update = EmptyPrizeAccountConvertEntity.createUpdateAccountRecordDO(accountRecordId, accountId,
                status, equityRpcDto, ruleLimitInfoMap);
        return this.update(update, Wrappers.<EmptyPrizeAccountRecordDO>lambdaUpdate()
                .eq(EmptyPrizeAccountRecordDO::getUserId, userId)
                .eq(EmptyPrizeAccountRecordDO::getId, accountRecordId));
    }

    @Override
    public EmptyPrizeAccountRecordDO queryAccountRecordByUniqueKeyAndStatuses(String userId, Integer operateType,
                                                                              String reqNo, List<Integer> statuses) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "空奖权益子账户操作流水 userId 不能为空");
        Assert.mustTrue(OperateTypeEnum.contains(operateType), ExceptionEnum.ILLEGAL_ARGS.getCode(), "空奖权益子账户操作流水操作类型异常");
        Assert.mustTrue(StringUtils.isNotBlank(reqNo), ExceptionEnum.ILLEGAL_ARGS.getCode(), "空奖权益子账户操作流水 reqNo 不能为空");

        Wrapper<EmptyPrizeAccountRecordDO> wrapper = Wrappers.<EmptyPrizeAccountRecordDO>lambdaQuery()
                .eq(EmptyPrizeAccountRecordDO::getUserId, userId)
                .eq(EmptyPrizeAccountRecordDO::getOperateType, operateType)
                .eq(EmptyPrizeAccountRecordDO::getReqNo, reqNo)
                .in(CollectionUtils.isNotEmpty(statuses), EmptyPrizeAccountRecordDO::getStatus, statuses);
        return this.getOne(wrapper);
    }
}
