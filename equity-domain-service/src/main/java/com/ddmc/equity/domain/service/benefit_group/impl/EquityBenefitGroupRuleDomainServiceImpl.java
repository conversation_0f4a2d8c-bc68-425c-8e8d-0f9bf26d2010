package com.ddmc.equity.domain.service.benefit_group.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.common.exception.ApiBusinessException;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.common.util.LongUtils;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.domain.service.benefit_group.EquityBenefitGroupRuleDomainService;
import com.ddmc.equity.infra.repository.dao.EquityBenefitGroupRuleDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitGroupRuleMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class EquityBenefitGroupRuleDomainServiceImpl implements EquityBenefitGroupRuleDomainService {

    @Resource
    private EquityBenefitGroupRuleMapper equityBenefitGroupRuleMapper;

    /**
     * 保存
     *
     * @param needSaveDO
     */
    @Override
    public void save(EquityBenefitGroupRuleDO needSaveDO) {
        if (LongUtils.isTrue(needSaveDO.getId())) {
            equityBenefitGroupRuleMapper.updateById(needSaveDO);
        } else {
            equityBenefitGroupRuleMapper.insert(needSaveDO);
        }
    }

    /**
     * 新建
     *
     * @param equityBenefitGroupRuleDO
     */
    @Override
    public void insert(EquityBenefitGroupRuleDO equityBenefitGroupRuleDO) {
        if (Objects.isNull(equityBenefitGroupRuleDO)) {
            throw new ApiBusinessException(ExceptionEnum.SAVE_BENEFIT_GROUP_FAILED);
        }
        int result = equityBenefitGroupRuleMapper.insert(equityBenefitGroupRuleDO);
        if (result != 1) {
            throw new ApiBusinessException(ExceptionEnum.SAVE_BENEFIT_GROUP_FAILED);
        }
    }


    /**
     * 批量新建
     *
     * @param list
     */
    @Override
    public void batchInsertOrUpdate(List<EquityBenefitGroupRuleDO> list) {
        //判空
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        try {
            equityBenefitGroupRuleMapper.batchInsertOrUpdate(list);
        } catch (Exception e) {
            log.error("EquityBenefitGroupRuleDomainServiceImpl.batchInsert.e", e);
            throw new ApiBusinessException(ExceptionEnum.SAVE_RULE_FAILED);
        }
    }

    @Override
    public List<EquityBenefitGroupRuleDO> batchInsert(List<EquityBenefitGroupRuleDO> equityBenefitGroupRuleDOList) {
        if (CollectionUtils.isEmpty(equityBenefitGroupRuleDOList)) {
            return equityBenefitGroupRuleDOList;
        }
        equityBenefitGroupRuleMapper.insertBatchSomeColumn(equityBenefitGroupRuleDOList);
        return equityBenefitGroupRuleDOList;
    }

    @Override
    public List<EquityBenefitGroupRuleDO> batchUpdate(List<EquityBenefitGroupRuleDO> equityBenefitGroupRuleDOList) {
        if (CollectionUtils.isEmpty(equityBenefitGroupRuleDOList)) {
            return equityBenefitGroupRuleDOList;
        }
        equityBenefitGroupRuleMapper.updateBatch(equityBenefitGroupRuleDOList);
        return equityBenefitGroupRuleDOList;
    }

    @Override
    public List<EquityBenefitGroupRuleDO> queryByBenefitGroupIdAndBenefitId(Long strategyId, Long benefitGroupId, Long benefitId) {
        if (!ObjectUtils.allNotNull(strategyId, benefitGroupId, benefitId)) {
            return null;
        }
        LambdaQueryWrapper<EquityBenefitGroupRuleDO> wrapper = Wrappers.<EquityBenefitGroupRuleDO>lambdaQuery()
                .eq(EquityBenefitGroupRuleDO::getStrategyId, strategyId)
                .eq(EquityBenefitGroupRuleDO::getBenefitGroupId, benefitGroupId)
                .eq(EquityBenefitGroupRuleDO::getBenefitId, benefitId);
        return equityBenefitGroupRuleMapper.selectList(wrapper);
    }

    @Override
    public List<EquityBenefitGroupRuleDO> queryByBenefitGroupRuleByStrategyIdAndBenefitGroupId(Long strategyId, Long benefitGroupId) {
        QueryWrapper<EquityBenefitGroupRuleDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("strategy_id", strategyId).eq("benefit_group_id", benefitGroupId);
        return equityBenefitGroupRuleMapper.selectList(queryWrapper);
    }

    @Override
    public List<EquityBenefitGroupRuleDO> queryByBenefitGroupIds(List<Long> benefitGroupIds) {
        if (CollectionUtils.isEmpty(benefitGroupIds)) {
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<EquityBenefitGroupRuleDO> wrapper = Wrappers.<EquityBenefitGroupRuleDO>lambdaQuery()
                .in(EquityBenefitGroupRuleDO::getBenefitGroupId, benefitGroupIds);
        return equityBenefitGroupRuleMapper.selectList(wrapper);
    }

    @Override
    public void batchDelete(List<EquityBenefitGroupRuleDO> equityBenefitGroupRuleDOList) {
        if (CollectionUtils.isEmpty(equityBenefitGroupRuleDOList)) {
            return;
        }
        for (EquityBenefitGroupRuleDO ruleDO : equityBenefitGroupRuleDOList) {
            UpdateWrapper<EquityBenefitGroupRuleDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", ruleDO.getId()).eq("is_delete", 0).set("is_delete", 1).set("aux_key", ruleDO.getId());
            ruleDO.setIsDelete(1);
            ruleDO.setAuxKey(ruleDO.getId());
            equityBenefitGroupRuleMapper.update(ruleDO, updateWrapper);
            //update tb set is_delete=1,aux_key=id where is_delete=0 and id in (id1,id2);
        }
    }

    public List<EquityBenefitGroupRuleDO> queryEquityBenefitGroupRuleDOListByStrategyIdAndRuleType(Long strategyId, Integer ruleType) {
        LambdaQueryWrapper<EquityBenefitGroupRuleDO> wrapper = Wrappers.<EquityBenefitGroupRuleDO>lambdaQuery()
                .eq(EquityBenefitGroupRuleDO::getStrategyId, strategyId)
                .eq(EquityBenefitGroupRuleDO::getRuleType, ruleType);
        return equityBenefitGroupRuleMapper.selectList(wrapper);
    }

    @Override
    public StockLimitRuleDTO getBenefitStockLimitRule(Long strategyId, Long benefitId) {
        if (Objects.isNull(strategyId) || Objects.isNull(benefitId)) {
            log.warn("getBenefitStockLimitRule args isNull. strategyId={}, benefitId={}", strategyId, benefitId);
            return null;
        }
        LambdaQueryWrapper<EquityBenefitGroupRuleDO> wrapper = Wrappers.<EquityBenefitGroupRuleDO>lambdaQuery()
                .eq(EquityBenefitGroupRuleDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(EquityBenefitGroupRuleDO::getStrategyId, strategyId)
                .eq(EquityBenefitGroupRuleDO::getBenefitId, benefitId)
                .eq(EquityBenefitGroupRuleDO::getRuleType, RuleTypeEnum.STOCK_LIMIT_RULE.getCode())
                .last("LIMIT 1");
        EquityBenefitGroupRuleDO benefitGroupRuleDO = equityBenefitGroupRuleMapper.selectOne(wrapper);
        return Objects.isNull(benefitGroupRuleDO) ? null :
                JsonUtil.parseObject(benefitGroupRuleDO.getRuleValue(), StockLimitRuleDTO.class);
    }
}
