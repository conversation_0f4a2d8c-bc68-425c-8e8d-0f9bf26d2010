package com.ddmc.equity.domain.service.core.impl;

import com.ddmc.equity.domain.converter.benefit.BenefitDeepCopyConverter;
import com.ddmc.equity.domain.service.core.ThirdBenefitHandler;
import com.ddmc.equity.domain.service.core.ThirdBenefitService;
import com.ddmc.equity.dto.customer.*;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.SetMultimap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;

@Service
@Slf4j
public class ThirdBenefitServiceImpl implements ThirdBenefitService {

    @Autowired
    List<ThirdBenefitHandler> handlers;

    private final SetMultimap<String /* sceneCode */, ThirdBenefitHandler> HANDLER_MAP = HashMultimap.create();

    /**
     * 处理类放到map中
     */
    @PostConstruct
    public void afterPropertiesSet() {
        for (ThirdBenefitHandler handler : handlers) {
            for (String sceneCode : handler.getSceneCode()) {
                HANDLER_MAP.put(sceneCode, handler);
            }
        }
        log.info("[ThirdBenefitService][afterPropertiesSet][处理类放到map中]map=" + HANDLER_MAP);
    }

    @Override
    public @Nullable List<SceneActivityDTO> consultExtraBenefitOfSpecScene(
            @NotNull ConsultSceneBenefitReqDTO consultSceneBenefitReqDTO) {
        if (HANDLER_MAP.containsKey(consultSceneBenefitReqDTO.getSceneCode())) {
            List<SceneActivityDTO> sceneActivityDTOList = Lists.newArrayList();
            for (ThirdBenefitHandler handler : HANDLER_MAP.get(consultSceneBenefitReqDTO.getSceneCode())) {
                SceneActivityDTO benefitActivity = handler.getBenefitActivity(consultSceneBenefitReqDTO);
                if (null != benefitActivity) {
                    sceneActivityDTOList.add(benefitActivity);
                }
            }
            return sceneActivityDTOList;
        }
        return null;
    }

    @Override
    public @NotNull Pair<@NotNull ReceiveSceneBenefitReqDTO,
            @Nullable ReceiveSceneBenefitReqDTO> divideExtraBenefitByReq(
            @NotNull ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO) {
        if (CollectionUtils.isEmpty(receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS())) {
            return Pair.of(receiveSceneBenefitReqDTO, null);
        }
        List<ReceiveBenefitReqDTO> thirdReqList = Lists.newArrayList();
        if (HANDLER_MAP.containsKey(receiveSceneBenefitReqDTO.getSceneCode())) {
            for (ThirdBenefitHandler handler : HANDLER_MAP.get(receiveSceneBenefitReqDTO.getSceneCode())) {
                thirdReqList.addAll(handler.getThirdReqList(receiveSceneBenefitReqDTO));
            }
        }
        if (CollectionUtils.isNotEmpty(thirdReqList)) {
            ReceiveSceneBenefitReqDTO thirdReceiveReq = BenefitDeepCopyConverter.INSTANCE.deepCopy(receiveSceneBenefitReqDTO);
            thirdReceiveReq.setReceiveBenefitReqDTOS(thirdReqList);
            receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS().removeIf(thirdReqList::contains);
            return Pair.of(receiveSceneBenefitReqDTO, thirdReceiveReq);
        }

        return Pair.of(receiveSceneBenefitReqDTO, null);
    }

    @NotNull
    @Override
    public Pair<List<BenefitDTO> /* success*/, List<BenefitDTO> /* fail */> receiveExtraBenefitOfSpecScene(@NotNull ReceiveSceneBenefitReqDTO req) {
        List<BenefitDTO> success = Lists.newArrayList();
        List<BenefitDTO> fail = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(req.getReceiveBenefitReqDTOS())) {
            if (HANDLER_MAP.containsKey(req.getSceneCode())) {
                for (ThirdBenefitHandler handler : HANDLER_MAP.get(req.getSceneCode())) {
                    Pair<List<BenefitDTO> /* success*/, List<BenefitDTO> /* fail */>
                            pair = handler.receive(req.getBaseRequestDTO().getUserId(), req.getReceiveBenefitReqDTOS());
                    success.addAll(pair.getLeft());
                    fail.addAll(pair.getRight());
                }
            }
        }
        return Pair.of(success, fail);
    }

}
