package com.ddmc.equity.domain.entity.benefit;

import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.BenefitStatusEnum;
import com.ddmc.equity.domain.converter.benefit.BenefitConverter;
import com.ddmc.equity.domain.valueobject.benefit.BenefitListFilterReqVO;
import com.ddmc.equity.dto.business.BenefitBusinessDTO;
import com.ddmc.equity.dto.business.BenefitListReqDTO;
import com.ddmc.equity.dto.business.BenefitSaveReqDTO;
import com.ddmc.equity.dto.business.provide.ProvideBenefitDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.enums.ExpireTimeUnitEnum;
import com.ddmc.equity.enums.ExpireTypeEnum;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.promocore.admin.vo.PrizeVO;
import com.google.common.collect.Lists;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Getter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class EquityBenefitEntity extends EquityBenefitDO implements Serializable {

    private static final long serialVersionUID = -1615217628976350528L;

    public static BenefitListFilterReqVO getBenefitFilterByListReq(BenefitListReqDTO req) {
        return BenefitConverter.INSTANCE.d2v(req);
    }

    public static List<BenefitBusinessDTO> listBenefitBusinessDTO(List<EquityBenefitDO> list) {
        return BenefitConverter.INSTANCE.v2ds(list);
    }

    public static EquityBenefitDO getBenefitDOBySaveReq(BenefitSaveReqDTO req) {
        return BenefitConverter.INSTANCE.srd2e(req);
    }

    public List<EquityBenefitDO> convertToNeedCreateBenefitList(List<ProvideBenefitDTO> benefitDTOList) {
        if(CollectionUtils.isEmpty(benefitDTOList)){
            return Lists.newArrayList();
        }
        List<EquityBenefitDO> needSaveList=Lists.newArrayList();
        for(ProvideBenefitDTO provideBenefitDTO:benefitDTOList){
            if(!Objects.isNull(provideBenefitDTO.getBenefitId())){
                continue;
            }
            EquityBenefitDO equityBenefitDO=new EquityBenefitEntity();
            equityBenefitDO.setBenefitType(provideBenefitDTO.getBenefitType());
            equityBenefitDO.setBenefitValue(provideBenefitDTO.getBenefitValue());
            equityBenefitDO.setName(provideBenefitDTO.getBenefitName());
            equityBenefitDO.setPublishTime(new Date());
            equityBenefitDO.setStatus(BenefitStatusEnum.PUBLISHED.getId());
            equityBenefitDO.setOpAdminId(Constants.DEFAULT_ADMIN_ID);
            equityBenefitDO.setOpAdminName(Constants.DEFAULT_ADMIN_ID);
            equityBenefitDO.setEditAdminName(Constants.DEFAULT_ADMIN_ID);
            equityBenefitDO.setEditAdminId(Constants.DEFAULT_ADMIN_ID);
            equityBenefitDO.setExpireType(ExpireTypeEnum.NONE.getStatus());
            equityBenefitDO.setExpireTimeUnit(ExpireTimeUnitEnum.NONE.getStatus());
            equityBenefitDO.setExpireTime(0L);
            equityBenefitDO.setExtInfo("");
            equityBenefitDO.setIsDelete(Constants.STATUS_NO);
            needSaveList.add(equityBenefitDO);
        }
        return needSaveList;
    }

    public List<ProvideBenefitDTO> createProvideBenefitDTOList(PrizeVO prizes){
        ProvideBenefitDTO provideBenefitDTO = new ProvideBenefitDTO();
        provideBenefitDTO.setBenefitType(BenefitTypeEnum.TICKET.getId());
        provideBenefitDTO.setBenefitValue(prizes.getReferParam());
        provideBenefitDTO.setMappingId(prizes.getReferParam());
        return Arrays.asList(provideBenefitDTO);
    }
}
