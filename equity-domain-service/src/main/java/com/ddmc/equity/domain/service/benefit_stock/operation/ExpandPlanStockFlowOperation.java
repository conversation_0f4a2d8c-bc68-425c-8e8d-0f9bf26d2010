package com.ddmc.equity.domain.service.benefit_stock.operation;

import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.enums.PlanDateTypeEnum;
import com.ddmc.equity.common.enums.StockOperationEnum;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.domain.dto.benefit_stock.StockOperationBaseParam;
import com.ddmc.equity.domain.service.benefitStockPlan.BenefitStockPlanService;
import com.ddmc.equity.domain.service.benefit_stock.AbstractStockFlowFactorHandler;
import com.ddmc.equity.domain.service.equityBenefitStockFlow.EquityBenefitStockFlowService;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockFlowDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockPlanDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitStockPlanMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Date;
import java.util.Random;

/**
 * 扩大计划库存和剩余计划库存
 */
@Component
@Slf4j
public class ExpandPlanStockFlowOperation extends AbstractStockFlowFactorHandler {

    private static final Random RANDOM = new Random();

    @Resource
    private EquityBenefitStockPlanMapper equityBenefitStockPlanMapper;

    @Autowired
    private EquityBenefitStockFlowService equityBenefitStockFlowService;

    @Autowired
    private BenefitStockPlanService benefitStockPlanService;

    @Resource
    private RedisCache redisCache;

    @Override
    public Integer getStockOperationType() {
        return StockOperationEnum.EXPEND_PLAN.getOperation();
    }

    @Override
    public Boolean handleCacheUpdate(StockOperationBaseParam msg) {
        Long strategyId = msg.getStrategyId();
        Long benefitId = msg.getBenefitId();
        Integer planDateType = msg.getPlanDateType();
        Long operationNum = msg.getStockOperation();

        // @吴文蓝，直接原子增加redis容量就行了，正常情况不会重发
        // @卢永党，计划库存和总库存分开处理
        EquityBenefitStockPlanDO equityBenefitStockPlanDO = benefitStockPlanService.queryBenefitStockPlanByIdAndKey(msg);
        int offset = offSetDate(DateUtil.parse_yyyyMMdd(equityBenefitStockPlanDO.getPlanDate()));
        boolean updateTodayRedis = incrRedis(equityBenefitStockPlanDO.getPlanDate(), strategyId, benefitId, planDateType, operationNum, offset + 2);
        if (!updateTodayRedis) {
            return false;
        }
        EquityBenefitStockFlowDO equityBenefitStockFlowDO = new EquityBenefitStockFlowDO();
        equityBenefitStockFlowDO.setId(msg.getId());
        equityBenefitStockFlowDO.setLoadCache(1);
        equityBenefitStockFlowService.updateEquityBenefitStockFlowById(equityBenefitStockFlowDO);

        return true;
    }

    /****
     * 计算时间偏差
     * @param date 需要计算的时间
     * @return 返回时间偏差
     */
    private int offSetDate(Date date){
        Date now = new Date();
        if (date.after(now)) {
            long count = cn.hutool.core.date.DateUtil.betweenDay(new Date(),date,true);
            return Integer.parseInt(count+"") ;
        }
        return 0;
    }

    private boolean incrRedis(String nowStr, Long strategyId, Long benefitId, Integer planDateType,
                              Long operationNum, long expireDayOffset) {
        Duration duration = Duration.ofDays(expireDayOffset);
        duration = duration.plusSeconds(RANDOM.nextInt(86400));
        String planBalanceCacheKey = String.format(
                CacheKeyConstants.PLAN_BALANCE_COUNT_KEY, strategyId, benefitId, planDateType, nowStr);
        String planStockCacheKey = String.format(
                CacheKeyConstants.PLAN_STOCK_COUNT_KEY, strategyId, benefitId, planDateType, nowStr);
        if (!redisCache.exist(planStockCacheKey) || !redisCache.exist(planBalanceCacheKey)) {
            log.error("当前计划库存或剩余计划库存未经过初始化 planStockCacheKey={}, planBalanceCacheKey={}", planStockCacheKey, planBalanceCacheKey);
            return false;
        }
        // 初始化场景下，都是trySet
        return redisCache.incrRedis(planBalanceCacheKey,planStockCacheKey,operationNum,duration);
    }

    /**
     * 相关的扩容要更新T和T+1的情况
     *
     * @param msg StockOperationBaseParam
     * @return Boolean
     */
    @Override
    public Boolean consumerStockOperationMsg(StockOperationBaseParam msg) {
        if (PlanDateTypeEnum.DAY.getType().equals(msg.getPlanDateType())) {
            EquityBenefitStockFlowDO equityBenefitStockFlowDO = new EquityBenefitStockFlowDO();
            equityBenefitStockFlowDO.setId(msg.getId());
            equityBenefitStockFlowDO.setLoadDb(1);
            equityBenefitStockFlowService.updateEquityBenefitStockFlowById(equityBenefitStockFlowDO);
            int row = equityBenefitStockPlanMapper.expandPlanStock(msg.getStockOperation(), msg.getStrategyId(), msg.getActivityId(),
                    msg.getBenefitId(), msg.getPlanStockId(), msg.getPlanDateType());
            if (row == 0) {
                log.error("扩容权益计划表T失败 msg:{}", msg);
                return false;
            }
        }
        return true;
    }
}
