package com.ddmc.equity.domain.service.benefit_stock;

import com.ddmc.equity.domain.dto.benefit_stock.StockOperationBaseParam;

/**
 * <AUTHOR>
 */
public abstract class AbstractStockFlowFactorHandler {

    /**
     * 库存操作类型
     *
     * @return 库存操作类型
     */
    public abstract Integer getStockOperationType();

    /**
     * 直接操作缓存
     */
    public abstract Boolean handleCacheUpdate(StockOperationBaseParam msg);

    /**
     * 消费消息，更新数据库表
     */
    public abstract Boolean consumerStockOperationMsg(StockOperationBaseParam msg);
}
