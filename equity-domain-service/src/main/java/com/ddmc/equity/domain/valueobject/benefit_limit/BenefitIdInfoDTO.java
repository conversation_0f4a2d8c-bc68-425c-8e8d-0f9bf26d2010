package com.ddmc.equity.domain.valueobject.benefit_limit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/20 16:23
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class BenefitIdInfoDTO {

    /**
     * 活动 id
     */
    private Long activityId;
    /**
     * 策略 id
     */
    private Long strategyId;
    /**
     * 权益组 id
     */
    private Long benefitGroupId;
    /**
     * 权益 id
     */
    private Long benefitId;
}
