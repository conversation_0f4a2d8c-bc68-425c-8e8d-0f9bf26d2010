package com.ddmc.equity.domain.valueobject.strategy;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class StrategyListFilterReqVO implements Serializable {
    private static final long serialVersionUID = -6075902447020211568L;

    @Builder.Default
    private Integer page = 1;
    @Builder.Default
    private Integer pageSize = 20;

    private Long id;

    private String name;

    private String creator;

    private String updater;

    private List<Integer> statuses;

    private String description;


}
