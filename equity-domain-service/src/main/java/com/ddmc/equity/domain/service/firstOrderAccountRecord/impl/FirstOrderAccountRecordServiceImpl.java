package com.ddmc.equity.domain.service.firstOrderAccountRecord.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.service.firstOrderAccountRecord.FirstOrderAccountRecordService;
import com.ddmc.equity.infra.repository.dao.FirstOrderAccountRecordDO;
import com.ddmc.equity.infra.repository.dao.mapper.FirstOrderAccountRecordMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class FirstOrderAccountRecordServiceImpl implements FirstOrderAccountRecordService {

    @Resource
    private FirstOrderAccountRecordMapper firstOrderAccountRecordMapper;


    @Override
    public int addFirstOrderAccountRecordDO(FirstOrderAccountRecordDO firstOrderAccountRecordDO) {
        return firstOrderAccountRecordMapper.insert(firstOrderAccountRecordDO);
    }

    @Override
    public int updateFirstOrderAccountRecordDOStatusByIdAndUserId(FirstOrderAccountRecordDO firstOrderAccountRecordDO) {
        UpdateWrapper<FirstOrderAccountRecordDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", firstOrderAccountRecordDO.getAccountId()).eq("user_id", firstOrderAccountRecordDO.getUserId());
        return firstOrderAccountRecordMapper.update(firstOrderAccountRecordDO, updateWrapper);
    }

    @Override
    public FirstOrderAccountRecordDO queryFirstOrderAccountRecordDOById(Long id) {
        return firstOrderAccountRecordMapper.selectById(id);
    }

    @Override
    public FirstOrderAccountRecordDO queryFirstOrderAccountRecordDOByDO(FirstOrderAccountRecordDO firstOrderAccountRecordDO) {
        QueryWrapper<FirstOrderAccountRecordDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", firstOrderAccountRecordDO.getUserId()).
                eq("req_no", firstOrderAccountRecordDO.getReqNo()).
                eq("aux_key", firstOrderAccountRecordDO.getReqNo()).
                eq("operate_type", firstOrderAccountRecordDO.getOperateType()).
                eq("status", firstOrderAccountRecordDO.getStatus())
        ;
        return firstOrderAccountRecordMapper.selectOne(queryWrapper);
    }

    @Override
    public FirstOrderAccountRecordDO queryFirstOrderAccountRecordDOByDO(FirstOrderAccountRecordDO firstOrderAccountRecordDO, List<Integer> statusList) {
        QueryWrapper<FirstOrderAccountRecordDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", firstOrderAccountRecordDO.getUserId()).
                eq("req_no", firstOrderAccountRecordDO.getReqNo()).
                eq("aux_key", firstOrderAccountRecordDO.getReqNo()).
                eq("operate_type", firstOrderAccountRecordDO.getOperateType()).
                in("status", statusList);
        return firstOrderAccountRecordMapper.selectOne(queryWrapper);
    }

    @Override
    public int updateRecord(String uid, Long id, int status, String serialNumber, EquityRpcDto equityRpcDto) {
        UpdateWrapper<FirstOrderAccountRecordDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("user_id", uid);
        updateWrapper.eq("id", id);
        updateWrapper.set("status", status);
        if (StatusEnum.FAIL.getCode() == status) {
            updateWrapper.set("aux_key", serialNumber + id);
        }
        if (!Objects.isNull(equityRpcDto)) {
            if (StringUtils.isNotBlank(equityRpcDto.getCode())) {
                updateWrapper.set("rpc_code", equityRpcDto.getCode());
            }
            if (StringUtils.isNotBlank(equityRpcDto.getMessage())) {
                updateWrapper.set("rpc_msg", equityRpcDto.getMessage());
            }
        }
        return firstOrderAccountRecordMapper.update(null, updateWrapper);
    }

    @Override
    public boolean updateRecordStatus(String uid, Long accountRecordId, Long accountId, Integer status, String serialNumber, EquityRpcDto equityRpcDto) {
        UpdateWrapper<FirstOrderAccountRecordDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("user_id", uid);
        updateWrapper.eq("id", accountRecordId);
        updateWrapper.set("status", status);
        if (Objects.equals(StatusEnum.FAIL.getCode(), status)) {
            updateWrapper.set("aux_key", serialNumber + accountRecordId);
        }
        if (!Objects.isNull(accountId)) {
            updateWrapper.set("account_id", accountId);
        }
        if (!Objects.isNull(equityRpcDto)) {
            if (StringUtils.isNotBlank(equityRpcDto.getCode())) {
                updateWrapper.set("rpc_code", equityRpcDto.getCode());
            }
            if (StringUtils.isNotBlank(equityRpcDto.getMessage())) {
                updateWrapper.set("rpc_msg", equityRpcDto.getMessage());
            }
        }
        return firstOrderAccountRecordMapper.update(null, updateWrapper) != 0;
    }

    @Override
    public boolean updateRecordByIdAndUserId(Long id, String uid, String serialNumber, Long accountId, Integer status, String code, String message) {
        UpdateWrapper<FirstOrderAccountRecordDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("user_id", uid);
        updateWrapper.eq("id", id);
        updateWrapper.set("status", status);
        if (StatusEnum.FAIL.getCode().equals(status)) {
            updateWrapper.set("aux_key", serialNumber + id);
        }
        if (!Objects.isNull(accountId)) {
            updateWrapper.set("account_id", accountId);
        }
        if (StringUtils.isNotBlank(code)) {
            updateWrapper.set("rpc_code", code);
        }
        if (StringUtils.isNotBlank(message)) {
            updateWrapper.set("rpc_msg", message);
        }
        return firstOrderAccountRecordMapper.update(null, updateWrapper) != 0;
    }

    @Override
    public FirstOrderAccountRecordDO queryFirstOrderAccountRecordDOByUserIdAndOperateTypeAndReqNo(FirstOrderAccountRecordDO firstOrderAccountRecordDO) {
        QueryWrapper<FirstOrderAccountRecordDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", firstOrderAccountRecordDO.getUserId()).
                eq("req_no", firstOrderAccountRecordDO.getReqNo()).
                eq("operate_type", firstOrderAccountRecordDO.getOperateType()).
                eq("status", firstOrderAccountRecordDO.getStatus()).
                eq("aux_key",firstOrderAccountRecordDO.getReqNo()).
                eq("account_id", firstOrderAccountRecordDO.getAccountId());
        return firstOrderAccountRecordMapper.selectOne(queryWrapper);
    }

    @Override
    public boolean updateFirstOrderAccountRecordAuxKeyByTagAndOperateType(String userId, Integer operateType) {
        return firstOrderAccountRecordMapper.updateFirstOrderAccountRecordAuxKey(userId,operateType) > 0 ;
    }

    @Override
    public FirstOrderAccountRecordDO queryFirstOrderAccountRecordDOByUserIdAndReqNo(String userId, String reqNo,Integer operateType) {
        FirstOrderAccountRecordDO firstOrderAccountRecordDO = firstOrderAccountRecordMapper.selectFirstOrderAccountRecordMapperByUserIdAndReqNoAndOperateType(userId,reqNo,operateType);
        if(Objects.nonNull(firstOrderAccountRecordDO)){
            return firstOrderAccountRecordDO;
        }
        //TODO 首单免运费全部切量的时候这个验证需要去掉
        return firstOrderAccountRecordMapper.selectFirstOrderAccountRecordMapperByUserIdAndOperateType(userId,operateType);
    }
}
