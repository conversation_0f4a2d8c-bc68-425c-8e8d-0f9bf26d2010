package com.ddmc.equity.domain.dto.rule;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/30 16:22
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class ActivityReceiveLimitFactorValueDTO {

    /**
     * 限制领取数量
     */
    private Long limitCount;
    /**
     * 已领取数量
     */
    private Long receiveCount;
    /**
     * 是否可领取
     */
    private boolean isCanReceive;
}
