package com.ddmc.equity.domain.service.low_stock_alarm.impl;

import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.util.business.BenefitUtil;
import com.ddmc.equity.domain.dto.benefit_stock.LowStockAlarmContextDTO;
import com.ddmc.product.operation.workbench.common.enums.NotificationIdempotentStrategy;
import com.ddmc.product.operation.workbench.common.enums.NotificationPageButtonType;
import com.ddmc.product.operation.workbench.common.enums.NotificationPersonCheckRuleType;
import com.ddmc.product.operation.workbench.common.enums.NotificationPriorityEnum;
import com.ddmc.product.operation.workbench.common.enums.NotificationType;
import com.ddmc.product.operation.workbench.common.enums.NotificationWeChatLinkType;
import com.ddmc.product.operation.workbench.request.notification.WorkbenchNotificationDTO;
import com.ddmc.product.operation.workbench.request.notification.WorkbenchNotificationPageInfoDTO;
import com.ddmc.product.operation.workbench.request.notification.WorkbenchNotificationPersonDTO;
import com.ddmc.product.operation.workbench.request.notification.WorkbenchNotificationReachRequest;
import com.ddmc.product.operation.workbench.request.notification.WorkbenchNotificationWeChatInfoDTO;
import com.ddmc.ugc.commons.util.UUIDUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 创建人库存不足提醒实现类
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2024/12/19 10:00
 * @description 给对应创建人推送库存不足提醒的实现类
 */
@Slf4j
@Component("creatorLowStockAlarm")
public class CreatorLowStockAlarm extends AbstractLowStockAlarm {

    @Override
    protected String getLowStockAlarmTimesKey(LowStockAlarmContextDTO contextDTO, String hitLowStockAlarmType) {
        String benefitUni = BenefitUtil.getBenefitUni(contextDTO.getActivityId(), contextDTO.getStrategyId(),
                contextDTO.getBenefitId());
        return String.format(CacheKeyConstants.LOW_STOCK_ALARM_TIMES_KEY, "creator", benefitUni, hitLowStockAlarmType);
    }

    @Override
    protected String buildAlarmContent(LowStockAlarmContextDTO contextDTO) {
        return String.format("**%s，活动奖励库存预警**\n" +
                        "> **活动名称：** %s\n" +
                        "> **活动 ID：** %s\n" +
                        "> **奖品类型：** %s\n" +
                        "> **奖品信息：** %s\n" +
                        "%s%s" +
                        "> **操作人：** @%s",
                contextDTO.buildDisplaySceneCode(),
                contextDTO.getActivityName(),
                contextDTO.buildDisplayActivityId(),
                contextDTO.buildDisplayBenefitType(),
                contextDTO.buildDisplayBenefitInfo(),
                contextDTO.buildStockContentStr(),
                contextDTO.buildPlanStockContentStr(),
                contextDTO.buildCreatorName());
    }

    @Override
    protected WorkbenchNotificationReachRequest buildWorkbenchNotificationRequest(LowStockAlarmContextDTO contextDTO) {
        WorkbenchNotificationReachRequest request = new WorkbenchNotificationReachRequest();
        request.setWorkbenchNotificationList(buildWorkbenchNotifications(contextDTO));
        return request;
    }

    /**
     * 构建工作台通知列表
     *
     * @param contextDTO 提醒上下文
     */
    private List<WorkbenchNotificationDTO> buildWorkbenchNotifications(LowStockAlarmContextDTO contextDTO) {
        WorkbenchNotificationDTO workbenchNotificationDTO = new WorkbenchNotificationDTO();
        // 活动库存不足
        workbenchNotificationDTO.setBizType(18);
        workbenchNotificationDTO.setPersonList(buildWorkbenchPersons(contextDTO));
        // 1-强校验，2-弱校验
        workbenchNotificationDTO.setPersonCheckRule(NotificationPersonCheckRuleType.FORCE_CHECK.getCode());
        // 1-预警，2-消息通知
        workbenchNotificationDTO.setType(NotificationType.EARLY_WARNING.getCode());
        workbenchNotificationDTO.setPageInfoDTO(buildWorkbenchPageInfo(contextDTO));
        workbenchNotificationDTO.setNotifyWeChat(true);
        workbenchNotificationDTO.setWeChatInfoDTO(buildWorkbenchWeChatInfo(contextDTO));
        workbenchNotificationDTO.setPriority(NotificationPriorityEnum.P0.getCode());
        workbenchNotificationDTO.setNotifyTime(System.currentTimeMillis());
        // 1-业务方唯一流水号幂等，2-工作台根据消息结构体所有参数生成流水号做幂等
        workbenchNotificationDTO.setIdempotentStrategy(NotificationIdempotentStrategy.BIZ_SERIAL_NUMBER.getCode());
        workbenchNotificationDTO.setSerialNumber(UUIDUtils.getUUID("stockAlarm_"));
        return Lists.newArrayList(workbenchNotificationDTO);
    }

    /**
     * 构建工作台通知人员列表
     *
     * @param contextDTO 提醒上下文
     */
    private Set<WorkbenchNotificationPersonDTO> buildWorkbenchPersons(LowStockAlarmContextDTO contextDTO) {
        WorkbenchNotificationPersonDTO personDTO = new WorkbenchNotificationPersonDTO();
        personDTO.setSsoId(contextDTO.getCreatedById());
        return Sets.newHashSet(personDTO);
    }

    /**
     * 构建工作台页面信息
     *
     * @param contextDTO 提醒上下文
     */
    private WorkbenchNotificationPageInfoDTO buildWorkbenchPageInfo(LowStockAlarmContextDTO contextDTO) {
        String title = buildWorkbenchAlarmTitle(contextDTO);
        String content = buildWorkBenchAlarmContent(contextDTO);
        String jumpLink = contextDTO.getLowStockAlarmRule().getJumpLink();

        WorkbenchNotificationPageInfoDTO pageInfoDTO = new WorkbenchNotificationPageInfoDTO();
        pageInfoDTO.setTitle(title);
        pageInfoDTO.setContent(content);
        // 1-不展示操作按钮，2-展示操作按钮
        pageInfoDTO.setButtonType(NotificationPageButtonType.HIDE_OPERATE_BUTTON.getCode());
        if (StringUtils.isNotBlank(jumpLink)) {
            pageInfoDTO.setButtonType(NotificationPageButtonType.SHOW_OPERATE_BUTTON.getCode());
            pageInfoDTO.setButtonText("去处理");
            pageInfoDTO.setButtonLink(jumpLink);
        }
        return pageInfoDTO;
    }

    /**
     * 构建工作台微信信息
     *
     * @param contextDTO 提醒上下文
     */
    private WorkbenchNotificationWeChatInfoDTO buildWorkbenchWeChatInfo(LowStockAlarmContextDTO contextDTO) {
        String title = buildWorkbenchAlarmTitle(contextDTO);
        String content = buildWorkBenchAlarmContent(contextDTO);
        String jumpLink = contextDTO.getLowStockAlarmRule().getJumpLink();

        WorkbenchNotificationWeChatInfoDTO weChatInfoDTO = new WorkbenchNotificationWeChatInfoDTO();
        weChatInfoDTO.setTitle(title);
        weChatInfoDTO.setContent(content);
        // 1-跳转到工作台通知中心详情，2-自定义跳转链接
        weChatInfoDTO.setLinkType(NotificationWeChatLinkType.NOTIFICATION_CENTER_DETAIL.getCode());
        if (StringUtils.isNotBlank(jumpLink)) {
            weChatInfoDTO.setLinkType(NotificationWeChatLinkType.CUSTOM.getCode());
            weChatInfoDTO.setButtonText("去处理");
            weChatInfoDTO.setLink(jumpLink);
        }
        return weChatInfoDTO;
    }

    /**
     * 构建工作台提醒标题
     *
     * @param contextDTO 提醒上下文
     */
    private String buildWorkbenchAlarmTitle(LowStockAlarmContextDTO contextDTO) {
        return String.format("%s，活动奖励库存预警", contextDTO.buildDisplaySceneCode());
    }

    /**
     * 构建工作台提醒内容
     *
     * @param contextDTO 提醒上下文
     */
    private String buildWorkBenchAlarmContent(LowStockAlarmContextDTO contextDTO) {
        return String.format("您配置的【%s】【%s】，【%s】【%s】当前剩余库存为【%s】，库存上限为【%s】%s。",
                contextDTO.buildDisplayActivityId(),
                contextDTO.getActivityName(),
                contextDTO.buildDisplayBenefitType(),
                contextDTO.buildDisplayBenefitInfo(),
                contextDTO.getBalanceStock(),
                contextDTO.getStock(),
                contextDTO.buildWorkbenchPlanStockContentStr());
    }
}
