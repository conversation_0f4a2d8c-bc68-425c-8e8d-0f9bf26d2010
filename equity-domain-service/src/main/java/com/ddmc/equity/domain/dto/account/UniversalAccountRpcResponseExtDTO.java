package com.ddmc.equity.domain.dto.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/23 15:02
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("通用权益账户-外部 rpc 调用请求出参拓展信息")
public class UniversalAccountRpcResponseExtDTO {

    @ApiModelProperty("用户优惠券 id")
    private String userTicketId;

    @ApiModelProperty("用户优惠券包 id")
    private String userTicketPackageId;

    @ApiModelProperty("会员充值订单号")
    private String vipChargeOrderNumber;

    @ApiModelProperty("余额充值卡号")
    private String balanceCardNumber;

    @ApiModelProperty("发放权益的过期时间。格式 yyyy-MM-dd HH:mm:ss")
    private String benefitExpireTime;
}
