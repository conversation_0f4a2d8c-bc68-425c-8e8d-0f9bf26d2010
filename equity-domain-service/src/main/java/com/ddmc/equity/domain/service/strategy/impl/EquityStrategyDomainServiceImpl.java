package com.ddmc.equity.domain.service.strategy.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.exception.ApiBusinessException;
import com.ddmc.equity.common.util.LongUtils;
import com.ddmc.equity.domain.entity.strategy.EquityStrategyEntity;
import com.ddmc.equity.domain.service.strategy.EquityStrategyDomainService;
import com.ddmc.equity.domain.valueobject.strategy.StrategyListFilterReqVO;
import com.ddmc.equity.domain.valueobject.strategy.StrategyListFilterRespVO;
import com.ddmc.equity.enums.StrategyStatusEnum;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.infra.repository.dao.EquityStrategyDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityStrategyMapper;
import com.ddmc.equity.model.dto.StrategyCacheBaseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EquityStrategyDomainServiceImpl implements EquityStrategyDomainService {

    @Resource
    private EquityStrategyMapper equityStrategyMapper;

    @Resource
    private LocalCacheManager localCacheManager;

    @Nullable
    @Override
    public StrategyListFilterRespVO getListedStrategiesRespByFilter(@NotNull StrategyListFilterReqVO filter) {
        @NotNull QueryWrapper<EquityStrategyDO> wrapper = getStrategyListQueryWrapper(filter);
        IPage<EquityStrategyDO> strategyDataPage = new Page<>(filter.getPage(), filter.getPageSize());
        strategyDataPage = equityStrategyMapper.listStrategiesByCondition(strategyDataPage, wrapper);
        if (null == strategyDataPage) {
            return null;
        }
        List<EquityStrategyDO> records = strategyDataPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new StrategyListFilterRespVO();
        } else {
            return StrategyListFilterRespVO.builder().list(records).total(strategyDataPage.getTotal()).build();
        }
    }

    private QueryWrapper<EquityStrategyDO> getStrategyListQueryWrapper(@NotNull StrategyListFilterReqVO req) {
        QueryWrapper<EquityStrategyDO> wrapper = new QueryWrapper<>();
        wrapper.and(LongUtils.isTrue(req.getId()) || StringUtils.isNotEmpty(req.getName()),
                        e -> e.eq(LongUtils.isTrue(req.getId()), "id", req.getId())
                                .or(StringUtils.isNotEmpty(req.getName()),
                                        e1 -> e1.like("strategy_name", req.getName())))
                .in(CollectionUtils.isNotEmpty(req.getStatuses()), "status", req.getStatuses())
                .eq(StringUtils.isNotEmpty(req.getCreator()), "op_admin_name", req.getCreator())
                .eq(StringUtils.isNotEmpty(req.getUpdater()), "edit_admin_name", req.getUpdater())
                .eq("is_delete", CommonEnum.INTEGER_BOOL.NO.getCode());
        return wrapper.orderByDesc("create_time");
    }

    /**
     * 保存数据
     *
     * @param needSaveDO
     * @return 返回受影响的数据
     */
    @Override
    public int save(EquityStrategyDO needSaveDO) {
        if (LongUtils.isTrue(needSaveDO.getId())) {
            return equityStrategyMapper.updateById(needSaveDO);
        } else {
            return equityStrategyMapper.insert(needSaveDO);
        }
    }

    @Override
    public List<EquityStrategyDO> queryAllUnOffline(int limitCount) {
        QueryWrapper<EquityStrategyDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("status", Arrays.asList(1, 0));
        queryWrapper.last(" order by activity_id desc limit " + limitCount);
        return equityStrategyMapper.selectList(queryWrapper);
    }


    @Override
    public List<Long> queryAllUnOfflineStrategyIds(int limitCount) {
        QueryWrapper<EquityStrategyDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(" id ");
        queryWrapper.in("status", Arrays.asList(1, 0));
        queryWrapper.last(" order by activity_id desc limit " + limitCount);
        return equityStrategyMapper.selectObjs(queryWrapper).stream().map(x -> Long.parseLong(x.toString())).collect(Collectors.toList());
    }


    @Override
    public List<EquityStrategyDO> queryAllUnOfflineByActivityIdList(List<Long> activityList) {
        QueryWrapper<EquityStrategyDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("status", Arrays.asList(1, 0));
        queryWrapper.in("activity_id", activityList);
        return equityStrategyMapper.selectList(queryWrapper);
    }

    @Override
    public List<EquityStrategyDO> queryAllUnOffline() {
        QueryWrapper<EquityStrategyDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("status", StrategyStatusEnum.OFFLINE.getId());
        return equityStrategyMapper.selectList(queryWrapper);
    }

    @Override
    public List<EquityStrategyDO> queryAllUnOfflineByActivityId(Long activityId) {
        QueryWrapper<EquityStrategyDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("status", Arrays.asList(1, 0));
        queryWrapper.eq("activity_id", activityId);
        return equityStrategyMapper.selectList(queryWrapper);
    }

    @Override
    public Long insertOrUpdate(EquityStrategyDO equityStrategyDO) {
        if (Objects.isNull(equityStrategyDO)) {
            throw new ApiBusinessException(ExceptionEnum.COMMON_ERROR);
        }
        Long ret = equityStrategyMapper.insertOrUpdate(equityStrategyDO);
        if (ret > 0) {
            return equityStrategyDO.getId();
        } else {
            throw new ApiBusinessException(ExceptionEnum.COMMON_ERROR);
        }
    }

    @Override
    public boolean updateBatch(List<EquityStrategyDO> equityStrategyDOList) {
        return equityStrategyMapper.updateBatch(equityStrategyDOList) == equityStrategyDOList.size();
    }

    /**
     * 软删除
     *
     * @param strategyId
     * @return
     */
    @Override
    public int deleteById(Long strategyId) {
        return equityStrategyMapper.deleteById(strategyId);
    }


    @Override
    public List<EquityStrategyDO> queryEquityStrategyDOByActivityId(Long activityId) {
        QueryWrapper<EquityStrategyDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("status", Arrays.asList(1, 0));
        queryWrapper.eq("activity_id", activityId);
        return equityStrategyMapper.selectList(queryWrapper);
    }

    @Override
    public EquityStrategyDO queryByIdTryCacheFirst(Long strategyId) {
        if (Objects.isNull(strategyId)) {
            return null;
        }
        //缓存中查询策略信息
        StrategyCacheBaseDto strategyCacheBaseDto = localCacheManager.selectStrategyById(strategyId);
        if (Objects.nonNull(strategyCacheBaseDto)) {
            EquityStrategyEntity equityStrategyEntity = new EquityStrategyEntity();
            return equityStrategyEntity.convertToEquityStrategyDO(strategyCacheBaseDto);
        }
        return equityStrategyMapper.selectById(strategyId);
    }

    @Override
    public List<Long> queryAllUnOfflineIds(Collection<Long> ids) {
        QueryWrapper<EquityStrategyDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("status", StrategyStatusEnum.OFFLINE.getId());
        queryWrapper.in("id", ids);
        List<EquityStrategyDO> equityStrategyDOS = equityStrategyMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(equityStrategyDOS)) {
            return equityStrategyDOS.stream().map(EquityStrategyDO::getId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public void offlineByActivityId(Long activityId) {
        equityStrategyMapper.update(null, new LambdaUpdateWrapper<EquityStrategyDO>()
                .set(EquityStrategyDO::getStatus, StrategyStatusEnum.OFFLINE.getId())
                .eq(EquityStrategyDO::getActivityId, activityId));
    }

    /**
     * 根据ID查询
     *
     * @param strategyId
     * @return
     */
    public EquityStrategyDO getStrategyById(Long strategyId) {
        return equityStrategyMapper.selectById(strategyId);
    }

    @Override
    public EquityStrategyDO queryByActivityIdAndExternalId(Long activityId, String externalId) {
        QueryWrapper<EquityStrategyDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("activity_id", activityId);
        queryWrapper.eq("external_id", externalId);
        return equityStrategyMapper.selectOne(queryWrapper);
    }

    @Override
    public int update(EquityStrategyDO equityStrategyDO) {
        return equityStrategyMapper.updateById(equityStrategyDO);
    }

    @Override
    public List<EquityStrategyDO> queryStrategiesByIds(List<Long> strategyIds, List<Integer> statuses) {
        if (CollectionUtils.isEmpty(strategyIds)) {
            log.warn("queryStrategiesByIds strategyIds isNull");
            return null;
        }
        LambdaQueryWrapper<EquityStrategyDO> wrapper = Wrappers.<EquityStrategyDO>lambdaQuery()
                .in(EquityStrategyDO::getId, strategyIds)
                .in(CollectionUtils.isNotEmpty(statuses), EquityStrategyDO::getStatus, statuses);
        return equityStrategyMapper.selectList(wrapper);
    }
}
