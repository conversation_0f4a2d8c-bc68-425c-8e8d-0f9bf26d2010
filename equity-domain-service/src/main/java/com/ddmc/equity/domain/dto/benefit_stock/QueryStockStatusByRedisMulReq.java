package com.ddmc.equity.domain.dto.benefit_stock;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class QueryStockStatusByRedisMulReq {

    @NotNull
    private List<BenefitStockLimitReq> reqList;
    @NotNull
    @Builder.Default
    private Date date = new Date();
}
