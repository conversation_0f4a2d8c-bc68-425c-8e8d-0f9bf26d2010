package com.ddmc.equity.domain.service.ticketAccountRecord.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.domain.service.ticketAccountRecord.TicketAccountRecordService;
import com.ddmc.equity.infra.repository.dao.TicketAccountRecordDO;
import com.ddmc.equity.infra.repository.dao.mapper.TicketAccountRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class TicketAccountRecordServiceImpl implements TicketAccountRecordService {
    @Autowired
    private TicketAccountRecordMapper ticketAccountRecordMapper;


    /**
     * 记录保存
     *
     * @param recordDO 记录
     * @return 结果
     */
    @Override
    public int save(TicketAccountRecordDO recordDO) {
        if (Objects.isNull(recordDO)) {
            return 0;
        }
        return ticketAccountRecordMapper.insert(recordDO);
    }

    /**
     * 更新操作记录
     *
     * @param recordId                   操作记录id
     * @param uid                        用户id
     * @param status                     状态
     * @param accountId                  账户记录id
     * @param innerSerialNumber          内部流水号
     * @param userTicketId               用户券id
     * @param serialNumber               请求流水号
     * @param code                       错误码
     * @param msg                        错误描述
     * @param freezeReceiveLimitResultId 冻结频次id
     * @return 更新结果
     */
    @Override
    public boolean updateRecord(Long recordId, String uid, Integer status,
                                Long accountId, String innerSerialNumber,
                                String userTicketId, String serialNumber,
                                String code, String msg, Long freezeReceiveLimitResultId,
                                @Nullable Integer sendTicketScene) {
        try {
            UpdateWrapper<TicketAccountRecordDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("user_id", uid);
            updateWrapper.eq("id", recordId);
            updateWrapper.set("status", status);
            if (Objects.equals(StatusEnum.FAIL.getCode(), status)) {
                updateWrapper.set("aux_key", serialNumber + recordId);
            }
            if (StringUtils.isNotBlank(code)) {
                updateWrapper.set("rpc_code", code);
            }
            if (StringUtils.isNotBlank(msg)) {
                updateWrapper.set("rpc_msg", msg);
            }
            if (Objects.nonNull(accountId)) {
                updateWrapper.set("account_id", accountId);
            }
            if (StringUtils.isNotBlank(userTicketId)) {
                updateWrapper.set("user_ticket_id", userTicketId);
            }
            if (StringUtils.isNotBlank(innerSerialNumber)) {
                updateWrapper.set("inner_serial_number", innerSerialNumber);
            }
            if (Objects.nonNull(freezeReceiveLimitResultId)) {
                updateWrapper.set("freeze_receive_limit_id", freezeReceiveLimitResultId);
            }
            if (null != sendTicketScene) {
                updateWrapper.set("send_ticket_scene", sendTicketScene);
            }
            int result = ticketAccountRecordMapper.update(null, updateWrapper);
            return result == 1;
        } catch (Exception e) {
            log.error("TicketAccountRecordServiceImpl.updateRecord.e.userId={};recordId={}", uid, recordId, e);
        }
        return false;
    }

    /**
     * 根据主键查询记录
     *
     * @param uid          用户id
     * @param serialNumber 请求流水号
     * @param strategyId   策略id
     * @param equityId     权益id
     * @param operateType  操作类型
     * @param auxKey       辅助健
     * @return 操作记录
     */
    @Override
    public TicketAccountRecordDO selectByUk(String uid, String serialNumber, Long strategyId, Long equityId, int operateType, String auxKey) {
        QueryWrapper<TicketAccountRecordDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", uid).
                eq("req_no", serialNumber).
                eq("strategy_id", strategyId).
                eq("benefit_id", equityId).
                eq("operate_type", operateType)
                .eq("aux_key", auxKey);
        return ticketAccountRecordMapper.selectOne(queryWrapper);
    }

    /**
     * 按照状态查询权益操作记录
     *
     * @param uid                  用户id
     * @param activityId           活动id
     * @param strategyId           策略id
     * @param benefitId            权益id
     * @param statusList           状态集合
     * @param operateType          操作类型
     * @param freezeReceiveLimitId 冻结频次id
     * @return 操作记录
     */
    @Override
    public List<TicketAccountRecordDO> select(String uid, Long activityId, Long strategyId, Long benefitId, List<Integer> statusList, int operateType, Long freezeReceiveLimitId) {
        QueryWrapper<TicketAccountRecordDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", uid).
                eq("strategy_id", strategyId).
                eq("activity_id", activityId).
                eq("benefit_id", benefitId).
                eq("operate_type", operateType)
                .eq("freeze_receive_limit_id", freezeReceiveLimitId)
                .in("status", statusList);
        return ticketAccountRecordMapper.selectList(queryWrapper);
    }


    /**
     * 更新记录状态
     *
     * @param recordId     记录id
     * @param uid          用户id
     * @param status       状态
     * @param serialNumber 流水号
     * @param code         错误码
     * @param message      错误信息
     */
    @Override
    public boolean updateRecordStatus(Long recordId, String uid, Integer status, String serialNumber, String code,
                                      String message, @Nullable Long freezeReceiveLimitId) {
        try {
            UpdateWrapper<TicketAccountRecordDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("user_id", uid);
            updateWrapper.eq("id", recordId);
            updateWrapper.set("status", status);
            if (null != freezeReceiveLimitId) {
                updateWrapper.set("freeze_receive_limit_id", freezeReceiveLimitId);
            }
            if (Objects.equals(StatusEnum.FAIL.getCode(), status)) {
                updateWrapper.set("aux_key", serialNumber + recordId);
            }
            if (StringUtils.isNotBlank(code)) {
                updateWrapper.set("rpc_code", code);
            }
            if (StringUtils.isNotBlank(message)) {
                updateWrapper.set("rpc_msg", message);
            }
            int result = ticketAccountRecordMapper.update(null, updateWrapper);
            return result == 1;
        } catch (Exception e) {
            log.error("TicketAccountRecordServiceImpl.updateRecordStatus.e.userId={};recordId={}", uid, recordId, e);
        }
        return false;
    }

    /**
     * 更新字段
     *
     * @param uid          用户id
     * @param recordId     记录id
     * @param serialNumber 流水号
     * @param userTicketId 用户券id
     * @param isRelated    是否关联
     * @param relatedReqNo 关联单号
     * @param status       状态
     * @param code         错误码
     * @param msg          错误信息
     */
    @Override
    public boolean updateRecordColumn(String uid, Long recordId, String serialNumber, String userTicketId, Integer isRelated, String relatedReqNo, Integer status,
                                      String code, String msg, Integer sendTicketScene) {
        try {
            UpdateWrapper<TicketAccountRecordDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("user_id", uid);
            updateWrapper.eq("id", recordId);
            updateWrapper.set("status", status);
            if (Objects.equals(StatusEnum.FAIL.getCode(), status)) {
                updateWrapper.set("aux_key", serialNumber + recordId);
            }
            if (StringUtils.isNotBlank(code)) {
                updateWrapper.set("rpc_code", code);
            }
            if (StringUtils.isNotBlank(msg)) {
                updateWrapper.set("rpc_msg", msg);
            }
            if (Objects.nonNull(isRelated)) {
                updateWrapper.set("is_related", isRelated);
            }
            if (StringUtils.isNotBlank(relatedReqNo)) {
                updateWrapper.set("related_req_no", relatedReqNo);
            }
            if (StringUtils.isNotBlank(userTicketId)) {
                updateWrapper.set("user_ticket_id", userTicketId);
            }
            if (null != sendTicketScene) {
                updateWrapper.set("send_ticket_scene", sendTicketScene);
            }
            int result = ticketAccountRecordMapper.update(null, updateWrapper);
            return result == 1;
        } catch (Exception e) {
            log.error("TicketAccountRecordServiceImpl.updateRecordColumn.e.userId={};recordId={}", uid, recordId, e);
        }
        return false;
    }

    @Override
    public List<TicketAccountRecordDO> queryTicketAccountRecordDOListByTicketAccountRecordDO(TicketAccountRecordDO ticketAccountRecordDO) {
//        ticketAccountRecordDO
//        QueryWrapper<TicketAccountRecordDO> queryWrapper = new QueryWrapper<>();
//        if(Objects.nonNull(ticketAccountRecordDO.getUserId()))
//        return ticketAccountRecordMapper.selectList(queryWrapper);
        return null;
    }

    @Override
    public void updateRecordFreezeReceiveLimitId(Long recordId, Long freezeReceiveLimitId, String userId) {
        ticketAccountRecordMapper.update(null, new LambdaUpdateWrapper<TicketAccountRecordDO>()
                .set(TicketAccountRecordDO::getFreezeReceiveLimitId, freezeReceiveLimitId)
                .eq(TicketAccountRecordDO::getUserId, userId)
                .eq(TicketAccountRecordDO::getId, recordId));
    }
}
