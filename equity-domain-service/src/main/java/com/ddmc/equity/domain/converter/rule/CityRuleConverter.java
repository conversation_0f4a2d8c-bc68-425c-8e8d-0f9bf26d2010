package com.ddmc.equity.domain.converter.rule;

import com.ddmc.equity.domain.dto.rule.condition.CityRuleDTO;
import com.ddmc.equity.domain.valueobject.rule.condition.CityRuleVO;
import com.ddmc.equity.dto.business.rule.CitiesDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface CityRuleConverter {
    CityRuleConverter INSTANCE = Mappers.getMapper(CityRuleConverter.class);

    CityRuleVO convertDTOToVO(CitiesDTO citiesDTO);

    CityRuleDTO convertDTOToCityRuleDTO(CitiesDTO citiesDTO);
}
