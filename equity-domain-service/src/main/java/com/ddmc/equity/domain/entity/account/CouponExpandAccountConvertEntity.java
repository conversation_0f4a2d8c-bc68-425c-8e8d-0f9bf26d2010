package com.ddmc.equity.domain.entity.account;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.constant.RuleLimitInfoKeys;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.enums.SubAccountRecordExtKeys;
import com.ddmc.equity.common.enums.SubAccountStatusEnum;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.infra.repository.dao.CouponExpandAccountCollectDO;
import com.ddmc.equity.infra.repository.dao.CouponExpandAccountDO;
import com.ddmc.equity.infra.repository.dao.CouponExpandAccountRecordDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.vo.CouponExpandReceiveVO;
import com.ddmc.vouchercore.client.request.ExpandTicketRequest;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/26 19:24
 * @description
 */
public class CouponExpandAccountConvertEntity {

    public static CouponExpandAccountDO createAccountDO(EquityAccountContext equityAccountContext) {
        String expandUserTicketId = getExpandUserTicketId(equityAccountContext);
        CouponExpandAccountDO couponExpandAccountDO = new CouponExpandAccountDO();
        couponExpandAccountDO.setActivityId(equityAccountContext.getActivityId());
        couponExpandAccountDO.setStrategyId(equityAccountContext.getStrategyId());
        couponExpandAccountDO.setBenefitGroupId(equityAccountContext.getBenefitGroupId());
        couponExpandAccountDO.setBenefitId(equityAccountContext.getBenefitId());
        couponExpandAccountDO.setUserId(equityAccountContext.getUid());
        couponExpandAccountDO.setMasterTicketId(equityAccountContext.getStrategyExternalId());
        couponExpandAccountDO.setMasterUserTicketId(equityAccountContext.getMasterUserTicketId());
        couponExpandAccountDO.setExpandTicketId(equityAccountContext.getEquityValue());
        couponExpandAccountDO.setExpandUserTicketId(expandUserTicketId);
        couponExpandAccountDO.setStatus(SubAccountStatusEnum.UNUSED.getCode());
        couponExpandAccountDO.setLastReqNo(equityAccountContext.getSerialNumber());
        return couponExpandAccountDO;
    }

    public static List<AccountInfoDTO> convertToAccountInfoDTOList(List<CouponExpandAccountDO> couponExpandAccountDOList) {

        return couponExpandAccountDOList.stream().map(x -> AccountInfoDTO.builder().activityId(x.getActivityId())
                .strategyId(x.getStrategyId())
                .benefitGroupId(x.getBenefitGroupId())
                .benefitId(x.getBenefitId())
                .accountId(x.getId())
                .activityId(x.getActivityId())
                .userId(x.getUserId())
                .status(x.getStatus())
                .createTime(x.getCreateTime())
                .lastReqNo(x.getLastReqNo())
                .build()).collect(Collectors.toList());
    }

    public static CouponExpandAccountRecordDO createAccountRecordDO(Integer operateType, EquityAccountContext equityAccountContext) {
        Map<String, Object> extMap = Maps.newHashMap();
        extMap.put(SubAccountRecordExtKeys.CITY_CODE, equityAccountContext.getCityCode());
        String reqNo = equityAccountContext.getSerialNumber();
        CouponExpandAccountRecordDO couponExpandAccountRecordDO = new CouponExpandAccountRecordDO();
        couponExpandAccountRecordDO.setAccountId(equityAccountContext.getAccountId());
        couponExpandAccountRecordDO.setUserId(equityAccountContext.getUid());
        couponExpandAccountRecordDO.setMasterTicketId(equityAccountContext.getStrategyExternalId());
        couponExpandAccountRecordDO.setMasterUserTicketId(equityAccountContext.getMasterUserTicketId());
        couponExpandAccountRecordDO.setExpandTicketId(equityAccountContext.getEquityValue());
        couponExpandAccountRecordDO.setOperateType(operateType);
        couponExpandAccountRecordDO.setStatus(StatusEnum.INIT.getCode());
        couponExpandAccountRecordDO.setAppId(equityAccountContext.getAppId());
        couponExpandAccountRecordDO.setPageId(equityAccountContext.getPageId());
        couponExpandAccountRecordDO.setReqNo(reqNo);
        couponExpandAccountRecordDO.setAuxKey(equityAccountContext.getMasterUserTicketId());
        couponExpandAccountRecordDO.setExt(MapUtils.isEmpty(extMap) ? null : JSON.toJSONString(extMap));
        return couponExpandAccountRecordDO;
    }

    public static CouponExpandReceiveVO concertToCouponExpandReceiveVO(EquityAccountContext equityAccountContext) {
        return CouponExpandReceiveVO.builder()
                .reqNo(equityAccountContext.getSerialNumber())
                .userId(equityAccountContext.getUid())
                .cityCode(equityAccountContext.getCityCode())
                .sceneCode(equityAccountContext.getSceneCode())
                .masterTicketId(equityAccountContext.getStrategyExternalId())
                .masterUserTicketId(equityAccountContext.getMasterUserTicketId())
                .expandTicketId(equityAccountContext.getEquityValue())
                .expandUserTicketId(getExpandUserTicketId(equityAccountContext))
                .build();
    }

    public static ExpandTicketRequest getExpandTicketRequest(EquityAccountContext equityAccountContext, String appId) {
        ExpandTicketRequest request = new ExpandTicketRequest();
        request.setUid(equityAccountContext.getUid());
        request.setOldTicketId(equityAccountContext.getStrategyExternalId());
        request.setOldUserTicketId(equityAccountContext.getMasterUserTicketId());
        request.setExpandTicketId(equityAccountContext.getEquityValue());
        request.setExpandBizId(equityAccountContext.getSerialNumber());
        request.setAppid(appId);
        request.setActivity(String.valueOf(equityAccountContext.getActivityId()));
        request.setPrize(equityAccountContext.getStrategyId() + Constants.UNDERLINE + equityAccountContext.getBenefitId());
        request.setExpandSource(equityAccountContext.getSceneCode());
        return request;
    }

    private static String getExpandUserTicketId(EquityAccountContext equityAccountContext) {
        EquityRpcDto equityRpcDto = equityAccountContext.getEquityRpcDto();
        return Optional.ofNullable(equityRpcDto).map(EquityRpcDto::getValue).orElse(null);
    }

    public static CouponExpandAccountCollectDO convertToAccountCollectDO(CouponExpandAccountDO accountDO) {
        CouponExpandAccountCollectDO accountCollectDO = new CouponExpandAccountCollectDO();
        accountCollectDO.setActivityId(accountDO.getActivityId());
        accountCollectDO.setUserId(accountDO.getUserId());
        accountCollectDO.setMasterTicketId(accountDO.getMasterTicketId());
        accountCollectDO.setMasterUserTicketId(accountDO.getMasterUserTicketId());
        accountCollectDO.setExpandTicketId(accountDO.getExpandTicketId());
        accountCollectDO.setExpandUserTicketId(accountDO.getExpandUserTicketId());
        return accountCollectDO;
    }

    public static Map<String, Object> getRuleLimitInfoMap(EquityAccountContext equityAccountContext) {
        Map<String, Object> ruleLimitInfoMap = Maps.newHashMap();
        ruleLimitInfoMap.put(RuleLimitInfoKeys.ACTIVITY_RECEIVE_LIMIT_ID, equityAccountContext.getActivityReceiveLimitId());
        return ruleLimitInfoMap;
    }
}
