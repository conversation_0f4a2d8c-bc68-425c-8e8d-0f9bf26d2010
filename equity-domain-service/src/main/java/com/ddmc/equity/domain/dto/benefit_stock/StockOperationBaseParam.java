package com.ddmc.equity.domain.dto.benefit_stock;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ddmc.equity.common.enums.PlanDateTypeEnum;
import com.ddmc.equity.common.enums.StockOperationEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;

import java.util.Map;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class StockOperationBaseParam {
    private Long id;

    private Long activityId;

    private Long benefitId;

    private Long strategyId;

    private String reqNo;

    private Long planStockId;

    /**
     * @see PlanDateTypeEnum
     */
    @ApiModelProperty("关联的计划库存类型, 0: 不指定 1: 日 2： 周 3: 月  4 : 年")
    @NotNull
    @Builder.Default
    private Integer planDateType = PlanDateTypeEnum.UNDEFINED.getType();

    /**
     * 变化量，是增量或减量
     */
    private Long stockOperation;

    /**
     * @see StockOperationEnum
     */
    @NotNull
    private Integer stockOperationType;

    /**
     * 模板 yyyyMMdd
     */
    @NotNull
    private String dateStr;

    /**
     * 计划库存变化量，按ID变化，要具体到ID
     */
    private Map<Long /* linkPlanStockId */,Long /*  stockOperation */ > planOperationMap;

    private Long planOperationNum;

    private Integer loadCache;

    private Integer loadDb;
}
