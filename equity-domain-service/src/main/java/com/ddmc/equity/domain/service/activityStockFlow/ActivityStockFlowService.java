package com.ddmc.equity.domain.service.activityStockFlow;

import com.ddmc.equity.infra.repository.dao.ActivityStockFlowDO;

import java.util.List;

public interface ActivityStockFlowService {


   int insertActivityStockFlow(ActivityStockFlowDO activityStockFlowDO);

   int updateActivityStockFlow(ActivityStockFlowDO activityStockFlowDO);

   int updateActivityStockFlowBatch(List<ActivityStockFlowDO> activityStockFlowDOList);

}
