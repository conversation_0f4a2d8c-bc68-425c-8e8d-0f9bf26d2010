package com.ddmc.equity.domain.converter.rule;

import com.ddmc.equity.domain.dto.rule.condition.OldSysBOCUserRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.OldSysMaiCaiNewOrOldUserLimitRuleDTO;
import com.ddmc.equity.dto.business.rule.OldSysBOCUserDTO;
import com.ddmc.equity.dto.business.rule.OldSysMaiCaiNewOrOldUserLimitDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface OldSysBOCUserRuleConverter {
    OldSysBOCUserRuleConverter INSTANCE = Mappers.getMapper(OldSysBOCUserRuleConverter.class);

    @Mapping(target = "strategyId", ignore = true)
    @Mapping(target = "activityId", ignore = true)
    OldSysBOCUserRuleDTO convertToOldSysBOCUserRuleDTO(OldSysBOCUserDTO stockLimitDTO);
}
