package com.ddmc.equity.domain.dto.benefit_group;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * @see <a href="https://cfl.corp.100.me/pages/viewpage.action?pageId=82030939"></a>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class BenefitGroupInfoDTO implements Serializable {
    private static final long serialVersionUID = -2942473800053769686L;

    private List<BaseInfoDTO> baseList;
}
