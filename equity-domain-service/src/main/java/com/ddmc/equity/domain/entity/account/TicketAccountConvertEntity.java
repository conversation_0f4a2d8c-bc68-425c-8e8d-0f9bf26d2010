package com.ddmc.equity.domain.entity.account;

import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcResponseExtDTO;
import com.ddmc.equity.infra.rpc.voucher.dto.SendTicketResDTO;
import com.ddmc.vouchercore.client.request.SendTicketPackageSyncRequest;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/7/18 17:43
 * @description
 */
public class TicketAccountConvertEntity {

    public static String getSendTicketActivity(EquityAccountContext equityAccountContext) {
        // 如果上下文中有指定，则用上下文中的 sendTicketActivity；如果上下文中未指定，则用权益的 activityId
        Long activityId = ObjectUtils.defaultIfNull(equityAccountContext.getActivityId(), 0L);
        String defaultSendTicketActivity = String.valueOf(activityId);
        return StringUtils.defaultIfBlank(equityAccountContext.getSendTicketActivity(), defaultSendTicketActivity);
    }

    public static String getSendTicketPrize(EquityAccountContext equityAccountContext) {
        // 如果上下文中有指定，则用上下文中的 sendTicketPrize；如果上下文中未指定，则使权益的 strategyId_benefitId
        Long strategyId = ObjectUtils.defaultIfNull(equityAccountContext.getStrategyId(), 0L);
        Long benefitId = ObjectUtils.defaultIfNull(equityAccountContext.getBenefitId(), 0L);
        String defaultSendTicketPrize = strategyId + Constants.UNDERLINE + benefitId;
        return StringUtils.defaultIfBlank(equityAccountContext.getSendTicketPrize(), defaultSendTicketPrize);
    }

    public static SendTicketPackageSyncRequest convertToSendTicketPackageRequest(EquityAccountContext equityAccountContext) {
        SendTicketPackageSyncRequest request = new SendTicketPackageSyncRequest();
        request.setAppid(Constants.EQUITY_CUSTOMER_SERVICE);
        request.setType(Constants.EQUITY_CUSTOMER_SERVICE);
        request.setUid(equityAccountContext.getUid());
        // 设置优惠券包 id
        request.setTicket_package_id(equityAccountContext.getEquityValue());
        request.setExternal_unique_id(equityAccountContext.getSerialNumber());
        // 设置发放优惠券包 activity、prize
        request.setActivity(getSendTicketActivity(equityAccountContext));
        request.setPrize(getSendTicketPrize(equityAccountContext));
        // 企微裂变没有指定发放优惠券包时的场景（和发放优惠券时的场景用同一字段），所以需要默认 124 = 企微裂变发放
        request.setSence(ObjectUtils.defaultIfNull(equityAccountContext.getSendTicketScene(), 124));
        return request;
    }

    public static EquityRpcDto convertToTicketPackageEquityRpcDTO(SendTicketResDTO sendTicketResDTO) {
        if (Objects.isNull(sendTicketResDTO)) {
            return EquityRpcDto.fail(String.valueOf(StatusEnum.PROCESSING.getCode()), "优惠券发放结果异常～");
        }
        StatusEnum statusEnum = sendTicketResDTO.getStatusEnum();
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        equityRpcDto.setCode(sendTicketResDTO.getCode());
        equityRpcDto.setMessage(sendTicketResDTO.getMsg());
        if (StatusEnum.SUCCESS.equals(statusEnum)) {
            String userTicketPackageId = sendTicketResDTO.getUserTicketPackageId();
            equityRpcDto.setStatusEnum(StatusEnum.SUCCESS);
            equityRpcDto.setValue(userTicketPackageId);
            equityRpcDto.setRpcResponseExtDTO(convertToTicketPackageRpcResponseExtDTO(userTicketPackageId));
            return equityRpcDto;
        }
        if (StatusEnum.FAIL.equals(statusEnum)) {
            equityRpcDto.setStatusEnum(StatusEnum.FAIL);
            return equityRpcDto;
        }
        equityRpcDto.setStatusEnum(StatusEnum.PROCESSING);
        return equityRpcDto;
    }

    private static UniversalAccountRpcResponseExtDTO convertToTicketPackageRpcResponseExtDTO(String userTicketPackageId) {
        if (StringUtils.isBlank(userTicketPackageId)) {
            return null;
        }
        return UniversalAccountRpcResponseExtDTO.builder()
                .userTicketPackageId(userTicketPackageId)
                .build();
    }
}
