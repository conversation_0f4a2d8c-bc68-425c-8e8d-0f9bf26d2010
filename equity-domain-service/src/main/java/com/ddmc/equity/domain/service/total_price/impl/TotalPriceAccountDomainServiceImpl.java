package com.ddmc.equity.domain.service.total_price.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.service.total_price.TotalPriceAccountDomainService;
import com.ddmc.equity.infra.repository.dao.TotalPriceAccountDO;
import com.ddmc.equity.infra.repository.dao.mapper.TotalPriceAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/16 11:31
 * @description
 */
@Slf4j
@Service
public class TotalPriceAccountDomainServiceImpl extends ServiceImpl<TotalPriceAccountMapper, TotalPriceAccountDO>
        implements TotalPriceAccountDomainService {

    @Override
    public void insertAccountDO(TotalPriceAccountDO accountDO) {
        Assert.mustTrue(StringUtils.isNotBlank(accountDO.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "总价促销品权益子账户 userId 不能为空");
        this.save(accountDO);
    }

    @Override
    public List<TotalPriceAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds, List<Integer> statuses, Date startDate, Date endDate) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询总价促销品权益子账户 userId 不能为空");

        Wrapper<TotalPriceAccountDO> wrapper = Wrappers.<TotalPriceAccountDO>lambdaQuery()
                .eq(TotalPriceAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(TotalPriceAccountDO::getUserId, userId)
                .in(CollectionUtils.isNotEmpty(activityIds), TotalPriceAccountDO::getActivityId, activityIds)
                .in(CollectionUtils.isNotEmpty(statuses), TotalPriceAccountDO::getStatus, statuses)
                .ge(Objects.nonNull(startDate), TotalPriceAccountDO::getCreateTime, startDate)
                .le(Objects.nonNull(endDate), TotalPriceAccountDO::getCreateTime, endDate)
                .orderByDesc(TotalPriceAccountDO::getId);
        return this.list(wrapper);
    }
}
