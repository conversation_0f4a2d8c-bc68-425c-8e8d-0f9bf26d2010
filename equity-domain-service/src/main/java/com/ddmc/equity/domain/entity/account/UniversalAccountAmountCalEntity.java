package com.ddmc.equity.domain.entity.account;

import com.ddmc.equity.account.DirectAccountContext;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.exception.BusinessException;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.common.util.lottery.LotteryUtils;
import com.ddmc.equity.enums.BenefitAmountTypeEnum;
import com.ddmc.equity.enums.BenefitTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/9/27 15:38
 * @description
 */
@Slf4j
public class UniversalAccountAmountCalEntity {

    /**
     * 获取实际发放数量的方法。
     *
     * @param benefitType       权益类型
     * @param benefitValue      权益值
     * @param benefitAmountType 权益数量类型
     * @param maxAmount         最大权益数量
     * @param reqSendAmount     请求发放数量
     * @return 实际发放数量
     * @throws BusinessException 如果参数不符合要求，则抛出业务异常
     */
    public static String getRealSendAmount(Integer benefitType, String benefitValue, Integer benefitAmountType,
                                           String maxAmount, String reqSendAmount) {
        // 如果是不固定数量权益且 reqSendAmount 为空，抛出参数异常
        boolean isUnFixedAmount = BenefitAmountTypeEnum.isUnFixedAmount(benefitAmountType);
        if (isUnFixedAmount && StringUtils.isBlank(reqSendAmount)) {
            log.warn("getRealSendAmount reqSendAmountIsMissing benefitType={}, benefitValue={}, benefitAmountType={}" +
                            ", maxAmount={}, reqSendAmount={}",
                    benefitType, benefitValue, benefitAmountType, maxAmount, reqSendAmount);
            CsossUtils.logEventWithSpan(MonitorConstants.REQ_SEND_AMOUNT_IS_ILLEGAL, "req_send_amount_is_missing");
            throw AdminExceptionBuilder.build(ExceptionEnum.REQ_SEND_AMOUNT_IS_ILLEGAL, "对于不固定数量权益，发放数量不能为空");
        }

        // 如果是不固定数量权益且 maxAmount 不为空，且 reqSendAmount 大于 maxAmount，抛出参数异常
        if (isUnFixedAmount && StringUtils.isNotBlank(maxAmount) &&
                NumberUtils.convertToBigDecimal(reqSendAmount).compareTo(NumberUtils.convertToBigDecimal(maxAmount)) > 0) {
            log.warn("getRealSendAmount reqSendAmountExceedsMax benefitType={}, benefitValue={}, benefitAmountType={}" +
                            ", maxAmount={}, reqSendAmount={}",
                    benefitType, benefitValue, benefitAmountType, maxAmount, reqSendAmount);
            CsossUtils.logEventWithSpan(MonitorConstants.REQ_SEND_AMOUNT_IS_ILLEGAL, "req_send_amount_exceeds_max");
            throw AdminExceptionBuilder.build(ExceptionEnum.REQ_SEND_AMOUNT_IS_ILLEGAL, "对于不固定数量权益，发放数量不能大于最大数量限制");
        }

        // 返回实际发放数量
        return getRealSendAmount(benefitType, benefitValue, benefitAmountType, reqSendAmount);
    }

    /**
     * 对于每次发放数量必须为 1 的权益类型（如优惠券等），sendAmount = 1
     * 发放固定数量权益时，sendAmount = benefit.benefitValue
     * 发放不固定数量权益时，sendAmount = reqSendAmount
     * 发放随机数量权益时，sendAmount = benefit.benefitValue 范围内随机的数量
     *
     * @param benefitType       权益类型
     * @param benefitValue      权益值
     * @param benefitAmountType 权益数量类型
     * @param reqSendAmount     请求参数中的发放数量
     * @return 记录在 accountDetail 表中的权益实际发放数量
     */
    public static String getRealSendAmount(Integer benefitType, String benefitValue, Integer benefitAmountType,
                                           String reqSendAmount) {
        String realSendAmount = null;
        try {
            // 对于每次发放数量必须为 1 的权益类型（如优惠券等），sendAmount = 1
            if (BenefitTypeEnum.isOneAmountBenefitTypeClassify(benefitType)) {
                realSendAmount = String.valueOf(1);
                return realSendAmount;
            }
            if (Objects.equals(benefitType, BenefitTypeEnum.RANDOM_BALANCE.getId())) {
                realSendAmount = getRandomSendAmount(benefitType, benefitValue);
                return realSendAmount;
            }
            if (Objects.equals(benefitAmountType, BenefitAmountTypeEnum.FIXED_AMOUNT.getCode())) {
                realSendAmount = benefitValue;
                return realSendAmount;
            }
            if (Objects.equals(benefitAmountType, BenefitAmountTypeEnum.UNFIXED_AMOUNT.getCode())) {
                realSendAmount = reqSendAmount;
                return realSendAmount;
            }
            if (Objects.equals(benefitAmountType, BenefitAmountTypeEnum.RANDOM_AMOUNT.getCode())) {
                realSendAmount = getRandomSendAmount(benefitType, benefitValue);
                return realSendAmount;
            }
            // 未知的权益数量类型，暂不支持
            return null;
        } finally {
            // 结果为空打点，判断是否为数字
            realSendAmountIllegalDot(realSendAmount, benefitType, benefitValue, benefitAmountType, reqSendAmount);
        }
    }

    private static void realSendAmountIllegalDot(String realSendAmount, Integer benefitType, String benefitValue,
                                                 Integer benefitAmountType, String reqSendAmount) {
        if (StringUtils.isBlank(realSendAmount)) {
            // 空
            log.warn("realSendAmountIllegalDot sendAmountIsBlack benefitType={}, benefitValue={}" +
                            ", benefitAmountType={}, reqSendAmount={}",
                    benefitType, benefitValue, benefitAmountType, reqSendAmount);
            CsossUtils.logEvent(MonitorConstants.REAL_SEND_AMOUNT_IS_ILLEGAL, "send_amount_is_black");
            return;
        }
        if (!org.apache.commons.lang3.math.NumberUtils.isCreatable(realSendAmount)) {
            // 非数字
            log.warn("realSendAmountIllegalDot sendAmountIsNotNumber benefitType={}, benefitValue={}" +
                            ", benefitAmountType={}, reqSendAmount={}",
                    benefitType, benefitValue, benefitAmountType, reqSendAmount);
            CsossUtils.logEvent(MonitorConstants.REAL_SEND_AMOUNT_IS_ILLEGAL, "send_amount_is_not_number");
            return;
        }
        if (BigDecimal.ZERO.compareTo(NumberUtils.convertToBigDecimal(realSendAmount)) >= 0) {
            // 非正数
            log.warn("realSendAmountIllegalDot sendAmountIsNonPositive benefitType={}, benefitValue={}" +
                            ", benefitAmountType={}, reqSendAmount={}",
                    benefitType, benefitValue, benefitAmountType, reqSendAmount);
            CsossUtils.logEvent(MonitorConstants.REAL_SEND_AMOUNT_IS_ILLEGAL, "send_amount_is_non_positive");
        }
    }

    /**
     * 兼容老的没有传 reqSendAmount 场景
     */
    public static String getCompatibleOldRealSendAmount(String contextSendAmount, BigDecimal contextBalanceMoney,
                                                        Integer benefitType, String benefitValue) {
        if (Objects.nonNull(contextSendAmount)) {
            return contextSendAmount;
        }

        CsossUtils.logEvent(MonitorConstants.GET_COMPATIBLE_OLD_REAL_SEND_AMOUNT, "context_send_amount_is_null");
        String realSendAmount = null;
        try {
            // 对于每次发放数量必须为 1 的权益类型（如优惠券等），sendAmount = 1
            if (BenefitTypeEnum.isOneAmountBenefitTypeClassify(benefitType)) {
                realSendAmount = String.valueOf(1);
                return realSendAmount;
            }
            realSendAmount = Objects.nonNull(contextBalanceMoney) ? contextBalanceMoney.toPlainString() : benefitValue;
            return realSendAmount;
        } finally {
            // 结果为空打点，判断是否为数字
            compatibleOldRealSendAmountIllegalDot(realSendAmount, contextBalanceMoney, benefitType, benefitValue);
        }
    }

    private static void compatibleOldRealSendAmountIllegalDot(String realSendAmount, BigDecimal contextBalanceMoney,
                                                              Integer benefitType, String benefitValue) {
        if (StringUtils.isBlank(realSendAmount)) {
            // 空
            log.warn("compatibleOldRealSendAmountIllegalDot sendAmountIsBlack contextBalanceMoney={}" +
                            ", benefitType={}, benefitValue={}",
                    contextBalanceMoney, benefitType, benefitValue);
            CsossUtils.logEvent(MonitorConstants.COMPATIBLE_OLD_REAL_SEND_AMOUNT_IS_ILLEGAL, "send_amount_is_black");
            return;
        }
        if (!org.apache.commons.lang3.math.NumberUtils.isCreatable(realSendAmount)) {
            // 非数字
            log.warn("compatibleOldRealSendAmountIllegalDot sendAmountIsNotNumber contextBalanceMoney={}" +
                            ", benefitType={}, benefitValue={}",
                    contextBalanceMoney, benefitType, benefitValue);
            CsossUtils.logEvent(MonitorConstants.COMPATIBLE_OLD_REAL_SEND_AMOUNT_IS_ILLEGAL, "send_amount_is_not_number");
            return;
        }
        if (BigDecimal.ZERO.compareTo(NumberUtils.convertToBigDecimal(realSendAmount)) >= 0) {
            // 非正数
            log.warn("compatibleOldRealSendAmountIllegalDot sendAmountIsNonPositive contextBalanceMoney={}" +
                            ", benefitType={}, benefitValue={}",
                    contextBalanceMoney, benefitType, benefitValue);
            CsossUtils.logEvent(MonitorConstants.COMPATIBLE_OLD_REAL_SEND_AMOUNT_IS_ILLEGAL, "send_amount_is_non_positive");
        }
    }

    private static String getRandomSendAmount(Integer benefitType, String benefitValue) {
        if (Objects.equals(benefitType, BenefitTypeEnum.BALANCE.getId()) ||
                Objects.equals(benefitType, BenefitTypeEnum.RANDOM_BALANCE.getId())) {
            // 随机余额
            BigDecimal randomBalanceSendAmount = LotteryUtils.generateRandomAmount(benefitValue);
            return Objects.isNull(randomBalanceSendAmount) ? null : randomBalanceSendAmount.toPlainString();
        }
        // 其余类型，暂时不支持随机数量
        return null;
    }

    public static BigDecimal getReceivedBalanceMoneyJudgedType(Integer benefitType, String realSendAmount) {
        if (BenefitTypeEnum.isBalanceBenefitType(benefitType)) {
            // 固定余额，realSendAmount = benefit.benefitValue；随机余额，realSendAmount = benefit.benefitValue 范围内随机的数量
            return NumberUtils.convertToBigDecimal(realSendAmount);
        }
        return null;
    }

    public static Integer getReceivedCount(Integer benefitType, String realSendAmount) {
        Integer benefitTypeClassify = BenefitTypeEnum.getBenefitTypeClassify(benefitType);
        if (Objects.equals(benefitTypeClassify, 1)) {
            return NumberUtils.convertToInteger(realSendAmount);
        }
        if (Objects.equals(benefitTypeClassify, 2)) {
            // benefitValue 存的余额金额单位为元（如 1.99），需要转换成分（199）
            BigDecimal balanceMoney = NumberUtils.convertToBigDecimal(realSendAmount);
            return balanceMoney.multiply(new BigDecimal("100")).intValue();
        }
        return Objects.nonNull(realSendAmount) ? NumberUtils.convertToInteger(realSendAmount) : 1;
    }

    public static BigDecimal getReceivedAmount(String realSendAmount) {
        return Objects.nonNull(realSendAmount) ? NumberUtils.convertToBigDecimal(realSendAmount) : BigDecimal.valueOf(1);
    }

    private static Integer getDoRpcIntegerAmount(String sendAmount, String benefitValue) {
        if (Objects.nonNull(sendAmount)) {
            return NumberUtils.convertToInteger(sendAmount);
        }
        return NumberUtils.convertToInteger(benefitValue);
    }

    private static Long getDoRpcLongAmount(String sendAmount, String benefitValue) {
        if (Objects.nonNull(sendAmount)) {
            return NumberUtils.convertToLong(sendAmount);
        }
        return NumberUtils.convertToLong(benefitValue);
    }

    private static BigDecimal getDoRpcBalanceMoneyAmount(String sendAmount, BigDecimal contextBalanceMoney,
                                                         String benefitValue) {
        if (Objects.nonNull(sendAmount)) {
            return NumberUtils.convertToBigDecimal(sendAmount);
        }
        if (Objects.nonNull(contextBalanceMoney)) {
            return contextBalanceMoney;
        }
        return NumberUtils.convertToBigDecimal(benefitValue);
    }

    public static Integer getDoRpcSendAmountInteger(EquityAccountContext equityAccountContext) {
        return UniversalAccountAmountCalEntity.getDoRpcIntegerAmount(equityAccountContext.getSendAmount(),
                equityAccountContext.getEquityValue());
    }

    public static Long getDoRpcSendAmountLong(EquityAccountContext equityAccountContext) {
        return UniversalAccountAmountCalEntity.getDoRpcLongAmount(equityAccountContext.getSendAmount(),
                equityAccountContext.getEquityValue());
    }

    public static BigDecimal getDoRpcBalanceMoneyAmountJudgedType(EquityAccountContext equityAccountContext) {
        if (BenefitTypeEnum.isBalanceBenefitType(equityAccountContext.getBenefitType())) {
            return getDoRpcBalanceMoneyAmount(equityAccountContext);
        }
        return null;
    }

    public static BigDecimal getDoRpcBalanceMoneyAmount(EquityAccountContext equityAccountContext) {
        return getDoRpcBalanceMoneyAmount(equityAccountContext.getSendAmount(), equityAccountContext.getBalanceMoney(),
                equityAccountContext.getEquityValue());
    }

    public static BigDecimal getDoRpcBalanceMoneyAmountJudgedType(DirectAccountContext equityAccountContext) {
        if (BenefitTypeEnum.isBalanceBenefitType(equityAccountContext.getBenefitType())) {
            return getDoRpcBalanceMoneyAmount(equityAccountContext);
        }
        return null;
    }

    private static BigDecimal getDoRpcBalanceMoneyAmount(DirectAccountContext equityAccountContext) {
        return getDoRpcBalanceMoneyAmount(equityAccountContext.getSendAmount(), equityAccountContext.getBalanceMoney(),
                equityAccountContext.getBenefitValue());
    }
}
