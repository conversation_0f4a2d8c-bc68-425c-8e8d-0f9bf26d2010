package com.ddmc.equity.domain.service.low_stock_alarm.impl;

import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.util.business.BenefitUtil;
import com.ddmc.equity.domain.dto.benefit_stock.LowStockAlarmConditionDTO;
import com.ddmc.equity.domain.dto.benefit_stock.LowStockAlarmContextDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/1/24 11:20
 * @description 会员膨胀券库存（计划 and 非计划）不足告警
 */
@Slf4j
@Component("vipCouponExpandLowStockAlarm")
public class VipCouponExpandLowStockAlarm extends AbstractLowStockAlarm {

    @Override
    protected String getLowStockAlarmTimesKey(LowStockAlarmContextDTO contextDTO, String hitLowStockAlarmType) {
        String benefitUni = BenefitUtil.getBenefitUni(contextDTO.getActivityId(), contextDTO.getStrategyId(),
                contextDTO.getBenefitId());
        return String.format(CacheKeyConstants.LOW_STOCK_ALARM_TIMES_KEY, "vipCouponExpand", benefitUni, hitLowStockAlarmType);
    }

    @Override
    protected String buildAlarmContent(LowStockAlarmContextDTO contextDTO) {
        LowStockAlarmConditionDTO.LowStockAlarmRuleDTO lowStockAlarmRule = contextDTO.getLowStockAlarmRule();

        return String.format("**[膨胀券] 库存不足提醒**\n" +
                        "> 活动 id& 名称：<font color=\"comment\">%d / %s</font>\n" +
                        "> 母券券模板 id& 名称：<font color=\"comment\">%s / %s</font>\n" +
                        "> 膨胀券模板 id& 名称：<font color=\"comment\">%s / %s</font>\n" +
                        "> 活动时间：<font color=\"comment\">%s - %s</font>\n" +
                        "%s" +
                        "> 请及时补充！！！[点击跳转膨胀券管理后台](%s)",
                contextDTO.getActivityId(), contextDTO.getActivityName(),
                contextDTO.getStrategyExternalId(), contextDTO.getStrategyName(),
                contextDTO.getBenefitValue(), contextDTO.getBenefitName(),
                contextDTO.buildStartDateStr(), contextDTO.buildEndDateStr(),
                contextDTO.buildStockContentStr(), lowStockAlarmRule.getJumpLink());
    }
}
