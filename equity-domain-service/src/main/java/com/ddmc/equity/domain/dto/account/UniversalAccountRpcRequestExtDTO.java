package com.ddmc.equity.domain.dto.account;

import com.ddmc.equity.dto.customer.ReceiveExternalInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/23 15:01
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("通用权益账户-外部 rpc 调用请求入参拓展信息")
public class UniversalAccountRpcRequestExtDTO {

    /**
     * 对于每次发放数量必须为 1 的权益类型（如优惠券等），sendAmount = 1
     * 发放固定数量权益时，sendAmount = benefit.benefitValue
     * 发放不固定数量权益时，sendAmount = reqSendAmount
     * 发放随机数量权益时，sendAmount = benefit.benefitValue 范围内随机的数量
     */
    @ApiModelProperty("权益实际发放数量")
    private String sendAmount;

    @ApiModelProperty("发放权益时调用下游透传的自定义参数")
    private Map<String, String> rpcReqCustomMap;

    @ApiModelProperty("余额金额，保留两位小数。如果权益类型为余额（固定余额、随机余额），才有该字段")
    private BigDecimal balanceMoney;

    @ApiModelProperty(value = "领取时关联的外部信息")
    private ReceiveExternalInfoDTO receiveExternalInfoDTO;

    @ApiModelProperty("来源任务的 missionInstanceId")
    private Long missionInstanceId;

    @ApiModelProperty("发放优惠券时的场景（发放优惠券包时的场景也用该字段）")
    private Integer sendTicketScene;

    @ApiModelProperty("发放优惠券时透传的 activity。如果为空，则使权益的 activityId")
    private String sendTicketActivity;

    @ApiModelProperty("发放优惠券时透传的 prize。如果为空，则使权益的 strategyId_benefitId")
    private String sendTicketPrize;

    @ApiModelProperty("发放余额时的场景")
    private String sendBalanceScene;

    @ApiModelProperty(value = "发放余额时的描述")
    private String sendBalanceDesc;

    @ApiModelProperty(value = "发放积分时的场景")
    private Integer sendPointScene;

    @ApiModelProperty(value = "发放积分时的描述")
    private String sendPointDesc;

    @ApiModelProperty(value = "发放积分时的来源")
    private Integer sendPointSource;

    @ApiModelProperty(value = "发放会员天数时的场景")
    private Integer sendVipDaysScene;
}
