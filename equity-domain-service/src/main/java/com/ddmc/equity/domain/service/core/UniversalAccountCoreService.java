package com.ddmc.equity.domain.service.core;

import com.ddmc.equity.domain.dto.QueryUniversalAccountDetailsReqDTO;
import com.ddmc.equity.domain.dto.QueryUniversalAccountRecordsReqDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountDetailDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRecordDTO;
import com.ddmc.equity.dto.business.PageListRespDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/23 15:27
 * @description
 */
public interface UniversalAccountCoreService {

    /**
     * 查询用户权益通用账户列表
     *
     * @param userId        用户 id
     * @param useActivityId 使用时的活动 id。不指定则查询所有
     * @param benefitTypes  权益类型。不指定则查询所有
     * @return 用户权益通用账户列表
     */
    List<UniversalAccountDTO> queryUniversalAccountDTOList(String userId, Long useActivityId, List<Integer> benefitTypes);

    /**
     * 查询用户权益通用账户明细列表
     *
     * @param req 请求参数
     * @return 用户权益通用账户明细列表
     */
    List<UniversalAccountDetailDTO> queryUniversalAccountDetailDTOList(QueryUniversalAccountDetailsReqDTO req);

    /**
     * 分页查询用户权益通用账户明细列表
     *
     * @param req 请求参数
     * @return 用户权益通用账户明细列表
     */
    PageListRespDTO<UniversalAccountDetailDTO> pageQueryUniversalAccountDetailDTOList(QueryUniversalAccountDetailsReqDTO req);

    /**
     * 查询用户指定的权益通用账户明细
     *
     * @param userId          用户 id
     * @param accountDetailId 账户明细 id
     * @return 权益通用账户明细
     */
    UniversalAccountDetailDTO queryOneUniversalAccountDetailDTO(String userId, Long accountDetailId);

    /**
     * 查询用户权益通用账户操作记录列表
     *
     * @param req 请求参数
     * @return 用户权益通用账户操作记录列表
     */
    List<UniversalAccountRecordDTO> queryUniversalAccountRecordDTOList(QueryUniversalAccountRecordsReqDTO req);

    /**
     * 分页用户权益通用账户操作记录列表
     *
     * @param req 请求参数
     * @return 用户权益通用账户操作记录列表
     */
    PageListRespDTO<UniversalAccountRecordDTO> pageQueryUniversalAccountRecordDTOList(QueryUniversalAccountRecordsReqDTO req);
}
