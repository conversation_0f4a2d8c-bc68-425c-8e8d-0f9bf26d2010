package com.ddmc.equity.domain.dto;

import com.ddmc.equity.dto.business.PageListReqDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/1/15 11:09
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("获取通用账户明细列表 req")
public class QueryUniversalAccountDetailsReqDTO extends PageListReqDTO {

    @ApiModelProperty(value = "是否绑定主库")
    private Boolean bindMaster;

    @ApiModelProperty(value = "用户 id", required = true)
    private String userId;

    /**
     * @see com.ddmc.equity.enums.SceneCodeEnum
     */
    @ApiModelProperty(value = "活动场景 code。如果不指定则查询所有")
    private String sceneCode;

    @ApiModelProperty(value = "活动 ids。如果不指定则查询所有")
    private List<Long> activityIds;

    @ApiModelProperty(value = "策略 ids。如果不指定则查询所有")
    private List<Long> strategyIds;

    @ApiModelProperty(value = "权益组 ids。如果不指定则查询所有")
    private List<Long> benefitGroupIds;

    @ApiModelProperty(value = "权益 ids。如果不指定则查询所有")
    private List<Long> benefitIds;

    /**
     * @see com.ddmc.equity.enums.BenefitTypeEnum
     */
    @ApiModelProperty(value = "权益类型。如果不指定则查询所有")
    private List<Integer> benefitTypes;

    @ApiModelProperty(value = "权益值。如果不指定则查询所有")
    private List<String> benefitValues;

    @ApiModelProperty("开始时间。可以指定开始时间（创建时间）查询，不指定则查询所有")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty("结束时间。可以指定结束时间（创建时间）查询，不指定则查询所有")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date endDate;
}
