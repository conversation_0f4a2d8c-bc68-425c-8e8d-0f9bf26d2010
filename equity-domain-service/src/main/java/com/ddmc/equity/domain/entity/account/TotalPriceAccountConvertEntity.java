package com.ddmc.equity.domain.entity.account;

import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.repository.dao.TotalPriceAccountDO;
import com.ddmc.equity.infra.repository.dao.TotalPriceAccountRecordDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.PriceProductAccountInfoDTO;
import com.ddmc.equity.model.dto.SubAccountRecordDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/25 17:17
 * @description
 */
public class TotalPriceAccountConvertEntity extends AccountConvertEntity {

    public static TotalPriceAccountRecordDO createAccountRecordDO(int operateType, EquityAccountContext equityAccountContext) {
        TotalPriceAccountRecordDO accountRecordDO = new TotalPriceAccountRecordDO();
        createSubAccountRecordBaseFieldDO(accountRecordDO, operateType, equityAccountContext);
        accountRecordDO.setProductId(equityAccountContext.getProductId());
        return accountRecordDO;
    }

    public static TotalPriceAccountDO createAccountDO(EquityAccountContext equityAccountContext) {
        TotalPriceAccountDO accountDO = new TotalPriceAccountDO();
        createSubAccountBaseFieldDO(accountDO, equityAccountContext);
        accountDO.setProductId(equityAccountContext.getProductId());
        return accountDO;
    }

    public static TotalPriceAccountRecordDO createUpdateAccountRecordDO(Long accountRecordId, Long accountId, Integer status,
                                                                        EquityRpcDto equityRpcDto, Map<String, Object> ruleLimitInfoMap) {
        TotalPriceAccountRecordDO update = new TotalPriceAccountRecordDO();
        fillUpdateBaseAccountRecordDO(update, accountRecordId, accountId, status, equityRpcDto, ruleLimitInfoMap);
        return update;
    }

    public static SubAccountRecordDTO convertToSubAccountRecordDTO(TotalPriceAccountRecordDO recordDO) {
        SubAccountRecordDTO subAccountRecordDTO = new SubAccountRecordDTO();
        fillSubAccountRecordDTO(subAccountRecordDTO, recordDO);
        subAccountRecordDTO.setProductId(recordDO.getProductId());
        return subAccountRecordDTO;
    }

    public static List<AccountInfoDTO> convertToAccountInfoDTOList(List<TotalPriceAccountDO> accountDOList) {
        if (CollectionUtils.isEmpty(accountDOList)) {
            return null;
        }
        return accountDOList.stream().map(TotalPriceAccountConvertEntity::convertToAccountInfoDTO).collect(Collectors.toList());
    }

    private static AccountInfoDTO convertToAccountInfoDTO(TotalPriceAccountDO accountDO) {
        PriceProductAccountInfoDTO priceProductAccountInfoDTO = PriceProductAccountInfoDTO.builder()
                .productId(accountDO.getProductId())
                .build();
        AccountInfoDTO accountInfoDTO = convertToBaseAccountInfoDTO(accountDO);
        accountInfoDTO.setBenefitType(BenefitTypeEnum.TOTAL_PRICE.getId());
        accountInfoDTO.setPriceProductAccountInfoDTO(priceProductAccountInfoDTO);
        return accountInfoDTO;
    }
}
