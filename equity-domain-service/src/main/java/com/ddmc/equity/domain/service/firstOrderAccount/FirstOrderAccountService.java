package com.ddmc.equity.domain.service.firstOrderAccount;

import com.ddmc.equity.infra.repository.dao.FirstOrderAccountDO;

public interface FirstOrderAccountService {
    int saveFirstOrderAccountDO(FirstOrderAccountDO firstOrderAccountDO);

    FirstOrderAccountDO queryFirstOrderAccountDOByUserId(String userId);


    int updateFirstOrderAccountDO(FirstOrderAccountDO firstOrderAccountDO);

    boolean useAccount(String uid, String lastReqNo);

    boolean fallBackAccount(String uid, Long accountId, String lastReqNo);
}
