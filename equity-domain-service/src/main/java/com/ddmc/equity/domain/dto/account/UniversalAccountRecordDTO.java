package com.ddmc.equity.domain.dto.account;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/23 10:54
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("用户通用账户操作记录")
public class UniversalAccountRecordDTO {

    @ApiModelProperty("用户 id")
    private String userId;

    @ApiModelProperty("通用账户操作记录 id")
    private Long accountRecordId;

    @ApiModelProperty("通用账户 id")
    private Long accountId;

    @ApiModelProperty("使用活动 id")
    private Long useActivityId;

    @ApiModelProperty("场景 code")
    private String sceneCode;

    @ApiModelProperty("活动 id")
    private Long activityId;

    @ApiModelProperty("策略 id")
    private Long strategyId;

    @ApiModelProperty("权益组 id")
    private Long benefitGroupId;

    @ApiModelProperty("权益 id")
    private Long benefitId;

    /**
     * @see com.ddmc.equity.enums.BenefitTypeEnum
     */
    @ApiModelProperty("权益类型")
    private Integer benefitType;

    @ApiModelProperty("权益值")
    private String benefitValue;

    @ApiModelProperty("权益名称")
    private String benefitName;

    @ApiModelProperty("权益图片")
    private String benefitImage;

    @ApiModelProperty("业务流水号")
    private String reqNo;

    /**
     * 操作类型。1-发放；2-使用；3-回退；
     *
     * @see com.ddmc.equity.common.enums.OperateTypeEnum
     */
    @ApiModelProperty("操作类型。1-发放；2-使用；3-回退；")
    private Integer operateType;

    /**
     * @see com.ddmc.equity.common.enums.StatusEnum
     */
    @ApiModelProperty(value = "操作记录状态。0-初始化；1-成功；2-失败；3-处理中；")
    private Integer status;

    @ApiModelProperty("操作数量")
    private Integer operateCount;

    @ApiModelProperty("请求来源 appId")
    private String appId;

    @ApiModelProperty("请求来源页面 id")
    private String pageId;

    @ApiModelProperty("请求来源")
    private String source;

    @ApiModelProperty("外部 rpc 调用请求入参拓展信息")
    private UniversalAccountRpcRequestExtDTO rpcRequestExtDTO;

    @ApiModelProperty("外部 rpc 调用请求出参拓展信息")
    private UniversalAccountRpcResponseExtDTO rpcResponseExtDTO;

    @ApiModelProperty("内部拓展信息")
    private UniversalAccountRecordInnerExtDTO innerExtDTO;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;
}
