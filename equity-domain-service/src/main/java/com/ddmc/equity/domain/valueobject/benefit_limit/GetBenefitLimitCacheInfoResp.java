package com.ddmc.equity.domain.valueobject.benefit_limit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class GetBenefitLimitCacheInfoResp extends BenefitIdInfoDTO {

    /**
     * 用户可领取总数量
     */
    private Long limitCount;
    /**
     * 用户已领取的数量
     */
    private Long receiveCount;
    /**
     * 用户已冻结数量
     */
    private Long freezeCount;
}
