package com.ddmc.equity.domain.service.equityAccount.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.interceptor.annotation.MonitorSpan;
import com.ddmc.equity.domain.dto.account_deduct.DeductOperateAccountDTO;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountDomainService;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDO;
import com.ddmc.equity.infra.repository.dao.mapper.UniversalAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class UniversalAccountDomainServiceImpl implements UniversalAccountDomainService {

    @Autowired
    private UniversalAccountMapper universalAccountMapper;

    @Override
    public void addOrUpdate(UniversalAccountDO universalAccountDO) {
        universalAccountMapper.insertOrUpdate(universalAccountDO);
    }

    @Override
    public List<UniversalAccountDO> queryUniversalAccounts(String userId, List<Long> useActivityIds, List<Integer> benefitTypes) {
        if (StringUtils.isBlank(userId)) {
            log.error("queryUniversalAccounts userId is null");
            return null;
        }
        Wrapper<UniversalAccountDO> wrapper = Wrappers.<UniversalAccountDO>lambdaQuery()
                .eq(UniversalAccountDO::getUserId, userId)
                .in(CollectionUtils.isNotEmpty(useActivityIds), UniversalAccountDO::getUseActivityId, useActivityIds)
                .in(CollectionUtils.isNotEmpty(benefitTypes), UniversalAccountDO::getBenefitType, benefitTypes);
        return universalAccountMapper.selectList(wrapper);
    }

    @Override
    public List<UniversalAccountDO> queryAvailableUniversalAccounts(String userId, List<Long> useActivityIds, Integer benefitType) {
        Wrapper<UniversalAccountDO> wrapper = Wrappers.<UniversalAccountDO>lambdaQuery()
                .eq(UniversalAccountDO::getUserId, userId)
                .in(UniversalAccountDO::getUseActivityId, useActivityIds)
                .eq(UniversalAccountDO::getBenefitType, benefitType)
                .gt(UniversalAccountDO::getAvailableCount, Constants.ZERO);
        return universalAccountMapper.selectList(wrapper);
    }

    @Override
    public int deductAvailableCount(String userId, Long id, Integer deductCount, Integer version) {
        return universalAccountMapper.deductAvailableCount(userId, id, deductCount, version);
    }

    @Override
    public UniversalAccountDO queryById(String userId, Long id) {
        Wrapper<UniversalAccountDO> wrapper = Wrappers.<UniversalAccountDO>lambdaQuery()
                .eq(UniversalAccountDO::getUserId, userId)
                .eq(UniversalAccountDO::getId, id);
        return universalAccountMapper.selectOne(wrapper);
    }

    @Override
    public List<UniversalAccountDO> queryAccounts(String userId, List<Long> accountIds) {
        if (StringUtils.isBlank(userId) || CollectionUtils.isEmpty(accountIds)) {
            log.warn("queryAccounts params isError userId={}, accountIds={}", userId, JSON.toJSONString(accountIds));
            return null;
        }
        Wrapper<UniversalAccountDO> wrapper = Wrappers.<UniversalAccountDO>lambdaQuery()
                .eq(UniversalAccountDO::getUserId, userId)
                .in(UniversalAccountDO::getId, accountIds);
        return universalAccountMapper.selectList(wrapper);
    }

    @MonitorSpan(name = "fetchDeductOperateAccounts")
    @Override
    public List<DeductOperateAccountDTO> fetchDeductOperateAccounts(String userId, List<Long> useActivityIds,
                                                                    Integer benefitType) {
        if (StringUtils.isBlank(userId) || CollectionUtils.isEmpty(useActivityIds) || Objects.isNull(benefitType)) {
            log.warn("fetchDeductOperateAccounts params isError userId={}, useActivityIds={}, benefitType={}",
                    userId, JSON.toJSONString(useActivityIds), benefitType);
            return null;
        }
        return universalAccountMapper.fetchDeductOperateAccounts(userId, useActivityIds, benefitType);
    }

    @MonitorSpan(name = "deductAccountCount")
    @Override
    public boolean deductAccountCount(String userId, Long accountId, Integer operateCount, Integer version) {
        if (StringUtils.isBlank(userId) || Objects.isNull(accountId) || Objects.isNull(operateCount) || operateCount <= 0) {
            log.warn("deductAccountCount param isError userId={}, accountId={}, operateCount={}", userId, accountId, operateCount);
            return false;
        }
        // 如果 availableCount >= operateCount，更新 availableCount -= operateCount
        Wrapper<UniversalAccountDO> wrapper = Wrappers.<UniversalAccountDO>lambdaUpdate()
                .setSql("`version` = `version` + 1" +
                        ", `available_count` = `available_count` - " + operateCount)
                .eq(UniversalAccountDO::getUserId, userId)
                .eq(UniversalAccountDO::getId, accountId)
                .ge(UniversalAccountDO::getAvailableCount, operateCount)
                .eq(Objects.nonNull(version), UniversalAccountDO::getVersion, version);
        int updateRow = universalAccountMapper.update(null, wrapper);
        return updateRow == 1;
    }

    @MonitorSpan(name = "expireAccountCount")
    @Override
    public boolean expireAccountCount(String userId, Long accountId, Integer operateCount, Integer version) {
        if (StringUtils.isBlank(userId) || Objects.isNull(accountId) || Objects.isNull(operateCount) || operateCount <= 0) {
            log.warn("expireAccountCount param isError userId={}, accountId={}, operateCount={}", userId, accountId, operateCount);
            return false;
        }
        Wrapper<UniversalAccountDO> wrapper = Wrappers.<UniversalAccountDO>lambdaUpdate()
                .setSql("`version` = `version` + 1" +
                        ", `expire_count` = `expire_count` + " + operateCount +
                        ", `available_count` = `available_count` - " + operateCount)
                .eq(UniversalAccountDO::getUserId, userId)
                .eq(UniversalAccountDO::getId, accountId)
                .ge(UniversalAccountDO::getAvailableCount, operateCount)
                .eq(Objects.nonNull(version), UniversalAccountDO::getVersion, version);
        int updateRow = universalAccountMapper.update(null, wrapper);
        return updateRow == 1;
    }
}
