package com.ddmc.equity.domain.dto.benefit;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 明细数据
 */
@Data
public class ActivityCouponExpandAccountCollectInDTO {

    /**
     * 活动ID
     */
    @NotNull(message = "活动ID必传")
    @ApiModelProperty(value = "活动ID", example = "1")
    @Min(value = 1, message = "活动ID必传")
    private Long activityId;

    /**
     * 用户信息
     */
    @ApiModelProperty(value = "用户手机号码或者uid", example = "************")
    private String user;

    /**
     *
     */
    @ApiModelProperty(value = "前端不要传")
    private String userId;

    @NotNull
    @Min(1)
    @ApiModelProperty(value = "活动列表的页数", example = "1")
    private Integer page = 1;

    @NotNull
    @ApiModelProperty(value = "活动列表页数大小", example = "20")
    private Integer pageSize = 20;
}
