package com.ddmc.equity.domain.converter.scene;

import com.ddmc.equity.domain.entity.scene.EquitySceneEntity;
import com.ddmc.equity.domain.valueobject.scene.SceneListFilterReqVO;
import com.ddmc.equity.dto.business.SceneBusinessDTO;
import com.ddmc.equity.dto.business.SceneListReqDTO;
import com.ddmc.equity.dto.business.SceneSaveReqDTO;
import com.ddmc.equity.infra.repository.dao.EquitySceneDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SceneConverter {
    SceneConverter INSTANCE = Mappers.getMapper(SceneConverter.class);

    @Mapping(target = "opAdminName", expression = "java(com.ddmc.equity.common.util.LongUtils.isTrue(req.getId()) ? null : req.getAdminName())")
    @Mapping(target = "opAdminId", expression = "java(com.ddmc.equity.common.util.LongUtils.isTrue(req.getId()) ? null : req.getAdminId())")
    @Mapping(target = "editAdminId", source = "adminId")
    @Mapping(target = "editAdminName", source = "adminName")
    @Mapping(target = "isDelete", constant = "0")
    @Mapping(target = "createTime", expression = "java(new java.util.Date())")
    @Mapping(target = "updateTime", expression = "java(new java.util.Date())")
    EquitySceneEntity srd2e(SceneSaveReqDTO req);

    @Mapping(target = "name", source = "idOrName")
    @Mapping(target = "id", expression = "java(com.ddmc.equity.common.util.NumStrUtil.getId(req.getIdOrName()))")
    SceneListFilterReqVO d2v(SceneListReqDTO req);

    List<SceneBusinessDTO> v2ds(List<EquitySceneDO> list);

    @Mapping(target = "adminId", ignore = true)
    @Mapping(target = "adminName", ignore = true)
    SceneSaveReqDTO d2d(EquitySceneDO sceneDO);
}
