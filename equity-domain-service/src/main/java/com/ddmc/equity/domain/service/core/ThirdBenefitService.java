package com.ddmc.equity.domain.service.core;

import com.ddmc.equity.dto.customer.*;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

public interface ThirdBenefitService {
    /**
     * 新增额外的权益逻辑 （如领券中心会员专享券方案）
     *
     * @param consultSceneBenefitReqDTO ConsultSceneBenefitReqDTO 请求体
     * @return ConsultSceneBenefitResDTO | 基于权益系统模型的返回信息 混合 来自会员系统权益的返回信息
     */
    @Nullable
    List<SceneActivityDTO> consultExtraBenefitOfSpecScene(@NotNull ConsultSceneBenefitReqDTO consultSceneBenefitReqDTO);

    /**
     * 领取的时候区分原权益平台领取模型和第三方权益领取模型
     *
     * @param receiveSceneBenefitReqDTO ReceiveSceneBenefitReqDTO
     * @return Pair
     */
    @NotNull Pair<@NotNull ReceiveSceneBenefitReqDTO /* originalReq */ ,
            @Nullable ReceiveSceneBenefitReqDTO /* thirdReq */> divideExtraBenefitByReq(
            @NotNull ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO);

    /**
     * 领取第三方权益
     *
     * @param req ReceiveSceneBenefitReqDTO
     * @return List<BenefitDTO>
     */
    @NotNull
    Pair<@NotNull List<BenefitDTO> /* success*/,
            @Nullable List<BenefitDTO> /* fail */> receiveExtraBenefitOfSpecScene(@NotNull ReceiveSceneBenefitReqDTO req);

}
