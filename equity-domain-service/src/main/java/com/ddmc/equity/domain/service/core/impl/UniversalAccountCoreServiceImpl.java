package com.ddmc.equity.domain.service.core.impl;

import com.ddmc.equity.domain.dto.QueryUniversalAccountDetailsReqDTO;
import com.ddmc.equity.domain.dto.QueryUniversalAccountRecordsReqDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountDetailDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRecordDTO;
import com.ddmc.equity.domain.entity.account.EquityAccountConvertEntity;
import com.ddmc.equity.domain.entity.account.OperateAccountConvertEntity;
import com.ddmc.equity.domain.service.benefit.EquityBenefitDomainService;
import com.ddmc.equity.domain.service.core.UniversalAccountCoreService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountDetailDomainService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountDomainService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountRecordDomainService;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/23 15:28
 * @description
 */
@Slf4j
@Service
public class UniversalAccountCoreServiceImpl implements UniversalAccountCoreService {

    @Autowired
    private EquityBenefitDomainService equityBenefitDomainService;
    @Autowired
    private UniversalAccountDomainService universalAccountDomainService;
    @Autowired
    private UniversalAccountDetailDomainService universalAccountDetailDomainService;
    @Autowired
    private UniversalAccountRecordDomainService universalAccountRecordDomainService;
    @Autowired
    private LocalCacheManager localCacheManager;

    @Override
    public List<UniversalAccountDTO> queryUniversalAccountDTOList(String userId, Long useActivityId, List<Integer> benefitTypes) {
        if (StringUtils.isBlank(userId)) {
            log.error("queryUniversalAccountDTOList userId is null");
            return null;
        }
        // 如果未指定 useActivityId，则只能查询 useActivityId = 0L 的权益账户列表；如果指定了 useActivityId，则需要查询包含 useActivityId = 0L 的权益账户列表；
        List<Long> useActivityIds = OperateAccountConvertEntity.getQueryUserAccountsUseActivityIds(useActivityId);
        List<UniversalAccountDO> dos = universalAccountDomainService.queryUniversalAccounts(userId, useActivityIds, benefitTypes);
        return EquityAccountConvertEntity.convertToUniversalAccountDTOList(dos);
    }

    @Override
    public List<UniversalAccountDetailDTO> queryUniversalAccountDetailDTOList(QueryUniversalAccountDetailsReqDTO req) {
        if (Objects.isNull(req) || StringUtils.isBlank(req.getUserId())) {
            log.error("queryUniversalAccountDetailDTOList userId is null");
            return null;
        }

        List<UniversalAccountDetailDO> accountDetailDOList = universalAccountDetailDomainService.queryUniversalAccountDetails(req);
        return getUniversalAccountDetailDTOList(accountDetailDOList);
    }

    @Override
    public PageListRespDTO<UniversalAccountDetailDTO> pageQueryUniversalAccountDetailDTOList(QueryUniversalAccountDetailsReqDTO req) {
        if (Objects.isNull(req) || StringUtils.isBlank(req.getUserId())) {
            log.error("pageQueryUniversalAccountDetailDTOList userId is null");
            return PageListRespDTO.<UniversalAccountDetailDTO>builder().build();
        }

        PageListRespDTO<UniversalAccountDetailDO> pageListRespDTO = universalAccountDetailDomainService.pageQueryUniversalAccountDetails(req);
        if (Objects.isNull(pageListRespDTO)) {
            return PageListRespDTO.<UniversalAccountDetailDTO>builder().build();
        }
        List<UniversalAccountDetailDTO> recordDTOList = getUniversalAccountDetailDTOList(pageListRespDTO.getList());
        return PageListRespDTO.<UniversalAccountDetailDTO>builder()
                .list(CollectionUtils.isNotEmpty(recordDTOList) ? recordDTOList : Lists.newArrayList())
                .total(pageListRespDTO.getTotal())
                .build();
    }

    @Override
    public UniversalAccountDetailDTO queryOneUniversalAccountDetailDTO(String userId, Long accountDetailId) {
        if (StringUtils.isBlank(userId) || Objects.isNull(accountDetailId)) {
            log.error("queryOneUniversalAccountDetailDTO userId or accountDetailId is null");
            return null;
        }

        UniversalAccountDetailDO accountDetailDO = universalAccountDetailDomainService.queryById(userId, accountDetailId);
        if (Objects.isNull(accountDetailDO)) {
            return null;
        }

        // 查询对应的权益信息（主要是名称、图片）
        EquityBenefitDO benefitDO = localCacheManager.getBenefitInfoById(accountDetailDO.getBenefitId());
        return EquityAccountConvertEntity.convertToUniversalAccountDetailDTO(accountDetailDO, benefitDO);
    }

    @Override
    public List<UniversalAccountRecordDTO> queryUniversalAccountRecordDTOList(QueryUniversalAccountRecordsReqDTO req) {
        if (Objects.isNull(req) || StringUtils.isBlank(req.getUserId())) {
            log.error("queryUniversalAccountRecordDTOList userId is null");
            return null;
        }

        List<UniversalAccountRecordDO> accountRecordDOList = universalAccountRecordDomainService.queryUniversalAccountRecords(req);
        return getUniversalAccountRecordDTOList(accountRecordDOList);
    }

    @Override
    public PageListRespDTO<UniversalAccountRecordDTO> pageQueryUniversalAccountRecordDTOList(QueryUniversalAccountRecordsReqDTO req) {
        if (Objects.isNull(req) || StringUtils.isBlank(req.getUserId())) {
            log.error("pageQueryUniversalAccountRecordDTOList userId is null");
            return PageListRespDTO.<UniversalAccountRecordDTO>builder().build();
        }

        PageListRespDTO<UniversalAccountRecordDO> pageListRespDTO = universalAccountRecordDomainService.pageQueryUniversalAccountRecords(req);
        if (Objects.isNull(pageListRespDTO)) {
            return PageListRespDTO.<UniversalAccountRecordDTO>builder().build();
        }
        List<UniversalAccountRecordDTO> recordDTOList = getUniversalAccountRecordDTOList(pageListRespDTO.getList());
        return PageListRespDTO.<UniversalAccountRecordDTO>builder()
                .list(CollectionUtils.isNotEmpty(recordDTOList) ? recordDTOList : Lists.newArrayList())
                .total(pageListRespDTO.getTotal())
                .build();
    }

    private List<UniversalAccountDetailDTO> getUniversalAccountDetailDTOList(List<UniversalAccountDetailDO> detailDOList) {
        // 查询对应的权益信息（获取权益名称、图片）
        List<EquityBenefitDO> benefitDOList = equityBenefitDomainService.getBenefitsByIds(EquityAccountConvertEntity.getBenefitIds(detailDOList));
        return EquityAccountConvertEntity.convertToUniversalAccountDetailDTOList(detailDOList, benefitDOList);
    }

    private List<UniversalAccountRecordDTO> getUniversalAccountRecordDTOList(List<UniversalAccountRecordDO> recordDOList) {
        // 查询对应的权益信息（获取权益名称、图片）
        List<EquityBenefitDO> benefitDOList = equityBenefitDomainService.getBenefitsByIds(EquityAccountConvertEntity.getBenefitIds(recordDOList));
        return EquityAccountConvertEntity.convertToUniversalAccountRecordDTOList(recordDOList, benefitDOList);
    }
}
