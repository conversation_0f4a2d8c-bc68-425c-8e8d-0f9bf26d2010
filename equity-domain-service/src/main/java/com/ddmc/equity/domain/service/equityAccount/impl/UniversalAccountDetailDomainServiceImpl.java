package com.ddmc.equity.domain.service.equityAccount.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.interceptor.annotation.MonitorSpan;
import com.ddmc.equity.domain.dto.QueryUniversalAccountDetailsReqDTO;
import com.ddmc.equity.domain.dto.account_deduct.DeductOperateAccountDetailDTO;
import com.ddmc.equity.domain.entity.account.UniversalAccountConvertEntity;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountDetailDomainService;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;
import com.ddmc.equity.infra.repository.dao.mapper.UniversalAccountDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class UniversalAccountDetailDomainServiceImpl implements UniversalAccountDetailDomainService {

    @Autowired
    private UniversalAccountDetailMapper universalAccountDetailMapper;

    @Override
    public void add(UniversalAccountDetailDO universalAccountDetailDO) {
        universalAccountDetailMapper.insert(universalAccountDetailDO);
    }

    @Override
    public List<UniversalAccountDetailDO> queryUniversalAccountDetails(QueryUniversalAccountDetailsReqDTO req) {
        if (Objects.isNull(req) || StringUtils.isBlank(req.getUserId())) {
            log.error("queryUniversalAccountDetails userId is null");
            return null;
        }

        Wrapper<UniversalAccountDetailDO> wrapper = UniversalAccountConvertEntity.convertToUniversalAccountDetailDOWrapper(req);
        return universalAccountDetailMapper.selectList(wrapper);
    }

    @Override
    public PageListRespDTO<UniversalAccountDetailDO> pageQueryUniversalAccountDetails(QueryUniversalAccountDetailsReqDTO req) {
        if (Objects.isNull(req) || StringUtils.isBlank(req.getUserId())) {
            log.error("pageQueryUniversalAccountDetails userId is null");
            return null;
        }

        IPage<UniversalAccountDetailDO> page = new Page<>(req.getPage(), req.getPageSize());
        Wrapper<UniversalAccountDetailDO> wrapper = UniversalAccountConvertEntity.convertToUniversalAccountDetailDOWrapper(req);
        IPage<UniversalAccountDetailDO> pageResult = universalAccountDetailMapper.selectPage(page, wrapper);
        if (Objects.isNull(pageResult)) {
            log.warn("pageQueryUniversalAccountDetails pageResult is null req={}", JSON.toJSONString(req));
            return PageListRespDTO.<UniversalAccountDetailDO>builder().build();
        }
        return PageListRespDTO.<UniversalAccountDetailDO>builder()
                .list(pageResult.getRecords())
                .total(page.getTotal())
                .build();
    }

    @Override
    public UniversalAccountDetailDO queryById(String userId, Long accountDetailId) {
        if (StringUtils.isBlank(userId) || Objects.isNull(accountDetailId)) {
            log.error("queryById userId or accountDetailId is null");
            return null;
        }
        Wrapper<UniversalAccountDetailDO> wrapper = Wrappers.<UniversalAccountDetailDO>lambdaQuery()
                .eq(UniversalAccountDetailDO::getUserId, userId)
                .eq(UniversalAccountDetailDO::getId, accountDetailId);
        return universalAccountDetailMapper.selectOne(wrapper);
    }

    @Override
    public List<UniversalAccountDetailDO> queryAvailableAccountDetails(String userId, List<Long> useActivityIds, Integer benefitType) {
        Wrapper<UniversalAccountDetailDO> wrapper = Wrappers.<UniversalAccountDetailDO>lambdaQuery()
                .eq(UniversalAccountDetailDO::getUserId, userId)
                .in(UniversalAccountDetailDO::getUseActivityId, useActivityIds)
                .eq(UniversalAccountDetailDO::getBenefitType, benefitType)
                .gt(UniversalAccountDetailDO::getAvailableCount, Constants.ZERO);
        return universalAccountDetailMapper.selectList(wrapper);
    }

    @Override
    public int deductDetailAvailableCount(String userId, Long id, Integer deductCount, Integer version) {
        return universalAccountDetailMapper.deductAvailableCount(userId, id, deductCount, version);
    }

    @Override
    public List<UniversalAccountDetailDO> queryNeedExpireAccountDetails(Integer shardingIndex, List<String> userIds,
                                                                        List<Integer> benefitTypes,
                                                                        Date expireStartTime, Date expireEndTime,
                                                                        Long lastId, Integer limit) {
        if (Objects.isNull(shardingIndex) || Objects.isNull(lastId) || Objects.isNull(limit)) {
            log.warn("queryNeedExpireAccountDetails shardingIndex or lastId or limit isNull shardingIndex={}, lastId={}, limit={}",
                    shardingIndex, lastId, limit);
            return null;
        }
        Wrapper<UniversalAccountDetailDO> wrapper = Wrappers.<UniversalAccountDetailDO>lambdaQuery()
                .and(w -> w.apply("sharding_index = {0}", shardingIndex))
                .in(CollectionUtils.isNotEmpty(userIds), UniversalAccountDetailDO::getUserId, userIds)
                .in(CollectionUtils.isNotEmpty(benefitTypes), UniversalAccountDetailDO::getBenefitType, benefitTypes)
                // detail.availableCount > 0，该账户明细才需要过期
                .gt(UniversalAccountDetailDO::getAvailableCount, 0L)
                // expireStartTime <= detail.expireTime <= expireEndTime，该账户明细才需要过期
                .ge(Objects.nonNull(expireStartTime), UniversalAccountDetailDO::getExpireTime, expireStartTime)
                .le(Objects.nonNull(expireEndTime), UniversalAccountDetailDO::getExpireTime, expireEndTime)
                .gt(UniversalAccountDetailDO::getId, lastId)
                .orderByAsc(UniversalAccountDetailDO::getId)
                .last("limit " + limit);
        return universalAccountDetailMapper.selectList(wrapper);
    }

    @MonitorSpan(name = "fetchDeductOperateAccountDetailsByPage")
    @Override
    public List<DeductOperateAccountDetailDTO> fetchDeductOperateAccountDetailsByPage(String userId, List<Long> useActivityIds,
                                                                                      Integer benefitType, Integer limit,
                                                                                      Long offset) {
        if (StringUtils.isBlank(userId) || CollectionUtils.isEmpty(useActivityIds) ||
                Objects.isNull(benefitType) || Objects.isNull(limit) || Objects.isNull(offset)) {
            log.warn("fetchDeductOperateAccountDetailsByPage params isError userId={}, useActivityIds={}, benefitType={}, " +
                    "limit={}, offset={}", userId, JSON.toJSONString(useActivityIds), benefitType, limit, offset);
            return null;
        }
        return universalAccountDetailMapper.fetchDeductOperateAccountDetailsByPage(userId, useActivityIds, benefitType,
                limit, offset);
    }

    @MonitorSpan(name = "deductDetailCount")
    @Override
    public boolean deductDetailCount(String userId, Long accountDetailId, Integer operateCount) {
        if (StringUtils.isBlank(userId) || Objects.isNull(accountDetailId) || Objects.isNull(operateCount) || operateCount <= 0) {
            log.warn("deductDetailCount param isError userId={}, accountDetailId={}, operateCount={}", userId,
                    accountDetailId, operateCount);
            return false;
        }
        // 如果 availableCount >= deduceCount，更新 availableCount -= deduceCount
        Wrapper<UniversalAccountDetailDO> wrapper = Wrappers.<UniversalAccountDetailDO>lambdaUpdate()
                .setSql("`version` = `version` + 1" +
                        ", `available_count` = `available_count` - " + operateCount)
                .eq(UniversalAccountDetailDO::getUserId, userId)
                .eq(UniversalAccountDetailDO::getId, accountDetailId)
                .ge(UniversalAccountDetailDO::getAvailableCount, operateCount);
        int updateRow = universalAccountDetailMapper.update(null, wrapper);
        return updateRow == 1;
    }

    @MonitorSpan(name = "deductDetailZero")
    @Override
    public boolean deductDetailZero(String userId, List<Long> accountDetailIds) {
        if (StringUtils.isBlank(userId) || CollectionUtils.isEmpty(accountDetailIds)) {
            log.warn("deductDetailZero param isError userId={}", userId);
            return false;
        }
        // 如果 availableCount > 0，更新 availableCount = 0
        Wrapper<UniversalAccountDetailDO> wrapper = Wrappers.<UniversalAccountDetailDO>lambdaUpdate()
                .setSql("`version` = `version` + 1" +
                        ", `available_count` = 0")
                .eq(UniversalAccountDetailDO::getUserId, userId)
                .in(UniversalAccountDetailDO::getId, accountDetailIds)
                .gt(UniversalAccountDetailDO::getAvailableCount, 0);
        int updateRow = universalAccountDetailMapper.update(null, wrapper);
        // 只有影响行数 = accountDetailIds.size 才算更新成功
        return updateRow == CollectionUtils.size(accountDetailIds);
    }

    @MonitorSpan(name = "expireDetailCount")
    @Override
    public boolean expireDetailCount(String userId, Long accountDetailId, Integer operateCount, Integer version) {
        if (StringUtils.isBlank(userId) || Objects.isNull(accountDetailId) || Objects.isNull(operateCount) || operateCount <= 0) {
            log.warn("expireDetailCount params isError userId={}, detailId={}, operateCount={}", userId,
                    accountDetailId, operateCount);
            return false;
        }
        Wrapper<UniversalAccountDetailDO> wrapper = Wrappers.<UniversalAccountDetailDO>lambdaUpdate()
                .setSql("`version` = `version` + 1" +
                        ", `expire_count` = `expire_count` + " + operateCount +
                        ", `available_count` = `available_count` - " + operateCount)
                .eq(UniversalAccountDetailDO::getUserId, userId)
                .eq(UniversalAccountDetailDO::getId, accountDetailId)
                .gt(UniversalAccountDetailDO::getAvailableCount, operateCount)
                .eq(Objects.nonNull(version), UniversalAccountDetailDO::getVersion, version);
        int updateRow = universalAccountDetailMapper.update(null, wrapper);
        return updateRow == 1;
    }

    @MonitorSpan(name = "expireDetailZero")
    @Override
    public boolean expireDetailZero(String userId, List<Long> accountDetailIds) {
        if (StringUtils.isBlank(userId) || CollectionUtils.isEmpty(accountDetailIds)) {
            log.warn("expireDetailZero param isError userId={}", userId);
            return false;
        }
        // 需要先 set expire_count，再 set available_count
        Wrapper<UniversalAccountDetailDO> wrapper = Wrappers.<UniversalAccountDetailDO>lambdaUpdate()
                .setSql("`version` = `version` + 1" +
                        ", `expire_count` = `expire_count` + `available_count`" +
                        ", `available_count` = 0")
                .eq(UniversalAccountDetailDO::getUserId, userId)
                .in(UniversalAccountDetailDO::getId, accountDetailIds)
                .gt(UniversalAccountDetailDO::getAvailableCount, 0);
        int updateRow = universalAccountDetailMapper.update(null, wrapper);
        // 只有影响行数 = accountDetailIds.size 才算更新成功
        return updateRow == CollectionUtils.size(accountDetailIds);
    }
}
