package com.ddmc.equity.domain.entity.account;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.RuleLimitInfoKeys;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.enums.SubAccountRecordExtKeys;
import com.ddmc.equity.common.enums.SubAccountStatusEnum;
import com.ddmc.equity.domain.dto.EquityBenefitSendMsgDTO;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.enums.AccountType;
import com.ddmc.equity.infra.repository.dao.SubAccountBaseFieldDO;
import com.ddmc.equity.infra.repository.dao.SubAccountRecordBaseFieldDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.SubAccountRecordDTO;
import com.ddmc.equity.model.vo.BenefitReceiveSuccessMsgVO;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/25 16:06
 * @description
 */
public class AccountConvertEntity {

    protected static void createSubAccountRecordBaseFieldDO(SubAccountRecordBaseFieldDO subAccountRecordBaseFieldDO,
                                                            int operateType, EquityAccountContext equityAccountContext) {

        Map<String, Object> extMap = Maps.newHashMap();
        extMap.put(SubAccountRecordExtKeys.CITY_CODE, equityAccountContext.getCityCode());
        String reqNo = equityAccountContext.getSerialNumber();
        subAccountRecordBaseFieldDO.setActivityId(equityAccountContext.getActivityId());
        subAccountRecordBaseFieldDO.setStrategyId(equityAccountContext.getStrategyId());
        subAccountRecordBaseFieldDO.setBenefitGroupId(equityAccountContext.getBenefitGroupId());
        subAccountRecordBaseFieldDO.setBenefitId(equityAccountContext.getBenefitId());
        subAccountRecordBaseFieldDO.setAccountId(equityAccountContext.getAccountId());
        subAccountRecordBaseFieldDO.setUserId(equityAccountContext.getUid());
        subAccountRecordBaseFieldDO.setOperateType(operateType);
        subAccountRecordBaseFieldDO.setStatus(StatusEnum.INIT.getCode());
        subAccountRecordBaseFieldDO.setAppId(equityAccountContext.getAppId());
        subAccountRecordBaseFieldDO.setPageId(equityAccountContext.getPageId());
        subAccountRecordBaseFieldDO.setSource(equityAccountContext.getSource());
        subAccountRecordBaseFieldDO.setReqNo(reqNo);
        subAccountRecordBaseFieldDO.setAuxKey(reqNo);
        subAccountRecordBaseFieldDO.setExt(MapUtils.isEmpty(extMap) ? null : JSON.toJSONString(extMap));
        subAccountRecordBaseFieldDO.setOpAdminName(equityAccountContext.getOperator());
        subAccountRecordBaseFieldDO.setEditAdminName(equityAccountContext.getOperator());
    }

    protected static void createSubAccountBaseFieldDO(SubAccountBaseFieldDO subAccountBaseFieldDO,
                                                      EquityAccountContext equityAccountContext) {
        subAccountBaseFieldDO.setActivityId(equityAccountContext.getActivityId());
        subAccountBaseFieldDO.setStrategyId(equityAccountContext.getStrategyId());
        subAccountBaseFieldDO.setBenefitGroupId(equityAccountContext.getBenefitGroupId());
        subAccountBaseFieldDO.setBenefitId(equityAccountContext.getBenefitId());
        subAccountBaseFieldDO.setUserId(equityAccountContext.getUid());
        subAccountBaseFieldDO.setStatus(SubAccountStatusEnum.UNUSED.getCode());
        subAccountBaseFieldDO.setLastReqNo(equityAccountContext.getSerialNumber());
        subAccountBaseFieldDO.setOpAdminName(equityAccountContext.getOperator());
        subAccountBaseFieldDO.setEditAdminName(equityAccountContext.getOperator());
    }

    public static Map<String, Object> getRuleLimitInfoMap(EquityAccountContext equityAccountContext) {
        Map<String, Object> ruleLimitInfoMap = Maps.newHashMap();
        Long activityReceiveLimitId = equityAccountContext.getActivityReceiveLimitId();
        if (Objects.nonNull(activityReceiveLimitId)) {
            ruleLimitInfoMap.put(RuleLimitInfoKeys.ACTIVITY_RECEIVE_LIMIT_ID, activityReceiveLimitId);
        }
        return ruleLimitInfoMap;
    }

    public static void fillUpdateBaseAccountRecordDO(SubAccountRecordBaseFieldDO update,
                                                     Long accountRecordId, Long accountId, Integer status,
                                                     EquityRpcDto equityRpcDto, Map<String, Object> ruleLimitInfoMap) {
        update.setAccountId(Objects.nonNull(accountId) ? accountId : null);
        update.setStatus(status);
        update.setAuxKey(Objects.equals(StatusEnum.FAIL.getCode(), status) ? String.valueOf(accountRecordId) : null);
        update.setRuleLimitInfo(MapUtils.isEmpty(ruleLimitInfoMap) ? null : JSON.toJSONString(ruleLimitInfoMap));
        update.setRpcCode(Objects.nonNull(equityRpcDto) ? equityRpcDto.getCode() : null);
        update.setRpcMsg(Objects.nonNull(equityRpcDto) ? equityRpcDto.getMessage() : null);
    }

    public static void fillSubAccountRecordDTO(SubAccountRecordDTO recordDTO, SubAccountRecordBaseFieldDO recordDO) {
        recordDTO.setId(recordDO.getId());
        recordDTO.setActivityId(recordDO.getActivityId());
        recordDTO.setStrategyId(recordDO.getStrategyId());
        recordDTO.setBenefitGroupId(recordDO.getBenefitGroupId());
        recordDTO.setBenefitId(recordDO.getBenefitId());
        recordDTO.setAccountId(recordDO.getAccountId());
        recordDTO.setUserId(recordDO.getUserId());
        recordDTO.setOperateType(recordDO.getOperateType());
        recordDTO.setStatus(recordDO.getStatus());
    }

    public static AccountInfoDTO convertToBaseAccountInfoDTO(SubAccountBaseFieldDO accountDO) {
        return AccountInfoDTO.builder()
                .activityId(accountDO.getActivityId())
                .strategyId(accountDO.getStrategyId())
                .benefitGroupId(accountDO.getBenefitGroupId())
                .benefitId(accountDO.getBenefitId())
                .accountId(accountDO.getId())
                .userId(accountDO.getUserId())
                .status(accountDO.getStatus())
                .createTime(accountDO.getCreateTime())
                .lastReqNo(accountDO.getLastReqNo())
                .build();
    }

    public static void changeEquityAccountContextWhileRetry(EquityAccountContext equityAccountContext,
                                                            SubAccountRecordDTO existRecord) {
        equityAccountContext.setAccountRecordId(existRecord.getId());
        equityAccountContext.setActivityId(existRecord.getActivityId());
        equityAccountContext.setStrategyId(existRecord.getStrategyId());
        equityAccountContext.setBenefitGroupId(existRecord.getBenefitGroupId());
        equityAccountContext.setBenefitId(existRecord.getBenefitId());
        equityAccountContext.setEquityValue(existRecord.getBenefitValue());
        equityAccountContext.setBalanceMoney(existRecord.getBalanceMoney());
        equityAccountContext.setProductId(existRecord.getProductId());
        equityAccountContext.setSendTicketScene(existRecord.getSendTicketScene());
        equityAccountContext.setSendBalanceScene(existRecord.getSendBalanceScene());
    }

    public static Integer convertToBenefitTypeByAccountType(Integer accountType) {
        return Optional.ofNullable(accountType).map(AccountType::getByAccountType)
                .map(AccountType::getBenefitType).orElse(null);
    }

    public static BenefitReceiveSuccessMsgVO convertToBenefitReceiveSuccessMsgVO(EquityAccountContext context) {
        Integer benefitTypeByAccountType = convertToBenefitTypeByAccountType(context.getAccountType());
        BenefitReceiveSuccessMsgVO msgVO = UniversalAccountConvertEntity.convertToSceneBenefitReceiveSuccessMsgVO(context);
        msgVO.setIsOldAccount(Boolean.TRUE);
        // 老账户领取成功上下文可能没有传 benefitType，需要通过 accountType 转化
        msgVO.setBenefitType(ObjectUtils.defaultIfNull(context.getBenefitType(), benefitTypeByAccountType));
        // 老账户领取成功上下文可能没有传 sendAmount
        String realSendAmount = UniversalAccountAmountCalEntity.getCompatibleOldRealSendAmount(context.getSendAmount(),
                context.getBalanceMoney(), benefitTypeByAccountType, context.getEquityValue());
        msgVO.setSendAmount(realSendAmount);
        // 老账户领取成功上下文可能没有传 benefitType，导致没有设置 balanceMoney
        msgVO.setBalanceMoney(UniversalAccountAmountCalEntity.getReceivedBalanceMoneyJudgedType(
                benefitTypeByAccountType, realSendAmount));
        return msgVO;
    }

    public static EquityBenefitSendMsgDTO convertToBenefitSendMsgDTO(EquityAccountContext equityAccountContext) {
        // 老账户领取成功上下文可能没有传 benefitType，需要通过 accountType 转化
        Integer benefitTypeByAccountType = convertToBenefitTypeByAccountType(equityAccountContext.getAccountType());
        return EquityBenefitSendMsgDTO.builder()
                .reqNo(equityAccountContext.getSerialNumber())
                .userId(equityAccountContext.getUid())
                .sceneCode(equityAccountContext.getSceneCode())
                .activityId(equityAccountContext.getActivityId())
                .strategyId(equityAccountContext.getStrategyId())
                .benefitGroupId(equityAccountContext.getBenefitGroupId())
                .benefitId(equityAccountContext.getBenefitId())
                .benefitType(ObjectUtils.defaultIfNull(equityAccountContext.getBenefitType(), benefitTypeByAccountType))
                .benefitValue(equityAccountContext.getEquityValue())
                .balanceMoney(equityAccountContext.getBalanceMoney())
                .build();
    }
}
