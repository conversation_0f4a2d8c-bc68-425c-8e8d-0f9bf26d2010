package com.ddmc.equity.domain.converter.common;

import com.ddmc.equity.domain.dto.ReceiveSceneBenefitDTO;
import com.ddmc.equity.dto.customer.ReceiveSceneBenefitResDTO;
import com.ddmc.equity.dto.customer.UniversalReceiveSceneBenefitResDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface ReceiveSceneBenefitConverter {
    ReceiveSceneBenefitConverter INSTANCE = Mappers.getMapper(ReceiveSceneBenefitConverter.class);

    @Mapping(target = "receivedBenefitList", source = "successList")
    @Mapping(target = "receiveFailBenefitList", source = "failList")
    @Mapping(target = "receiveProcessBenefitList", source = "processList")
    ReceiveSceneBenefitResDTO convertToReceiveSceneBenefitResDTO(ReceiveSceneBenefitDTO receiveSceneBenefitDTO);


    @Mapping(target = "receivedBenefitList", source = "successList")
    @Mapping(target = "receiveFailBenefitList", source = "failList")
    @Mapping(target = "receiveProcessBenefitList", source = "processList")
    UniversalReceiveSceneBenefitResDTO convertToUniversalReceiveSceneBenefitResDTO(ReceiveSceneBenefitDTO receiveSceneBenefitDTO);
}
