package com.ddmc.equity.domain.service.low_stock_alarm;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.domain.dto.benefit_stock.LowStockAlarmConditionDTO;
import com.ddmc.equity.domain.dto.benefit_stock.LowStockAlarmContextDTO;
import com.ddmc.equity.domain.entity.benefit_stock.BenefitStockConvertEntity;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.model.dto.ActivityCacheDto;
import com.ddmc.equity.model.dto.StrategyCacheBaseDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 库存不足提醒工厂类
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2024/1/25 14:48
 * @description 库存不足提醒工厂类
 */
@Slf4j
@Component
public class LowStockAlarmFactory {

    /**
     * 库存不足提醒条件
     */
    @ApolloJsonValue("${equity.lowStockAlarm.conditions.v1:[]}")
    private List<LowStockAlarmConditionDTO> lowStockAlarmConditions;

    @Resource
    private RedisCache redisCache;
    @Resource
    private LocalCacheManager localCacheManager;
    @Resource
    private Map<String, LowStockAlarm> lowStockAlarmMap;

    /**
     * 库存不足提醒处理入口方法
     *
     * @param activityId   活动 ID
     * @param strategyId   策略 ID
     * @param benefitId    权益 ID
     * @param planDateType 计划库存类型
     * @see com.ddmc.equity.common.enums.PlanDateTypeEnum
     */
    public void benefitLowStockAlarm(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                     Integer planDateType) {
        try {
            // 1 查询到的活动信息不完整，无需提醒
            ActivityCacheDto activityCacheDTO = localCacheManager.getActivityCacheDtoById(activityId);
            StrategyCacheBaseDto strategyCacheBaseDTO = localCacheManager.getStrategyCacheByActivityIdAndStrategyId(activityId,
                    strategyId);
            EquityBenefitDO benefitDO = localCacheManager.getBenefitInfoById(benefitId);
            if (Objects.isNull(activityCacheDTO) || Objects.isNull(strategyCacheBaseDTO) || Objects.isNull(benefitDO)) {
                log.warn("benefitLowStockAlarm activity or strategy or benefit is null activityId={}, strategyId={}, benefitId={}",
                        activityId, strategyId, benefitId);
                CsossUtils.logEvent(MonitorConstants.LOW_STOCK_ALARM, "activity_is_null");
                return;
            }

            // 1 未找到对应活动场景的处理类，无需提醒
            String sceneCode = activityCacheDTO.getSceneCode();
            // 先获取配置条件用于检查匹配的实现类
            List<LowStockAlarmConditionDTO.LowStockAlarmRuleDTO> matchedAlarmRules = getAllLowStockAlarmRules(sceneCode);
            if (CollectionUtils.isEmpty(matchedAlarmRules)) {
                log.info("benefitLowStockAlarm no matchedAlarmRules activityId={}, strategyId={}, benefitId={}, sceneCode={}",
                        activityId, strategyId, benefitId, sceneCode);
                return;
            }

            // 2 构建库存不足提醒上下文
            String planDateStr = DateUtil.format_yyyyMMdd(new Date());
            LowStockAlarmContextDTO alarmContextDTO = BenefitStockConvertEntity.convertToLowStockAlarmContextDTO(
                    activityCacheDTO, strategyCacheBaseDTO, benefitDO, planDateType, planDateStr);
            // 2.1 从 Redis 获取库存值，并设置到上下文中
            getAndSetContextStockValue(alarmContextDTO);

            // 3 库存不足提醒 - 执行所有匹配的实现类
            executeByAlarmRules(alarmContextDTO, matchedAlarmRules);
        } catch (Exception e) {
            log.error("benefitLowStockAlarm exception activityId={}, strategyId={}, benefitId={}",
                    activityId, strategyId, benefitId, e);
            CsossUtils.logEvent(MonitorConstants.LOW_STOCK_ALARM, "execute_exception");
        }
    }

    /**
     * 获取所有匹配的库存不足提醒规则
     *
     * @param sceneCode 场景代码
     */
    private List<LowStockAlarmConditionDTO.LowStockAlarmRuleDTO> getAllLowStockAlarmRules(String sceneCode) {
        List<LowStockAlarmConditionDTO.LowStockAlarmRuleDTO> allRules = Lists.newArrayList();

        // 合并默认规则、核心规则和场景规则
        Lists.newArrayList("default_low_stock_alarm", "core_low_stock_alarm", sceneCode)
                .forEach(code -> {
                    List<LowStockAlarmConditionDTO.LowStockAlarmRuleDTO> rules = getLowStockAlarmRules(code);
                    if (CollectionUtils.isNotEmpty(rules)) {
                        allRules.addAll(rules);
                    }
                });

        // 通过 alarmClass 去重，保留第一个匹配的规则
        return new ArrayList<>(allRules.stream()
                .collect(Collectors.toMap(
                        LowStockAlarmConditionDTO.LowStockAlarmRuleDTO::getAlarmClass,
                        rule -> rule,
                        (existing, replacement) -> existing))
                .values());
    }

    /**
     * 根据场景代码获取库存不足提醒规则
     *
     * @param sceneCode 场景代码
     */
    private List<LowStockAlarmConditionDTO.LowStockAlarmRuleDTO> getLowStockAlarmRules(String sceneCode) {
        return CollectionUtils.isEmpty(lowStockAlarmConditions) ? null :
                lowStockAlarmConditions.stream()
                        .filter(conditionDTO -> StringUtils.equals(conditionDTO.getSceneCode(), sceneCode))
                        .map(LowStockAlarmConditionDTO::getAlarmRules)
                        .filter(CollectionUtils::isNotEmpty)
                        .flatMap(List::stream)
                        .collect(Collectors.toList());
    }

    /**
     * 执行匹配的库存不足提醒规则
     *
     * @param contextDTO        提醒上下文
     * @param matchedAlarmRules 匹配的提醒规则列表
     */
    private void executeByAlarmRules(LowStockAlarmContextDTO contextDTO,
                                     List<LowStockAlarmConditionDTO.LowStockAlarmRuleDTO> matchedAlarmRules) {
        if (CollectionUtils.isEmpty(matchedAlarmRules)) {
            return;
        }

        for (LowStockAlarmConditionDTO.LowStockAlarmRuleDTO rule : matchedAlarmRules) {
            try {
                String alarmClass = rule.getAlarmClass();
                LowStockAlarm lowStockAlarm = lowStockAlarmMap.get(alarmClass);
                if (lowStockAlarm == null) {
                    log.error("executeByAlarmRules lowStockAlarm is null context={}, alarmClass={}",
                            JSON.toJSONString(contextDTO), alarmClass);
                    CsossUtils.logEvent(MonitorConstants.LOW_STOCK_ALARM, "low_stock_alarm_is_null");
                    continue;
                }

                contextDTO.setLowStockAlarmRule(rule);
                lowStockAlarm.lowStockAlarm(contextDTO);
            } catch (Exception e) {
                log.error("executeByAlarmRules execute exception context={}, rule={}",
                        JSON.toJSONString(contextDTO), rule.getAlarmClass(), e);
                CsossUtils.logEvent(MonitorConstants.LOW_STOCK_ALARM, "execute_by_alarm_rule_exception");
            }
        }
    }

    /**
     * 从缓存获取并设置上下文的库存数据
     *
     * @param contextDTO 提醒上下文
     */
    private void getAndSetContextStockValue(LowStockAlarmContextDTO contextDTO) {
        List<String> cacheKeys = Lists.newArrayList();
        String stockCacheKey = contextDTO.getStockCacheKey();
        String balanceStockCacheKey = contextDTO.getBalanceStockCacheKey();
        boolean hasStock = contextDTO.isHasStock();
        if (hasStock) {
            cacheKeys.add(stockCacheKey);
            cacheKeys.add(balanceStockCacheKey);
        }
        String planStockCacheKey = contextDTO.getPlanStockCacheKey();
        String planBalanceStockCacheKey = contextDTO.getPlanBalanceStockCacheKey();
        boolean hasPlanStock = contextDTO.isHasPlanStock();
        if (hasPlanStock) {
            cacheKeys.add(planStockCacheKey);
            cacheKeys.add(planBalanceStockCacheKey);
        }
        if (CollectionUtils.isEmpty(cacheKeys)) {
            log.info("getAndSetContextStockValue cacheKey is null contextDTO={}", JSON.toJSONString(contextDTO));
            return;
        }

        Map<String, Long> cacheResult = getStockValueMap(cacheKeys);
        if (hasStock) {
            long stock = cacheResult.getOrDefault(stockCacheKey, 0L);
            long balanceStock = cacheResult.getOrDefault(balanceStockCacheKey, 0L);
            contextDTO.setStock(stock);
            contextDTO.setBalanceStock(balanceStock);
        }
        if (hasPlanStock) {
            long planStock = cacheResult.getOrDefault(planStockCacheKey, 0L);
            long planBalanceStock = cacheResult.getOrDefault(planBalanceStockCacheKey, 0L);
            contextDTO.setPlanStock(planStock);
            contextDTO.setPlanBalanceStock(planBalanceStock);
        }
    }

    /**
     * 批量获取缓存中的库存值并转换为 Long 类型
     *
     * @param cacheKeys 缓存键列表
     */
    private Map<String, Long> getStockValueMap(List<String> cacheKeys) {
        if (CollectionUtils.isEmpty(cacheKeys)) {
            return Maps.newHashMap();
        }
        Map<String, String> cacheResult = redisCache.getMulStrValue(cacheKeys);
        if (MapUtils.isEmpty(cacheResult)) {
            return Maps.newHashMap();
        }
        Map<String, Long> stockValueMap = Maps.newHashMap();
        cacheResult.forEach((k, v) -> stockValueMap.put(k, NumberUtils.convertToLong(v)));
        return stockValueMap;
    }
}
