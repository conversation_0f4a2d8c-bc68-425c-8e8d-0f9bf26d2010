package com.ddmc.equity.domain.valueobject.benefit;

import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class BenefitListFilterRespVO implements Serializable {
    private static final long serialVersionUID = -2714475210446761754L;

    @ApiModelProperty("权益列表")
    @NotNull
    @Builder.Default
    private List<EquityBenefitDO> list = Lists.newArrayList();

    @ApiModelProperty("权益记录总数")
    @NotNull
    @Builder.Default
    private Long total = 0L;
}
