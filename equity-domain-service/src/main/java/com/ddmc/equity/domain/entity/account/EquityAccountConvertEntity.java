package com.ddmc.equity.domain.entity.account;

import cn.hutool.core.bean.BeanUtil;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.enums.AccountStatusEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.SubAccountStatusEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.dto.account.UniversalAccountDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountDetailDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountDetailInnerExtDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRecordDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRecordInnerExtDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcRequestExtDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcResponseExtDTO;
import com.ddmc.equity.domain.dto.benefit.BenefitExtInfoDTO;
import com.ddmc.equity.dto.business.EquityAccountRspDTO;
import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.ddmc.equity.dto.customer.account.AccountBenefitDTO;
import com.ddmc.equity.dto.customer.account.AccountDistributeBenefitReqDTO;
import com.ddmc.equity.dto.customer.account.AccountDistributeBenefitRespDTO;
import com.ddmc.equity.dto.customer.account.AccountQueryBenefitReqDTO;
import com.ddmc.equity.dto.customer.account.AccountQueryBenefitRespDTO;
import com.ddmc.equity.dto.customer.account.AccountUseBenefitReqDTO;
import com.ddmc.equity.dto.customer.account.BalanceUserAccountDTO;
import com.ddmc.equity.dto.customer.account.CouponUserAccountDTO;
import com.ddmc.equity.dto.customer.account.DrawCodeUserAccountDTO;
import com.ddmc.equity.dto.customer.account.PriceProductUserAccountDTO;
import com.ddmc.equity.dto.customer.account.QueryUserAccountsReqDTO;
import com.ddmc.equity.dto.customer.account.UserAccountDTO;
import com.ddmc.equity.dto.customer.benefit.BenefitAccountRpcResponseExtDTO;
import com.ddmc.equity.enums.AccountType;
import com.ddmc.equity.enums.UniversalAccountDetailStatusEnum;
import com.ddmc.equity.infra.repository.dao.EquityAccountDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.BalanceAccountInfoDTO;
import com.ddmc.equity.model.dto.CouponAccountInfoDTO;
import com.ddmc.equity.model.dto.DrawCodeAccountInfoDTO;
import com.ddmc.equity.model.dto.PriceProductAccountInfoDTO;
import com.ddmc.equity.model.dto.UseBenefitDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/5/30 15:41
 * @description
 */
public class EquityAccountConvertEntity {

    public static List<EquityAccountRspDTO> convertToEquityAccountRspDTOList(List<AccountInfoDTO> accountInfoDTOList) {
        if (CollectionUtils.isEmpty(accountInfoDTOList)) {
            return null;
        }
        return accountInfoDTOList.stream().map(e -> EquityAccountRspDTO.builder().id(e.getAccountId()).build())
                .collect(Collectors.toList());
    }

    public static Integer checkAndConvertToAccountType(Integer benefitType) {
        AccountType accountTypeEnum = AccountType.getByBenefitType(benefitType);
        Assert.notNull(accountTypeEnum, ExceptionEnum.ILLEGAL_ARGS.getCode(), "权益账户类型异常");
        return accountTypeEnum.getAccountType();
    }

    public static String getAccountOperateBeanName(Integer benefitType) {
        AccountType accountTypeEnum = AccountType.getByBenefitType(benefitType);
        if (Objects.isNull(accountTypeEnum) || StringUtils.isBlank(accountTypeEnum.getBeanName())) {
            return null;
        }
        return accountTypeEnum.getBeanName();
    }

    public static List<String> getAccountOperateBeanNames(List<Integer> benefitTypes) {
        if (CollectionUtils.isEmpty(benefitTypes)) {
            return null;
        }
        return benefitTypes.stream()
                .map(EquityAccountConvertEntity::getAccountOperateBeanName)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    public static EquityAccountContext createEquityAccountContext(AccountDistributeBenefitReqDTO req, Integer accountType,
                                                                  Long benefitId) {
        BaseRequestDTO baseRequestDTO = req.getBaseRequestDTO();
        Assert.mustTrue(Objects.nonNull(baseRequestDTO) && StringUtils.isNotBlank(baseRequestDTO.getUserId()),
                ExceptionEnum.ILLEGAL_ARGS.getCode(), "发放权益 userId 不能为空");
        EquityAccountContext equityAccountContext = new EquityAccountContext();
        equityAccountContext.setAppId(req.getAppId());
        equityAccountContext.setUid(baseRequestDTO.getUserId());
        equityAccountContext.setAccountType(accountType);
        equityAccountContext.setBenefitId(benefitId);
        equityAccountContext.setOperateType(OperateTypeEnum.PROVIDE.getCode());
        equityAccountContext.setSerialNumber(req.getReqNo());
        equityAccountContext.setDrawCodeSource(req.getDrawCodeSource());
        equityAccountContext.setDistributeCount(req.getDistributeCount());
        equityAccountContext.setExtMap(req.getExtMap());
        return equityAccountContext;
    }

    public static EquityAccountContext createEquityAccountContext(AccountQueryBenefitReqDTO req, Integer accountType) {
        BaseRequestDTO baseRequestDTO = req.getBaseRequestDTO();
        Assert.mustTrue(Objects.nonNull(baseRequestDTO) && StringUtils.isNotBlank(baseRequestDTO.getUserId()),
                ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询权益 userId 不能为空");
        List<Integer> statuses = Lists.newArrayList(SubAccountStatusEnum.UNUSED.getCode());
        if (Boolean.TRUE.equals(req.getContainUsedBenefits())) {
            statuses.add(SubAccountStatusEnum.USED.getCode());
        }
        EquityAccountContext equityAccountContext = new EquityAccountContext();
        equityAccountContext.setUid(baseRequestDTO.getUserId());
        equityAccountContext.setAccountType(accountType);
        equityAccountContext.setStatuses(statuses);
        equityAccountContext.setStartDate(req.getStartDate());
        equityAccountContext.setEndDate(req.getEndDate());
        equityAccountContext.setDrawCodeSources(req.getDrawCodeSources());
        return equityAccountContext;
    }

    public static EquityAccountContext createEquityAccountContext(AccountUseBenefitReqDTO req, Integer accountType, Long benefitId) {
        BaseRequestDTO baseRequestDTO = req.getBaseRequestDTO();
        Assert.mustTrue(Objects.nonNull(baseRequestDTO) && StringUtils.isNotBlank(baseRequestDTO.getUserId()),
                ExceptionEnum.ILLEGAL_ARGS.getCode(), "使用权益 userId 不能为空");
        EquityAccountContext equityAccountContext = new EquityAccountContext();
        equityAccountContext.setAppId(req.getAppId());
        equityAccountContext.setUid(baseRequestDTO.getUserId());
        equityAccountContext.setAccountType(accountType);
        equityAccountContext.setBenefitId(benefitId);
        equityAccountContext.setOperateType(OperateTypeEnum.USE.getCode());
        equityAccountContext.setSerialNumber(req.getReqNo());
        equityAccountContext.setUseBenefits(convertToUseBenefitDTOList(req));
        return equityAccountContext;
    }

    private static List<UseBenefitDTO> convertToUseBenefitDTOList(AccountUseBenefitReqDTO req) {
        List<AccountUseBenefitReqDTO.BenefitDTO> useBenefitsReq = req.getUseBenefits();
        if (CollectionUtils.isEmpty(useBenefitsReq)) {
            return null;
        }
        return useBenefitsReq.stream().map(e -> UseBenefitDTO.builder().drawCode(e.getDrawCode()).build()).collect(Collectors.toList());
    }

    public static EquityAccountDO createEquityAccountDO(EquityAccountContext equityAccountContext) {
        Integer accountType = equityAccountContext.getAccountType();
        AccountType accountTypeEnum = AccountType.getByAccountType(accountType);
        String accountName = Optional.ofNullable(accountTypeEnum).map(AccountType::getDesc).orElse(StringUtils.EMPTY);
        EquityAccountDO equityAccountDO = new EquityAccountDO();
        equityAccountDO.setUserId(equityAccountContext.getUid());
        equityAccountDO.setStatus(AccountStatusEnum.VALID.getCode());
        equityAccountDO.setAccountType(accountType);
        equityAccountDO.setAccountName(accountName);
        return equityAccountDO;
    }

    public static AccountDistributeBenefitRespDTO createAccountDistributeBenefitRespDTO(List<AccountInfoDTO> accountInfoDTOList) {
        List<AccountBenefitDTO> accountBenefitDTOList = accountInfoDTOList.stream()
                .map(EquityAccountConvertEntity::convertToAccountBenefitDTO).collect(Collectors.toList());
        return AccountDistributeBenefitRespDTO.builder()
                .benefits(accountBenefitDTOList)
                .build();
    }

    public static AccountQueryBenefitRespDTO createAccountQueryBenefitRespDTO(List<AccountInfoDTO> accountInfoDTOList) {
        if (CollectionUtils.isEmpty(accountInfoDTOList)) {
            return AccountQueryBenefitRespDTO.builder().build();
        }
        List<AccountBenefitDTO> usableBenefits = accountInfoDTOList.stream()
                .filter(e -> SubAccountStatusEnum.UNUSED.getCode().equals(e.getStatus()))
                .map(EquityAccountConvertEntity::convertToAccountBenefitDTO).collect(Collectors.toList());
        List<AccountBenefitDTO> usedBenefits = accountInfoDTOList.stream()
                .filter(e -> SubAccountStatusEnum.USED.getCode().equals(e.getStatus()))
                .map(EquityAccountConvertEntity::convertToAccountBenefitDTO).collect(Collectors.toList());
        return AccountQueryBenefitRespDTO.builder()
                .usableBenefits(CollectionUtils.isEmpty(usableBenefits) ? null : usableBenefits)
                .usedBenefits(CollectionUtils.isEmpty(usedBenefits) ? null : usedBenefits)
                .build();
    }

    private static AccountBenefitDTO convertToAccountBenefitDTO(AccountInfoDTO accountInfoDTO) {
        AccountBenefitDTO accountBenefitDTO = AccountBenefitDTO.builder()
                .benefitId(accountInfoDTO.getBenefitId())
                .accountId(accountInfoDTO.getAccountId())
                .benefitType(accountInfoDTO.getBenefitType())
                .createTime(accountInfoDTO.getCreateTime())
                .build();
        DrawCodeAccountInfoDTO drawCodeAccountInfoDTO = accountInfoDTO.getDrawCodeAccountInfoDTO();
        if (Objects.nonNull(accountBenefitDTO)) {
            accountBenefitDTO.setDrawCode(drawCodeAccountInfoDTO.getDrawCode());
            accountBenefitDTO.setDrawCodeSource(drawCodeAccountInfoDTO.getDrawCodeSource());
            accountBenefitDTO.setExtMap(drawCodeAccountInfoDTO.getExtMap());
        }
        return accountBenefitDTO;
    }

    public static void checkQueryUserAccountsReqDTO(QueryUserAccountsReqDTO req) {
        Assert.notNull(req, ExceptionEnum.ILLEGAL_ARGS.getCode(), "获取用户权益账户记录 req 不能为空");
        Assert.mustTrue(StringUtils.isNotBlank(req.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "获取用户权益账户记录 userId 不能为空");
        Assert.mustTrue(StringUtils.isNotBlank(req.getSceneCode()) || CollectionUtils.isNotEmpty(req.getBenefitTypes()),
                ExceptionEnum.ILLEGAL_ARGS.getCode(), "获取用户权益账户记录场景 code 或权益类型不能为空");
    }

    public static EquityAccountContext createEquityAccountContext(QueryUserAccountsReqDTO req, Integer accountType) {
        EquityAccountContext equityAccountContext = new EquityAccountContext();
        equityAccountContext.setUid(req.getUserId());
        equityAccountContext.setAccountType(accountType);
        equityAccountContext.setActivityIds(req.getActivityIds());
        equityAccountContext.setStartDate(req.getStartDate());
        equityAccountContext.setEndDate(req.getEndDate());
        equityAccountContext.setStatuses(req.getStatuses());
        return equityAccountContext;
    }

    public static EquityAccountContext createEquityAccountContext(QueryUserAccountsReqDTO req) {
        return createEquityAccountContext(req, null);
    }

    public static List<UserAccountDTO> convertToAccountInfoDTOList(List<AccountInfoDTO> accountInfoDTOList) {
        return accountInfoDTOList.stream().map(e -> UserAccountDTO.builder()
                .activityId(e.getActivityId())
                .strategyId(e.getStrategyId())
                .benefitGroupId(e.getBenefitGroupId())
                .benefitId(e.getBenefitId())
                .benefitType(e.getBenefitType())
                .status(e.getStatus())
                .createTime(e.getCreateTime())
                .rpcResponseExtDTO(convertToRpcResponseExtDTO(e.getRpcResponseExtDTO()))
                .balanceUserAccountDTO(convertToBalanceUserAccountDTO(e.getBalanceAccountInfoDTO()))
                .couponUserAccountDTO(convertToCouponUserAccountDTO(e.getCouponAccountInfoDTO()))
                .drawCodeUserAccountDTO(convertToDrawCodeUserAccountDTO(e.getDrawCodeAccountInfoDTO()))
                .priceProductUserAccountDTO(convertToPriceProductUserAccountDTO(e.getPriceProductAccountInfoDTO()))
                .lastReqNo(e.getLastReqNo())
                .build()
        ).collect(Collectors.toList());
    }

    private static BenefitAccountRpcResponseExtDTO convertToRpcResponseExtDTO(UniversalAccountRpcResponseExtDTO rpcResponseExtDTO) {
        return Objects.isNull(rpcResponseExtDTO) ? null :
                BeanUtil.copyProperties(rpcResponseExtDTO, BenefitAccountRpcResponseExtDTO.class);
    }

    private static BalanceUserAccountDTO convertToBalanceUserAccountDTO(BalanceAccountInfoDTO balanceAccountInfoDTO) {
        if (Objects.isNull(balanceAccountInfoDTO)) {
            return null;
        }
        return BalanceUserAccountDTO.builder()
                .balanceMoney(balanceAccountInfoDTO.getBalanceMoney())
                .build();
    }

    private static CouponUserAccountDTO convertToCouponUserAccountDTO(CouponAccountInfoDTO couponAccountInfoDTO) {
        if (Objects.isNull(couponAccountInfoDTO)) {
            return null;
        }
        return CouponUserAccountDTO.builder()
                .ticketId(couponAccountInfoDTO.getTicketId())
                .userTicketId(couponAccountInfoDTO.getUserTicketId())
                .build();
    }

    private static DrawCodeUserAccountDTO convertToDrawCodeUserAccountDTO(DrawCodeAccountInfoDTO drawCodeAccountInfoDTO) {
        if (Objects.isNull(drawCodeAccountInfoDTO)) {
            return null;
        }
        return DrawCodeUserAccountDTO.builder()
                .drawCode(drawCodeAccountInfoDTO.getDrawCode())
                .drawCodeSource(drawCodeAccountInfoDTO.getDrawCodeSource())
                .extMap(drawCodeAccountInfoDTO.getExtMap())
                .build();
    }

    private static PriceProductUserAccountDTO convertToPriceProductUserAccountDTO(PriceProductAccountInfoDTO priceProductAccountInfoDTO) {
        if (Objects.isNull(priceProductAccountInfoDTO)) {
            return null;
        }
        return PriceProductUserAccountDTO.builder()
                .productId(priceProductAccountInfoDTO.getProductId())
                .build();
    }

    public static List<UniversalAccountDTO> convertToUniversalAccountDTOList(List<UniversalAccountDO> dos) {
        if (CollectionUtils.isEmpty(dos)) {
            return null;
        }
        return dos.stream().map(EquityAccountConvertEntity::convertToUniversalAccountDTO)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private static UniversalAccountDTO convertToUniversalAccountDTO(UniversalAccountDO universalAccountDO) {
        if (Objects.isNull(universalAccountDO)) {
            return null;
        }
        return UniversalAccountDTO.builder()
                .userId(universalAccountDO.getUserId())
                .accountId(universalAccountDO.getId())
                .activityId(universalAccountDO.getUseActivityId())
                .benefitType(universalAccountDO.getBenefitType())
                .totalCount(universalAccountDO.getTotalCount())
                .availableCount(universalAccountDO.getAvailableCount())
                .expireCount(universalAccountDO.getExpireCount())
                .build();
    }

    public static List<UniversalAccountDetailDTO> convertToUniversalAccountDetailDTOList(List<UniversalAccountDetailDO> accountDetailDOList,
                                                                                         List<EquityBenefitDO> benefitDOList) {
        if (CollectionUtils.isEmpty(accountDetailDOList)) {
            return null;
        }
        Map<Long /* benefitId */, EquityBenefitDO> benefitDOMap = convertToBenefitDOMap(benefitDOList);
        return accountDetailDOList.stream().map(accountDetailDO ->
                convertToUniversalAccountDetailDTO(accountDetailDO, benefitDOMap.get(accountDetailDO.getBenefitId()))
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static UniversalAccountDetailDTO convertToUniversalAccountDetailDTO(UniversalAccountDetailDO accountDetailDO,
                                                                               EquityBenefitDO benefitDO) {
        if (Objects.isNull(accountDetailDO)) {
            return null;
        }
        return UniversalAccountDetailDTO.builder()
                .userId(accountDetailDO.getUserId())
                .accountDetailId(accountDetailDO.getId())
                .accountId(accountDetailDO.getAccountId())
                .sceneCode(accountDetailDO.getSceneCode())
                .activityId(accountDetailDO.getActivityId())
                .strategyId(accountDetailDO.getStrategyId())
                .benefitGroupId(accountDetailDO.getBenefitGroupId())
                .benefitId(accountDetailDO.getBenefitId())
                .benefitType(accountDetailDO.getBenefitType())
                .benefitValue(accountDetailDO.getBenefitValue())
                .benefitName(Optional.ofNullable(benefitDO).map(EquityBenefitDO::getName).orElse(null))
                .benefitImage(getBenefitImage(benefitDO))
                .totalCount(accountDetailDO.getTotalCount())
                .availableCount(accountDetailDO.getAvailableCount())
                .expireCount(accountDetailDO.getExpireCount())
                .status(convertToUniversalAccountDetailStatus(accountDetailDO))
                .expireTime(accountDetailDO.getExpireTime())
                .source(accountDetailDO.getSource())
                .rpcRequestExtDTO(JsonUtil.parseObject(accountDetailDO.getRpcRequestExt(), UniversalAccountRpcRequestExtDTO.class))
                .rpcResponseExtDTO(JsonUtil.parseObject(accountDetailDO.getRpcResponseExt(), UniversalAccountRpcResponseExtDTO.class))
                .innerExtDTO(JsonUtil.parseObject(accountDetailDO.getInnerExt(), UniversalAccountDetailInnerExtDTO.class))
                .createTime(accountDetailDO.getCreateTime())
                .updateTime(accountDetailDO.getUpdateTime())
                .build();
    }

    private static Integer convertToUniversalAccountDetailStatus(UniversalAccountDetailDO accountDetailDO) {
        Integer expireCount = accountDetailDO.getExpireCount();
        if (Objects.nonNull(expireCount) && expireCount > 0) {
            return UniversalAccountDetailStatusEnum.EXPIRED.getCode();
        }
        Integer availableCount = accountDetailDO.getAvailableCount();
        if (Objects.nonNull(availableCount) && availableCount > 0) {
            return UniversalAccountDetailStatusEnum.UNUSED.getCode();
        }
        return UniversalAccountDetailStatusEnum.USED.getCode();
    }

    public static List<UniversalAccountRecordDTO> convertToUniversalAccountRecordDTOList(List<UniversalAccountRecordDO> accountRecordDOList,
                                                                                         List<EquityBenefitDO> benefitDOList) {

        if (CollectionUtils.isEmpty(accountRecordDOList)) {
            return null;
        }
        Map<Long /* benefitId */, EquityBenefitDO> benefitDOMap = convertToBenefitDOMap(benefitDOList);
        return accountRecordDOList.stream().map(accountRecordDO ->
                convertToUniversalAccountRecordDTO(accountRecordDO, benefitDOMap.get(accountRecordDO.getBenefitId()))
        ).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static UniversalAccountRecordDTO convertToUniversalAccountRecordDTO(UniversalAccountRecordDO accountRecordDO,
                                                                               EquityBenefitDO benefitDO) {
        if (Objects.isNull(accountRecordDO)) {
            return null;
        }
        return UniversalAccountRecordDTO.builder()
                .userId(accountRecordDO.getUserId())
                .accountRecordId(accountRecordDO.getId())
                .accountId(accountRecordDO.getAccountId())
                .useActivityId(accountRecordDO.getUseActivityId())
                .sceneCode(accountRecordDO.getSceneCode())
                .activityId(accountRecordDO.getActivityId())
                .strategyId(accountRecordDO.getStrategyId())
                .benefitGroupId(accountRecordDO.getBenefitGroupId())
                .benefitId(accountRecordDO.getBenefitId())
                .benefitType(accountRecordDO.getBenefitType())
                .benefitValue(accountRecordDO.getBenefitValue())
                .benefitName(Optional.ofNullable(benefitDO).map(EquityBenefitDO::getName).orElse(null))
                .benefitImage(getBenefitImage(benefitDO))
                .reqNo(accountRecordDO.getReqNo())
                .operateType(accountRecordDO.getOperateType())
                .status(accountRecordDO.getStatus())
                .operateCount(accountRecordDO.getOperateCount())
                .appId(accountRecordDO.getAppId())
                .pageId(accountRecordDO.getPageId())
                .source(accountRecordDO.getSource())
                .rpcRequestExtDTO(JsonUtil.parseObject(accountRecordDO.getRpcRequestExt(), UniversalAccountRpcRequestExtDTO.class))
                .rpcResponseExtDTO(JsonUtil.parseObject(accountRecordDO.getRpcResponseExt(), UniversalAccountRpcResponseExtDTO.class))
                .innerExtDTO(JsonUtil.parseObject(accountRecordDO.getInnerExt(), UniversalAccountRecordInnerExtDTO.class))
                .createTime(accountRecordDO.getCreateTime())
                .updateTime(accountRecordDO.getUpdateTime())
                .build();
    }

    public static Map<Long, EquityBenefitDO> convertToBenefitDOMap(List<EquityBenefitDO> benefitDOList) {
        return CollectionUtils.isEmpty(benefitDOList) ? Maps.newHashMap() :
                benefitDOList.stream().collect(Collectors.toMap(EquityBenefitDO::getId, Function.identity(), (var1, var2) -> var1));
    }

    private static String getBenefitImage(EquityBenefitDO benefitDO) {
        return Optional.ofNullable(benefitDO).map(EquityBenefitDO::getExtInfo).map(e -> JsonUtil.parseObject(e, BenefitExtInfoDTO.class))
                .map(BenefitExtInfoDTO::getBenefitImage).orElse(null);
    }

    public static <T> List<Long> getBenefitIds(List<T> dOList) {
        if (CollectionUtils.isEmpty(dOList)) {
            return null;
        }
        return dOList.stream().map(e -> {
                    if (e instanceof UniversalAccountDetailDO) {
                        return ((UniversalAccountDetailDO) e).getBenefitId();
                    }
                    if (e instanceof UniversalAccountRecordDO) {
                        return ((UniversalAccountRecordDO) e).getBenefitId();
                    }
                    return null;
                })
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }
}
