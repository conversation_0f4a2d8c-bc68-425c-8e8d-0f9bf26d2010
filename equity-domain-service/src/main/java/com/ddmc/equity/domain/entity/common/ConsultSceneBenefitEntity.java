package com.ddmc.equity.domain.entity.common;

import com.ddmc.equity.dto.customer.ActivityStrategyDTO;
import com.ddmc.equity.dto.customer.BenefitDTO;
import com.ddmc.equity.dto.customer.ConsultSceneBenefitResDTO;
import com.ddmc.equity.dto.customer.SceneActivityDTO;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class ConsultSceneBenefitEntity {

    /**
     * 实体转换
     *
     * @param sceneCode                 场景code
     * @param sceneActivityCacheDtoList 场景活动信息
     * @return ConsultSceneBenefitResDTO
     */
    @NotNull
    public ConsultSceneBenefitResDTO convertToConsultSceneBenefitResDTO(String sceneCode, List<SceneActivityCacheDto> sceneActivityCacheDtoList) {
        ConsultSceneBenefitResDTO consultSceneBenefitResDTO = ConsultSceneBenefitResDTO.builder().sceneCode(sceneCode).build();
        if (CollectionUtils.isEmpty(sceneActivityCacheDtoList)) {
            return consultSceneBenefitResDTO;
        }
        List<SceneActivityDTO> sceneActivityDTOList = Lists.newArrayList();
        for (SceneActivityCacheDto sceneActivityCacheDto : sceneActivityCacheDtoList) {
            if (CollectionUtils.isEmpty(sceneActivityCacheDto.getStrategyCacheDtoList())) {
                continue;
            }
            List<ActivityStrategyDTO> strategyDTOList = Lists.newArrayList();
            for (StrategyCacheDto strategyCacheDto : sceneActivityCacheDto.getStrategyCacheDtoList()) {
                //权益过滤
                List<BenefitDTO> benefitDTOList = filterBenefitList(strategyCacheDto.getStrategyBenefitGroup(),
                        sceneActivityCacheDto.getActivityId(), sceneActivityCacheDto.getExternalId());
                if (CollectionUtils.isEmpty(benefitDTOList)) {
                    continue;
                }
                ActivityStrategyDTO activityStrategyDTO = new ActivityStrategyDTO();
                activityStrategyDTO.setStrategyId(strategyCacheDto.getStrategyId());
                activityStrategyDTO.setExternalId(strategyCacheDto.getExternalId());
                activityStrategyDTO.setBenefitDTOList(benefitDTOList);
                strategyDTOList.add(activityStrategyDTO);
            }
            if (CollectionUtils.isEmpty(strategyDTOList)) {
                continue;
            }
            SceneActivityDTO sceneActivityDTO = new SceneActivityDTO();
            sceneActivityDTO.setActivityId(sceneActivityCacheDto.getActivityId());
            sceneActivityDTO.setExternalId(sceneActivityCacheDto.getExternalId());
            sceneActivityDTO.setStartTime(sceneActivityCacheDto.getStartTime());
            sceneActivityDTO.setEndTime(sceneActivityCacheDto.getEndTime());
            sceneActivityDTO.setExternalId(sceneActivityCacheDto.getExternalId());
            sceneActivityDTO.setStrategyDTOList(strategyDTOList);
            sceneActivityDTOList.add(sceneActivityDTO);
        }
        consultSceneBenefitResDTO.setSceneActivityDTOList(sceneActivityDTOList);
        return consultSceneBenefitResDTO;
    }

    /**
     * 权益过滤
     *
     * @param strategyBenefitGroup 权益组信息
     * @param activityId           活动id
     * @return 权益集合
     */
    private List<BenefitDTO> filterBenefitList(
            Map<Long, List<BenefitIdWithConfDto>> strategyBenefitGroup,
            Long activityId, String externalId) {
        if (CollectionUtils.isEmpty(strategyBenefitGroup)) {
            return null;
        }
        List<BenefitDTO> benefitDTOList = Lists.newArrayList();
        for (Long key : strategyBenefitGroup.keySet()) {
            List<BenefitIdWithConfDto> benefitIdWithConfDtos = strategyBenefitGroup.get(key);
            if (CollectionUtils.isEmpty(benefitIdWithConfDtos)) {
                continue;
            }
            for (BenefitIdWithConfDto benefitIdWithConfDto : benefitIdWithConfDtos) {
                BenefitDTO benefitDTO = new BenefitDTO();
                benefitDTO.setBenefitId(benefitIdWithConfDto.getBenefitDO().getId());
                benefitDTO.setBenefitName(benefitIdWithConfDto.getBenefitDO().getName());
                benefitDTO.setBenefitValue(benefitIdWithConfDto.getBenefitDO().getBenefitValue());
                benefitDTO.setBenefitType(benefitIdWithConfDto.getBenefitDO().getBenefitType());
                benefitDTO.setActivityId(activityId);
                benefitDTO.setExternalId(externalId);
                benefitDTOList.add(benefitDTO);
            }
        }
        return benefitDTOList;
    }
}
