package com.ddmc.equity.domain.service.benefit_stock;

import com.ddmc.equity.domain.dto.benefit_stock.QueryStockStatusByRedisMulReq;
import com.ddmc.equity.domain.dto.benefit_stock.QueryStockStatusByRedisMulResp;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.dto.customer.benefit_stock.QueryBenefitStockReqDTO;
import com.ddmc.equity.dto.customer.benefit_stock.QueryBenefitStockRespDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockPlanDO;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface EquityBenefitStockDomainService {

    /**
     * 扣减库存，权益组维度（具体到权益组下的权益维度）
     */
    @Nullable Boolean deductStock(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                  @NotNull Long num, @NotNull String auxKey,
                                  @NotNull StockLimitRuleDTO stockLimitRuleDTO);

    /**
     * 释放库存，权益组维度（具体到权益组下的权益维度）
     */
    @Nullable Boolean releaseStock(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                   @NotNull Long num, @NotNull String auxKey,
                                   @NotNull StockLimitRuleDTO stockLimitRuleDTO);

    /**
     * 是否存在库存和计划库存
     *
     * @param strategyId     策略 id
     * @param benefitGroupId 权益组 id
     * @param benefitId      权益 id
     * @param planDateType   计划库存类型
     * @return 是否存在库存
     */
    boolean queryStockStatusByRedis(Long strategyId, Long benefitGroupId,
                                    Long benefitId, @NotNull Integer planDateType,
                                    @NotNull Date date);

    QueryStockStatusByRedisMulResp queryStockStatusByRedisMul(QueryStockStatusByRedisMulReq req);

    List<EquityBenefitStockDO> queryStockByStrategyIds(Collection<Long> strategyIds);


    void scanFlowTableJob(Long cursor, long batchNumber, Set<Integer> operationTypes, List<Long> includeIds, List<Long> excludeIds);

    void generatePlanStockNextTime(Long cursor, long batchNumber, String date, List<Long> includeIds, List<Long> excludeIds);

    /**
     * 查询计划库存 DO
     *
     * @param activityId   活动 id
     * @param strategyId   策略 id
     * @param benefitId    权益 id
     * @param planDateType 计划库存类型
     * @param dateStr      日期
     * @return 计划库存 DO
     */
    EquityBenefitStockPlanDO queryBenefitStockPlanDO(@NotNull Long activityId, @NotNull Long strategyId,
                                                     @NotNull Long benefitId, @NotNull Integer planDateType,
                                                     @NotNull String dateStr);

    /**
     * 从 BD 查询权益维度库存（包含总库存、剩余库存）
     *
     * @param activityId 活动 id
     * @param strategyId 策略 id
     * @param benefitId  权益 id
     * @return 权益维度库存
     */
    EquityBenefitStockDO getBenefitStock(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId);

    /**
     * 从 BD 查询权益维度计划库存（包含总计划库存、剩余计划库存）
     *
     * @param activityId   活动 id
     * @param strategyId   策略 id
     * @param benefitId    权益 id
     * @param planDateType 计划库存类型
     * @param planDate     计划库存日期
     * @return 权益维度计划库存
     */
    EquityBenefitStockPlanDO getBenefitPlanStock(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                                 @NotNull Integer planDateType, @NotNull String planDate);

    /**
     * 检查并刷新权益维度库存（包含总库存、剩余库存）到 redis
     * <p>
     * 1 如果 redis 值不存在。BD 中不存在，则打点告警；BD 中存在，则从 BD 刷新到 redis，并打点告警
     * 2 如果 redis 值存在，但是过期时间不对，则延长有效期
     *
     * @param activityId 活动 id
     * @param strategyId 策略 id
     * @param benefitId  权益 id
     * @param actEndTime 活动结束时间
     */
    void checkAndRefreshRedisStock(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                   Date actEndTime);

    /**
     * 检查并刷新权益维度计划库存（包含总计划库存、剩余计划库存）到 redis
     * <p>
     * 1 如果 redis 值不存在。BD 中不存在，则打点告警；BD 中存在，则从 BD 刷新到 redis，并打点告警
     * 2 如果 redis 值存在，但是过期时间不对，则延长有效期
     *
     * @param activityId   活动 id
     * @param strategyId   策略 id
     * @param benefitId    权益 id
     * @param planDateType 计划库存类型
     * @param planDate     计划库存日期
     */
    void checkAndRefreshRedisPlanStock(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                       Integer planDateType, @NotNull Date planDate);

    /**
     * 批量查询权益库存信息
     *
     * @param req 入参
     * @return 查询结果
     */
    QueryBenefitStockRespDTO queryBenefitStock(QueryBenefitStockReqDTO req);
}
