package com.ddmc.equity.domain.service.cash;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ddmc.equity.infra.repository.dao.CashCouponAccountDO;

import java.util.Date;
import java.util.List;


public interface CashCouponAccountDomainService extends IService<CashCouponAccountDO> {

    /**
     * 创建权益子账户
     *
     * @param accountDO DB_DO
     */
    void insertAccountDO(CashCouponAccountDO accountDO);

    /**
     * 通过 userId 查询权益子账户列表
     *
     * @param userId      用户 id
     * @param activityIds 活动 id。如果未指定活动 id，则查询所有
     * @param statuses    权益子账户状态。如果未指定状态，则查询所有
     * @param startDate   开始时间。可以指定开始时间（创建时间）查询，不指定则查询所有
     * @param endDate     结束时间。可以指定结束时间（创建时间）查询，不指定则查询所有
     * @return 权益子账户列表
     */
    List<CashCouponAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds,
                                                    List<Integer> statuses, Date startDate, Date endDate);
}
