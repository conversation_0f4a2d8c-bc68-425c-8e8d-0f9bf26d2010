package com.ddmc.equity.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/8/29 15:13
 * @description 区间范围
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class NumberRangeDTO {

    /**
     * 当前值 >= lower
     */
    private Long lower;
    /**
     * 当前值 <= upper
     */
    private Long upper;
}
