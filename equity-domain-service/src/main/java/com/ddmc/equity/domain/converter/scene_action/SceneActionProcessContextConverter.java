package com.ddmc.equity.domain.converter.scene_action;

import com.ddmc.equity.common.config.MapstructBaseMapperConfig;
import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessContext;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/7/2 15:41
 * @description
 */
@Mapper(config = MapstructBaseMapperConfig.class)
public interface SceneActionProcessContextConverter {

    SceneActionProcessContextConverter INSTANCE = Mappers.getMapper(SceneActionProcessContextConverter.class);

    SceneActionProcessContext buildSceneActionProcessContext(BaseRequestDTO req);
}
