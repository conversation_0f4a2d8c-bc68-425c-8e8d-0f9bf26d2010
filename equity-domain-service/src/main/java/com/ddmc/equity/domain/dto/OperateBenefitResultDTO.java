package com.ddmc.equity.domain.dto;

import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.model.dto.EquityAccountInfoDTO;
import lombok.Data;

@Data
public class OperateBenefitResultDTO {

    /**
     * 是否成功
     */
    private boolean isSuccess;
    /**
     * 返回值
     */
    private String result;

    private String code;

    private String msg;

    private EquityAccountInfoDTO equityAccountInfoDto;

    public static OperateBenefitResultDTO result(boolean result) {
        OperateBenefitResultDTO operateBenefitResultDTO = new OperateBenefitResultDTO();
        operateBenefitResultDTO.setSuccess(result);
        return operateBenefitResultDTO;
    }

    public static OperateBenefitResultDTO result(boolean result, EquityAccountInfoDTO equityAccountInfoDto) {
        OperateBenefitResultDTO operateBenefitResultDTO = new OperateBenefitResultDTO();
        operateBenefitResultDTO.setSuccess(result);
        operateBenefitResultDTO.setEquityAccountInfoDto(equityAccountInfoDto);
        return operateBenefitResultDTO;
    }

    public static OperateBenefitResultDTO fail(ExceptionEnum exceptionEnum) {
        OperateBenefitResultDTO operateBenefitResultDTO = new OperateBenefitResultDTO();
        operateBenefitResultDTO.setSuccess(false);
        operateBenefitResultDTO.setCode(exceptionEnum.getCode());
        operateBenefitResultDTO.setMsg(exceptionEnum.getMessage());
        return operateBenefitResultDTO;
    }
}
