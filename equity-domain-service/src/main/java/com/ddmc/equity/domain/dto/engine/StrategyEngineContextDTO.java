package com.ddmc.equity.domain.dto.engine;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/7/6 16:27
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class StrategyEngineContextDTO extends ActivityEngineContextDTO {

    /**
     * 策略 id
     */
    private Long strategyId;
    /**
     * 策略外部关联 id。如果是来源玩法的活动，则为活动 prizeId；如果是膨胀券活动，则为母券券模板 id；
     */
    private String strategyExternalId;
    /**
     * 策略上的发放类型。1-全部发放；2-随机发放；3-概率发放；4-优先级发放；
     *
     * @see com.ddmc.equity.enums.StrategySendTypeEnum
     */
    private Integer strategySendType;
    /**
     * 是否需要忽略库存、频次限制规则
     */
    private boolean ignoreReceiveLimit;
}
