package com.ddmc.equity.domain.service.core;

import com.ddmc.equity.domain.dto.DrawAndReceiveReqDTO;
import com.ddmc.equity.domain.dto.DrawAndReceiveRespDTO;
import com.ddmc.equity.domain.dto.InitActivityCountReqDTO;
import com.ddmc.equity.domain.dto.SceneReceiveReqDTO;
import com.ddmc.equity.domain.dto.SceneReceiveRespDTO;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/29 16:21
 * @description
 */
public interface UniversalBenefitOperateCoreService {

    /**
     * 初始化活动次数
     *
     * @param req 入参
     */
    Boolean initActivityCount(InitActivityCountReqDTO req);

    /**
     * 指定活动（或者指定活动 + 策略）抽奖并领取
     *
     * @param req 入参
     * @return 领取结果
     */
    DrawAndReceiveRespDTO drawAndReceive(DrawAndReceiveReqDTO req);

    /**
     * 指定活动（或者指定活动 + 策略）抽奖
     *
     * @param req 入参
     * @return 抽中奖品信息
     */
    DrawAndReceiveRespDTO draw(DrawAndReceiveReqDTO req);

    /**
     * 指定活动 + 策略 + 权益领取（先咨询后领取）
     *
     * @param req 入参
     * @return 领取结果
     */
    SceneReceiveRespDTO sceneReceive(SceneReceiveReqDTO req);
}
