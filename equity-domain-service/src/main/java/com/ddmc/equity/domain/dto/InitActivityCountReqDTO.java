package com.ddmc.equity.domain.dto;

import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.ddmc.equity.dto.customer.CommonBaseRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/1/2 14:11
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("初始化活动次数 req")
public class InitActivityCountReqDTO extends CommonBaseRequestDTO {

    @ApiModelProperty(value = "基础请求")
    private BaseRequestDTO baseRequestDTO;

    @ApiModelProperty(value = "使用活动 id。具体权益发送到哪个活动上使用", required = true)
    @NotNull(message = "使用活动 id 为空")
    private Long useActivityId;

    @ApiModelProperty(value = "权益 id。发放活动次数需要指定使用哪个权益 id", required = true)
    @NotNull(message = "权益 id 为空")
    private Long benefitId;

    private String sendAmount;
}
