package com.ddmc.equity.domain.entity.common;

import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.converter.common.EngineContextConverter;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.ReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.ReceiveSceneBenefitDTO;
import com.ddmc.equity.domain.dto.ReqReceiveBenefitDTO;
import com.ddmc.equity.dto.customer.coupon_expand.CouponExpandConsultExpandTicketDTO;
import com.ddmc.equity.dto.customer.coupon_expand.CouponExpandConsultMasterTicketDTO;
import com.ddmc.equity.dto.customer.coupon_expand.CouponExpandConsultReqDTO;
import com.ddmc.equity.dto.customer.coupon_expand.CouponExpandConsultRespDTO;
import com.ddmc.equity.dto.customer.coupon_expand.CouponExpandReceiveBenefitDTO;
import com.ddmc.equity.dto.customer.coupon_expand.CouponExpandReceiveReqDTO;
import com.ddmc.equity.dto.customer.coupon_expand.CouponExpandReceiveRespBenefitDTO;
import com.ddmc.equity.dto.customer.coupon_expand.CouponExpandReceiveRespDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/12 16:28
 * @description
 */
public class CouponExpandConvertEntity {

    public static EngineContextDTO convertToEngineContextDTO(CouponExpandConsultReqDTO req) {
        EngineContextDTO engineContextDTO = EngineContextConverter.INSTANCE.convertToEngineContextDTO(req.getBaseRequestDTO());
        engineContextDTO.setAppId(req.getAppId());
        engineContextDTO.setPageId(req.getPageId());
        engineContextDTO.setSource(req.getSource());
        engineContextDTO.setActivityIdList(req.getActivityIds());
        engineContextDTO.setStrategyExternalIdList(req.getMasterTicketIds());
        return engineContextDTO;
    }

    public static EngineContextDTO convertToEngineContextDTO(CouponExpandReceiveReqDTO req) {
        List<Long> strategyIds = req.getReceiveBenefits().stream().map(CouponExpandReceiveBenefitDTO::getStrategyId)
                .collect(Collectors.toList());
        EngineContextDTO engineContextDTO = EngineContextConverter.INSTANCE.convertToEngineContextDTO(req.getBaseRequestDTO());
        engineContextDTO.setAppId(req.getAppId());
        engineContextDTO.setPageId(req.getPageId());
        engineContextDTO.setSource(req.getSource());
        engineContextDTO.setStrategyIdList(strategyIds);
        engineContextDTO.setIsReceive(Boolean.TRUE);
        return engineContextDTO;
    }

    public static List<ReqReceiveBenefitDTO> convertToReqReceiveBenefitDTOList(CouponExpandReceiveReqDTO req) {
        return req.getReceiveBenefits().stream().map(e -> {
            ReqReceiveBenefitDTO reqReceiveBenefitDTO = new ReqReceiveBenefitDTO();
            reqReceiveBenefitDTO.setStrategyId(e.getStrategyId());
            reqReceiveBenefitDTO.setMasterUserTicketId(e.getMasterUserTicketId());
            reqReceiveBenefitDTO.setBenefitId(e.getBenefitId());
            return reqReceiveBenefitDTO;
        }).collect(Collectors.toList());
    }

    public static CouponExpandConsultRespDTO convertToCouponExpandConsultRespDTO(List<SceneActivityCacheDto> sceneActivityCacheDtoList) {
        if (CollectionUtils.isEmpty(sceneActivityCacheDtoList)) {
            return null;
        }
        List<CouponExpandConsultMasterTicketDTO> masterTicketDTOList = sceneActivityCacheDtoList.stream()
                .map(CouponExpandConvertEntity::convertToMasterTicketDTOList)
                .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
        return CouponExpandConsultRespDTO.builder().masterTicketDTOList(masterTicketDTOList).build();
    }

    private static List<CouponExpandConsultMasterTicketDTO> convertToMasterTicketDTOList(SceneActivityCacheDto activityCacheDto) {
        List<StrategyCacheDto> strategyCacheDtoList = activityCacheDto.getStrategyCacheDtoList();
        if (CollectionUtils.isEmpty(strategyCacheDtoList)) {
            return null;
        }
        return strategyCacheDtoList.stream().map(e -> convertToMasterTicketDTO(activityCacheDto, e))
                .collect(Collectors.toList());
    }

    private static CouponExpandConsultMasterTicketDTO convertToMasterTicketDTO(SceneActivityCacheDto activityCacheDto,
                                                                               StrategyCacheDto strategyCacheDto) {
        List<BenefitIdWithConfDto> benefitIdWithConfDtoList = strategyCacheDto.getStrategyBenefitGroup().get(strategyCacheDto.getStrategyId());
        return CouponExpandConsultMasterTicketDTO.builder()
                .activityId(activityCacheDto.getActivityId())
                .strategyId(strategyCacheDto.getStrategyId())
                .masterTicketId(strategyCacheDto.getExternalId())
                .expandTicketDTOList(convertToExpandTicketDTOList(benefitIdWithConfDtoList))
                .build();
    }

    private static List<CouponExpandConsultExpandTicketDTO> convertToExpandTicketDTOList(List<BenefitIdWithConfDto> benefitIdWithConfDtoList) {
        if (CollectionUtils.isEmpty(benefitIdWithConfDtoList)) {
            return null;
        }
        return benefitIdWithConfDtoList.stream().map(CouponExpandConvertEntity::convertToExpandTicketDTO).collect(Collectors.toList());
    }

    private static CouponExpandConsultExpandTicketDTO convertToExpandTicketDTO(BenefitIdWithConfDto benefitIdWithConfDto) {
        String benefitValue = Optional.ofNullable(benefitIdWithConfDto.getBenefitDO()).map(EquityBenefitDO::getBenefitValue).orElse(null);
        return CouponExpandConsultExpandTicketDTO.builder()
                .benefitGroupId(benefitIdWithConfDto.getBenefitGroupId())
                .benefitId(benefitIdWithConfDto.getId())
                .expandTicketId(benefitValue)
                .build();
    }

    public static CouponExpandReceiveRespDTO convertToCouponExpandReceiveRespDTO(ReceiveSceneBenefitDTO receiveSceneBenefitDTO) {
        Assert.notNull(receiveSceneBenefitDTO, ExceptionEnum.COUPON_EXPAND_FAILURE);
        return CouponExpandReceiveRespDTO.builder()
                .receiveSuccessBenefits(convertToReceiveRespBenefitDTOList(receiveSceneBenefitDTO.getSuccessList()))
                .receiveFailureBenefits(convertToReceiveRespBenefitDTOList(receiveSceneBenefitDTO.getFailList()))
                .receivingBenefits(convertToReceiveRespBenefitDTOList(receiveSceneBenefitDTO.getProcessList()))
                .build();
    }

    public static List<CouponExpandReceiveRespBenefitDTO> convertToReceiveRespBenefitDTOList(List<ReceiveBenefitDTO> receiveBenefitDTOList) {
        if (CollectionUtils.isEmpty(receiveBenefitDTOList)) {
            return null;
        }
        return receiveBenefitDTOList.stream().map(e -> CouponExpandReceiveRespBenefitDTO.builder()
                .activityId(e.getActivityId())
                .strategyId(e.getStrategyId())
                .masterUserTicketId(e.getMasterUserTicketId())
                .benefitGroupId(e.getBenefitGroupId())
                .benefitId(e.getBenefitId())
                .expandUserTicketId(e.getThirdResNo())
                .build()).collect(Collectors.toList());
    }
}
