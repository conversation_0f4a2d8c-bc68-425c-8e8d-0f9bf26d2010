package com.ddmc.equity.domain.service.benefitMapping.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ddmc.equity.domain.service.benefitMapping.BenefitMappingService;
import com.ddmc.equity.dto.business.provide.ProvideBenefitDTO;
import com.ddmc.equity.infra.repository.dao.AutoIncrementDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitMappingDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitMappingMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BenefitMappingServiceImpl implements BenefitMappingService {

    @Resource
    private EquityBenefitMappingMapper benefitMappingMapper;

    @Override
    public List<EquityBenefitMappingDO> queryAllBenefitMapping() {
        return benefitMappingMapper.selectList(null);
    }

    @Override
    public EquityBenefitMappingDO queryAllBenefitMappingByMappingId(String mappingId, Long strategyId) {
        QueryWrapper<EquityBenefitMappingDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mapping_id", mappingId);
        queryWrapper.eq("strategy_id", strategyId);
        return benefitMappingMapper.selectOne(queryWrapper);
    }

    @Override
    public EquityBenefitMappingDO queryAllBenefitMappingByBenefitIdAndStrategyId(Long benefitId, Long strategyId) {
        QueryWrapper<EquityBenefitMappingDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("benefit_id", benefitId);
        queryWrapper.eq("strategy_id", strategyId);
        return benefitMappingMapper.selectOne(queryWrapper);
    }

    @Override
    public int addMapping(List<EquityBenefitDO> needCreateBenefitList, Long strategyId, List<ProvideBenefitDTO> benefitDTOList) {
        List<Long> benefitIdList = needCreateBenefitList.stream().map(AutoIncrementDO::getId).collect(Collectors.toList());
        LambdaQueryWrapper<EquityBenefitMappingDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(EquityBenefitMappingDO::getStrategyId, strategyId).in(EquityBenefitMappingDO::getBenefitId, benefitIdList);
        benefitMappingMapper.delete(lambdaQueryWrapper);
        lambdaQueryWrapper = new LambdaQueryWrapper<>();
        List<String> mappingList = benefitDTOList.stream().map(ProvideBenefitDTO::getMappingId).collect(Collectors.toList());
        lambdaQueryWrapper.eq(EquityBenefitMappingDO::getStrategyId, strategyId).in(EquityBenefitMappingDO::getMappingId, mappingList);
        benefitMappingMapper.delete(lambdaQueryWrapper);
        Map<String, String> stringMap = new HashMap<>();
        benefitDTOList.forEach(x -> stringMap.put(x.getBenefitValue() + "_" + x.getBenefitType(), x.getMappingId()));
        List<EquityBenefitMappingDO> benefitMappingDOList = needCreateBenefitList.stream().map(x -> {
            EquityBenefitMappingDO equityBenefitMappingDO = new EquityBenefitMappingDO();
            equityBenefitMappingDO.setBenefitId(x.getId());
            equityBenefitMappingDO.setStrategyId(strategyId);
            equityBenefitMappingDO.setMappingId(stringMap.get(x.getBenefitValue() + "_" + x.getBenefitType()));
            return equityBenefitMappingDO;
        }).collect(Collectors.toList());

        // 测试环境遇到一个情况，如果原mapping表里已有mapping_id映射了其他活动，
        // 即违反了一个外部活动id加一个权益id只对应一个mapping_id的原则
        // 这里会同步不进来，所以加一个日志和删除?
        if (CollectionUtils.isNotEmpty(benefitMappingDOList)) {
            List<String> mappIdList = benefitMappingDOList.stream().map(EquityBenefitMappingDO::getMappingId).collect(Collectors.toList());
            LambdaQueryWrapper<EquityBenefitMappingDO> queryCond = new LambdaQueryWrapper<EquityBenefitMappingDO>()
                    .in(EquityBenefitMappingDO::getMappingId, mappIdList)
                    .ne(EquityBenefitMappingDO::getStrategyId, strategyId);
            List<EquityBenefitMappingDO> needDelete = benefitMappingMapper.selectList(queryCond);
            if (CollectionUtils.isNotEmpty(needDelete)) {
                // 这种情况是有脏数据了，为了不阻塞Job，需要删除掉
                log.error("出现不同外部活动id对应到一个mapping id的情况，属于脏数据，需要删除 脏数据:{}", JSON.toJSONString(needDelete));
                benefitMappingMapper.deleteBatchIds(needDelete.stream().map(EquityBenefitMappingDO::getId).collect(Collectors.toList()));
            }
        }

        if (CollectionUtils.isNotEmpty(benefitMappingDOList)) {
            benefitMappingMapper.insertBatchSomeColumn(benefitMappingDOList);
        }
        return 1;
    }
}
