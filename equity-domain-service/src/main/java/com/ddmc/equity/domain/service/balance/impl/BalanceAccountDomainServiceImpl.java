package com.ddmc.equity.domain.service.balance.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.service.balance.BalanceAccountDomainService;
import com.ddmc.equity.dto.business.PageListReqDTO;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.infra.repository.dao.BalanceAccountDO;
import com.ddmc.equity.infra.repository.dao.mapper.BalanceAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/16 11:22
 * @description
 */
@Slf4j
@Service
public class BalanceAccountDomainServiceImpl extends ServiceImpl<BalanceAccountMapper, BalanceAccountDO>
        implements BalanceAccountDomainService {

    @Override
    public void insertAccountDO(BalanceAccountDO accountDO) {
        Assert.mustTrue(StringUtils.isNotBlank(accountDO.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "余额权益子账户 userId 不能为空");
        this.save(accountDO);
    }

    @Override
    public List<BalanceAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds,
                                                        List<Integer> statuses, Date startDate, Date endDate) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询余额权益子账户 userId 不能为空");

        Wrapper<BalanceAccountDO> wrapper = Wrappers.<BalanceAccountDO>lambdaQuery()
                .eq(BalanceAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(BalanceAccountDO::getUserId, userId)
                .in(CollectionUtils.isNotEmpty(activityIds), BalanceAccountDO::getActivityId, activityIds)
                .in(CollectionUtils.isNotEmpty(statuses), BalanceAccountDO::getStatus, statuses)
                .ge(Objects.nonNull(startDate), BalanceAccountDO::getCreateTime, startDate)
                .le(Objects.nonNull(endDate), BalanceAccountDO::getCreateTime, endDate)
                .orderByDesc(BalanceAccountDO::getId);
        return this.list(wrapper);
    }

    @Override
    public PageListRespDTO<BalanceAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds,
                                                                   List<Integer> statuses, Date startDate, Date endDate,
                                                                   PageListReqDTO pageListReq) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(),
                "分页查询余额权益子账户 userId 不能为空");

        Wrapper<BalanceAccountDO> wrapper = Wrappers.<BalanceAccountDO>lambdaQuery()
                .eq(BalanceAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(BalanceAccountDO::getUserId, userId)
                .in(CollectionUtils.isNotEmpty(activityIds), BalanceAccountDO::getActivityId, activityIds)
                .in(CollectionUtils.isNotEmpty(statuses), BalanceAccountDO::getStatus, statuses)
                .ge(Objects.nonNull(startDate), BalanceAccountDO::getCreateTime, startDate)
                .le(Objects.nonNull(endDate), BalanceAccountDO::getCreateTime, endDate)
                .orderByDesc(BalanceAccountDO::getCreateTime);
        IPage<BalanceAccountDO> page = new com.baomidou.mybatisplus.extension.plugins.pagination
                .Page<>(pageListReq.getPage(), pageListReq.getPageSize());

        IPage<BalanceAccountDO> pageResult = baseMapper.selectPage(page, wrapper);
        if (Objects.isNull(pageResult)) {
            log.warn("queryAccountsByUserId pageResult is null userId={}, activityIds={}, statuses={}" +
                            ", startDate={}, endDate={}, pageListReq={}",
                    userId, activityIds, statuses, startDate, endDate, JSON.toJSONString(pageListReq));
            return PageListRespDTO.<BalanceAccountDO>builder().build();
        }
        return PageListRespDTO.<BalanceAccountDO>builder()
                .list(pageResult.getRecords())
                .total(page.getTotal())
                .build();
    }

    @Override
    public BigDecimal getTotalBalanceMoney(String userId, List<Long> activityIds, Date startDate, Date endDate) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(),
                "查询用户获得的余额总金额 userId 不能为空");

        QueryWrapper<BalanceAccountDO> wrapper = new QueryWrapper<>();
        wrapper.select("SUM(balance_money) as balanceMoney")
                .eq("user_id", userId)
                // 目前 tb_balance_account 表只有翻牌的余额数据。如果不指定活动，则查询的是翻牌场景下的用户获得的余额总金额；
                .in(CollectionUtils.isNotEmpty(activityIds), "activity_id", activityIds)
                .ge(Objects.nonNull(startDate), "create_time", startDate)
                .le(Objects.nonNull(endDate), "create_time", endDate);
        Map<String, Object> result = this.getMap(wrapper);
        String balanceMoneyKey = "balanceMoney";
        return Objects.isNull(result) || !result.containsKey(balanceMoneyKey) ?
                BigDecimal.ZERO : (BigDecimal) result.get(balanceMoneyKey);
    }
}
