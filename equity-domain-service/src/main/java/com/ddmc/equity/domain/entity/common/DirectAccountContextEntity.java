package com.ddmc.equity.domain.entity.common;

import com.ddmc.equity.account.DirectAccountContext;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcRequestExtDTO;
import com.ddmc.equity.domain.entity.account.UniversalAccountAmountCalEntity;
import com.ddmc.equity.domain.entity.benefit.BenefitConvertEntity;
import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.ddmc.equity.dto.customer.SaveAndGetBenefitDTO;
import com.ddmc.equity.dto.customer.account.AccountDirectBenefitReqDTO;
import com.ddmc.equity.dto.customer.account.UniversalUseBenefitDetailDTO;
import com.ddmc.equity.dto.customer.account.UniversalUseBenefitTypeDTO;
import com.ddmc.equity.enums.BenefitReceiveTypeEnum;
import com.ddmc.equity.enums.DirectProvideBenefitExtKeys;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;
import com.ddmc.equity.model.vo.BenefitReceiveSuccessMsgVO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@Slf4j
public class DirectAccountContextEntity {

    public static DirectAccountContext convertDirectAccountContext(AccountDirectBenefitReqDTO dto, EquityBenefitDO benefitDO) {
        Integer benefitType = benefitDO.getBenefitType();
        String benefitValue = benefitDO.getBenefitValue();
        String realSendAmount = UniversalAccountAmountCalEntity.getRealSendAmount(benefitType, benefitValue,
                benefitDO.getBenefitAmountType(), dto.getSendAmount());

        DirectAccountContext directAccountContext = new DirectAccountContext();
        BaseRequestDTO baseRequestDTO = dto.getBaseRequestDTO();
        directAccountContext.setCityCode(baseRequestDTO.getCityNumber());
        directAccountContext.setStationId(baseRequestDTO.getStationId());
        directAccountContext.setUseActivityId(dto.getUseActivityId());
        directAccountContext.setActivityId(dto.getUseActivityId());
        directAccountContext.setExternalId(dto.getExternalId());
        directAccountContext.setExternalType(dto.getExternalType());
        directAccountContext.setUserId(baseRequestDTO.getUserId());
        directAccountContext.setSource(dto.getSource());
        directAccountContext.setBenefitValue(benefitValue);
        directAccountContext.setReqNo(dto.getReqNo());
        directAccountContext.setBenefitType(benefitType);
        directAccountContext.setBenefitId(benefitDO.getId());
        directAccountContext.setBenefitName(benefitDO.getName());
        // 计算发放权益的过期时间
        directAccountContext.setExpireTime(BenefitConvertEntity.convertToExpireTime(new Date(),
                benefitDO.getExpireType(), benefitDO.getExpireTimeUnit(), benefitDO.getExpireTime()));
        directAccountContext.setPageId(dto.getPageId());
        directAccountContext.setAppId(dto.getAppId());
        directAccountContext.setOperateType(OperateTypeEnum.PROVIDE.getCode());
        directAccountContext.setExtMap(dto.getExtMap());
        directAccountContext.setInnerExtMap(dto.getInnerExtMap());
        directAccountContext.setOperateCount(UniversalAccountAmountCalEntity.getReceivedCount(benefitType, realSendAmount));
        directAccountContext.setSendAmount(realSendAmount);
        directAccountContext.setBalanceMoney(UniversalAccountAmountCalEntity.getReceivedBalanceMoneyJudgedType(benefitType, realSendAmount));
        return directAccountContext;
    }

    public static UniversalAccountRecordDO convertUniversalAccountRecordDO(DirectAccountContext context) {
        UniversalAccountRecordDO recordDO = new UniversalAccountRecordDO();
        recordDO.setOperateCount(context.getOperateCount());
        recordDO.setInnerSerialNumber(UUID.randomUUID().toString().trim());
        recordDO.setBenefitId(context.getBenefitId());
        recordDO.setActivityId(context.getUseActivityId());
        recordDO.setUseActivityId(context.getUseActivityId());
        recordDO.setBenefitType(context.getBenefitType());
        recordDO.setBenefitValue(context.getBenefitValue());
        recordDO.setUserId(context.getUserId());
        recordDO.setOperateType(context.getOperateType());
        recordDO.setStatus(StatusEnum.INIT.getCode());
        recordDO.setAppId(context.getAppId());
        recordDO.setPageId(context.getPageId());
        recordDO.setSource(context.getSource());
        recordDO.setReqNo(context.getReqNo());
        recordDO.setAuxKey(context.getReqNo());
        recordDO.setAccountId(context.getAccountId());
        recordDO.setRpcRequestExt(convertToRpcRequestExt(context));
        recordDO.setInnerExt(convertToRecordInnerExt(context));
        return recordDO;
    }

    public static UniversalAccountDO convertUniversalAccountDO(DirectAccountContext context) {
        UniversalAccountDO universalAccountDO = new UniversalAccountDO();
        universalAccountDO.setBenefitType(context.getBenefitType());
        universalAccountDO.setAvailableCount(context.getOperateCount());
        universalAccountDO.setTotalCount(context.getOperateCount());
        universalAccountDO.setUseActivityId(context.getUseActivityId());
        universalAccountDO.setUserId(context.getUserId());
        return universalAccountDO;
    }

    public static UniversalAccountDetailDO convertUniversalAccountDetailDO(DirectAccountContext context) {
        UniversalAccountDetailDO universalAccountDetailDO = new UniversalAccountDetailDO();
        universalAccountDetailDO.setBenefitId(context.getBenefitId());
        universalAccountDetailDO.setBenefitType(context.getBenefitType());
        universalAccountDetailDO.setBenefitValue(context.getBenefitValue());
        universalAccountDetailDO.setActivityId(context.getUseActivityId());
        universalAccountDetailDO.setUseActivityId(context.getUseActivityId());
        universalAccountDetailDO.setTotalCount(context.getOperateCount());
        universalAccountDetailDO.setAvailableCount(context.getOperateCount());
        universalAccountDetailDO.setExpireCount(0);
        // 设置发放权益的过期时间，为 null 设置成默认的永久时间
        Date expireTime = ObjectUtils.defaultIfNull(context.getExpireTime(), DateUtil.getDefaultPermanentTime());
        universalAccountDetailDO.setExpireTime(expireTime);
        universalAccountDetailDO.setUserId(context.getUserId());
        universalAccountDetailDO.setSource(context.getSource());
        universalAccountDetailDO.setInnerExt(convertToDetailInnerExt(context));
        universalAccountDetailDO.setRpcRequestExt(convertToRpcRequestExt(context));
        universalAccountDetailDO.setRpcResponseExt(Optional.ofNullable(context.getEquityRpcDto())
                .map(EquityRpcDto::getRpcResponseExtDTO).map(JsonUtil::toString).orElse(null));
        return universalAccountDetailDO;
    }

    public static DirectAccountContext convertUseAccountContext(UniversalUseBenefitTypeDTO dto) {
        DirectAccountContext directAccountContext = new DirectAccountContext();
        BaseRequestDTO baseRequestDTO = dto.getBaseRequestDTO();
        directAccountContext.setUseActivityId(dto.getUseActivityId());
        directAccountContext.setExternalId(dto.getExternalId());
        directAccountContext.setExternalType(dto.getExternalType());
        directAccountContext.setUserId(baseRequestDTO.getUserId());
        directAccountContext.setSource(dto.getSource());
        directAccountContext.setReqNo(dto.getReqNo());
        directAccountContext.setBenefitType(dto.getBenefitType());
        directAccountContext.setPageId(dto.getPageId());
        directAccountContext.setAppId(dto.getAppId());
        directAccountContext.setOperateType(OperateTypeEnum.USE.getCode());
        directAccountContext.setOperateCount(dto.getCount());
        return directAccountContext;
    }

    public static DirectAccountContext convertUseDetailAccountContext(UniversalUseBenefitDetailDTO dto) {
        DirectAccountContext directAccountContext = new DirectAccountContext();
        BaseRequestDTO baseRequestDTO = dto.getBaseRequestDTO();
        directAccountContext.setUserId(baseRequestDTO.getUserId());
        directAccountContext.setSource(dto.getSource());
        directAccountContext.setReqNo(dto.getReqNo());
        directAccountContext.setBenefitType(dto.getBenefitType());
        directAccountContext.setPageId(dto.getPageId());
        directAccountContext.setAppId(dto.getAppId());
        directAccountContext.setOperateType(OperateTypeEnum.USE.getCode());
        directAccountContext.setOperateCount(Constants.ONE);
        directAccountContext.setAccountDetailId(dto.getAccountDetailId());
        return directAccountContext;
    }

    public static EquityAccountContext convertToProvideAccountContext(DirectAccountContext context) {
        EquityAccountContext provideAccountContext = new EquityAccountContext();
        provideAccountContext.setAppId(context.getAppId());
        provideAccountContext.setPageId(context.getPageId());
        provideAccountContext.setSource(context.getSource());
        provideAccountContext.setCityCode(context.getCityCode());
        provideAccountContext.setStationId(context.getStationId());
        provideAccountContext.setUid(context.getUserId());
        provideAccountContext.setSerialNumber(context.getReqNo());
        // 调用优惠券接口使用前端流水号，不使用内部流水号
        provideAccountContext.setIsUseSerialNumberSave(1);
        // 设置操作人为当前用户 id
        provideAccountContext.setOperator(context.getUserId());
        provideAccountContext.setOperateType(context.getOperateType());

        provideAccountContext.setSceneCode(context.getSceneCode());
        provideAccountContext.setActivityId(context.getActivityId());
        provideAccountContext.setExternalType(context.getExternalType());
        provideAccountContext.setExternalId(context.getExternalId());
        provideAccountContext.setStrategyId(context.getStrategyId());
        provideAccountContext.setBenefitGroupId(context.getBenefitGroupId());
        provideAccountContext.setBenefitId(context.getBenefitId());
        provideAccountContext.setBenefitType(context.getBenefitType());
        provideAccountContext.setEquityValue(context.getBenefitValue());
        provideAccountContext.setSendAmount(context.getSendAmount());
        provideAccountContext.setBalanceMoney(context.getBalanceMoney());
        provideAccountContext.setUseActivityId(context.getUseActivityId());

        Map<String, Object> extMap = context.getExtMap();
        if (MapUtils.isNotEmpty(extMap)) {
            provideAccountContext.setMissionInstanceId(MapUtils.getLong(extMap, DirectProvideBenefitExtKeys.MISSION_INSTANCE_ID));
            provideAccountContext.setSendTicketScene(MapUtils.getInteger(extMap, DirectProvideBenefitExtKeys.SEND_TICKET_SCENE));
            provideAccountContext.setSendBalanceScene(MapUtils.getString(extMap, DirectProvideBenefitExtKeys.SEND_BALANCE_SCENE));
            provideAccountContext.setSendPointScene(MapUtils.getInteger(extMap, DirectProvideBenefitExtKeys.SEND_POINT_SCENE));
            provideAccountContext.setSendPointDesc(MapUtils.getString(extMap, DirectProvideBenefitExtKeys.SEND_POINT_DESC));
            provideAccountContext.setSendVipDaysScene(MapUtils.getInteger(extMap, DirectProvideBenefitExtKeys.SEND_VIP_DAYS_SCENE));
        }
        return provideAccountContext;
    }

    private static String convertToRpcRequestExt(DirectAccountContext context) {
        Integer operateType = context.getOperateType();
        Map<String, Object> rpcRequestExtMap = ObjectUtils.defaultIfNull(context.getExtMap(), Maps.newHashMap());

        // 发放时，需要将权益实际发放数量 sendAmount、发放余额金额 balanceMoney（发放权益类型为余额才有值） 存到 rpcRequestExt 中
        boolean isProvide = Objects.equals(OperateTypeEnum.PROVIDE.getCode(), operateType);
        if (isProvide) {
            rpcRequestExtMap.put("sendAmount", context.getSendAmount());
            BigDecimal balanceMoney = UniversalAccountAmountCalEntity.getDoRpcBalanceMoneyAmountJudgedType(context);
            com.ddmc.equity.common.util.MapUtils.putIfNotNull(rpcRequestExtMap, "balanceMoney", balanceMoney);
        }

        return JsonUtil.toString(rpcRequestExtMap);
    }

    private static String convertToRecordInnerExt(DirectAccountContext context) {
        return JsonUtil.toString(context.getInnerExtMap());
    }

    private static String convertToDetailInnerExt(DirectAccountContext context) {
        Map<String, Object> innerExtMap = ObjectUtils.defaultIfNull(context.getInnerExtMap(), Maps.newHashMap());
        innerExtMap.put("lastReqNo", context.getReqNo());
        return JsonUtil.toString(innerExtMap);
    }

    public static BenefitReceiveSuccessMsgVO convertToDirectBenefitReceiveSuccessMsgVO(DirectAccountContext context) {
        EquityRpcDto equityRpc = context.getEquityRpcDto();
        return BenefitReceiveSuccessMsgVO.builder()
                .receiveType(BenefitReceiveTypeEnum.DIRECT_RECEIVE.getCode())
                .reqNo(context.getReqNo())
                .userId(context.getUserId())
                .accountDetailId(context.getAccountDetailId())
                .useActivityId(context.getUseActivityId())
                // 直接领取，没有活动 + 策略 + 权益信息
                .sceneCode(null)
                .activityId(null)
                .externalType(null)
                .externalId(null)
                .activityName(null)
                .strategyId(null)
                .strategyExternalId(null)
                .benefitGroupId(null)
                .benefitId(context.getBenefitId())
                .benefitType(context.getBenefitType())
                .benefitValue(context.getBenefitValue())
                .benefitName(context.getBenefitName())
                .sendAmount(context.getSendAmount())
                .balanceMoney(UniversalAccountAmountCalEntity.getDoRpcBalanceMoneyAmountJudgedType(context))
                .rpcRespNo(Optional.ofNullable(equityRpc).map(EquityRpcDto::getValue).orElse(null))
                .receiveTime(System.currentTimeMillis())
                .innerExtMap(context.getInnerExtMap())
                .build();
    }

    public static SaveAndGetBenefitDTO convertToSaveAndGetBenefitDTO(AccountDirectBenefitReqDTO req) {
        return SaveAndGetBenefitDTO.builder()
                .benefitName(req.getBenefitName())
                .benefitType(req.getBenefitType())
                .benefitValue(req.getBenefitValue())
                .expireType(req.getBenefitExpireType())
                .expireTimeUnit(req.getBenefitExpireTimeUnit())
                .expireTime(req.getBenefitExpireTime())
                .extInfo(null)
                .build();
    }

    public static void changeDirectAccountContextWhileRetry(DirectAccountContext accountContext,
                                                            UniversalAccountRecordDO existRecord) {
        accountContext.setSceneCode(existRecord.getSceneCode());
        accountContext.setActivityId(existRecord.getActivityId());
        accountContext.setStrategyId(existRecord.getStrategyId());
        accountContext.setBenefitGroupId(existRecord.getBenefitGroupId());
        accountContext.setBenefitId(existRecord.getBenefitId());
        accountContext.setBenefitType(existRecord.getBenefitType());
        accountContext.setUseActivityId(existRecord.getUseActivityId());
        UniversalAccountRpcRequestExtDTO rpcRequestExtDTO = JsonUtil.parseObject(existRecord.getRpcRequestExt(),
                UniversalAccountRpcRequestExtDTO.class);
        accountContext.setOperateCount(existRecord.getOperateCount());
        accountContext.setSendAmount(Objects.isNull(rpcRequestExtDTO) ? null : rpcRequestExtDTO.getSendAmount());
        accountContext.setBalanceMoney(Objects.isNull(rpcRequestExtDTO) ? null : rpcRequestExtDTO.getBalanceMoney());
    }
}
