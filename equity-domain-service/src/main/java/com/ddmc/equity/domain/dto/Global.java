package com.ddmc.equity.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Setter
@Getter
@ToString
public class Global {

    @JsonProperty("station_id")
    private String stationId;

    @JsonProperty("city_number")
    private String cityNumber;

    private String uid;

    @JsonProperty("api_version")
    private String apiVersion; //api版本

    @JsonProperty("native_version")
    private String nativeVersion; //app版本号 h5使用

    @JsonProperty("app_client_id")
    private Integer appClientId; //应用ID(1:ios,2:安卓,3:微信,4:小程序,支付宝小程序:10)

    @JsonProperty("h5_source")
    private String h5Source;

    private Integer wx;

    @JsonProperty("ab_config")
    private String abConfig; //ab版本控制

}