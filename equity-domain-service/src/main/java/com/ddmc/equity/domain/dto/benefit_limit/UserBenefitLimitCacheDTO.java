package com.ddmc.equity.domain.dto.benefit_limit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserBenefitLimitCacheDTO {
    private Long limitCount;
    private Long receiveCount;

    private Long freezeCount;

    private Long fallbackCount;

    private Long freezeId;

    private Date endTime;
}
