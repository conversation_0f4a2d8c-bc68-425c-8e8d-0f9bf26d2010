package com.ddmc.equity.domain.service.receive_limit;

import com.ddmc.equity.domain.dto.receive_limit.UserActivityReceiveLimitCacheDTO;
import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.domain.valueobject.receive_limit.OperateActivityReceiveLimitResp;
import com.ddmc.equity.infra.repository.dao.UserActivityReceiveLimitDO;
import org.jetbrains.annotations.NotNull;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/29 17:18
 * @description
 */
public interface UserActivityReceiveLimitDomainService {

    /**
     * 插入用户单活动领取频次限制
     *
     * @param receiveLimitDO 用户单活动领取频次限制
     * @return 是否成功
     */
    boolean insertReceiveLimitDO(@NotNull UserActivityReceiveLimitDO receiveLimitDO);

    /**
     * 查询指定时间用户单活动领取频次限制
     *
     * @param userId     用户 id
     * @param activityId 活动 id
     * @param date       时间
     * @return 用户单活动领取频次限制
     */
    UserActivityReceiveLimitDO queryByUserIdAndDate(@NotNull String userId, @NotNull Long activityId, @NotNull Date date);

    /**
     * 查询指定 id 用户单活动领取频次限制
     *
     * @param userId     用户 id
     * @param activityId 活动 id
     * @param id         用户单活动领取频次限制表 id
     * @return 用户单活动领取频次限制
     */
    UserActivityReceiveLimitDO queryByReceiveLimitId(@NotNull String userId, @NotNull Long activityId, @NotNull Long id);

    /**
     * 用户单活动领取频次限制扣减
     *
     * @param userId              用户 id
     * @param activityId          活动 id
     * @param receiveLimitRuleDTO 领取频次限制规则
     * @return 操作结果
     */
    OperateActivityReceiveLimitResp deductReceiveLimit(@NotNull String userId, @NotNull Long activityId,
                                                       @NotNull ReceiveLimitRuleDTO receiveLimitRuleDTO);

    /**
     * 用户单活动领取频次限制释放
     *
     * @param userId         用户 id
     * @param activityId     活动 id
     * @param receiveLimitId 用户单活动领取频次限制表 id
     * @return 操作结果
     */
    OperateActivityReceiveLimitResp releaseReceiveLimit(@NotNull String userId, @NotNull Long activityId,
                                                        @NotNull Long receiveLimitId);

    /**
     * 批量获取活动领取频次限制
     *
     * @param userId      用户 id
     * @param activityIds 活动 ids
     */
    Map<Long /*activityId */, UserActivityReceiveLimitCacheDTO> getActivityReceiveLimitCacheMul(String userId, List<Long> activityIds);
}
