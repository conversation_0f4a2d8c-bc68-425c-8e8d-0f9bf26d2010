package com.ddmc.equity.domain.entity.activityRule;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.common.util.IntegerUtil;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.dto.rule.condition.RfTypeRuleDTO;
import com.ddmc.equity.dto.business.UniversalRuleDTO;
import com.ddmc.equity.dto.business.rule.*;
import com.ddmc.equity.enums.RuleScopeEnum;
import com.ddmc.equity.infra.repository.dao.EquityActivityRuleDO;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

@Getter
@ToString(callSuper = true)
@Accessors(chain = true)
public class EquityActivityRuleEntity {

    public EquityActivityRuleDO createEquityActivityRuleDO(VipIdentityDTO vipIdentityDTO, Long activityId) {
        EquityActivityRuleDO equityActivityRuleDO = new EquityActivityRuleDO();
        equityActivityRuleDO.setActivityId(activityId);
        equityActivityRuleDO.setRuleType(RuleTypeEnum.VIP_IDENTITY_RULE.getCode());
        equityActivityRuleDO.setRuleValue(JsonUtil.toJsonString(vipIdentityDTO));
        equityActivityRuleDO.setRuleScope(RuleScopeEnum.RECEIVE.getCode());
        return equityActivityRuleDO;
    }

    public EquityActivityRuleDO createEquityActivityRuleDO(Long activityId, UserTagDTO userTagDTO) {
        EquityActivityRuleDO equityActivityRuleDO = new EquityActivityRuleDO();
        equityActivityRuleDO.setActivityId(activityId);
        equityActivityRuleDO.setRuleType(RuleTypeEnum.USER_TAG_RULE.getCode());
        equityActivityRuleDO.setRuleValue(JsonUtil.toJsonString(userTagDTO));
        return equityActivityRuleDO;
    }

    public EquityActivityRuleDO convertReceiveLimitRuleDO(Long activityId, ReceiveLimitDTO
            receiveLimitDTO) {
        EquityActivityRuleDO equityActivityRuleDO = new EquityActivityRuleDO();
        equityActivityRuleDO.setRuleType(RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(receiveLimitDTO);
        equityActivityRuleDO.setRuleValue(ruleValue);
        equityActivityRuleDO.setActivityId(activityId);
        equityActivityRuleDO.setRuleScope(RuleScopeEnum.CONSULT_AND_RECEIVE.getCode());
        return equityActivityRuleDO;
    }

    public EquityActivityRuleDO convertReceiveLimitRuleDO(Long activityId, ABTestDTO
            abTestDTO) {
        EquityActivityRuleDO equityActivityRuleDO = new EquityActivityRuleDO();
        equityActivityRuleDO.setRuleType(RuleTypeEnum.AB_TEST_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(JSON.toJSONString(abTestDTO));
        equityActivityRuleDO.setRuleValue(ruleValue);
        equityActivityRuleDO.setActivityId(activityId);
        equityActivityRuleDO.setRuleScope(RuleScopeEnum.CONSULT_AND_RECEIVE.getCode());
        return equityActivityRuleDO;
    }

    public EquityActivityRuleDO convertReceiveLimitRuleDO(Long activityId, CitiesDTO
            citiesDTO) {
        EquityActivityRuleDO equityActivityRuleDO = new EquityActivityRuleDO();
        equityActivityRuleDO.setRuleType(RuleTypeEnum.CITY_RULE.getCode());
        equityActivityRuleDO.setRuleValue(JSON.toJSONString(citiesDTO));
        equityActivityRuleDO.setActivityId(activityId);
        equityActivityRuleDO.setRuleScope(RuleScopeEnum.CONSULT_AND_RECEIVE.getCode());
        return equityActivityRuleDO;
    }

    public static List<EquityActivityRuleDO> buildActivityRuleDoListFromDTO(UniversalRuleDTO ruleDTO, String opAdminId, String opAdminName) {
        if (Objects.isNull(ruleDTO)) {
            return null;
        }
        List<EquityActivityRuleDO> equityRuleDOS = Lists.newArrayList();
        //ab转换
        EquityActivityRuleDO abEquityRuleDO = convertAbTestRuleFromDTO(ruleDTO.getAbTestDTO());
        if (!Objects.isNull(abEquityRuleDO)) {
            abEquityRuleDO.setRuleScope(ruleDTO.getAbTestDTO().getRuleScope());
            fillOpAdminInfo(abEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(abEquityRuleDO);
        }
        //城市规则转换
        EquityActivityRuleDO cityEquityRuleDO = convertCityRuleFromDTO(ruleDTO.getCitiesDTO());
        if (!Objects.isNull(cityEquityRuleDO)) {
            cityEquityRuleDO.setRuleScope(ruleDTO.getCitiesDTO().getRuleScope());
            fillOpAdminInfo(cityEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(cityEquityRuleDO);
        }

        //app版本规则转换
        EquityActivityRuleDO appVersionEquityRuleDO = convertAppVersionRuleFromDTO(ruleDTO.getAppVersionDTO());
        if (!Objects.isNull(appVersionEquityRuleDO)) {
            appVersionEquityRuleDO.setRuleScope(ruleDTO.getAppVersionDTO().getRuleScope());
            fillOpAdminInfo(appVersionEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(appVersionEquityRuleDO);
        }

        //人群规则转换
        EquityActivityRuleDO userTagEquityRuleDO = convertUserTagRuleFromDTO(ruleDTO.getUserTagDTO());
        if (!Objects.isNull(userTagEquityRuleDO)) {
            userTagEquityRuleDO.setRuleScope(ruleDTO.getUserTagDTO().getRuleScope());
            fillOpAdminInfo(userTagEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(userTagEquityRuleDO);
        }
        //用户白名单规则转换
        EquityActivityRuleDO whiteUserEquityRuleDO = convertWhiteUserRuleFromDTO(ruleDTO.getWhiteUserListDTO());
        if (!Objects.isNull(whiteUserEquityRuleDO)) {
            whiteUserEquityRuleDO.setRuleScope(ruleDTO.getWhiteUserListDTO().getRuleScope());
            fillOpAdminInfo(whiteUserEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(whiteUserEquityRuleDO);
        }
        //会员身份规则转换
        EquityActivityRuleDO vipIdentityEquityRuleDO = convertVipIdentityRuleFromDTO(ruleDTO.getVipIdentityDTO());
        if (!Objects.isNull(vipIdentityEquityRuleDO)) {
            vipIdentityEquityRuleDO.setRuleScope(ruleDTO.getVipIdentityDTO().getRuleScope());
            fillOpAdminInfo(vipIdentityEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(vipIdentityEquityRuleDO);
        }

        //频次
        EquityActivityRuleDO receiveLimitEquityRuleDO = convertReceiveLimitRuleDO(ruleDTO.getReceiveLimitDTO());
        if (!Objects.isNull(receiveLimitEquityRuleDO)) {
            receiveLimitEquityRuleDO.setRuleScope(ruleDTO.getReceiveLimitDTO().getRuleScope());
            fillOpAdminInfo(receiveLimitEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(receiveLimitEquityRuleDO);
        }
        //库存
        EquityActivityRuleDO stockLimitEquityRuleDO = convertStockLimitRuleDO(ruleDTO.getStockLimitDTO());
        if (!Objects.isNull(stockLimitEquityRuleDO)) {
            stockLimitEquityRuleDO.setRuleScope(ruleDTO.getStockLimitDTO().getRuleScope());
            fillOpAdminInfo(stockLimitEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(stockLimitEquityRuleDO);
        }
        // RF 人群身份
        EquityActivityRuleDO rfTypeRuleDO = convertRfTypeRuleDO(ruleDTO.getRfTypeDTO());
        if (Objects.nonNull(rfTypeRuleDO)) {
            fillOpAdminInfo(rfTypeRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(rfTypeRuleDO);
        }
        return equityRuleDOS;
    }

    private static EquityActivityRuleDO convertAbTestRuleFromDTO(ABTestDTO abTestRuleVO) {
        if (Objects.isNull(abTestRuleVO)) {
            return null;
        }
        if (StringUtils.isBlank(abTestRuleVO.getLayerId()) || CollectionUtils.isEmpty(abTestRuleVO.getDefaultGroup())
                || CollectionUtils.isEmpty(abTestRuleVO.getChoosePutGroup())) {
            return null;
        }
        EquityActivityRuleDO equityRuleDO = new EquityActivityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.AB_TEST_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(abTestRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityActivityRuleDO convertCityRuleFromDTO(CitiesDTO cityRuleVO) {
        if (Objects.isNull(cityRuleVO)
                || CollectionUtils.isEmpty(cityRuleVO.getCityCodeList())) {
            return null;
        }
        EquityActivityRuleDO equityRuleDO = new EquityActivityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.CITY_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(cityRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityActivityRuleDO convertAppVersionRuleFromDTO(AppVersionDTO appVersionRuleVO) {
        if (Objects.isNull(appVersionRuleVO)) {
            return null;
        }
        if (CollectionUtils.isEmpty(appVersionRuleVO.getAppVersionList())) {
            return null;
        }
        EquityActivityRuleDO equityRuleDO = new EquityActivityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.APP_VERSION_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(appVersionRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityActivityRuleDO convertUserTagRuleFromDTO(UserTagDTO userTagRuleVO) {
        if (Objects.isNull(userTagRuleVO)
                || CollectionUtils.isEmpty(userTagRuleVO.getRuleIds())) {
            return null;
        }
        EquityActivityRuleDO equityRuleDO = new EquityActivityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.USER_TAG_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(userTagRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityActivityRuleDO convertWhiteUserRuleFromDTO(WhiteUserListDTO whiteUserListRuleVO) {
        if (Objects.isNull(whiteUserListRuleVO)
                || CollectionUtils.isEmpty(whiteUserListRuleVO.getWhiteUserList())) {
            return null;
        }
        EquityActivityRuleDO equityRuleDO = new EquityActivityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.WHITE_USER_LIST_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(whiteUserListRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityActivityRuleDO convertVipIdentityRuleFromDTO(VipIdentityDTO vipIdentityRuleVO) {
        if (Objects.isNull(vipIdentityRuleVO)
                || Objects.isNull(vipIdentityRuleVO.getUserStatus())) {
            return null;
        }
        EquityActivityRuleDO equityRuleDO = new EquityActivityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.VIP_IDENTITY_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(vipIdentityRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }


    private static EquityActivityRuleDO convertReceiveLimitRuleDO(ReceiveLimitDTO receiveLimitDTO) {

        if (Objects.isNull(receiveLimitDTO) || IntegerUtil.isFalse(receiveLimitDTO.getLimitType())) {
            return null;
        }
        EquityActivityRuleDO equityRuleDO = new EquityActivityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(receiveLimitDTO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityActivityRuleDO convertStockLimitRuleDO(StockLimitDTO stockLimitDTO) {

        if (Objects.isNull(stockLimitDTO) || IntegerUtil.isFalse(stockLimitDTO.getTotalStock())) {
            return null;
        }
        EquityActivityRuleDO equityRuleDO = new EquityActivityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.STOCK_LIMIT_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(stockLimitDTO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityActivityRuleDO convertRfTypeRuleDO(RfTypeDTO rfTypeDTO) {
        if (Objects.isNull(rfTypeDTO)) {
            return null;
        }
        if (CollectionUtils.isEmpty(rfTypeDTO.getRfFirstTypeNames()) && CollectionUtils.isEmpty(rfTypeDTO.getRfTypeNames())) {
            return null;
        }
        RfTypeRuleDTO rfTypeRuleDTO = RfTypeRuleDTO.builder()
                .rfFirstTypeNames(rfTypeDTO.getRfFirstTypeNames())
                .rfTypeNames(rfTypeDTO.getRfTypeNames())
                .build();
        EquityActivityRuleDO ruleDO = new EquityActivityRuleDO();
        ruleDO.setRuleType(RuleTypeEnum.RF_TYPE_RULE.getCode());
        ruleDO.setRuleValue(JSON.toJSONString(rfTypeRuleDTO));
        ruleDO.setRuleScope(ObjectUtils.defaultIfNull(rfTypeDTO.getRuleScope(), RuleScopeEnum.CONSULT_AND_RECEIVE.getCode()));
        return ruleDO;
    }

    private static void fillOpAdminInfo(EquityActivityRuleDO abEquityRuleDO, String opAdminId, String opAdminName) {
        if (!Objects.isNull(abEquityRuleDO)) {
            String addOpAdminId = StringUtils.isBlank(opAdminId) ? Constants.DEFAULT_ADMIN_ID : opAdminId;
            String addOpAdminName = StringUtils.isBlank(opAdminName) ? Constants.DEFAULT_ADMIN_ID : opAdminName;
            abEquityRuleDO.setOpAdminId(addOpAdminId);
            abEquityRuleDO.setOpAdminName(addOpAdminName);
            abEquityRuleDO.setEditAdminId(addOpAdminId);
            abEquityRuleDO.setEditAdminName(addOpAdminName);
            abEquityRuleDO.setIsDelete(Constants.STATUS_NO);
            abEquityRuleDO.setRuleScope(ObjectUtils.defaultIfNull(abEquityRuleDO.getRuleScope(), RuleScopeEnum.CONSULT_AND_RECEIVE.getCode()));
        }
    }
}
