package com.ddmc.equity.domain.entity.benefit_stock;

import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.enums.PlanDateTypeEnum;
import com.ddmc.equity.domain.dto.benefit_stock.LowStockAlarmContextDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.model.dto.ActivityCacheDto;
import com.ddmc.equity.model.dto.StrategyCacheBaseDto;
import org.jetbrains.annotations.NotNull;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/1/25 14:57
 * @description
 */
public class BenefitStockConvertEntity {

    public static String getStockCacheKey(@NotNull Long strategyId, @NotNull Long benefitId) {
        return String.format(CacheKeyConstants.STOCK_COUNT_KEY, strategyId, benefitId);
    }

    public static String getBalanceStockCacheKey(@NotNull Long strategyId, @NotNull Long benefitId) {
        return String.format(CacheKeyConstants.BALANCE_COUNT_KEY, strategyId, benefitId);
    }

    public static String getPlanStockCacheKey(@NotNull Long strategyId, @NotNull Long benefitId,
                                              @NotNull Integer planDateType, @NotNull String dateYmd) {
        return String.format(CacheKeyConstants.PLAN_STOCK_COUNT_KEY, strategyId, benefitId, planDateType, dateYmd);
    }

    public static String getPlanBalanceStockCacheKey(@NotNull Long strategyId, @NotNull Long benefitId,
                                                     @NotNull Integer planDateType, @NotNull String dateYmd) {
        return String.format(CacheKeyConstants.PLAN_BALANCE_COUNT_KEY, strategyId, benefitId, planDateType, dateYmd);
    }

    public static LowStockAlarmContextDTO convertToLowStockAlarmContextDTO(ActivityCacheDto activityCacheDTO,
                                                                           StrategyCacheBaseDto strategyCacheBaseDTO,
                                                                           EquityBenefitDO benefitDO,
                                                                           Integer planDateType, String planDateStr) {
        Long strategyId = strategyCacheBaseDTO.getStrategyId();
        Long benefitId = benefitDO.getId();
        String stockCacheKey = getStockCacheKey(strategyId, benefitId);
        String balanceStockCacheKey = getBalanceStockCacheKey(strategyId, benefitId);
        String planStockCacheKey = null;
        String planBalanceStockCacheKey = null;
        if (Objects.nonNull(planDateType) && !Objects.equals(planDateType, PlanDateTypeEnum.UNDEFINED.getType())) {
            planStockCacheKey = getPlanStockCacheKey(strategyId, benefitId, planDateType, planDateStr);
            planBalanceStockCacheKey = getPlanBalanceStockCacheKey(strategyId, benefitId, planDateType, planDateStr);
        }
        return LowStockAlarmContextDTO.builder()
                .sceneCode(activityCacheDTO.getSceneCode())
                .createdById(activityCacheDTO.getOpAdminId())
                .createdBy(activityCacheDTO.getOpAdminName())
                .updatedById(activityCacheDTO.getEditAdminId())
                .updatedBy(activityCacheDTO.getEditAdminName())
                .activityId(activityCacheDTO.getId())
                .activityName(activityCacheDTO.getActivityName())
                .startDate(activityCacheDTO.getStartTime())
                .endDate(activityCacheDTO.getEndTime())
                .externalType(activityCacheDTO.getExternalType())
                .externalId(activityCacheDTO.getExternalId())
                .strategyId(strategyCacheBaseDTO.getStrategyId())
                .strategyName(strategyCacheBaseDTO.getStrategyName())
                .strategyExternalId(strategyCacheBaseDTO.getExternalId())
                .strategyType(strategyCacheBaseDTO.getStrategyType())
                .benefitGroupId(null)
                .benefitId(benefitDO.getId())
                .benefitName(benefitDO.getName())
                .benefitType(benefitDO.getBenefitType())
                .benefitValue(benefitDO.getBenefitValue())
                .planDateType(planDateType)
                .planDateStr(planDateStr)
                .stockCacheKey(stockCacheKey)
                .balanceStockCacheKey(balanceStockCacheKey)
                .planStockCacheKey(planStockCacheKey)
                .planBalanceStockCacheKey(planBalanceStockCacheKey)
                .build();
    }
}
