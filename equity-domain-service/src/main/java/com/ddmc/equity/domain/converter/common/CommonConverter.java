package com.ddmc.equity.domain.converter.common;

import com.ddmc.equity.domain.dto.common.CommonProductInfoDTO;
import com.ddmc.equity.domain.dto.common.SearchTicketInDTO;
import com.ddmc.greenhouse.carbon.admin.third.api.client.response.product.ProductSearchResponse;
import com.ddmc.vouchercore.client.request.TicketExpandSearchRequest;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(imports = {StringUtils.class, Lists.class})
public interface CommonConverter {


    CommonConverter INSTANCE = Mappers.getMapper(CommonConverter.class);


    /**
     * to
     *
     * @param in
     * @return
     */
    TicketExpandSearchRequest toTicketExpandSearchRequest(SearchTicketInDTO in);


    CommonProductInfoDTO toCommonProductInfoDTO(ProductSearchResponse in);

    List<CommonProductInfoDTO> toCommonProductInfoDTO(List<ProductSearchResponse> in);

}
