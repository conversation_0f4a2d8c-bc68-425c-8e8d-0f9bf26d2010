package com.ddmc.equity.domain.entity.activityStockFlow;

import com.ddmc.equity.infra.repository.dao.ActivityStockFlowDO;
import com.ddmc.equity.infra.repository.dao.EquityActivityDO;
import com.ddmc.equity.infra.repository.dao.EquityActivityRuleDO;

public class ActivityStockFlowEntity {

    public ActivityStockFlowDO create(EquityActivityDO equityActivityDO, Integer operationType, EquityActivityRuleDO equityActivityRuleDO){
        ActivityStockFlowDO activityStockFlowDO = new ActivityStockFlowDO();
        return activityStockFlowDO;
    }
}
