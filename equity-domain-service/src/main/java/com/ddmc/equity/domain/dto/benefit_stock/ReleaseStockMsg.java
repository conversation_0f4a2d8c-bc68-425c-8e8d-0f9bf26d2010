package com.ddmc.equity.domain.dto.benefit_stock;

import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ReleaseStockMsg implements Serializable {
    private static final long serialVersionUID = 1121674400592530032L;

    private Long activityId;

    private Long strategyId;

    private Long benefitId;

    private Long num;


    private StockLimitRuleDTO stockLimitRuleDTO;

    private String auxKey;

    // todo: 必要字段
}
