package com.ddmc.equity.domain.valueobject.scene;

import com.ddmc.equity.infra.repository.dao.EquitySceneDO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class SceneListFilterRespVO implements Serializable {
    private static final long serialVersionUID = -4213390950841450694L;
    @ApiModelProperty("场景列表")
    @NotNull
    @Builder.Default
    private List<EquitySceneDO> list = Lists.newArrayList();

    @ApiModelProperty("列表记录总数")
    @NotNull
    @Builder.Default
    private Long total = 0L;
}
