package com.ddmc.equity.domain.converter.rule;

import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.dto.business.rule.ReceiveLimitDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface ReceiveLimitRuleConverter {
    ReceiveLimitRuleConverter INSTANCE = Mappers.getMapper(ReceiveLimitRuleConverter.class);

    ReceiveLimitRuleDTO convertToReceiveLimitRuleDTO(ReceiveLimitDTO receiveLimitDTO);
}
