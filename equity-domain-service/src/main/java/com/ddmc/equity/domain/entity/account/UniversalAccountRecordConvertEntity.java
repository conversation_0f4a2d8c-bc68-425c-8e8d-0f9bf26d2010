package com.ddmc.equity.domain.entity.account;

import com.ddmc.equity.account.UniversalAccountRecordContext;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.dto.customer.account.UniversalBenefitRecordDTO;
import com.ddmc.equity.dto.customer.account.UniversalBenefitRecordReqDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * hulei
 */
public class UniversalAccountRecordConvertEntity {


    public static UniversalAccountRecordContext convertToUniversalAccountRecordContext(UniversalBenefitRecordReqDTO dto) {
        UniversalAccountRecordContext context = new UniversalAccountRecordContext();
        context.setUserId(dto.getBaseRequestDTO().getUserId());
        context.setBenefitTypes(Sets.newHashSet(dto.getBenefitTypes()));
        context.setStartTime(dto.getStartTime());
        context.setEndTime(dto.getEndTime());
        context.setStatus(StatusEnum.SUCCESS.getCode());
        context.setOperateTypes(dto.getOperateTypes());
        context.setPage(dto.getPage());
        context.setPageSize(dto.getPageSize());
        return context;
    }

    public static List<UniversalBenefitRecordDTO> convertToUniversalBenefitRecordDTO(List<UniversalAccountRecordDO> accountRecordDOList,
                                                                                     Map<Long, EquityBenefitDO> benefitDOMap) {
        List<UniversalBenefitRecordDTO> recordDTOList = new ArrayList<>();
        if(CollectionUtils.isEmpty(accountRecordDOList)){
            return recordDTOList;
        }
        for (UniversalAccountRecordDO recordDO : accountRecordDOList) {
            UniversalBenefitRecordDTO dto = new UniversalBenefitRecordDTO();
            dto.setBenefitId(recordDO.getBenefitId());
            dto.setOperateCount(recordDO.getOperateCount());
            dto.setOperateType(recordDO.getOperateType());
            dto.setCreateTime(recordDO.getCreateTime());
            dto.setReqNo(recordDO.getReqNo());
            dto.setSceneCode(recordDO.getSceneCode());
            dto.setSource(recordDO.getSource());
            EquityBenefitDO benefitDO = benefitDOMap.get(recordDO.getBenefitId());
            if(Objects.nonNull(benefitDO)){
                dto.setBenefitImage(benefitDO.getExtInfo());
                dto.setBenefitType(benefitDO.getBenefitType());
                dto.setBenefitName(benefitDO.getName());
            }
            recordDTOList.add(dto);
        }
        return recordDTOList;
    }
}
