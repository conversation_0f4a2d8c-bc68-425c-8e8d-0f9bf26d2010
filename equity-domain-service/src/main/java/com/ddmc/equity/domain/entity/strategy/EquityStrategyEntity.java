package com.ddmc.equity.domain.entity.strategy;


import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.util.MapUtils;
import com.ddmc.equity.domain.converter.strategy.StrategyConverter;
import com.ddmc.equity.domain.entity.benefit.EquityBenefitEntity;
import com.ddmc.equity.domain.entity.rule.EquityRuleEntity;
import com.ddmc.equity.domain.valueobject.strategy.StrategyListFilterReqVO;
import com.ddmc.equity.dto.business.StrategyBusinessDTO;
import com.ddmc.equity.dto.business.StrategyListReqDTO;
import com.ddmc.equity.dto.business.StrategySaveReqDTO;
import com.ddmc.equity.dto.business.UniversalStrategyDTO;
import com.ddmc.equity.dto.business.provide.ProvideBenefitDTO;
import com.ddmc.equity.dto.business.provide.ProvideStrategyDTO;
import com.ddmc.equity.dto.business.rule.RuleDTO;
import com.ddmc.equity.enums.RuleScopeEnum;
import com.ddmc.equity.infra.repository.dao.EquityStrategyDO;
import com.ddmc.equity.model.dto.StrategyCacheBaseDto;
import com.ddmc.promocore.admin.vo.ActivityVO;
import com.ddmc.promocore.admin.vo.PrizeVO;
import com.ddmc.promoequity.client.EquityAdminClient;
import com.ddmc.station.client.ServiceStationClient;
import com.ddmc.voucherprod.client.TicketAdminClient;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.*;


@Getter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class EquityStrategyEntity extends EquityStrategyDO implements Serializable {
    private static final long serialVersionUID = -463087934825496450L;

    @NotNull
    public static StrategyListFilterReqVO getStrategyFilterByListReq(StrategyListReqDTO req) {
        return StrategyConverter.INSTANCE.d2v(req);
    }

    @NotNull
    public static List<StrategyBusinessDTO> listStrategyBusinessDTO(List<EquityStrategyDO> list) {
        return StrategyConverter.INSTANCE.v2ds(list);
    }

    public static EquityStrategyDO getStrategyDOBySaveReq(StrategySaveReqDTO req) {
        return StrategyConverter.INSTANCE.srd2e(req);
    }

    public EquityStrategyDO convertToDO(ProvideStrategyDTO provideStrategyDTO, Long activityId, String adminId, String adminName) {
        EquityStrategyDO strategyDO = new EquityStrategyDO();
        strategyDO.setExternalId(provideStrategyDTO.getExternalId());
        strategyDO.setActivityId(activityId);
        strategyDO.setStrategyName(provideStrategyDTO.getStrategyName());
        strategyDO.setActivityWeight(provideStrategyDTO.getActivityWeight());
        strategyDO.setStatus(provideStrategyDTO.getStatus());
        String operateId = StringUtils.isBlank(adminId) ? Constants.DEFAULT_ADMIN_ID : adminId;
        String operateName = StringUtils.isBlank(adminName) ? Constants.DEFAULT_ADMIN_ID : adminName;
        strategyDO.setOpAdminId(operateId);
        strategyDO.setOpAdminName(operateName);
        strategyDO.setEditAdminId(operateId);
        strategyDO.setEditAdminName(operateName);
        strategyDO.setDescription(provideStrategyDTO.getDescription());
        if (Objects.nonNull(provideStrategyDTO.getHasReceiveLimit())) {
            strategyDO.setHasReceiveLimit(provideStrategyDTO.getHasReceiveLimit());
        } else {
            strategyDO.setHasReceiveLimit(Constants.ZERO);
        }
        if (Objects.nonNull(provideStrategyDTO.getHasStockLimit())) {
            strategyDO.setHasStockLimit(provideStrategyDTO.getHasStockLimit());
        } else {
            strategyDO.setHasStockLimit(Constants.ZERO);
        }
        return strategyDO;
    }

    public EquityStrategyDO convertToEquityStrategyDO(StrategyCacheBaseDto strategyCacheBaseDto) {
        EquityStrategyDO equityStrategyDO = new EquityStrategyEntity();
        equityStrategyDO.setHasStockLimit(strategyCacheBaseDto.getHasStockLimit());
        equityStrategyDO.setHasReceiveLimit(strategyCacheBaseDto.getHasReceiveLimit());
        equityStrategyDO.setId(strategyCacheBaseDto.getStrategyId());
        equityStrategyDO.setActivityWeight(strategyCacheBaseDto.getActivityWeight());
        return equityStrategyDO;
    }

    /****
     * 创建活动client
     * @param activityDTO 玩法活动数据
     * @param activityId 内部活动id
     * @param equityStrategyDOMap 内部活动对应的策略map
     * @param equityAdminClient 老权益client，玩法奖品保存的是老权益的id
     * @param ticketAdminClient 券平台的client 为了查询券所在的城市
     * @param serviceStationClient 站点服务，根据券的城市查询站点服务对应的城市代码
     * @return List
     */
    public List<ProvideStrategyDTO> createProvideStrategyDTOList(ActivityVO activityDTO, Long activityId, Map<String, EquityStrategyDO> equityStrategyDOMap, EquityAdminClient equityAdminClient, TicketAdminClient ticketAdminClient, ServiceStationClient serviceStationClient) {
        List<ProvideStrategyDTO> provideStrategyDTOList = new ArrayList<>();
        //创建策略，一个奖品==一个策略
        for (PrizeVO prizes : activityDTO.getPrizes()) {
            EquityStrategyDO equityStrategyDO = equityStrategyDOMap.get(prizes.getPrizeId());
            //权益中心已经存在该策略，则存储权益中心的策略Id
            Long strategyId = Objects.nonNull(equityStrategyDO) ? equityStrategyDO.getId() : null;
            ProvideStrategyDTO provideStrategyDTO = createProvideStrategyDTO(strategyId, activityDTO, prizes, equityAdminClient, ticketAdminClient, serviceStationClient);
            provideStrategyDTO.setActivityId(activityId);
            provideStrategyDTOList.add(provideStrategyDTO);
        }
        return provideStrategyDTOList;
    }

    /****
     * 创建活动对应的策略
     * @param activityDTO 玩法活动数据
     * @param activityId 内部活动id
     * @param equityStrategyDOMap 内部活动对应的策略map
     * @return List
     */
    public List<ProvideStrategyDTO> createProvideStrategyDTOListWithLimitCityList(
            ActivityVO activityDTO, Long activityId, Map<String, EquityStrategyDO> equityStrategyDOMap,
            Map<Long /* oldEquityId */ , List<String>> ticketLimitCityMap,
            Map<Long /* oldEquityId */, List<ProvideBenefitDTO>> provideBenefitMap) {
        List<ProvideStrategyDTO> provideStrategyDTOList = new ArrayList<>();
        //创建策略，一个奖品==一个策略
        for (PrizeVO prizes : activityDTO.getPrizes()) {
            EquityStrategyDO equityStrategyDO = equityStrategyDOMap.get(prizes.getPrizeId());
            //权益中心已经存在该策略，则存储权益中心的策略Id
            Long strategyId = Objects.nonNull(equityStrategyDO) ? equityStrategyDO.getId() : null;
            List<String> limitCityList = null;
            Long equityId = Long.parseLong(prizes.getReferParam());
            if (MapUtils.isNotEmpty(ticketLimitCityMap)) {
                limitCityList = ticketLimitCityMap.get(equityId);
            }
            List<ProvideBenefitDTO> provideBenefitList = null;
            if (MapUtils.isNotEmpty(provideBenefitMap)) {
                provideBenefitList = provideBenefitMap.get(equityId);
            }
            ProvideStrategyDTO provideStrategyDTO = createProvideStrategyDTOWithLimitCityList(strategyId, activityDTO, prizes, limitCityList, provideBenefitList);
            provideStrategyDTO.setActivityId(activityId);
            provideStrategyDTOList.add(provideStrategyDTO);
        }
        return provideStrategyDTOList;
    }


    /***
     * 创建策略基本信息
     * @param strategyId 策略id
     * @param activityDTO 玩法活动数据
     * @param prizes 奖品信息
     * @param equityAdminClient EquityAdminClient
     * @param ticketAdminClient TicketAdminClient
     * @param serviceStationClient ServiceStationClient
     * @return ProvideStrategyDTO
     */
    private ProvideStrategyDTO createProvideStrategyDTO(Long strategyId, ActivityVO activityDTO, PrizeVO prizes, EquityAdminClient equityAdminClient, TicketAdminClient ticketAdminClient, ServiceStationClient serviceStationClient) {
        ProvideStrategyDTO provideStrategyDTO = new ProvideStrategyDTO();
        provideStrategyDTO.setStrategyName(activityDTO.getName());
        provideStrategyDTO.setStatus(1);
        EquityBenefitEntity benefitEntity = new EquityBenefitEntity();
        List<ProvideBenefitDTO> benefitDTOList = benefitEntity.createProvideBenefitDTOList(prizes);
        provideStrategyDTO.setBenefitDTOList(benefitDTOList);
        EquityRuleEntity equityRuleEntity = EquityRuleEntity.builder().build();
        RuleDTO ruleDTO = equityRuleEntity.createRuleDTO(prizes, strategyId, activityDTO.getCountControlConfig(),
                benefitDTOList, equityAdminClient, ticketAdminClient, serviceStationClient, getCrowdRuleScope(activityDTO));
        provideStrategyDTO.setRuleDTO(ruleDTO);
        provideStrategyDTO.setExternalId(prizes.getPrizeId());
        return provideStrategyDTO;
    }

    /***
     * 创建策略基本信息
     * @param strategyId 策略id
     * @param activityDTO 玩法活动数据
     * @param prizes 奖品信息
     * @return ProvideStrategyDTO
     */
    private ProvideStrategyDTO createProvideStrategyDTOWithLimitCityList(Long strategyId, ActivityVO activityDTO,
                                                                         PrizeVO prizes, List<String> limitCityList,
                                                                         List<ProvideBenefitDTO> provideBenefitList) {
        ProvideStrategyDTO provideStrategyDTO = new ProvideStrategyDTO();
        provideStrategyDTO.setStrategyName(activityDTO.getName());
        provideStrategyDTO.setStatus(1);
        provideStrategyDTO.setBenefitDTOList(provideBenefitList);
        RuleDTO ruleDTO = EquityRuleEntity.builder().build().createRuleDTOWithLimitCityList(prizes, strategyId,
                activityDTO.getCountControlConfig(), limitCityList, getCrowdRuleScope(activityDTO));
        provideStrategyDTO.setRuleDTO(ruleDTO);
        if (ruleDTO.getOldSysBOCUserDTO() != null) {
            if (StringUtils.isNotBlank(ruleDTO.getOldSysBOCUserDTO().getBankCode())) {
                provideStrategyDTO.setActivityWeight("9");
            } else {
                provideStrategyDTO.setActivityWeight("1");
            }
        }
        provideStrategyDTO.setExternalId(prizes.getPrizeId());
        return provideStrategyDTO;
    }

    private Integer getCrowdRuleScope(ActivityVO activityDTO) {
        String canSeeCanGet = Optional.ofNullable(activityDTO).map(ActivityVO::getExtendInfo)
                .map(e -> e.get("canSeeCanGet")).orElse("Y");
        // 所见不所得，领取生效；
        if (StringUtils.equals(canSeeCanGet, "N")) {
            return RuleScopeEnum.RECEIVE.getCode();
        }
        // 人群规则范围。默认，咨询和领取都生效；所见所得，咨询和领取都生效；
        return RuleScopeEnum.CONSULT_AND_RECEIVE.getCode();
    }

    public static EquityStrategyDO dto2do(UniversalStrategyDTO dto) {
        return StrategyConverter.INSTANCE.dto2do(dto);
    }

    public static List<EquityStrategyDO> dto2dos(List<UniversalStrategyDTO> list) {
        return StrategyConverter.INSTANCE.dto2dos(list);
    }
}
