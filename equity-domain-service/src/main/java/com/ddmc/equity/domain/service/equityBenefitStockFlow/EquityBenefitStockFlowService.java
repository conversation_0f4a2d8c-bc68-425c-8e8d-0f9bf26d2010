package com.ddmc.equity.domain.service.equityBenefitStockFlow;

import com.ddmc.equity.domain.dto.benefit_stock.UpdateStockFlowIsHandleParam;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockFlowDO;
import org.jetbrains.annotations.NotNull;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface EquityBenefitStockFlowService {

    boolean updateEquityBenefitStockFlowById(EquityBenefitStockFlowDO equityBenefitStockFlowDO);


    /**
     * 批量写入
     *
     * @param list
     * @return
     */
    Long batchInsert(List<EquityBenefitStockFlowDO> list);

    void insert(EquityBenefitStockFlowDO equityBenefitStockFlowDO);

    /**
     * 查询待处理的库存流水
     *
     * @param operationTypes 操作类型
     * @param startTime      createTime >= startTime
     * @param cursor         id > cursor
     * @param batchLimit     limit = batchLimit
     * @return 库存流水列表
     */
    List<EquityBenefitStockFlowDO> queryIsHandleFlowDOList(@NotNull Set<Integer> operationTypes, Date startTime,
                                                           @NotNull Long cursor, @NotNull Long batchLimit);

    /**
     * 通过 flowIds 更新流水为已处理。乐观锁更新，如果更新成功数量 != flowIds.size，则需要回滚
     *
     * @param flowIds 流水 ids
     * @return 更新成功数量
     */
    boolean updateFlowHandledByFlowIds(@NotNull List<Long> flowIds);

    /**
     * 通过 flowIds 更新流水为未处理。乐观锁更新，如果更新成功数量 != flowIds.size，则需要回滚
     *
     * @param flowIds 流水 ids
     * @return 更新成功数量
     */
    boolean updateFlowUnHandledByFlowIds(@NotNull List<Long> flowIds);

    /**
     * 通过 activityIds 等参数更新流水为未处理或已处理
     *
     * @param param 参数
     * @return 更新成功数量
     */
    int updateFlowIsHandleByParam(UpdateStockFlowIsHandleParam param);

    /**
     * 统计未处理的扣减和释放库存流水操作库存（非计划库存）数量
     *
     * @param activityId 活动 id
     * @param strategyId 策略 id
     * @param benefitId  权益 id
     * @return 数量
     */
    Long sumUnHandledFlowOperationCount(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId);

    /**
     * 统计未处理的扣减和释放库存流水操作库存（计划库存）数量
     *
     * @param activityId  活动 id
     * @param strategyId  策略 id
     * @param benefitId   权益 id
     * @param planStockId 计划库存 id
     * @return 数量
     */
    Long sumUnHandledFlowPlanOperationCount(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                            @NotNull Long planStockId);

    List<EquityBenefitStockFlowDO> queryLastFlowByOperationType(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId, List<Integer> operationType);
}
