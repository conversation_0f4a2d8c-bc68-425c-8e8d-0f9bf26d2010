package com.ddmc.equity.domain.dto.engine;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/7/6 16:27
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class ActivityEngineContextDTO extends SceneEngineContextDTO {

    /**
     * 活动 id
     */
    private Long activityId;
    /**
     * 外部关联类型。1-玩法；
     */
    private Integer externalType;
    /**
     * 外部关联 id（一般是外部关联方的活动 id，如 promo.activity.activityId）
     */
    private String externalId;
    /**
     * 活动上的发放类型。1-全部发放；2-随机发放；3-概率发放；4-优先级发放；
     *
     * @see com.ddmc.equity.enums.ActivitySendTypeEnum
     */
    private Integer activitySendType;

    /**
     * 是否需要忽略库存、频次限制规则
     */
    private boolean ignoreActivityReceiveLimit;

}
