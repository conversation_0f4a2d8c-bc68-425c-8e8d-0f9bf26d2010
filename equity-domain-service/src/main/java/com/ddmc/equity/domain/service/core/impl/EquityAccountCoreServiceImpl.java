package com.ddmc.equity.domain.service.core.impl;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.AccountStrategyContext;
import com.ddmc.equity.account.DirectAccountContext;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.account.UniversalAccountRecordContext;
import com.ddmc.equity.account.UniversalAccountStrategy;
import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.exception.BusinessException;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.SpringContextUtil;
import com.ddmc.equity.common.util.ThreadsUtils;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.entity.account.EquityAccountConvertEntity;
import com.ddmc.equity.domain.service.account.OperateAccountCoreService;
import com.ddmc.equity.domain.service.core.EquityAccountCoreService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountRecordDomainService;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.dto.customer.account.AccountDistributeBenefitReqDTO;
import com.ddmc.equity.dto.customer.account.AccountDistributeBenefitRespDTO;
import com.ddmc.equity.dto.customer.account.QueryUserAccountsReqDTO;
import com.ddmc.equity.dto.customer.account.UserAccountDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/5/30 15:30
 * @description
 */
@Service
@Slf4j
public class EquityAccountCoreServiceImpl implements EquityAccountCoreService {

    /**
     * 获取用户权益账户记录等待毫秒时长
     */
    @Value("${query.user.accounts.wait.time:500}")
    private Integer queryUserAccountsWaitTime;
    /**
     * 获取用户权益账户记录，对应不同场景 code 需要获取的权益类型
     */
    @ApolloJsonValue("${query.user.accounts.scene.benefit.types:{\"FLOP\":[2,3,9,10,19]}}")
    private Map<String, List<Integer>> queryUserAccountsSceneBenefitTypes;
    /**
     * 使用新方式扣减权益账户开关。默认打开，走新的方式扣减
     */
    @Value("${equity.new.deduct.account.switchOn:true}")
    private Boolean newDeductAccountSwitchOn;

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private LocalCacheManager localCacheManager;
    @Autowired
    private UniversalAccountRecordDomainService universalAccountRecordDomainService;
    @Autowired
    private OperateAccountCoreService operateAccountCoreService;

    @Override
    public AccountDistributeBenefitRespDTO distributeBenefit(AccountDistributeBenefitReqDTO req) {
        // 1 获取对应权益 id，后续发放需要使用到这个 benefitId
        EquityBenefitDO benefitDO = getEquityBenefitDO(req.getBenefitId(), req.getBenefitType());
        if (Objects.isNull(benefitDO)) {
            log.warn("distributeBenefit benefitNotFound failure. req={}", JSON.toJSONString(req));
            throw AdminExceptionBuilder.build(ExceptionEnum.DISTRIBUTE_BENEFIT_BENEFIT_NOT_FOUND);
        }

        // 2 发放权益，生成对应的权益子账户和权益子账户操作流水
        Long benefitId = benefitDO.getId();
        Integer benefitType = benefitDO.getBenefitType();
        Integer accountType = EquityAccountConvertEntity.checkAndConvertToAccountType(benefitType);
        EquityAccountContext equityAccountContext = EquityAccountConvertEntity.createEquityAccountContext(req, accountType, benefitId);
        AccountStrategyContext accountStrategy = AccountStrategyContext.builderByBenefitType(benefitType);
        List<AccountInfoDTO> accountInfoDTOList = accountStrategy.provideEquityAndGetResult(equityAccountContext);
        if (CollectionUtils.isEmpty(accountInfoDTOList)) {
            log.warn("distributeBenefit batchProvideEquity failure. req={}", JSON.toJSONString(req));
            throw AdminExceptionBuilder.build(ExceptionEnum.DISTRIBUTE_BENEFIT_FAILURE);
        }
        return EquityAccountConvertEntity.createAccountDistributeBenefitRespDTO(accountInfoDTOList);
    }

    @Override
    public List<UserAccountDTO> queryUserAccounts(QueryUserAccountsReqDTO req) {
        EquityAccountConvertEntity.checkQueryUserAccountsReqDTO(req);
        // 如果传入权益类型为空，则查询对应场景下的所有权益类型（Apollo 配置）
        List<Integer> benefitTypes = req.getBenefitTypes();
        if (CollectionUtils.isEmpty(benefitTypes)) {
            benefitTypes = queryUserAccountsSceneBenefitTypes.get(req.getSceneCode());
        }

        // 根据权益类型获取对应的权益账户操作类。如果不通权益类型使用同样的权益账户操作类，需要去重
        List<String> accountOperateBeanNames = EquityAccountConvertEntity.getAccountOperateBeanNames(benefitTypes);
        if (CollectionUtils.isEmpty(accountOperateBeanNames)) {
            log.warn("queryUserAccounts accountOperateBeanNames is null benefitTypes={}, req={}",
                    JSON.toJSONString(benefitTypes), JSON.toJSONString(req));
            return null;
        }
        // 异步查询不同账户类型的权益子账户记录，并聚合
        List<UserAccountDTO> userAccountDTOList = Lists.newCopyOnWriteArrayList();
        List<CompletableFuture<Void>> asyncTasks = Lists.newArrayList();
        for (String accountOperateBeanName : accountOperateBeanNames) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() ->
                    queryAndFillUserAccounts(userAccountDTOList, req, accountOperateBeanName), ThreadsUtils.getCoreThreadPoll()
            );
            asyncTasks.add(future);
        }
        // 等待异步处理结果
        if (CollectionUtils.isNotEmpty(asyncTasks)) {
            ThreadsUtils.getCompletableFutureList(asyncTasks, queryUserAccountsWaitTime, TimeUnit.MILLISECONDS);
        }

        return userAccountDTOList;
    }

    @Override
    public List<UserAccountDTO> queryAndFillUserAccounts(QueryUserAccountsReqDTO req, Integer accountType) {
        AccountStrategyContext accountStrategy = AccountStrategyContext.builder(accountType);
        EquityAccountContext equityAccountContext = EquityAccountConvertEntity.createEquityAccountContext(req, accountType);
        List<AccountInfoDTO> queryAccountInfos = accountStrategy.queryEquityAccountInfos(equityAccountContext);
        return EquityAccountConvertEntity.convertToAccountInfoDTOList(queryAccountInfos);
    }

    @Override
    public ReceiveBenefitResDTO receiveDirectBenefit(DirectAccountContext directAccountContext) {
        String beanName = BenefitTypeEnum.getAccountStrategy(directAccountContext.getBenefitType());
        UniversalAccountStrategy universalAccountStrategy = SpringContextUtil.getBean(beanName, UniversalAccountStrategy.class);
        return universalAccountStrategy.doReceiveEquity(directAccountContext);
    }

    @Override
    public PageListRespDTO<UniversalAccountRecordDO> queryBenefitRecordDTOS(UniversalAccountRecordContext context) {
        return universalAccountRecordDomainService.queryUniversalAccountRecordsForPage(context);
    }

    @Override
    public Boolean universalUseBenefitType(DirectAccountContext directAccountContext) {
        if (Boolean.TRUE.equals(newDeductAccountSwitchOn)) {
            return operateAccountCoreService.deductAccount(directAccountContext);
        }

        String uid = directAccountContext.getUserId();
        boolean isLocked = false;
        String lockKey = String.format(CacheKeyConstants.UNIVERSAL_USE_BENEFIT_LOCK, uid, directAccountContext.getBenefitType());
        try {
            // 加锁
            isLocked = redisCache.lock(lockKey, 10, TimeUnit.SECONDS);
            if (!isLocked) {
                log.warn("universalUseBenefitType lock failure. context={}", JSON.toJSONString(directAccountContext));
                return false;
            }
            String beanName = BenefitTypeEnum.getAccountStrategy(directAccountContext.getBenefitType());
            UniversalAccountStrategy universalAccountStrategy = SpringContextUtil.getBean(beanName, UniversalAccountStrategy.class);
            return universalAccountStrategy.doUseEquity(directAccountContext);
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            CsossUtils.logEventFail(MonitorConstants.UNIVERSAL_DIRECT_USE_BENEFIT_TYPE, MonitorConstants.EXCEPTION, JSON.toJSONString(directAccountContext));
            log.error("universalUseBenefitType Exception {}", JSON.toJSONString(directAccountContext), e);
        } finally {
            redisCache.unlock(lockKey, isLocked);
        }
        return false;
    }

    @Override
    public Boolean universalUseBenefitDetail(DirectAccountContext directAccountContext) {
        try {
            String beanName = BenefitTypeEnum.getAccountStrategy(directAccountContext.getBenefitType());
            UniversalAccountStrategy universalAccountStrategy = SpringContextUtil.getBean(beanName, UniversalAccountStrategy.class);
            return universalAccountStrategy.doUseEquityByDetail(directAccountContext);
        } catch (BusinessException be) {
            throw be;
        } catch (Exception e) {
            CsossUtils.logEventFail(MonitorConstants.UNIVERSAL_DIRECT_USE_BENEFIT_DETAIL, MonitorConstants.EXCEPTION, JSON.toJSONString(directAccountContext));
            log.error("universalUseBenefitDetail Exception {}", JSON.toJSONString(directAccountContext), e);
        }
        return false;
    }

    private void queryAndFillUserAccounts(List<UserAccountDTO> userAccountDTOList, QueryUserAccountsReqDTO req,
                                          String accountOperateBeanName) {
        EquityAccountContext equityAccountContext = EquityAccountConvertEntity.createEquityAccountContext(req);
        AbstractEquityAccountOperate accountOperate = SpringContextUtil.getBean(accountOperateBeanName, AbstractEquityAccountOperate.class);
        List<AccountInfoDTO> queryAccountInfos = accountOperate.queryEquityAccountInfos(equityAccountContext);
        if (CollectionUtils.isNotEmpty(queryAccountInfos)) {
            userAccountDTOList.addAll(EquityAccountConvertEntity.convertToAccountInfoDTOList(queryAccountInfos));
        }
    }

    private EquityBenefitDO getEquityBenefitDO(Long benefitId, Integer benefitType) {
        if (Objects.nonNull(benefitId)) {
            return localCacheManager.getBenefitInfoById(benefitId);
        }
        if (Objects.nonNull(benefitType)) {
            return AccountStrategyContext.builderByBenefitType(benefitType).queryEquityBenefitDO(benefitType);
        }
        return null;
    }
}
