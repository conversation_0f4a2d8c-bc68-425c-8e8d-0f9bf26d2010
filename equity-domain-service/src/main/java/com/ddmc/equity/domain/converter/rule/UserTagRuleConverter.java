package com.ddmc.equity.domain.converter.rule;

import com.ddmc.equity.domain.dto.rule.condition.UserTagRuleDTO;
import com.ddmc.equity.domain.valueobject.rule.condition.UserTagRuleVO;
import com.ddmc.equity.dto.business.rule.UserTagDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface UserTagRuleConverter {
    UserTagRuleConverter INSTANCE = Mappers.getMapper(UserTagRuleConverter.class);

    UserTagRuleVO convertDTOToVO(UserTagDTO userTagDTO);

    UserTagRuleDTO convertToUserTagRuleDTO(UserTagDTO userTagDTO);
}
