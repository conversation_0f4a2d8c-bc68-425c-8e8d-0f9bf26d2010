package com.ddmc.equity.domain.service.benefitStockPlan.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.domain.dto.benefit_stock.StockOperationBaseParam;
import com.ddmc.equity.domain.service.benefitStockPlan.BenefitStockPlanService;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockPlanDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitStockPlanMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class BenefitStockPlanServiceImpl implements BenefitStockPlanService {

    @Resource
    private EquityBenefitStockPlanMapper equityBenefitStockPlanMapper;

    @Override
    public EquityBenefitStockPlanDO queryBenefitStockPlanByIdAndKey(StockOperationBaseParam stockOperationBaseParam) {
        QueryWrapper<EquityBenefitStockPlanDO> queryWrapper = new QueryWrapper();
        queryWrapper.eq("id",stockOperationBaseParam.getPlanStockId())
                    .eq("activity_id",stockOperationBaseParam.getActivityId())
                    .eq("strategy_id",stockOperationBaseParam.getStrategyId());
        return equityBenefitStockPlanMapper.selectOne(queryWrapper);
    }

    @Override
    public List<EquityBenefitStockPlanDO> listEffectiveBenefitStockPlans(Long activityId,
                                                                         Long strategyId,
                                                                         Long benefitId) {
        if (activityId == null || strategyId == null || benefitId == null) {
            return null;
        }

        LambdaQueryWrapper<EquityBenefitStockPlanDO> wrapper = new LambdaQueryWrapper<EquityBenefitStockPlanDO>()
                .eq(EquityBenefitStockPlanDO::getActivityId, activityId)
                .eq(EquityBenefitStockPlanDO::getStrategyId, strategyId)
                .eq(EquityBenefitStockPlanDO::getBenefitId, benefitId)
                .ge(EquityBenefitStockPlanDO::getPlanDate, DateUtil.format_yyyyMMdd(new Date()));
        return equityBenefitStockPlanMapper.selectList(wrapper);
    }
}
