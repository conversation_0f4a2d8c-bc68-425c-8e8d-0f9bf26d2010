package com.ddmc.equity.domain.service.ticketAccount;

import com.ddmc.equity.dto.business.PageListReqDTO;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.infra.repository.dao.TicketAccountDO;

import java.util.Date;
import java.util.List;

public interface TicketAccountService {

    /**
     * 账户保存
     *
     * @param ticketAccountDO 账户
     * @return 是否成功
     */
    boolean save(TicketAccountDO ticketAccountDO);

    /****
     * 获取用户权益数据
     * @param userId 用户id
     * @param activityId 活动id
     * @param ticketTemplateId 券模版id
     * @return 返回权益数量
     */
    Integer getUserTicketCountByActivityId(String userId, Long activityId, String ticketTemplateId);

    /**
     * 通过 userId 查询权益子账户列表
     *
     * @param userId      用户 id
     * @param activityIds 活动 id。如果未指定活动 id，则查询所有
     * @param statuses    权益子账户状态。如果未指定状态，则查询所有
     * @param startDate   开始时间。可以指定开始时间（创建时间）查询，不指定则查询所有
     * @param endDate     结束时间。可以指定结束时间（创建时间）查询，不指定则查询所有
     * @return 权益子账户列表
     */
    List<TicketAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds,
                                                List<Integer> statuses, Date startDate, Date endDate);


    /**
     * 通过 userId 分页查询权益子账户列表
     *
     * @param userId      用户 id
     * @param activityIds 活动 id。如果未指定活动 id，则查询所有
     * @param statuses    权益子账户状态。如果未指定状态，则查询所有
     * @param startDate   开始时间。可以指定开始时间（创建时间）查询，不指定则查询所有
     * @param endDate     结束时间。可以指定结束时间（创建时间）查询，不指定则查询所有
     * @param pageListReq 分页参数
     * @return 权益子账户列表
     */
    PageListRespDTO<TicketAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds,
                                                           List<Integer> statuses, Date startDate, Date endDate,
                                                           PageListReqDTO pageListReq);

    Integer getUserTicketCount(String userId, Long activityId, Long benefitId, Date startDate, Date endDate);
}
