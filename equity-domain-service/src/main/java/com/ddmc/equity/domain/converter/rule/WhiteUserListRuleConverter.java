package com.ddmc.equity.domain.converter.rule;

import com.ddmc.equity.domain.dto.rule.condition.WhiteUserListRuleDTO;
import com.ddmc.equity.domain.valueobject.rule.condition.WhiteUserListRuleVO;
import com.ddmc.equity.dto.business.rule.WhiteUserListDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface WhiteUserListRuleConverter {
    WhiteUserListRuleConverter INSTANCE = Mappers.getMapper(WhiteUserListRuleConverter.class);

    WhiteUserListRuleVO convertDTOToVO(WhiteUserListDTO whiteUserListDTO);

    WhiteUserListRuleDTO convertToWhiteUserListRuleDTO(WhiteUserListDTO whiteUserListDTO);
}
