package com.ddmc.equity.domain.converter.benefit;

import com.ddmc.equity.domain.dto.benefit.StrategyBenefitMultiCreateDTO;
import com.ddmc.equity.domain.dto.benefit.StrategyBenefitMultiCreateInDTO;
import com.ddmc.equity.domain.dto.benefit_stock.StockOperationBaseParam;
import com.ddmc.equity.domain.entity.benefit_stock.EquityBenefitStockFlowEntity;
import com.ddmc.equity.domain.valueobject.benefit.BenefitListFilterReqVO;
import com.ddmc.equity.dto.business.BenefitBusinessDTO;
import com.ddmc.equity.dto.business.BenefitListReqDTO;
import com.ddmc.equity.dto.business.BenefitSaveReqDTO;
import com.ddmc.equity.dto.customer.BenefitDTO;
import com.ddmc.equity.dto.customer.ReceiveBenefitReqDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockFlowDO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface BenefitConverter {
    BenefitConverter INSTANCE = Mappers.getMapper(BenefitConverter.class);

    @Mapping(target = "name", source = "idOrName")
    @Mapping(target = "id", expression = "java(com.ddmc.equity.common.util.NumStrUtil.getId(req.getIdOrName()))")
    BenefitListFilterReqVO d2v(BenefitListReqDTO req);

    BenefitBusinessDTO v2d(EquityBenefitDO input);

    List<BenefitBusinessDTO> v2ds(List<EquityBenefitDO> list);

    @Mapping(target = "opAdminName", expression = "java(com.ddmc.equity.common.util.LongUtils.isTrue(req.getId()) ? null : req.getAdminName())")
    @Mapping(target = "opAdminId", expression = "java(com.ddmc.equity.common.util.LongUtils.isTrue(req.getId()) ? null : req.getAdminId())")
    @Mapping(target = "editAdminId", source = "adminId")
    @Mapping(target = "editAdminName", source = "adminName")
    @Mapping(target = "isDelete", constant = "0")
    @Mapping(target = "createTime", expression = "java(new java.util.Date())")
    @Mapping(target = "updateTime", expression = "java(new java.util.Date())")
    EquityBenefitDO srd2e(BenefitSaveReqDTO req);

    @Mapping(target = "stockOperationType", source = "operationType")
    @Mapping(target = "stockOperation", source = "operationNum")
    @Mapping(target = "planOperationMap", ignore = true)
    @Mapping(target = "dateStr", expression = "java(com.ddmc.equity.common.util.DateUtil.format_yyyyMMdd(entity.getCreateTime()))")
    StockOperationBaseParam d2b(EquityBenefitStockFlowDO entity);

    EquityBenefitStockFlowEntity d2e(EquityBenefitStockFlowDO flowDO);

    @Mapping(target = "thirdResNo", ignore = true)
    @Mapping(target = "benefitValue", ignore = true)
    @Mapping(target = "benefitType", ignore = true)
    @Mapping(target = "benefitName", ignore = true)
    BenefitDTO rd2dEmpty(ReceiveBenefitReqDTO req);

    List<BenefitDTO> rd2dEmpties(List<ReceiveBenefitReqDTO> req);

    /**
     * in
     *
     * @param in
     * @return
     */
    StrategyBenefitMultiCreateDTO toStrategyBenefitMultiCreateDTO(StrategyBenefitMultiCreateInDTO in);

    /**
     * in
     *
     * @param in
     * @return
     */
    StrategyBenefitMultiCreateDTO.MasterCouponDTO toMasterCouponDTO(StrategyBenefitMultiCreateInDTO.MasterCouponInDTO in);

    /**
     * in
     *
     * @param in
     * @return
     */
    StrategyBenefitMultiCreateDTO.InflationCouponDTO toInflationCouponDTO(StrategyBenefitMultiCreateInDTO.InflationCouponInDTO in);
}
