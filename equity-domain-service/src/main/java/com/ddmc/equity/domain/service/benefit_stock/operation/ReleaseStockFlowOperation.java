package com.ddmc.equity.domain.service.benefit_stock.operation;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.PlanDateTypeEnum;
import com.ddmc.equity.common.enums.StockOperationEnum;
import com.ddmc.equity.common.exception.TransactionRollbackException;
import com.ddmc.equity.common.util.MapUtils;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.domain.dto.benefit_stock.StockOperationBaseParam;
import com.ddmc.equity.domain.service.benefit_stock.AbstractStockFlowFactorHandler;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitStockMapper;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitStockPlanMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * 释放剩余库存和计划剩余库存
 */
@Component
@Slf4j
public class ReleaseStockFlowOperation extends AbstractStockFlowFactorHandler {

    @Autowired
    private TransactionUtil transactionUtil;
    @Resource
    private EquityBenefitStockMapper equityBenefitStockMapper;
    @Resource
    private EquityBenefitStockPlanMapper equityBenefitStockPlanMapper;

    @Override
    public Integer getStockOperationType() {
        return StockOperationEnum.RELEASE.getOperation();
    }

    @Override
    public Boolean handleCacheUpdate(StockOperationBaseParam msg) {
        // 这个场景是脚本批量聚合到数据库，不对缓存做操作
        return true;
    }

    @Override
    public Boolean consumerStockOperationMsg(StockOperationBaseParam msg) {
        return transactionUtil.transactionalAndCatch(t -> {
            Long activityId = msg.getActivityId();
            Long strategyId = msg.getStrategyId();
            Long benefitId = msg.getBenefitId();

            // 1 释放非计划库存
            int releaseStockResult = equityBenefitStockMapper.releaseStock(msg.getStockOperation(), strategyId, activityId, benefitId);
            if (releaseStockResult <= 0) {
                log.warn("release consumerStockOperationMsg releaseStock failure. msg={}", JSON.toJSONString(msg));
                throw new TransactionRollbackException(String.format("释放非计划库存失败. activityId=%s, strategyId=%s, benefitId=%s",
                        activityId, strategyId, benefitId));
            }

            // 2 释放计划库存
            // 2.1 如果没有计划库存，则直接 return
            Integer planDateType = ObjectUtils.defaultIfNull(msg.getPlanDateType(), PlanDateTypeEnum.UNDEFINED.getType());
            Map<Long, Long> planStockIdOperateNumMap = msg.getPlanOperationMap();
            if (PlanDateTypeEnum.UNDEFINED.getType().equals(planDateType) || MapUtils.isEmpty(planStockIdOperateNumMap)) {
                return;
            }
            // 2.2 释放计划库存
            for (Map.Entry<Long, Long> entry : planStockIdOperateNumMap.entrySet()) {
                Long planStockId = entry.getKey();
                Long operateNum = entry.getValue();
                int deductPlanStockResult = equityBenefitStockPlanMapper.releasePlanStock(operateNum, planStockId, activityId);
                if (deductPlanStockResult <= 0) {
                    log.warn("release consumerStockOperationMsg releasePlanStock failure. activityId={}, planStockId={}, operateNum={}",
                            activityId, planStockId, operateNum);
                    throw new TransactionRollbackException(String.format("扣减计划库存失败. activityId=%s, planStockId=%s", activityId, planStockId));
                }
            }
        });
    }
}
