package com.ddmc.equity.domain.dto;

import com.ddmc.equity.domain.dto.account.UniversalAccountRpcResponseExtDTO;
import com.ddmc.equity.dto.customer.RiskControlInterceptRuleDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/29 16:05
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("指定活动（或者指定活动 + 策略）抽奖并领取 resp")
public class DrawAndReceiveRespDTO {

    @ApiModelProperty("领取的权益信息")
    private FullBenefitInfoDTO receiveBenefit;

    /**
     * @see com.ddmc.equity.common.enums.StatusEnum
     */
    @ApiModelProperty("领取结果状态")
    private Integer receiveResultStatus;

    /**
     * @see com.ddmc.equity.enums.BenefitUnableReceiveReasonType
     */
    @ApiModelProperty("不能领取原因 code")
    private String unableReceiveReasonCode;

    @ApiModelProperty("风控拦截规则列表。如果不能领取原因为命中风控，才返回该字段")
    private List<RiskControlInterceptRuleDTO> riskControlInterceptRuleDTOList;

    @ApiModelProperty(value = "账户明细 id")
    private Long accountDetailId;

    @ApiModelProperty("用户优惠券 id。只有权益类型为优惠券该字段才有值")
    private String userTicketId;

    @ApiModelProperty("用户优惠券包 id。只有权益类型为优惠券包该字段才有值")
    private String userTicketPackageId;

    @ApiModelProperty("外部 rpc 调用请求出参拓展信息")
    private UniversalAccountRpcResponseExtDTO rpcResponseExtDTO;
}
