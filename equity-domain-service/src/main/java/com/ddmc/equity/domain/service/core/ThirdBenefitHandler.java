package com.ddmc.equity.domain.service.core;

import com.ddmc.equity.dto.customer.*;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;
import java.util.Set;

public interface ThirdBenefitHandler {
    @Nullable
    SceneActivityDTO getBenefitActivity(ConsultSceneBenefitReqDTO consultSceneBenefitReqDTO);

    @NotNull
    Set<String> getSceneCode();

    @NotNull
    List<ReceiveBenefitReqDTO> getThirdReqList(ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO);

    @NotNull
    Pair<List<BenefitDTO> /* success */,
            List<BenefitDTO> /* fail */ > receive(String userId, List<ReceiveBenefitReqDTO> receiveBenefitReqDTOS);
}
