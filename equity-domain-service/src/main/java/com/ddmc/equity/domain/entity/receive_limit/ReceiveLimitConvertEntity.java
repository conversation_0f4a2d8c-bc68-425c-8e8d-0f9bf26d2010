package com.ddmc.equity.domain.entity.receive_limit;

import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.FrequencyDateTypeEnum;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.dto.receive_limit.UserActivityReceiveLimitCacheDTO;
import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.domain.valueobject.benefit_limit.ReceiveLimitFieldByRuleResp;
import com.ddmc.equity.infra.repository.dao.UserActivityReceiveLimitDO;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/29 20:00
 * @description
 */
public class ReceiveLimitConvertEntity {

    public static UserActivityReceiveLimitCacheDTO convertToReceiveLimitCacheDTO(String cacheValue) {
        return JsonUtil.parseObject(cacheValue, UserActivityReceiveLimitCacheDTO.class);
    }

    public static UserActivityReceiveLimitDO convertToUserActivityReceiveLimitDO(String userId, Long activityId,
                                                                                 ReceiveLimitRuleDTO receiveLimitRuleDTO) {
        ReceiveLimitFieldByRuleResp receiveLimitFieldByRuleResp = convertToReceiveLimitFieldByRuleResp(receiveLimitRuleDTO);
        if (Objects.isNull(receiveLimitFieldByRuleResp)) {
            return null;
        }
        UserActivityReceiveLimitDO userReceiveBenefitLimitDO = new UserActivityReceiveLimitDO();
        userReceiveBenefitLimitDO.setActivityId(activityId);
        userReceiveBenefitLimitDO.setUserId(userId);
        userReceiveBenefitLimitDO.setLimitCount(receiveLimitFieldByRuleResp.getLimitCount());
        // 只有在领取的时候会初始化用户活动频次规则，所以默认已领取数量为 1
        userReceiveBenefitLimitDO.setReceiveCount(1L);
        userReceiveBenefitLimitDO.setStartTime(receiveLimitFieldByRuleResp.getStartTime());
        userReceiveBenefitLimitDO.setEndTime(receiveLimitFieldByRuleResp.getEndTime());
        userReceiveBenefitLimitDO.setVersion(0L);
        return userReceiveBenefitLimitDO;
    }

    public static ReceiveLimitFieldByRuleResp convertToReceiveLimitFieldByRuleResp(@NotNull ReceiveLimitRuleDTO receiveLimitRuleDTO) {
        Integer limitType = receiveLimitRuleDTO.getLimitType();
        // 指定日期领取，暂不支持
        if (Constants.ONE.equals(limitType)) {
            return null;
        }
        if (Constants.TWO.equals(limitType)) {
            return convertToReceiveLimitFieldByRuleResp(receiveLimitRuleDTO.getFrequencyDateType(),
                    receiveLimitRuleDTO.getFrequencyDateTypeValue());
        }
        return null;
    }

    private static ReceiveLimitFieldByRuleResp convertToReceiveLimitFieldByRuleResp(Integer frequencyDateType, Long frequencyDateTypeValue) {
        frequencyDateTypeValue = ObjectUtils.defaultIfNull(frequencyDateTypeValue, 0L);
        if (FrequencyDateTypeEnum.DAY.getType().equals(frequencyDateType)) {
            Date now = new Date();
            return ReceiveLimitFieldByRuleResp.builder()
                    .limitCount(frequencyDateTypeValue)
                    .startTime(DateUtil.getStartOfDay(now))
                    .endTime(DateUtil.getEndOfDay(now))
                    .build();
        }
        if (FrequencyDateTypeEnum.WEEK.getType().equals(frequencyDateType)) {
            Date now = new Date();
            return ReceiveLimitFieldByRuleResp.builder()
                    .limitCount(frequencyDateTypeValue)
                    .startTime(DateUtil.getStartOfDay(now))
                    .endTime(DateUtil.getEndOfWeek(now))
                    .build();
        }
        if (FrequencyDateTypeEnum.MONTH.getType().equals(frequencyDateType)) {
            Date now = new Date();
            return ReceiveLimitFieldByRuleResp.builder()
                    .limitCount(frequencyDateTypeValue)
                    .startTime(DateUtil.getStartOfDay(now))
                    .endTime(DateUtil.getEndOfMonth(now))
                    .build();
        }
        if (FrequencyDateTypeEnum.YEAR.getType().equals(frequencyDateType)) {
            Date now = new Date();
            return ReceiveLimitFieldByRuleResp.builder()
                    .limitCount(frequencyDateTypeValue)
                    .startTime(DateUtil.getStartOfDay(now))
                    .endTime(DateUtil.getEndOfYear(now))
                    .build();
        }
        if (FrequencyDateTypeEnum.LIFE.getType().equals(frequencyDateType)) {
            Date now = new Date();
            Date startOfDay = DateUtil.getStartOfDay(now);
            return ReceiveLimitFieldByRuleResp.builder()
                    .limitCount(frequencyDateTypeValue)
                    .startTime(startOfDay)
                    .endTime(DateUtil.getEndOfDay(DateUtils.addYears(now, 100)))
                    .build();
        }
        return null;
    }
}
