package com.ddmc.equity.domain.service.equityAccount;

import com.ddmc.equity.infra.repository.dao.EquityAccountDO;

public interface EquityAccountService {

    /***
     * 根据uid和账户类型获取对应的do
     * @param uid 用户id
     * @param accountType 账户类型
     * @return
     */
    EquityAccountDO queryEquityAccountDOByUserIdAndAccountType(String uid , int accountType);

    int createEquityAccountByDO(EquityAccountDO equityAccountDO);

}
