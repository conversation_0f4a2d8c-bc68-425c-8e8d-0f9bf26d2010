package com.ddmc.equity.domain.service.activity;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ddmc.equity.enums.ActivityInitStockEnum;
import com.ddmc.equity.enums.ActivityStatusEnum;
import com.ddmc.equity.domain.valueobject.activity.ActivityListFilterReqVO;
import com.ddmc.equity.domain.valueobject.activity.ActivityListFilterRespVO;
import com.ddmc.equity.enums.ActivityStatusEnum;
import com.ddmc.equity.infra.repository.dao.EquityActivityDO;
import org.jetbrains.annotations.NotNull;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface EquityActivityDomainService {

    Boolean updateActivityStartTimeById(Long activityId, Date startTime);

    @NotNull List<EquityActivityDO> listBySceneCode(@NotNull String sceneCode);

    @NotNull List<EquityActivityDO> listUnOfflineBySceneCode(@NotNull String sceneCode);

    List<EquityActivityDO> queryAllUnOffline();

    List<EquityActivityDO> queryAllUnOfflineByIds(@NotNull Collection<Long> ids);

    Page<EquityActivityDO> queryAllUnOfflineByPage(int pageNo, int pageSize);

    EquityActivityDO queryEquityActivityDOById(Long id);

    List<EquityActivityDO> queryEquityActivityDOByIdS(List<Long> ids);

    ActivityListFilterRespVO getListedActivitiesRespByFilter(ActivityListFilterReqVO getListFilterReq);

    void save(EquityActivityDO activityDO);

    Long insertOrUpdate(EquityActivityDO activityDO);

    EquityActivityDO queryByThirdTypeAndId(Integer externalType, String externalId);

    int offlineById(Long id);

    /**
     * 原子化更新活动状态
     *
     * @param activityId
     * @param oldStatus
     * @param newStatus
     * @return
     */
    int updateActivityStatusByIdAndStatus(Long activityId, ActivityStatusEnum oldStatus, ActivityStatusEnum newStatus);

    int updateActivityStatusByIdAndInitStatus(Long activityId);
    /**
     * 查询时间区间内有效的活动，含新建和已发布
     *
     * @param sceneCode
     * @param start
     * @param end
     * @param excludeId
     * @return
     */
    List<EquityActivityDO> getEffectiveActivity(String sceneCode, Date start, Date end, Long excludeId);


    /**
     * 获取时间已经结束，但是状态非结束态的活动
     *
     * @param sceneCodes
     * @return
     */
    List<EquityActivityDO> getEndActivityAndNotOffline(List<String> sceneCodes);


    List<EquityActivityDO> getActivityBySceneCodeAndStatusAndBegTimeAndEndTime(List<String> sceneCodeList, Date start);

    List<EquityActivityDO> getActivityUnInitOnLineActivityBySceneCodes(List<String> sceneCodes);

    Integer updateEquityActivityDO(EquityActivityDO equityActivityDO);

    Boolean updateById(EquityActivityDO equityActivityDO);

    /**
     * 通过场景 code 查询所有活动 ids
     *
     * @param sceneCode 场景 code
     * @param statuses  状态
     * @return 活动 ids
     */
    List<Long> queryActivityIdsBySceneCode(String sceneCode, List<Integer> statuses);

    /**
     * 通过场景 code 查询所有非操作（发布过的）活动 ids
     *
     * @param sceneCode 场景 code
     * @return 活动 ids
     */
    List<Long> queryNonDraftActivityIdsBySceneCode(String sceneCode);

    /**
     * 通过外部关联类型 + 外部关联 ids 查询对应的活动 ids
     *
     * @param externalType 外部关联类型
     * @param externalIds  外部关联 id
     * @return 活动 ids
     */
    List<Long> queryActivityIdsByExternal(Integer externalType, List<String> externalIds);
}
