package com.ddmc.equity.domain.service.rule;

import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.infra.repository.dao.EquityRuleDO;
import org.jetbrains.annotations.NotNull;

import java.util.Collection;
import java.util.List;

public interface EquityRuleService {


    List<EquityRuleDO> queryAllUnOffline();

    List<EquityRuleDO> queryAllUnOfflineByStrategyIds(List<Long> strategyList);

    List<EquityRuleDO> queryEquityRuleDOListByStrategyId(Long strategyId);

    /**
     * 批量保存策略规则
     *
     * @param equityRuleDOS 规则
     * @return 是否保存成功
     */
    Boolean batchSave(List<EquityRuleDO> equityRuleDOS);


    /**
     * 批量保存策略规则
     *
     * @param list 规则
     * @return 是否保存成功
     */
    Long batchInsertOrUpdate(List<EquityRuleDO> list);

    /**
     * 查询所有的规则
     *
     * @param isContainsDeleted 是否包含被删除的数据
     * @param idIsOrderByDesc   是否按照id倒序
     * @return List
     */
    List<EquityRuleDO> selectAllRules(boolean isContainsDeleted, boolean idIsOrderByDesc);

    /**
     * 根据主键id
     * 查询规则
     *
     * @param ruleId 规则id
     * @return 规则
     */
    EquityRuleDO selectById(Long ruleId);

    /**
     * 规则更新
     *
     * @param equityRuleDO 规则
     * @return 更新结果
     */
    boolean update(EquityRuleDO equityRuleDO);

    void batchInsertAndUpdate(List<EquityRuleDO> insertDo, List<EquityRuleDO> updateDo);

    List<EquityRuleDO> queryAllUnOfflineByStrategyIdsAndType(Collection<Long> strategyList,
                                                             @NotNull Integer ruleType);

    boolean save(EquityRuleDO equityRuleDO);


    /**
     * 根据id列表获取策略规则
     *
     * @param strategyIds
     * @param typeEnum
     * @return
     */
    List<EquityRuleDO> getEquityRulesByStrategyId(List<Long> strategyIds, RuleTypeEnum typeEnum);


    void batchDelete(List<EquityRuleDO> ruleList);

    List<EquityRuleDO> batchUpdate(List<EquityRuleDO> ruleList);

    /**
     * 通过 strategyId 查询库存规则
     *
     * @param strategyId 策略 id
     * @return 库存规则
     */
    StockLimitRuleDTO getBenefitStockLimitRule(Long strategyId);
}
