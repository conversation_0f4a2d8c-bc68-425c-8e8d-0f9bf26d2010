package com.ddmc.equity.domain.dto.engine;

import com.ddmc.equity.enums.ActivityFilterTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class EngineContextDTO {

    /**
     * 应用ID(1:ios,2:安卓,3:微信,4:小程序,支付宝小程序:10)
     */
    private Integer appClientId;
    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 站点
     */
    private String stationId;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * api版本号
     */
    private String apiVersion;

    /**
     * app版本号 h5使用
     */
    private String nativeVersion;

    /**
     * session_id
     */
    private String sessionId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * H5
     */
    private String h5Source;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 城市编码
     */
    private String cityNumber;

    /**
     * 数美设备指纹token
     */
    private String deviceToken;
    /**
     * rnVersion
     */
    private String rnVersion;

    /**
     * 渠道id
     */
    private String appId;

    /**
     * 页面id
     */
    private String pageId;

    /**
     * 来源
     */
    private String source;

    /**
     * 活动查询类型
     */
    private ActivityFilterTypeEnum actFilterType;

    private Integer sendTicketScene;

    /**
     * 批量领取活动 id
     * <p>
     * 20230312 去除批量领取功能
     */
    @Deprecated
    private Long batchActivityId;

    /**
     * 活动 idList，指定活动 idList 咨询
     */
    private List<Long> activityIdList;

    /**
     * 活动外部关联类型。1-玩法；
     */
    private Integer externalType;

    /**
     * 活动外部关联 id 列表。一般是外部关联方的活动 id，如 promo.activity.activityId
     */
    private List<String> externalIdList;

    /**
     * 策略 idList，指定策略 idList 咨询
     */
    private List<Long> strategyIdList;

    /**
     * 策略外部关联 id 列表。如果是来源玩法的活动，则为活动 prizeId；如果是膨胀券活动，则为母券券模板 id；
     */
    private List<String> strategyExternalIdList;

    /**
     * 权益 idList，指定权益 idList 咨询
     */
    private List<Long> benefitIdList;

    private Boolean isTicketIsRead;

    /**
     * 是否用前端的流水号
     */
    private Integer isUseSerialNumberSave;

    /**
     * 是否领取场景下的咨询（领取时调用咨询，必须设置为 true）
     */
    private Boolean isReceive;

    /**
     * 是否需要不能领取原因。为空则为不需要
     */
    private Boolean needUnableReceiveReason;

    /**
     * @see com.ddmc.equity.enums.ActivitySendTypeEnum
     */
    @ApiModelProperty("活动上的发放类型（来源请求参数），用于决定策略的发放方式。1-全部发放；2-随机发放；3-概率发放；4-优先级发放；")
    private Integer activitySendTypeFromReq;

    /**
     * 权益咨询引擎过滤返回结果上下文
     */
    @Builder.Default
    private EngineResultContextDTO resultContextDTO = EngineResultContextDTO.builder().build();

    private String openId;
}
