package com.ddmc.equity.domain.service.benefit_limit.impl;

import com.alibaba.fastjson.JSON;
import com.csoss.monitor.api.trace.Span;
import com.csoss.monitor.api.trace.Traces;
import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.common.util.LongUtils;
import com.ddmc.equity.common.util.ThreadsUtils;
import com.ddmc.equity.domain.dto.benefit_limit.UserBenefitLimitCacheDTO;
import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.domain.entity.receive_limit.ReceiveLimitConvertEntity;
import com.ddmc.equity.domain.service.benefit_limit.EquityBenefitLimitService;
import com.ddmc.equity.domain.service.receive_limit.UserBenefitReceiveLimitMapperProxy;
import com.ddmc.equity.domain.valueobject.benefit_limit.BenefitIdInfoDTO;
import com.ddmc.equity.domain.valueobject.benefit_limit.DeductBenefitLimitResp;
import com.ddmc.equity.domain.valueobject.benefit_limit.FreezeBenefitLimitResp;
import com.ddmc.equity.domain.valueobject.benefit_limit.GetBenefitLimitCacheInfoResp;
import com.ddmc.equity.domain.valueobject.benefit_limit.ReceiveLimitFieldByRuleResp;
import com.ddmc.equity.domain.valueobject.benefit_limit.ReleaseBenefitLimitResp;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.repository.dao.UserReceiveBenefitLimitDO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EquityBenefitLimitServiceImpl implements EquityBenefitLimitService {

    @Resource
    private RedisCache redisCache;

    @Resource
    private UserBenefitReceiveLimitMapperProxy userBenefitReceiveLimitMapperProxy;

    private static final Random RANDOM = new Random();

    @Override
    public FreezeBenefitLimitResp freezeBenefitLimit(@NotNull String userId, @NotNull Long activityId,
                                                     @NotNull Long strategyId, @NotNull Long benefitId,
                                                     @NotNull ReceiveLimitRuleDTO receiveLimitRuleDTO) {
        FreezeBenefitLimitResp result = FreezeBenefitLimitResp.builder().build();
        Date now = new Date();
        String cacheKey = getCacheKey(userId, strategyId, benefitId);
        @Nullable UserBenefitLimitCacheDTO limitCache = getLimitCache(cacheKey, now);
        if (null != limitCache && LongUtils.isTrue(limitCache.getLimitCount())) {
            // 领取数到达上限
            if (Objects.equals(limitCache.getReceiveCount(), (limitCache.getLimitCount()))) {
                result.setFreezeId(limitCache.getFreezeId());
                result.setSerialNumber(getSerialNumber(limitCache.getLimitCount(), limitCache.getReceiveCount(), limitCache.getFallbackCount()));
                return result;
            }
            if (limitCache.getReceiveCount() >= limitCache.getLimitCount()) {
                result.setFreezeId(limitCache.getFreezeId());
                result.setSerialNumber(getSerialNumber(limitCache.getLimitCount(), limitCache.getReceiveCount(), limitCache.getFallbackCount()));
                return result;
            }
            // 冻结数达到上限
            if (limitCache.getFreezeCount() >= limitCache.getLimitCount()) {
                result.setSerialNumber(getSerialNumber(limitCache.getLimitCount(), limitCache.getReceiveCount(), limitCache.getFallbackCount()));
                result.setFreezeId(limitCache.getFreezeId());
                result.setFreezeCountBeforeFreeze(limitCache.getFreezeCount());
                return result;
            }
            // 冻结加领取数达到上限
            if (limitCache.getFreezeCount() + limitCache.getReceiveCount() >= limitCache.getLimitCount()) {
                result.setSerialNumber(getSerialNumber(limitCache.getLimitCount(), limitCache.getReceiveCount(), limitCache.getFallbackCount()));
                result.setFreezeId(limitCache.getFreezeId());
                result.setFreezeCountBeforeFreeze(limitCache.getFreezeCount());
                return result;
            }
        }

        Span GetUserReceiveBenefitLimitDOSpan = Traces.spanBuilder("GetUserReceiveBenefitLimitDO").startSpan();
        UserReceiveBenefitLimitDO limitDO;
        try {
            // 频次穿透
            limitDO = getUserReceiveBenefitLimitDO(userId, activityId, strategyId, benefitId, now,
                    (null == limitCache || LongUtils.isFalse(limitCache.getFreezeId())) ?
                            null : limitCache.getFreezeId());
        } finally {
            GetUserReceiveBenefitLimitDOSpan.end();
        }

        if (null == limitDO) {
            // 根据规则计算频次开始时间和结束时间
            @Nullable ReceiveLimitFieldByRuleResp resp = ReceiveLimitConvertEntity.convertToReceiveLimitFieldByRuleResp(receiveLimitRuleDTO);
            if (null == resp) {
                // 异常case，从最外层进入频次校验却没有有效的频次配置，默认不通过
                log.error("根据规则计算频次开始时间和结束时间为空 receiveLimitRuleDTO:{}", receiveLimitRuleDTO);
                return result;
            }
            UserReceiveBenefitLimitDO insertLimitDO = new UserReceiveBenefitLimitDO();
            insertLimitDO.setUserId(userId);
            insertLimitDO.setActivityId(activityId);
            insertLimitDO.setBenefitId(benefitId);
            insertLimitDO.setLimitCount(resp.getLimitCount());
            insertLimitDO.setReceiveCount(0L);
            insertLimitDO.setFreezeCount(1L);
            insertLimitDO.setFallbackCount(0L);
            insertLimitDO.setStartTime(resp.getStartTime());
            insertLimitDO.setEndTime(resp.getEndTime());
            insertLimitDO.setVersion(0L);
            insertLimitDO.setStrategyId(strategyId);
            int row = 0;
            try {
                row = userBenefitReceiveLimitMapperProxy.insert(insertLimitDO);
            } catch (Exception e) {
                log.error("频次数据插入数据库失败 insertLimitDO:{}", insertLimitDO, e);
            }
            if (row > 0 && LongUtils.isTrue(insertLimitDO.getId())) {
                resetCache(resp.getLimitCount(), 0L, 1L, 0L,
                        insertLimitDO.getId(),
                        cacheKey, now, resp.getEndTime());
                result.setFreezeResult(Boolean.TRUE);
                result.setSerialNumber(getSerialNumber(resp.getLimitCount(),
                        0L, 0L));
                result.setFreezeId(insertLimitDO.getId());
                result.setFreezeCountBeforeFreeze(insertLimitDO.getFreezeCount());
            } else {
                // 频次数据插入数据库失败，需要重试查询
                log.error("频次数据插入数据库失败 insertLimitDO:{}", insertLimitDO);
            }
            return result;
        } else {
            Long id = limitDO.getId();
            result.setFreezeId(limitDO.getId());
            result.setFreezeCountBeforeFreeze(limitDO.getFreezeCount());
            // 该分支出口均更新缓存
            // 如果达到上限，返回失败
            if (Objects.equals(limitDO.getReceiveCount(), limitDO.getLimitCount())) {
                log.warn("freezeBenefitLimit receiveCount ge limitCount. userId={}, activityId={}, strategyId={}, benefitId={}, id={}",
                        userId, activityId, strategyId, benefitId, id);
                // 冻结权益频次失败，需要重置 redis 缓存值
                refreshRedisAfterUpdateDB(userId, activityId, strategyId, benefitId, now, id);
                result.setSerialNumber(getSerialNumber(limitDO.getLimitCount(), limitDO.getReceiveCount(), limitDO.getFallbackCount()));
                return result;
            }
            // 如果已经存在冻结的直接返回冻结失败
            if (limitDO.getFreezeCount() >= limitDO.getLimitCount()) {
                log.warn("freezeBenefitLimit freezeCount ge limitCount. userId={}, activityId={}, strategyId={}, benefitId={}, id={}",
                        userId, activityId, strategyId, benefitId, id);
                // 冻结权益频次失败，需要重置 redis 缓存值
                refreshRedisAfterUpdateDB(userId, activityId, strategyId, benefitId, now, id);
                result.setSerialNumber(getSerialNumber(limitDO.getLimitCount(), limitDO.getReceiveCount(), limitDO.getFallbackCount()));
                return result;
            }
            // 执行数据库更新，带锁
            Long preVersion = limitDO.getVersion();
            int rows = userBenefitReceiveLimitMapperProxy.freezeBenefitLimit(id, userId, activityId, strategyId, benefitId,
                    preVersion);
            if (rows > 0) {
                refreshRedisAfterUpdateDB(userId, activityId, strategyId, benefitId, now, id);
                result.setFreezeResult(Boolean.TRUE);
                result.setSerialNumber(getSerialNumber(limitDO.getLimitCount(), limitDO.getReceiveCount(), limitDO.getFallbackCount()));
            } else {
                // 更新频次失败，需要重试，就没有流水号和成功状态了
                log.warn("freezeBenefitLimit freeze failure. userId={}, activityId={}, strategyId={}, benefitId={}, id={}",
                        userId, activityId, strategyId, benefitId, id);
                // 冻结权益频次失败，需要重置 redis 缓存值
                refreshRedisAfterUpdateDB(userId, activityId, strategyId, benefitId, now, id);
            }
        }
        return result;
    }

    @Override
    @Nullable
    public DeductBenefitLimitResp deductBenefitLimit(@NotNull String userId, @NotNull Long activityId,
                                                     @NotNull Long strategyId, @NotNull Long benefitId,
                                                     @NotNull ReceiveLimitRuleDTO receiveLimitRuleDTO,
                                                     @Nullable Long freezeId) {
        DeductBenefitLimitResp result = DeductBenefitLimitResp.builder().build();
        Date now = new Date();
        String cacheKey = getCacheKey(userId, strategyId, benefitId);
        @Nullable UserBenefitLimitCacheDTO limitCache = getLimitCache(cacheKey, now);
        if (null != limitCache && LongUtils.isTrue(limitCache.getLimitCount())) {
            // 领取数到达上限
            if (Objects.equals(limitCache.getReceiveCount(), (limitCache.getLimitCount()))) {
                result.setSerialNumber(getSerialNumber(limitCache.getLimitCount(), limitCache.getReceiveCount(), limitCache.getFallbackCount()));
                return result;
            }
            if (limitCache.getReceiveCount() >= limitCache.getLimitCount()) {
                result.setSerialNumber(getSerialNumber(limitCache.getLimitCount(), limitCache.getReceiveCount(), limitCache.getFallbackCount()));
                return result;
            }
        }

        // 频次穿透
        UserReceiveBenefitLimitDO limitDO = getUserReceiveBenefitLimitDO(userId, activityId, strategyId, benefitId, now, freezeId);
        if (null == limitDO) {
            log.error("查询频次不存在出错,userId:{},activityId:{},strategyId:{},benefitId:{},freezeId:{}", userId, activityId, strategyId, benefitId, freezeId);
            // 没冻结过不能扣减
            return result;
        } else {
            // 该分支出口均更新缓存
            Long freezeCount = limitDO.getFreezeCount();
            // 如果达到上限，返回失败
            if (Objects.equals(limitDO.getReceiveCount(), limitDO.getLimitCount())
                    || LongUtils.isFalse(freezeCount)) {
                resetCache(limitDO.getLimitCount(), limitDO.getReceiveCount(),
                        limitDO.getFreezeCount(),
                        limitDO.getFallbackCount(),
                        limitDO.getId(),
                        cacheKey, now, limitDO.getEndTime());
                result.setSerialNumber(getSerialNumber(limitDO.getLimitCount(), limitDO.getReceiveCount(), limitDO.getFallbackCount()));
                return result;
            }
            // 执行数据库更新，带锁
            Long id = limitDO.getId();
            Long preVersion = limitDO.getVersion();
            int rows = userBenefitReceiveLimitMapperProxy.deductBenefitLimit(id, userId, activityId, strategyId, benefitId,
                    preVersion);
            if (rows > 0) {
                refreshRedisAfterUpdateDB(userId, activityId, strategyId, benefitId, now, id);
                result.setDeductResult(Boolean.TRUE);
                result.setSerialNumber(getSerialNumber(limitDO.getLimitCount(), limitDO.getReceiveCount(), limitDO.getFallbackCount()));
            } else {
                // 更新频次失败，需要重试，就没有流水号和成功状态了
                log.error("更新频次失败 id:{} strategyId:{} activityId:{} userId:{}", id, strategyId, activityId, userId);
            }
        }
        return result;
    }

    private static String getSerialNumber(@NotNull Long limitCount, @NotNull Long receiveCount, @NotNull Long fallbackCount) {
        return String.format("%d%d%d", limitCount, receiveCount, fallbackCount);
    }

    @Nullable
    private UserBenefitLimitCacheDTO getLimitCache(String cacheKey, Date now) {
        String limitCacheRaw = redisCache.getValueWithMaster(cacheKey);
        UserBenefitLimitCacheDTO limitCache;
        if (StringUtils.isBlank(limitCacheRaw)) {
            return null;
        } else {
            limitCache = JSON.parseObject(limitCacheRaw, UserBenefitLimitCacheDTO.class);
        }
        if (null != limitCache && now.after(limitCache.getEndTime())) {
            return null;
        }
        return limitCache;
    }

    /**
     * 重建缓存
     */
    private void resetCache(long limitCount, long receiveCount, long freezeCount, long fallbackCount,
                            long freezeId, String cacheKey, Date now, Date endDate) {
        if (now.after(endDate)) {
            log.error("当前时间已超过结束时间 cacheKey:{} limitCount:{}  receiveCount:{} freezeCount:{} now:{} endDate:{}",
                    cacheKey, limitCount, receiveCount, freezeCount, now, endDate);
            return;
        }
        UserBenefitLimitCacheDTO cached = buildCacheVal(limitCount, receiveCount, freezeCount, fallbackCount, freezeId);
        cached.setEndTime(endDate);
        // 更新缓存
        redisCache.setValue(cacheKey, JSON.toJSONString(cached), buildExpireSec(now, endDate), TimeUnit.SECONDS);
    }

    /**
     * 增加随机5分钟时间过期，避免0点redis大批量淘汰抖动
     *
     * @param now     Date
     * @param endDate Date
     * @return long
     */
    private static long buildExpireSec(Date now, Date endDate) {
        return DateUtil.getBetweenSec(now, endDate) + RANDOM.nextInt(300);
    }

    /**
     * 构建缓存key
     *
     * @param userId     String
     * @param strategyId Long
     * @param benefitId  Long
     * @return String
     */
    @NotNull
    private static String getCacheKey(@NotNull String userId, @NotNull Long strategyId, @NotNull Long benefitId) {
        return String.format(CacheKeyConstants.RECEIVE_BENEFIT_LIMIT_KEY, userId, strategyId, benefitId);
    }

    @Override
    public @Nullable ReleaseBenefitLimitResp releaseBenefitLimit(@NotNull String userId, @NotNull Long activityId,
                                                                 @NotNull Long strategyId, @NotNull Long benefitId,
                                                                 @NotNull ReceiveLimitRuleDTO receiveLimitRuleDTO,
                                                                 @Nullable Long freezeId) {
        ReleaseBenefitLimitResp result = ReleaseBenefitLimitResp.builder().build();
        Date now = new Date();
        UserReceiveBenefitLimitDO limitDO = getUserReceiveBenefitLimitDO(userId, activityId, strategyId, benefitId, now, freezeId);
        if (null == limitDO || null == limitDO.getFreezeCount()) {
            // 不存在频次限制，不解冻
            return result;
        }
        // 该分支出口均更新缓存
        if (limitDO.getFreezeCount() <= 0) {
            // 没有频次使用，不解冻
            result.setSerialNumber(getSerialNumber(limitDO.getLimitCount(), limitDO.getReceiveCount(), limitDO.getFallbackCount()));
            return result;
        }
        Long id = limitDO.getId();
        Long preVersion = limitDO.getVersion();
        int row = userBenefitReceiveLimitMapperProxy.releaseBenefitLimit(id, userId, activityId, strategyId, benefitId,
                preVersion);
        if (row > 0) {
            refreshRedisAfterUpdateDB(userId, activityId, strategyId, benefitId, now, id);
            result.setReleaseResult(Boolean.TRUE);
            result.setSerialNumber(getSerialNumber(limitDO.getLimitCount(), limitDO.getReceiveCount(), limitDO.getFallbackCount()));
        } else {
            log.error("释放频次失败 id:{} strategyId:{} activityId:{} benefitId:{}", id, strategyId, activityId, benefitId);
        }
        return result;
    }

    // 异步实现刷新redis
    // 考虑是不是用延迟队列更好
    private void refreshRedisAfterUpdateDB(@NotNull String userId, @NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId, Date now,
                                           @Nullable Long freezeId) {
        ThreadsUtils.getOrdinaryThreadPoll().submit(() -> {
            String cacheKey = getCacheKey(userId, strategyId, benefitId);
            // 理论上version锁住了，不需要第二次查询，但和下一句写缓存是有gap的，不原子
            UserReceiveBenefitLimitDO recentLimitDO = getUserReceiveBenefitLimitDO(userId, activityId, strategyId, benefitId, now, freezeId);
            if (null != recentLimitDO) {
                resetCache(recentLimitDO.getLimitCount(), recentLimitDO.getReceiveCount(),
                        recentLimitDO.getFreezeCount(), recentLimitDO.getFallbackCount(),
                        recentLimitDO.getId(),
                        cacheKey, now, recentLimitDO.getEndTime());
            }
        });
    }

    @Override
    public @Nullable GetBenefitLimitCacheInfoResp getBenefitLimitCache(
            @NotNull String userId, @NotNull Long activityId,
            @NotNull Long strategyId, @NotNull Long benefitId) {
        String cacheKey = getCacheKey(userId, strategyId, benefitId);
        String limitCache = redisCache.getValueWithMaster(cacheKey);
        if (StringUtils.isNotBlank(limitCache)) {
            UserBenefitLimitCacheDTO dto = parseCacheVal(cacheKey, limitCache, new Date());
            if (null != dto) {
                return GetBenefitLimitCacheInfoResp.builder()
                        .limitCount(dto.getLimitCount())
                        .receiveCount(dto.getReceiveCount())
                        .freezeCount(dto.getFreezeCount())
                        .benefitId(benefitId)
                        .build();
            }
        }
        return null;
    }

    @Override
    public List<GetBenefitLimitCacheInfoResp> getBenefitLimitCacheMul(String userId, List<BenefitIdInfoDTO> benefitIdInfos) {
        if (StringUtils.isBlank(userId) || CollectionUtils.isEmpty(benefitIdInfos)) {
            return null;
        }

        Map<String /* cacheKey */, BenefitIdInfoDTO> mKeyMap = Maps.newHashMap();
        benefitIdInfos.forEach(e -> mKeyMap.put(getCacheKey(userId, e.getStrategyId(), e.getBenefitId()), e));
        Map<String /* cacheKey */, String /* val */> mGetResult = redisCache.getMulStrValue(mKeyMap.keySet());

        return mGetResult.entrySet().stream().map(entry -> {
            String cacheKey = entry.getKey();
            String cacheValue = entry.getValue();
            BenefitIdInfoDTO benefitIdInfoDTO = mKeyMap.get(cacheKey);
            if (Objects.isNull(benefitIdInfoDTO) || StringUtils.isBlank(cacheValue)) {
                return null;
            }
            UserBenefitLimitCacheDTO benefitLimitCacheDTO = parseCacheVal(cacheKey, cacheValue, new Date());
            if (Objects.isNull(benefitLimitCacheDTO)) {
                return null;
            }
            return GetBenefitLimitCacheInfoResp.builder()
                    .activityId(benefitIdInfoDTO.getActivityId())
                    .strategyId(benefitIdInfoDTO.getStrategyId())
                    .benefitGroupId(benefitIdInfoDTO.getBenefitGroupId())
                    .benefitId(benefitIdInfoDTO.getBenefitId())
                    .receiveCount(benefitLimitCacheDTO.getReceiveCount())
                    .limitCount(benefitLimitCacheDTO.getLimitCount())
                    .freezeCount(benefitLimitCacheDTO.getFreezeCount())
                    .build();
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Nullable
    private static UserBenefitLimitCacheDTO parseCacheVal(String key, String cached, Date now) {
        UserBenefitLimitCacheDTO dto = null;
        try {
            dto = JSON.parseObject(cached, UserBenefitLimitCacheDTO.class);
        } catch (Exception e) {
            log.error("解析缓存数据出错 key:{}", key, e);
        }
        if (null != dto && now.after(dto.getEndTime())) {
            return null;
        }
        return dto;
    }

    private UserReceiveBenefitLimitDO getUserReceiveBenefitLimitDO(
            String userId, Long activityId, Long strategyId, Long benefitId, Date now,
            @Nullable Long freezeId) {
        return userBenefitReceiveLimitMapperProxy.queryByDate(userId, activityId, strategyId, benefitId, now, freezeId);
    }


    @NotNull
    private static UserBenefitLimitCacheDTO buildCacheVal(
            Long limitCount, Long receiveCount, Long freezeCount, Long fallbackCount,
            Long freezeId) {
        return UserBenefitLimitCacheDTO.builder()
                .limitCount(limitCount)
                .receiveCount(receiveCount)
                .freezeCount(freezeCount)
                .fallbackCount(fallbackCount)
                .freezeId(freezeId)
                .build();
    }
}
