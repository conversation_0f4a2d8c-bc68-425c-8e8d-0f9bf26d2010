package com.ddmc.equity.domain.service.benefit;

import com.ddmc.equity.dto.customer.QueryReceivedBenefitsReqDTO;
import com.ddmc.equity.dto.customer.ReceiveSceneBenefitReqDTO;
import com.ddmc.equity.dto.customer.ReceiveSceneBenefitResDTO;
import org.jetbrains.annotations.NotNull;

public interface BenefitAggregateService {
    /**
     * 领取主流程中的领取动作
     *
     * @param receiveSceneBenefitReqDTO ReceiveSceneBenefitReqDTO
     * @return ReceiveSceneBenefitResDTO
     */
    @NotNull
    ReceiveSceneBenefitResDTO receiveSceneBenefit(@NotNull ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO);

    /**
     * 查询当前场景可领权益接口，供C端玩法调用
     *
     * @param queryReceivedBenefitsReqDTO QueryReceivedBenefitsReqDTO
     * @return ReceiveSceneBenefitResDTO
     */
    @NotNull
    ReceiveSceneBenefitResDTO queryReceivedBenefits(QueryReceivedBenefitsReqDTO queryReceivedBenefitsReqDTO);

    /**
     * 幂等的领取接口，供C端玩法调用和JOB端同步脚本调用
     *
     * @param receiveSceneBenefitReqDTO ReceiveSceneBenefitReqDTO
     * @return ReceiveSceneBenefitResDTO
     */
    @NotNull
    ReceiveSceneBenefitResDTO receiveSceneBenefitProcess(@NotNull ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO);
}
