package com.ddmc.equity.domain.valueobject.activity;

import com.ddmc.equity.infra.repository.dao.EquityActivityDO;
import com.ddmc.equity.infra.repository.dao.EquitySceneDO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class ActivityListFilterRespVO implements Serializable {
    private static final long serialVersionUID = 8084897122819632888L;
    @ApiModelProperty("活动列表")
    @NotNull
    @Builder.Default
    private List<EquityActivityDO> list = Lists.newArrayList();

    @ApiModelProperty("活动记录总数")
    @NotNull
    @Builder.Default
    private Long total = 0L;
}
