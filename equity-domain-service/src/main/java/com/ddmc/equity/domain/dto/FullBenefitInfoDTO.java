package com.ddmc.equity.domain.dto;

import com.ddmc.equity.domain.dto.benefit.BenefitExtInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/19 14:57
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class FullBenefitInfoDTO {

    @ApiModelProperty(value = "活动 id")
    private Long activityId;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    /**
     * @see com.ddmc.equity.enums.ExternalTypeEnum
     */
    @ApiModelProperty(value = "外部关联类型。1-玩法；")
    private Integer externalType;

    @ApiModelProperty(value = "外部关联 id（一般是外部关联方的活动 id，如 promo.activity.activityId）")
    private String externalId;

    @ApiModelProperty(value = "策略 id")
    private Long strategyId;

    @ApiModelProperty(value = "策略名称")
    private String strategyName;

    @ApiModelProperty(value = "策略外部关联 id。如果是来源玩法的活动，则为活动 prizeId；如果是膨胀券活动，则为母券券模板 id；")
    private String strategyExternalId;

    /**
     * @see com.ddmc.equity.enums.StrategyTypeEnum
     */
    @ApiModelProperty("策略类型。0-普通策略；1-兜底策略；2-黑灰产策略；")
    private Integer strategyType;

    @ApiModelProperty(value = "权益组 id")
    private Long benefitGroupId;

    @ApiModelProperty(value = "权益 id")
    private Long benefitId;

    @ApiModelProperty(value = "权益名称")
    private String benefitName;

    /**
     * @see com.ddmc.equity.enums.BenefitTypeEnum
     */
    @ApiModelProperty("权益类型")
    private Integer benefitType;

    @ApiModelProperty("权益值")
    private String benefitValue;

    /**
     * @see com.ddmc.equity.enums.BenefitAmountTypeEnum
     */
    @ApiModelProperty("权益数量类型。0-固定数量权益；1-不固定数量权益；2-随机数量权益；不传默认为 0")
    private Integer benefitAmountType;

    /**
     * 最大权益数量。仅在权益数量类型为不固定数量权益时有效，用于控制发放数量不超过最大值限制
     */
    private String maxAmount;

    /**
     * 对于每次发放数量必须为 1 的权益类型（如优惠券等），sendAmount = 1
     * 发放固定数量权益时，sendAmount = benefit.benefitValue
     * 发放不固定数量权益时，sendAmount = reqSendAmount
     * 发放随机数量权益时，sendAmount = benefit.benefitValue 范围内随机的数量
     */
    @ApiModelProperty("权益实际发放数量")
    private String sendAmount;

    @ApiModelProperty("余额金额，保留两位小数。如果权益类型为余额（固定余额、随机余额），才有该字段")
    private BigDecimal balanceMoney;

    @ApiModelProperty("权益拓展信息")
    private BenefitExtInfoDTO benefitExtInfoDTO;
}
