package com.ddmc.equity.domain.converter.activity;

import com.ddmc.equity.domain.entity.activity.EquityActivityEntity;
import com.ddmc.equity.domain.valueobject.activity.ActivityListFilterReqVO;
import com.ddmc.equity.dto.business.ActivityBusinessDTO;
import com.ddmc.equity.dto.business.ActivityListReqDTO;
import com.ddmc.equity.dto.business.ActivitySaveReqDTO;
import com.ddmc.equity.dto.business.UniversalActivityDTO;
import com.ddmc.equity.infra.repository.dao.EquityActivityDO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface ActivityConverter {
    ActivityConverter INSTANCE = Mappers.getMapper(ActivityConverter.class);

    EquityActivityEntity activityD2E(EquityActivityDO target);

    List<EquityActivityEntity> activityD2Es(List<EquityActivityDO> target);

    @Mapping(target = "name", source = "idOrName")
    @Mapping(target = "id", expression = "java(com.ddmc.equity.common.util.NumStrUtil.getId(req.getIdOrName()))")
    ActivityListFilterReqVO d2v(ActivityListReqDTO req);

    ActivityBusinessDTO v2d(EquityActivityDO target);

    List<ActivityBusinessDTO> v2ds(List<EquityActivityDO> list);

    @Mapping(target = "opAdminName", expression = "java(com.ddmc.equity.common.util.LongUtils.isTrue(req.getId()) ? null : req.getAdminName())")
    @Mapping(target = "opAdminId", expression = "java(com.ddmc.equity.common.util.LongUtils.isTrue(req.getId()) ? null : req.getAdminId())")
    @Mapping(target = "editAdminId", source = "adminId")
    @Mapping(target = "editAdminName", source = "adminName")
    @Mapping(target = "isDelete", constant = "0")
    @Mapping(target = "createTime", expression = "java(new java.util.Date())")
    @Mapping(target = "updateTime", expression = "java(new java.util.Date())")
    EquityActivityDO srd2e(ActivitySaveReqDTO req);

    @Mapping(target = "adminId", ignore = true)
    @Mapping(target = "adminName", ignore = true)
    ActivitySaveReqDTO d2d(EquityActivityDO activityDO);

    EquityActivityDO dto2do(UniversalActivityDTO req);
}
