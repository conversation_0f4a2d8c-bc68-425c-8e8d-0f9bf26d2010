package com.ddmc.equity.domain.service.unit_price.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.entity.account.UnitPriceAccountConvertEntity;
import com.ddmc.equity.domain.service.unit_price.UnitPriceAccountRecordDomainService;
import com.ddmc.equity.infra.repository.dao.UnitPriceAccountRecordDO;
import com.ddmc.equity.infra.repository.dao.mapper.UnitPriceAccountRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/16 11:30
 * @description
 */
@Slf4j
@Service
public class UnitPriceAccountRecordDomainServiceImpl extends ServiceImpl<UnitPriceAccountRecordMapper, UnitPriceAccountRecordDO>
        implements UnitPriceAccountRecordDomainService {

    @Override
    public void insertAccountRecordDO(UnitPriceAccountRecordDO accountRecordDO) {
        Assert.notNull(accountRecordDO, ExceptionEnum.ILLEGAL_ARGS.getCode(), "单价促销品权益子账户操作流水不能为空");
        Assert.mustTrue(StringUtils.isNotBlank(accountRecordDO.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "单价促销品权益子账户操作流水 userId 不能为空");
        this.save(accountRecordDO);
    }

    @Override
    public boolean updateAccountRecordStatusAndRpcResult(String userId, Long accountRecordId, Long accountId, Integer status,
                                                         EquityRpcDto equityRpcDto, Map<String, Object> ruleLimitInfoMap) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "单价促销品权益子账户操作流水 userId 不能为空");
        Assert.notNull(accountRecordId, ExceptionEnum.ILLEGAL_ARGS.getCode(), "单价促销品权益子账户操作流水 accountRecordId 不能为空");
        Assert.mustTrue(StatusEnum.isContain(status), ExceptionEnum.ILLEGAL_ARGS.getCode(), "单价促销品权益子账户操作流水状态异常");

        UnitPriceAccountRecordDO update = UnitPriceAccountConvertEntity.createUpdateAccountRecordDO(accountRecordId, accountId,
                status, equityRpcDto, ruleLimitInfoMap);
        return this.update(update, Wrappers.<UnitPriceAccountRecordDO>lambdaUpdate()
                .eq(UnitPriceAccountRecordDO::getUserId, userId)
                .eq(UnitPriceAccountRecordDO::getId, accountRecordId));
    }

    @Override
    public UnitPriceAccountRecordDO queryAccountRecordByUniqueKeyAndStatuses(String userId, Integer operateType,
                                                                             String reqNo, List<Integer> statuses) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "单价促销品权益子账户操作流水 userId 不能为空");
        Assert.mustTrue(OperateTypeEnum.contains(operateType), ExceptionEnum.ILLEGAL_ARGS.getCode(), "单价促销品权益子账户操作流水操作类型异常");
        Assert.mustTrue(StringUtils.isNotBlank(reqNo), ExceptionEnum.ILLEGAL_ARGS.getCode(), "单价促销品权益子账户操作流水 reqNo 不能为空");

        Wrapper<UnitPriceAccountRecordDO> wrapper = Wrappers.<UnitPriceAccountRecordDO>lambdaQuery()
                .eq(UnitPriceAccountRecordDO::getUserId, userId)
                .eq(UnitPriceAccountRecordDO::getOperateType, operateType)
                .eq(UnitPriceAccountRecordDO::getReqNo, reqNo)
                .in(CollectionUtils.isNotEmpty(statuses), UnitPriceAccountRecordDO::getStatus, statuses);
        return this.getOne(wrapper);
    }
}
