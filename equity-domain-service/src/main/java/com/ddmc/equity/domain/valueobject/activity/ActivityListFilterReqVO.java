package com.ddmc.equity.domain.valueobject.activity;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class ActivityListFilterReqVO implements Serializable {
    private static final long serialVersionUID = 1876750553671013963L;

    @Builder.Default
    private Integer page = 1;
    @Builder.Default
    private Integer pageSize = 20;

    private Long id;

    private String name;

    private String sceneCode;
    private Date startTime;
    private Date endTime;
    private List<Integer> sendTypes;
    private List<Integer> externalTypes;

    private String creator;

    private String updater;

    private List<Integer> statuses;

}
