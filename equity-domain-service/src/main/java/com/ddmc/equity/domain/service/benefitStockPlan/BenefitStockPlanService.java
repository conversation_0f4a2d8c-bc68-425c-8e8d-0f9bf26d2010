package com.ddmc.equity.domain.service.benefitStockPlan;

import com.ddmc.equity.domain.dto.benefit_stock.StockOperationBaseParam;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockPlanDO;

import java.util.List;

public interface BenefitStockPlanService {

    EquityBenefitStockPlanDO queryBenefitStockPlanByIdAndKey(StockOperationBaseParam stockOperationBaseParam);

    /**
     * 查询生效中的权益计划库存
     *
     * @param activityId 活动 Id
     * @param strategyId 策略 Id
     * @param benefitId  权益 Id
     * @return 权益计划库存列表
     */
    List<EquityBenefitStockPlanDO> listEffectiveBenefitStockPlans(Long activityId, Long strategyId, Long benefitId);
}
