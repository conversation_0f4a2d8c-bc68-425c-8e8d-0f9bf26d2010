package com.ddmc.equity.domain.dto.benefit;

import com.ddmc.equity.infra.repository.dao.CouponExpandAccountCollectDO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * 明细数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityCouponExpandAccountCollectOutDTO {
    private static final long serialVersionUID = 8084897122819632888L;
    @ApiModelProperty("活动列表")
    @NotNull
    @Builder.Default
    private List<CouponExpandAccountCollectDO> list = Lists.newArrayList();

    @ApiModelProperty("活动记录总数")
    @NotNull
    @Builder.Default
    private Long total = 0L;

    @ApiModelProperty("总页数")
    @NotNull
    @Builder.Default
    private Long pages = 0L;
}
