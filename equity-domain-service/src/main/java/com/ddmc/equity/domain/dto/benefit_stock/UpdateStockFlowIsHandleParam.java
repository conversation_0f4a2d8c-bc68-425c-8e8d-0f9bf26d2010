package com.ddmc.equity.domain.dto.benefit_stock;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/8/28 10:37
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class UpdateStockFlowIsHandleParam {

    /**
     * 是否更新为已处理
     */
    private Boolean handle;
    /**
     * 活动 ids
     */
    private List<Long> activityIds;
    /**
     * 策略 ids
     */
    private List<Long> strategyIds;
    /**
     * 权益 ids
     */
    private List<Long> benefitIds;
    /**
     * 活动 ids
     */
    private List<Long> operationTypes;
    /**
     * 结束时间
     */
    private Date endTime;
}
