package com.ddmc.equity.domain.service.decoration.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.exception.BusinessException;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.common.util.business.BenefitUtil;
import com.ddmc.equity.domain.service.decoration.UserDecorationCacheDomainService;
import com.ddmc.equity.domain.service.decoration.UserWearingDecorationDomainService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountDetailDomainService;
import com.ddmc.equity.dto.customer.decoration.UnwearDecorationReqDTO;
import com.ddmc.equity.dto.customer.decoration.WearDecorationReqDTO;
import com.ddmc.equity.enums.DecorationTypeEnum;
import com.ddmc.equity.enums.DecorationWearStatusEnum;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;
import com.ddmc.equity.infra.repository.dao.UserWearingDecorationDO;
import com.ddmc.equity.infra.repository.dao.mapper.UserWearingDecorationMapper;
import csoss.daas.redis.client.lock.DdmcLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/16 11:31
 * @description 用户佩戴装饰领域服务实现类
 */
@Slf4j
@Service
public class UserWearingDecorationDomainServiceImpl implements UserWearingDecorationDomainService {

    @Resource
    private UserWearingDecorationMapper userWearingDecorationMapper;
    @Resource
    private UniversalAccountDetailDomainService universalAccountDetailDomainService;
    @Resource
    private LocalCacheManager localCacheManager;
    @Resource
    private RedisCache redisCache;
    @Resource
    private UserDecorationCacheDomainService userDecorationCacheDomainService;

    @Override
    public UserWearingDecorationDO queryCurrentUserWearingDecoration(String userId, Integer decorationType,
                                                                     Boolean includeExpired) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "用户 ID 不能为空");
        Assert.notNull(decorationType, ExceptionEnum.ILLEGAL_ARGS.getCode(), "装饰类型不能为空");

        Wrapper<UserWearingDecorationDO> wrapper = Wrappers.<UserWearingDecorationDO>lambdaQuery()
                // 查询指定用户 ID 的装饰
                .eq(UserWearingDecorationDO::getUserId, userId)
                // 查询指定装饰类型
                .eq(UserWearingDecorationDO::getDecorationType, decorationType)
                // 查询状态为佩戴中的装饰
                .eq(UserWearingDecorationDO::getWearStatus, DecorationWearStatusEnum.WEARING.getCode())
                // 根据 includeExpired 参数决定是否添加过期时间条件
                .and(includeExpired == null || Boolean.FALSE.equals(includeExpired),
                        w -> w.isNull(UserWearingDecorationDO::getExpireTime)
                                .or()
                                .ge(UserWearingDecorationDO::getExpireTime, DateUtil.currentDate()))
                // 限制只返回一条记录
                .last("LIMIT 1");

        return userWearingDecorationMapper.selectOne(wrapper);
    }

    @Override
    public UserWearingDecorationDO queryUserWearingDecoration(String userId, Integer decorationType) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "用户 ID 不能为空");
        Assert.notNull(decorationType, ExceptionEnum.ILLEGAL_ARGS.getCode(), "装饰类型不能为空");

        Wrapper<UserWearingDecorationDO> wrapper = Wrappers.<UserWearingDecorationDO>lambdaQuery()
                .eq(UserWearingDecorationDO::getUserId, userId)
                .eq(UserWearingDecorationDO::getDecorationType, decorationType)
                .last("LIMIT 1");

        return userWearingDecorationMapper.selectOne(wrapper);
    }

    @Override
    public UserWearingDecorationDO queryUserWearingDecorationBindMaster(String userId, Integer decorationType) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "用户 ID 不能为空");
        Assert.notNull(decorationType, ExceptionEnum.ILLEGAL_ARGS.getCode(), "装饰类型不能为空");

        Wrapper<UserWearingDecorationDO> wrapper = Wrappers.<UserWearingDecorationDO>lambdaQuery()
                .and(w -> w.apply("'bind_master' = 'bind_master'"))
                .eq(UserWearingDecorationDO::getUserId, userId)
                .eq(UserWearingDecorationDO::getDecorationType, decorationType)
                .last("LIMIT 1");

        return userWearingDecorationMapper.selectOne(wrapper);
    }

    @Override
    public void wearDecoration(WearDecorationReqDTO req) {
        // 1. 参数校验
        Assert.notNull(req, ExceptionEnum.ILLEGAL_ARGS.getCode(), "请求 req 不能为空");
        String userId = req.getUserId();
        Integer decorationType = req.getDecorationType();
        Long decorationRecordId = req.getDecorationRecordId();
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "用户 ID 不能为空");
        Assert.notNull(decorationType, ExceptionEnum.ILLEGAL_ARGS.getCode(), "装饰类型不能为空");
        Assert.notNull(decorationRecordId, ExceptionEnum.ILLEGAL_ARGS.getCode(), "装饰记录 ID 不能为空");
        String sourceWithDefault = StringUtils.defaultIfBlank(req.getSource(), "unknownSource");

        // 2. 查询并校验装饰记录和装饰权益信息（锁外查询）
        UniversalAccountDetailDO accountDetailDO = validateDecorationRecord(userId, decorationType, decorationRecordId);
        EquityBenefitDO benefitDO = validateDecorationBenefit(userId, decorationType, accountDetailDO.getBenefitId());

        // 3. 构建分布式锁
        String lockKey = getUserWearingDecorationOpLockKey(userId, decorationType);
        DdmcLock lock = redisCache.createLock(lockKey);
        boolean isLocked = false;

        try {
            // 4. 尝试获取锁，等待 0 秒，锁过期时间 10 秒
            isLocked = redisCache.tryLock(lock, 10, TimeUnit.SECONDS);
            if (!isLocked) {
                log.warn("wearDecoration failed to acquire op lock for wearing. userId={}, decorationType={}, decorationRecordId={}",
                        userId, decorationType, decorationRecordId);
                throw new BusinessException(ExceptionEnum.TOO_FREQUENTING.getCode(), "系统繁忙，请稍后再试");
            }

            // 5. 查询当前用户是否已有佩戴装饰记录（需要 bindMaster 获取最新数据）
            UserWearingDecorationDO existingDecoration = this.queryUserWearingDecorationBindMaster(userId, decorationType);

            // 6. 处理佩戴逻辑
            if (existingDecoration == null) {
                // 6.1 如果没有佩戴中的装饰，则创建新的佩戴记录
                createNewWearingDecorationRecord(userId, decorationType, decorationRecordId, accountDetailDO, benefitDO);

                // 佩戴成功，打印日志、打点
                log.info("wearDecoration create new decoration successfully. userId={}, decorationType={}" +
                                ", decorationRecordId={}, source={}",
                        userId, decorationType, decorationRecordId, sourceWithDefault);
                CsossUtils.logEventWithSpan(MonitorConstants.USER_DECORATION_WEAR_SUCCESS, "createNew_" + sourceWithDefault);
            } else {
                // 6.2 如果已有佩戴中的装饰，则更新为新的装饰记录
                switchToNewWearingDecoration(userId, decorationType, decorationRecordId, existingDecoration,
                        accountDetailDO, benefitDO);

                // 佩戴成功，打印日志、打点
                log.info("wearDecoration switch to new decoration successfully. userId={}, decorationType={}" +
                                ", decorationRecordId={}, source={}",
                        userId, decorationType, decorationRecordId, sourceWithDefault);
                CsossUtils.logEventWithSpan(MonitorConstants.USER_DECORATION_WEAR_SUCCESS, "switchToNew_" + sourceWithDefault);
            }
        } catch (BusinessException e) {
            // 业务异常直接抛出，里面已经有日志打印
            throw e;
        } catch (Exception e) {
            // 系统异常记录日志并包装为业务异常
            log.error("wearDecoration exception. userId={}, decorationType={}, decorationRecordId={}",
                    userId, decorationType, decorationRecordId, e);
            throw new BusinessException(ExceptionEnum.USER_DECORATION_WEAR_EXCEPTION);
        } finally {
            // 7. 释放锁
            redisCache.unlock(lock, isLocked);
        }
    }

    @Override
    public void unwearDecoration(UnwearDecorationReqDTO req) {
        // 1. 参数校验
        Assert.notNull(req, ExceptionEnum.ILLEGAL_ARGS.getCode(), "请求 req 不能为空");
        String userId = req.getUserId();
        Integer decorationType = req.getDecorationType();
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "用户 ID 不能为空");
        Assert.notNull(decorationType, ExceptionEnum.ILLEGAL_ARGS.getCode(), "装饰类型不能为空");
        String sourceWithDefault = StringUtils.defaultIfBlank(req.getSource(), "unknownSource");

        // 2. 构建分布式锁 key（与 wearDecoration 使用相同的锁）
        String lockKey = getUserWearingDecorationOpLockKey(userId, decorationType);
        DdmcLock lock = redisCache.createLock(lockKey);
        boolean isLocked = false;

        try {
            // 3. 尝试获取锁，等待 0 秒，锁过期时间 10 秒
            isLocked = redisCache.tryLock(lock, 10, TimeUnit.SECONDS);
            if (!isLocked) {
                log.warn("unwearDecoration failed to acquire op lock for unwearing. userId={}, decorationType={}", userId, decorationType);
                throw new BusinessException(ExceptionEnum.TOO_FREQUENTING.getCode(), "系统繁忙，请稍后再试");
            }

            // 4. 查询当前佩戴中的装饰（需要 bindMaster 获取最新数据）
            UserWearingDecorationDO existingDecoration = this.queryUserWearingDecorationBindMaster(userId, decorationType);

            // 5. 取消佩戴装饰记录
            cancelWearingDecorationRecord(userId, decorationType, existingDecoration);

            log.info("unwearDecoration successfully unweared. userId={}, decorationType={}, source={}",
                    userId, decorationType, sourceWithDefault);
            CsossUtils.logEventWithSpan(MonitorConstants.USER_DECORATION_UNWEAR_SUCCESS, sourceWithDefault);
        } catch (BusinessException e) {
            // 业务异常直接抛出，里面已经有日志打印
            throw e;
        } catch (Exception e) {
            // 系统异常记录日志并包装为业务异常
            log.error("unwearDecoration exception. userId={}, decorationType={}", userId, decorationType, e);
            throw new BusinessException(ExceptionEnum.USER_DECORATION_UNWEAR_EXCEPTION);
        } finally {
            // 6. 释放锁
            redisCache.unlock(lock, isLocked);
        }
    }

    /**
     * 校验装饰记录
     *
     * @param userId             用户 ID
     * @param decorationType     装饰类型
     * @param decorationRecordId 装饰记录 ID
     * @return 装饰记录
     * @throws BusinessException 当装饰记录不存在、装饰类型不匹配或装饰已过期时抛出
     */
    private @NotNull UniversalAccountDetailDO validateDecorationRecord(String userId, Integer decorationType,
                                                                       Long decorationRecordId) {
        // 查询装饰记录
        UniversalAccountDetailDO accountDetailDO = universalAccountDetailDomainService.queryById(userId, decorationRecordId);

        // 校验装饰记录是否存在
        if (accountDetailDO == null) {
            log.error("validateDecorationRecord decorationRecord not found. userId={}, decorationType={}, decorationRecordId={}",
                    userId, decorationType, decorationRecordId);
            throw new BusinessException(ExceptionEnum.ILLEGAL_ARGS.getCode(), "装饰记录不存在");
        }

        // 校验装饰类型是否匹配
        if (!Objects.equals(accountDetailDO.getBenefitType(), DecorationTypeEnum.getRelatedBenefitTypeByCode(decorationType))) {
            log.warn("validateDecorationRecord decorationType mismatch. userId={}, decorationType={}, decorationRecordId={}, benefitType={}",
                    userId, decorationType, decorationRecordId, accountDetailDO.getBenefitType());
            throw new BusinessException(ExceptionEnum.ILLEGAL_ARGS.getCode(), "装饰类型不匹配");
        }

        // 校验是否过期
        if (accountDetailDO.getExpireTime() != null && accountDetailDO.getExpireTime().before(DateUtil.currentDate())) {
            log.warn("validateDecorationRecord decorationRecord expired. userId={}, decorationType={}, decorationRecordId={}, expireTime={}",
                    userId, decorationType, decorationRecordId, accountDetailDO.getExpireTime());
            throw new BusinessException(ExceptionEnum.USER_DECORATION_EXPIRED);
        }

        return accountDetailDO;
    }

    /**
     * 校验装饰权益信息
     *
     * @param userId         用户 ID
     * @param decorationType 装饰类型
     * @param decorationId   装饰 ID
     * @return 装饰权益信息
     * @throws BusinessException 当装饰权益信息不存在时抛出
     */
    private @NotNull EquityBenefitDO validateDecorationBenefit(String userId, Integer decorationType,
                                                               Long decorationId) {
        // 查询权益信息
        EquityBenefitDO benefitDO = localCacheManager.getBenefitInfoById(decorationId);

        // 校验权益信息是否存在
        if (benefitDO == null) {
            log.error("validateDecoration decorationBenefit not found. userId={}, decorationType={}, decorationId={}",
                    userId, decorationType, decorationId);
            throw new BusinessException(ExceptionEnum.ILLEGAL_ARGS.getCode(), "装饰权益信息不存在");
        }

        return benefitDO;
    }

    /**
     * 创建新的用户佩戴装饰记录
     *
     * @param userId             用户 ID
     * @param decorationType     装饰类型
     * @param decorationRecordId 装饰记录 ID
     * @param accountDetailDO    装饰记录
     * @param benefitDO          权益信息
     * @throws BusinessException 当插入数据库失败时抛出 USER_DECORATION_WEAR_FAILED 异常
     */
    private void createNewWearingDecorationRecord(String userId, Integer decorationType, Long decorationRecordId,
                                                  UniversalAccountDetailDO accountDetailDO, EquityBenefitDO benefitDO) {
        // 构建新的佩戴记录
        UserWearingDecorationDO wearingDecorationDO = buildUserWearingDecorationDO(userId, decorationType,
                decorationRecordId, accountDetailDO, benefitDO);

        // 插入数据库
        int insertResult = userWearingDecorationMapper.insert(wearingDecorationDO);
        if (insertResult != 1) {
            log.error("createNewWearingDecorationRecord failed to insert. userId={}, decorationType={}, decorationRecordId={}",
                    userId, decorationType, decorationRecordId);
            throw new BusinessException(ExceptionEnum.USER_DECORATION_WEAR_FAILED);
        }
        // put cache
        UserWearingDecorationDO cacheDO = new UserWearingDecorationDO();
        cacheDO.setUserId(userId);
        cacheDO.setDecorationType(decorationType);
        cacheDO.setWearStatus(DecorationWearStatusEnum.WEARING.getCode());
        cacheDO.setDecorationName(wearingDecorationDO.getDecorationName());
        cacheDO.setDecorationImage(wearingDecorationDO.getDecorationImage());
        cacheDO.setExpireTime(wearingDecorationDO.getExpireTime());
        this.cacheAction(cacheDO, userId, "putCache_exception");
    }

    /**
     * 切换到新的装饰佩戴
     *
     * @param userId             用户 ID
     * @param decorationType     装饰类型
     * @param decorationRecordId 装饰记录 ID
     * @param existingDecoration 已存在的佩戴记录
     * @param accountDetailDO    装饰记录
     * @param benefitDO          权益信息
     * @throws BusinessException 当更新数据库失败时抛出 USER_DECORATION_SWITCH_WEAR_FAILED 异常
     */
    private void switchToNewWearingDecoration(String userId, Integer decorationType, Long decorationRecordId,
                                              UserWearingDecorationDO existingDecoration,
                                              UniversalAccountDetailDO accountDetailDO,
                                              EquityBenefitDO benefitDO) {
        // 如果已存在佩戴记录是佩戴中状态，且是同一个装饰记录，则不需要更新
        if (Objects.equals(existingDecoration.getWearStatus(), DecorationWearStatusEnum.WEARING.getCode())
                && Objects.equals(existingDecoration.getDecorationRecordId(), decorationRecordId)) {
            log.info("switchToNewWearingDecoration user is already wearing the same decoration, no need to update. userId={}" +
                            ", decorationType={}, decorationRecordId={}",
                    userId, decorationType, decorationRecordId);
            return;
        }

        // 构建更新 SQL，添加 version 递增和判断
        String decorationImage = StringUtils.defaultIfBlank(BenefitUtil.getBenefitImage(benefitDO), StringUtils.EMPTY);
        Wrapper<UserWearingDecorationDO> wrapper = Wrappers.<UserWearingDecorationDO>lambdaUpdate()
                .setSql("`version` = `version` + 1")
                .set(UserWearingDecorationDO::getDecorationRecordId, decorationRecordId)
                .set(UserWearingDecorationDO::getDecorationId, accountDetailDO.getBenefitId())
                .set(UserWearingDecorationDO::getDecorationName, benefitDO.getName())
                .set(UserWearingDecorationDO::getDecorationImage, decorationImage)
                .set(UserWearingDecorationDO::getExpireTime, accountDetailDO.getExpireTime())
                .set(UserWearingDecorationDO::getWearStatus, DecorationWearStatusEnum.WEARING.getCode())
                .set(UserWearingDecorationDO::getWearTime, DateUtil.currentDate())
                .set(UserWearingDecorationDO::getUnwearTime, null)
                .eq(UserWearingDecorationDO::getUserId, userId)
                .eq(UserWearingDecorationDO::getId, existingDecoration.getId())
                .eq(UserWearingDecorationDO::getVersion, existingDecoration.getVersion());

        // 更新数据库
        int updateResult = userWearingDecorationMapper.update(null, wrapper);
        if (updateResult != 1) {
            log.error("switchToNewWearingDecoration failed to update. userId={}, decorationType={}, decorationRecordId={}",
                    userId, decorationType, decorationRecordId);
            throw new BusinessException(ExceptionEnum.USER_DECORATION_SWITCH_WEAR_FAILED);
        }
        //update cache
        UserWearingDecorationDO cacheDO = new UserWearingDecorationDO();
        cacheDO.setUserId(userId);
        cacheDO.setDecorationType(decorationType);
        cacheDO.setWearStatus(DecorationWearStatusEnum.WEARING.getCode());
        cacheDO.setDecorationName(benefitDO.getName());
        cacheDO.setDecorationImage(decorationImage);
        cacheDO.setExpireTime(accountDetailDO.getExpireTime());
        this.cacheAction(cacheDO, userId, "updateCache_exception");
    }

    /**
     * 取消用户装饰佩戴记录
     * 将用户当前佩戴的装饰状态更新为已取消佩戴，并记录取消佩戴时间
     *
     * @param userId             用户 ID
     * @param decorationType     装饰类型
     * @param existingDecoration 当前佩戴的装饰记录，不能为空且必须是佩戴中状态
     * @throws BusinessException 当装饰记录不存在、不是佩戴中状态或更新失败时抛出
     */
    private void cancelWearingDecorationRecord(String userId, Integer decorationType,
                                               UserWearingDecorationDO existingDecoration) {
        // 校验装饰记录是否存在且是佩戴中状态
        if (existingDecoration == null || !Objects.equals(existingDecoration.getWearStatus(), DecorationWearStatusEnum.WEARING.getCode())) {
            log.error("cancelWearingDecorationRecord user has no wearing decoration, no need to unwear. userId={}, decorationType={}",
                    userId, decorationType);
            throw new BusinessException(ExceptionEnum.USER_DECORATION_NOT_WEARING);
        }

        // 构建更新 SQL，添加 version 递增和判断
        Wrapper<UserWearingDecorationDO> wrapper = Wrappers.<UserWearingDecorationDO>lambdaUpdate()
                .setSql("`version` = `version` + 1")
                .set(UserWearingDecorationDO::getWearStatus, DecorationWearStatusEnum.UNWEARING.getCode())
                .set(UserWearingDecorationDO::getUnwearTime, DateUtil.currentDate())
                .eq(UserWearingDecorationDO::getUserId, userId)
                .eq(UserWearingDecorationDO::getId, existingDecoration.getId())
                .eq(UserWearingDecorationDO::getVersion, existingDecoration.getVersion());

        // 更新数据库
        int updateResult = userWearingDecorationMapper.update(null, wrapper);
        if (updateResult != 1) {
            log.error("cancelWearingDecorationRecord failed to update. userId={}, decorationType={}, decorationId={}",
                    userId, decorationType, existingDecoration.getDecorationId());
            throw new BusinessException(ExceptionEnum.USER_DECORATION_UNWEAR_FAILED);
        }
        //remove cache
        UserWearingDecorationDO cacheDO = new UserWearingDecorationDO();
        cacheDO.setUserId(userId);
        cacheDO.setDecorationType(decorationType);
        cacheDO.setWearStatus(DecorationWearStatusEnum.UNWEARING.getCode());
        cacheDO.setDecorationName(existingDecoration.getDecorationName());
        cacheDO.setDecorationImage(existingDecoration.getDecorationImage());
        cacheDO.setExpireTime(existingDecoration.getExpireTime());
        this.cacheAction(cacheDO, userId, "removeCache_exception");
    }

    /**
     * 执行缓存操作with try cache
     *
     * @param cacheDO   缓存做
     * @param userId    用户id
     * @param errorName 错误名字
     */
    private void cacheAction(UserWearingDecorationDO cacheDO, String userId, String errorName) {
        try {
            this.userDecorationCacheDomainService.refreshCacheByUser(cacheDO);
        } catch (Exception e) {
            log.error("userDecorationActionCache failed. userId:{}", userId, e);
            CsossUtils.logEventWithSpan(MonitorConstants.USER_DECORATION_CACHE, errorName);
        }
    }

    /**
     * 构建用户佩戴装饰记录对象
     *
     * @param userId             用户 ID
     * @param decorationType     装饰类型
     * @param decorationRecordId 装饰记录 ID
     * @param accountDetailDO    装饰记录
     * @param benefitDO          权益信息
     * @return 用户佩戴装饰记录对象
     */
    private @NotNull UserWearingDecorationDO buildUserWearingDecorationDO(String userId,
                                                                          Integer decorationType,
                                                                          Long decorationRecordId,
                                                                          UniversalAccountDetailDO accountDetailDO,
                                                                          EquityBenefitDO benefitDO) {
        String decorationImage = StringUtils.defaultIfBlank(BenefitUtil.getBenefitImage(benefitDO), StringUtils.EMPTY);
        UserWearingDecorationDO wearingDecorationDO = new UserWearingDecorationDO();
        wearingDecorationDO.setUserId(userId);
        wearingDecorationDO.setDecorationType(decorationType);
        wearingDecorationDO.setDecorationRecordId(decorationRecordId);
        wearingDecorationDO.setDecorationId(accountDetailDO.getBenefitId());
        wearingDecorationDO.setDecorationName(benefitDO.getName());
        wearingDecorationDO.setDecorationImage(decorationImage);
        wearingDecorationDO.setExpireTime(accountDetailDO.getExpireTime());
        wearingDecorationDO.setWearStatus(DecorationWearStatusEnum.WEARING.getCode());
        wearingDecorationDO.setWearTime(DateUtil.currentDate());
        wearingDecorationDO.setUnwearTime(null);
        return wearingDecorationDO;
    }

    /**
     * 获取用户装饰佩戴操作锁的 key
     * 用于佩戴装饰和取消佩戴装饰操作
     *
     * @param userId         用户 ID
     * @param decorationType 装饰类型
     * @return 锁 key
     */
    private String getUserWearingDecorationOpLockKey(String userId, Integer decorationType) {
        return String.format(CacheKeyConstants.USER_WEARING_DECORATION_OP_LOCK_KEY_TEMPLATE, userId, decorationType);
    }
} 