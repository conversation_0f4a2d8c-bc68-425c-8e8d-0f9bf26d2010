package com.ddmc.equity.domain.dto.account_deduct;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/7/16 15:03
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("扣减需要操作的账户明细")
public class DeductOperateAccountDetailDTO {

    @ApiModelProperty("账户明细 id")
    private Long accountDetailId;

    @ApiModelProperty("账户 id")
    private Long accountId;

    @ApiModelProperty("可用数量")
    private Integer availableCount;

    @ApiModelProperty("扣减数量")
    private Integer deduceCount;
}
