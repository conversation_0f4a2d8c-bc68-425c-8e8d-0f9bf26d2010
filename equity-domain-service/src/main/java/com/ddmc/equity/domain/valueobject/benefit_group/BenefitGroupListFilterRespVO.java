package com.ddmc.equity.domain.valueobject.benefit_group;

import com.ddmc.equity.infra.repository.dao.EquityBenefitGroupDO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class BenefitGroupListFilterRespVO implements Serializable {
    private static final long serialVersionUID = -6244526990620158591L;

    @ApiModelProperty("权益组列表")
    @NotNull
    @Builder.Default
    private List<EquityBenefitGroupDO> list = Lists.newArrayList();

    @ApiModelProperty("权益组记录总数")
    @NotNull
    @Builder.Default
    private Long total = 0L;
}
