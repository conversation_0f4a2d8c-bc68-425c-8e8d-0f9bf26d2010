package com.ddmc.equity.domain.service.receive_limit.config;

import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.util.CsossUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

@Slf4j
@Component
public class UserReceiveLimitTableSwitchConfig {

    /**
     * 全量切换新领取频次限制表 Apollo 开关（总开关）
     */
    @Value("${user.receive.limit.full.switch.enabled:false}")
    private Boolean fullTableSwitchEnabled;

    /**
     * Apollo 配置的灰度活动
     */
    @Value("${user.receive.limit.gray.activity.ids:}")
    private Set<Long> grayActivityIdsSet = new HashSet<>();

    /**
     * 新发布活动切换新领取频次限制表 Apollo 开关（新活动切换新逻辑开关）
     */
    @Value("${user.receive.limit.new.activity.switch.enabled:false}")
    private Boolean newActivityTableSwitchEnabled;

    /**
     * Apollo 配置活动 ID（新活动条件）
     */
    @Value("${user.receive.limit.new.activity.id.threshold:0}")
    private Long newActivityIdThreshold;

    /**
     * 判断是否使用老的用户活动领取频次限制表
     * 判断逻辑优先级：全量开关 > 灰度活动 > 新活动切换
     *
     * @param activityId     活动 ID
     * @param limitDimension 限制维度
     * @param methodName     方法名
     * @return true-使用老表，false-使用新表
     */
    public boolean shouldUseOldTable(Long activityId, String limitDimension, String methodName) {
        return !shouldUseNewTable(activityId, limitDimension, methodName);
    }

    /**
     * 判断是否使用新的用户活动领取频次限制表
     * 判断逻辑优先级：全量开关 > 灰度活动 > 新活动切换
     *
     * @param activityId     活动 ID
     * @param limitDimension 限制维度
     * @param methodName     方法名
     * @return true-使用新表，false-使用老表
     */
    private boolean shouldUseNewTable(Long activityId, String limitDimension, String methodName) {
        // 参数校验：活动 ID 为空或非法时使用老表，并打点记录
        if (activityId == null || activityId <= 0) {
            log.warn("shouldUseNewTable activityId is invalid. activityId={}, limitDimension={}, methodName={}",
                    activityId, limitDimension, methodName);
            CsossUtils.logEventWithSpan(MonitorConstants.USER_RECEIVE_LIMIT_MAPPER_OLD, "activity_id_is_invalid");
            return false;
        }

        // 构建监控事件名称：限制维度_方法名
        String eventName = limitDimension + Constants.UNDERLINE + methodName;

        // 步骤 1：检查全量切换开关
        // 如果全量开关打开，所有活动都切换到新表
        if (Boolean.TRUE.equals(fullTableSwitchEnabled)) {
            CsossUtils.logEventWithSpan(MonitorConstants.USER_RECEIVE_LIMIT_MAPPER_NEW, eventName);
            return true;
        }

        // 步骤 2：检查灰度活动列表
        // 如果活动在灰度列表中，该活动切换到新表
        if (!CollectionUtils.isEmpty(grayActivityIdsSet) && grayActivityIdsSet.contains(activityId)) {
            CsossUtils.logEventWithSpan(MonitorConstants.USER_RECEIVE_LIMIT_MAPPER_NEW, eventName);
            return true;
        }

        // 步骤 3：检查新活动切换逻辑
        // 如果新活动开关打开，且活动 ID 大于阈值，则该活动切换到新表
        if (Boolean.TRUE.equals(newActivityTableSwitchEnabled) && activityId > newActivityIdThreshold) {
            CsossUtils.logEventWithSpan(MonitorConstants.USER_RECEIVE_LIMIT_MAPPER_NEW, eventName);
            return true;
        }

        // 不满足以上任何条件，使用老表
        CsossUtils.logEventWithSpan(MonitorConstants.USER_RECEIVE_LIMIT_MAPPER_OLD, eventName);
        return false;
    }
}