package com.ddmc.equity.domain.entity.account;

import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcResponseExtDTO;
import com.ddmc.equity.dto.business.account.BalanceAccountRecordDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.repository.dao.BalanceAccountDO;
import com.ddmc.equity.infra.repository.dao.BalanceAccountRecordDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.BalanceAccountInfoDTO;
import com.ddmc.equity.model.dto.SubAccountRecordDTO;
import com.ddmc.trade.balance.dto.req.AutoBalanceRechargeReq;
import com.ddmc.trade.balance.dto.resp.AutoBalanceRechargeResp;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/25 15:49
 * @description
 */
public class BalanceAccountConvertEntity extends AccountConvertEntity {

    public static BalanceAccountRecordDO createAccountRecordDO(int operateType, EquityAccountContext equityAccountContext) {
        BalanceAccountRecordDO accountRecordDO = new BalanceAccountRecordDO();
        createSubAccountRecordBaseFieldDO(accountRecordDO, operateType, equityAccountContext);
        accountRecordDO.setBalanceMoney(getRechargeBalanceMoney(equityAccountContext));
        accountRecordDO.setSendBalanceScene(equityAccountContext.getSendBalanceScene());
        return accountRecordDO;
    }

    public static BalanceAccountDO createAccountDO(EquityAccountContext equityAccountContext) {
        BalanceAccountDO accountDO = new BalanceAccountDO();
        createSubAccountBaseFieldDO(accountDO, equityAccountContext);
        accountDO.setBalanceMoney(getRechargeBalanceMoney(equityAccountContext));
        accountDO.setRpcResponseExt(convertToRpcResponseExtStr(equityAccountContext.getEquityRpcDto()));
        return accountDO;
    }

    public static BalanceAccountRecordDO createUpdateAccountRecordDO(Long accountRecordId, Long accountId, Integer status,
                                                                     EquityRpcDto equityRpcDto, Map<String, Object> ruleLimitInfoMap) {
        BalanceAccountRecordDO update = new BalanceAccountRecordDO();
        fillUpdateBaseAccountRecordDO(update, accountRecordId, accountId, status, equityRpcDto, ruleLimitInfoMap);
        if (Objects.nonNull(equityRpcDto)) {
            update.setRpcResponseExt(convertToRpcResponseExtStr(equityRpcDto));
        }
        return update;
    }

    public static SubAccountRecordDTO convertToSubAccountRecordDTO(BalanceAccountRecordDO recordDO) {
        String balanceMoneyStr = Optional.ofNullable(recordDO.getBalanceMoney())
                .map(BigDecimal::stripTrailingZeros).map(BigDecimal::toString).orElse(null);
        UniversalAccountRpcResponseExtDTO rpcResponseExtDTO = JsonUtil.parseObject(recordDO.getRpcResponseExt(),
                UniversalAccountRpcResponseExtDTO.class);
        SubAccountRecordDTO subAccountRecordDTO = new SubAccountRecordDTO();
        fillSubAccountRecordDTO(subAccountRecordDTO, recordDO);
        subAccountRecordDTO.setBenefitValue(balanceMoneyStr);
        subAccountRecordDTO.setBalanceMoney(recordDO.getBalanceMoney());
        subAccountRecordDTO.setSendBalanceScene(recordDO.getSendBalanceScene());
        subAccountRecordDTO.setThirdResNo(Objects.isNull(rpcResponseExtDTO) ? null : rpcResponseExtDTO.getBalanceCardNumber());
        subAccountRecordDTO.setRpcResponseExtDTO(rpcResponseExtDTO);
        return subAccountRecordDTO;
    }

    public static AutoBalanceRechargeReq getAutoBalanceRechargeReq(EquityAccountContext equityAccountContext) {
        AutoBalanceRechargeReq req = new AutoBalanceRechargeReq();
        req.setUid(equityAccountContext.getUid());
        req.setBizScene(equityAccountContext.getSendBalanceScene());
        req.setCustomNote(equityAccountContext.getSendBalanceDesc());
        req.setSerialNum(equityAccountContext.getSerialNumber());
        req.setRechargeMoney(UniversalAccountAmountCalEntity.getDoRpcBalanceMoneyAmount(equityAccountContext));
        return req;
    }

    public static List<AccountInfoDTO> convertToAccountInfoDTOList(List<BalanceAccountDO> accountDOList) {
        if (CollectionUtils.isEmpty(accountDOList)) {
            return Lists.newArrayList();
        }
        return accountDOList.stream().map(BalanceAccountConvertEntity::convertToAccountInfoDTO).collect(Collectors.toList());
    }

    private static AccountInfoDTO convertToAccountInfoDTO(BalanceAccountDO accountDO) {
        BalanceAccountInfoDTO balanceAccountInfoDTO = BalanceAccountInfoDTO.builder()
                .balanceMoney(accountDO.getBalanceMoney())
                .build();
        AccountInfoDTO accountInfoDTO = convertToBaseAccountInfoDTO(accountDO);
        accountInfoDTO.setBenefitType(BenefitTypeEnum.BALANCE.getId());
        accountInfoDTO.setRpcResponseExtDTO(JsonUtil.parseObject(accountDO.getRpcResponseExt(),
                UniversalAccountRpcResponseExtDTO.class));
        accountInfoDTO.setBalanceAccountInfoDTO(balanceAccountInfoDTO);
        return accountInfoDTO;
    }

    public static List<BalanceAccountRecordDTO> convertToBalanceAccountRecordDTOList(List<BalanceAccountRecordDO> recordDOList) {
        if (CollectionUtils.isEmpty(recordDOList)) {
            return null;
        }
        return recordDOList.stream().map(BalanceAccountConvertEntity::convertToBalanceAccountRecordDTO).collect(Collectors.toList());
    }

    private static BalanceAccountRecordDTO convertToBalanceAccountRecordDTO(BalanceAccountRecordDO recordDO) {
        return BalanceAccountRecordDTO.builder()
                .activityId(recordDO.getActivityId())
                .strategyId(recordDO.getStrategyId())
                .benefitGroupId(recordDO.getBenefitGroupId())
                .benefitId(recordDO.getBenefitId())
                .accountId(recordDO.getAccountId())
                .userId(recordDO.getUserId())
                .operateType(recordDO.getOperateType())
                .status(recordDO.getStatus())
                .appId(recordDO.getAppId())
                .pageId(recordDO.getPageId())
                .source(recordDO.getSource())
                .reqNo(recordDO.getReqNo())
                .createTime(recordDO.getCreateTime())
                .balanceMoney(recordDO.getBalanceMoney())
                .sendBalanceScene(recordDO.getSendBalanceScene())
                .build();
    }

    public static BigDecimal getRechargeBalanceMoney(EquityAccountContext equityAccountContext) {
        BigDecimal balanceMoney = equityAccountContext.getBalanceMoney();
        return Objects.nonNull(balanceMoney) ? balanceMoney : NumberUtils.convertToBigDecimal(equityAccountContext.getEquityValue());
    }

    public static UniversalAccountRpcResponseExtDTO convertToAccountRpcResponseExtDTO(AutoBalanceRechargeResp balanceRechargeResp) {
        return UniversalAccountRpcResponseExtDTO.builder()
                .balanceCardNumber(balanceRechargeResp.getCard_number())
                .benefitExpireTime(balanceRechargeResp.getCardUseExpireTime())
                .build();
    }

    private static String convertToRpcResponseExtStr(EquityRpcDto equityRpcDTO) {
        if (Objects.isNull(equityRpcDTO) || Objects.isNull(equityRpcDTO.getRpcResponseExtDTO())) {
            return null;
        }
        return JsonUtil.toString(equityRpcDTO.getRpcResponseExtDTO());
    }
}
