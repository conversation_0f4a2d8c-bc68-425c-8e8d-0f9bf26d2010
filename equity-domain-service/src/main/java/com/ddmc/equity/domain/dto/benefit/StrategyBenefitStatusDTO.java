package com.ddmc.equity.domain.dto.benefit;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class StrategyBenefitStatusDTO {
    /**
     * 活动ID
     */
    @ApiModelProperty("活动ID")
    private Long activityId;

    /**
     * 当前状态开启状态
     */
    @ApiModelProperty("开启状态，true开启，false关闭")
    private Boolean enable;
    /**
     * 策略ID
     */
    @ApiModelProperty("策略ID，必填")
    private Long strategyId;
    /**
     * 权益ID
     */
    @ApiModelProperty("权益ID，必填")
    private Long benefitId;
}
