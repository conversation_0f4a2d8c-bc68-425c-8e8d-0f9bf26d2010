package com.ddmc.equity.domain.service.equityActivityRule;

import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.infra.repository.dao.EquityActivityRuleDO;

import java.util.List;

public interface EquityActivityRuleService {

    List<EquityActivityRuleDO> queryEquityActivityRuleByActivityId(Long activityId);

    /**
     * 通过活动 ids 查询活动规则列表
     *
     * @param activityIds 活动 ids
     * @return 活动规则列表
     */
    List<EquityActivityRuleDO> queryByActivityIds(List<Long> activityIds);

    int addEquityActivityRuleDOList(List<EquityActivityRuleDO> equityActivityRuleDOList);


    int updateEquityActivityRuleDOList(List<EquityActivityRuleDO> equityActivityRuleDOList);


    /**
     * 查询指定规则
     *
     * @param activityId
     * @param type
     * @return
     */
    EquityActivityRuleDO getActivityRule(Long activityId, RuleTypeEnum type);

    List<EquityActivityRuleDO> getActivityRule(Long activityId, List<RuleTypeEnum> types);


    void batchDelete(List<EquityActivityRuleDO> equityActivityRuleDOList);

}
