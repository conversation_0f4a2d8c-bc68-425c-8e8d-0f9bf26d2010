package com.ddmc.equity.domain.dto.benefit_stock;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.enums.ExternalTypeEnum;
import com.ddmc.equity.enums.SceneCodeEnum;
import com.ddmc.equity.enums.StrategyTypeEnum;
import com.ddmc.ugc.commons.util.DateUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/1/24 14:43
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class LowStockAlarmContextDTO {

    /**
     * @see com.ddmc.equity.enums.SceneCodeEnum
     */
    @ApiModelProperty(value = "活动场景 code")
    private String sceneCode;

    @ApiModelProperty(value = "创建人 ID")
    private String createdById;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人 ID")
    private String updatedById;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "活动 id")
    private Long activityId;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "开始时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    @ApiModelProperty(value = "结束时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * @see com.ddmc.equity.enums.ExternalTypeEnum
     */
    @ApiModelProperty(value = "外部关联类型。1-玩法；")
    private Integer externalType;

    @ApiModelProperty(value = "外部关联 id（一般是外部关联方的活动 id，如 promo.activity.activityId）")
    private String externalId;

    @ApiModelProperty(value = "策略 id")
    private Long strategyId;

    @ApiModelProperty(value = "策略名称")
    private String strategyName;

    @ApiModelProperty(value = "策略外部关联 id。如果是来源玩法的活动，则为活动 prizeId；如果是膨胀券活动，则为母券券模板 id；")
    private String strategyExternalId;

    /**
     * @see com.ddmc.equity.enums.StrategyTypeEnum
     */
    @ApiModelProperty("策略类型。0-普通策略；1-兜底策略；2-黑灰产策略；")
    private Integer strategyType;

    @ApiModelProperty(value = "权益组 id")
    private Long benefitGroupId;

    @ApiModelProperty(value = "权益 id")
    private Long benefitId;

    @ApiModelProperty(value = "权益名称")
    private String benefitName;

    /**
     * @see com.ddmc.equity.enums.BenefitTypeEnum
     */
    @ApiModelProperty("权益类型")
    private Integer benefitType;

    @ApiModelProperty("权益值")
    private String benefitValue;

    /**
     * @see com.ddmc.equity.common.enums.PlanDateTypeEnum
     */
    @ApiModelProperty(value = "计划库存类型")
    private Integer planDateType;

    @ApiModelProperty(value = "计划库存时间。格式：yyyyMMdd")
    private String planDateStr;

    @ApiModelProperty(value = "非计划总库存缓存 key")
    private String stockCacheKey;

    @ApiModelProperty(value = "非计划剩余库存缓存 key")
    private String balanceStockCacheKey;

    @ApiModelProperty(value = "计划总库存缓存 key")
    private String planStockCacheKey;

    @ApiModelProperty(value = "计划剩余库存缓存 key")
    private String planBalanceStockCacheKey;

    @ApiModelProperty(value = "非计划总库存")
    private Long stock;

    @ApiModelProperty(value = "非计划剩余库存")
    private Long balanceStock;

    @ApiModelProperty(value = "计划总库存")
    private Long planStock;

    @ApiModelProperty(value = "计划剩余库存")
    private Long planBalanceStock;

    @ApiModelProperty(value = "库存不足提醒规则配置")
    private LowStockAlarmConditionDTO.LowStockAlarmRuleDTO lowStockAlarmRule;

    /**
     * 是否有非计划库存
     */
    public boolean isHasStock() {
        return StringUtils.isNoneBlank(this.stockCacheKey, this.balanceStockCacheKey);
    }

    /**
     * 是否有计划库存
     */
    public boolean isHasPlanStock() {
        return StringUtils.isNoneBlank(this.planStockCacheKey, this.planBalanceStockCacheKey);
    }

    public String buildDisplaySceneCode() {
        return Optional.ofNullable(SceneCodeEnum.getByCode(this.sceneCode))
                .map(SceneCodeEnum::getDesc)
                .orElse("未知活动类型");
    }

    public String buildCreatorName() {
        return StringUtils.defaultIfBlank(this.createdBy, "未知创建人");
    }

    public String buildDisplayActivityId() {
        return Objects.equals(this.externalType, ExternalTypeEnum.GAME.getType()) ?
                this.externalId : String.valueOf(this.activityId);
    }

    public String buildExternal() {
        ExternalTypeEnum externalTypeEnum = ExternalTypeEnum.getByType(this.externalType);
        return Objects.isNull(externalTypeEnum) || externalTypeEnum == ExternalTypeEnum.INNER ? "null" :
                this.externalType + "_" + this.externalId;
    }

    public String buildStrategyExternal() {
        return this.strategyExternalId;
    }

    public String buildStartDateStr() {
        return Objects.nonNull(this.startDate) ? DateUtils.dateToStr(this.startDate) : StringUtils.EMPTY;
    }

    public String buildEndDateStr() {
        return Objects.nonNull(this.endDate) ? DateUtils.dateToStr(this.endDate) : "永久";
    }

    public String buildDisplayBenefitType() {
        return Optional.ofNullable(BenefitTypeEnum.getById(this.benefitType))
                .map(BenefitTypeEnum::getName)
                .orElse("未知奖励类型");
    }

    public String buildDisplayBenefitInfo() {
        return this.benefitName;
    }

    public String buildStrategyTypeContentStr() {
        StrategyTypeEnum strategyTypeEnum = StrategyTypeEnum.getByCode(this.strategyType);
        return Objects.isNull(strategyTypeEnum) || strategyTypeEnum == StrategyTypeEnum.NORMAL ?
                StringUtils.EMPTY : String.format("> 策略类型：<font color=\"warning\">%s</font>\n", strategyTypeEnum.getDesc());
    }

    public String buildStockContentStr() {
        return !this.isHasStock() ? StringUtils.EMPTY :
                String.format("> **库存上限：** <font color=\"comment\">%d</font>\n" +
                                "> **当前剩余库存：** <font color=\"warning\">%d</font>\n",
                        this.getStock(), this.getBalanceStock());
    }

    public String buildPlanStockContentStr() {
        return !this.isHasPlanStock() ? StringUtils.EMPTY :
                String.format("> **每日库存上限：** <font color=\"comment\">%d</font>\n" +
                                "> **当前每日剩余库存：** <font color=\"warning\">%d</font>\n",
                        this.getPlanStock(), this.getPlanBalanceStock());
    }

    public String buildWorkbenchPlanStockContentStr() {
        return !this.isHasPlanStock() ? StringUtils.EMPTY :
                String.format("，当前每日剩余库存为【%d】，每日库存上限为 【%d】",
                        this.getPlanBalanceStock(), this.getPlanStock());
    }

    public String toJsonStr() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sceneCode", this.sceneCode);
        jsonObject.put("activityId", this.activityId);
        jsonObject.put("externalType", this.externalType);
        jsonObject.put("externalId", this.externalId);
        jsonObject.put("strategyId", this.strategyId);
        jsonObject.put("benefitId", this.benefitId);
        return jsonObject.toJSONString();
    }
}
