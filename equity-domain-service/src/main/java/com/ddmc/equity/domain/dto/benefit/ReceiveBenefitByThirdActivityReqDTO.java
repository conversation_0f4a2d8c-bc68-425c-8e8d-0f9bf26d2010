package com.ddmc.equity.domain.dto.benefit;

import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.dto.customer.BaseRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;
import org.jetbrains.annotations.NotNull;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ReceiveBenefitByThirdActivityReqDTO implements Serializable {
    private static final long serialVersionUID = 4283577259697089928L;

    /**
     * 申请流水号
     */
    @ApiModelProperty(value = "流水号(幂等控制使用)", required = true)
    @Length(max = 64, message = "流水号长度控制在64位以内")
    @NotNull
    @NotBlank
    private String reqNo;

    /**
     * 下面这些参数能传尽量传，userId塞里头
     */
    @NotNull
    @ApiModelProperty("基础参数能传尽量传，userId塞里头")
    private BaseRequestDTO baseRequestDTO;

    @NotNull
    @NotBlank
    private String ticketTemplateId;

    @NotNull
    @Min(0)
    private Integer externalType;

    @NotNull
    @NotBlank
    private String activityExternalId;

    @NotNull
    @NotBlank
    private String strategyExternalId;

    @NotNull
    @NotBlank
    private String appId;

    @NotNull
    @NotBlank
    private String sceneCode;

    @NotNull
    @ApiModelProperty(value = "发券时券服务需要传的场景ID")
    private Integer sendTicketScene;


    @NotNull
    @ApiModelProperty(value = "是否用这个流水号替换发券的流水号，而不是系统生成，这个参数是因为文蓝的同步脚本场景用 0: 不使用 1: 使用")
    @Builder.Default
    private Integer isUseSerialNumberSave = CommonEnum.INTEGER_BOOL.NO.getCode();


}
