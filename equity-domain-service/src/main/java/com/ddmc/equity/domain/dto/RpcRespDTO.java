package com.ddmc.equity.domain.dto;

import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.userpoint.api.response.ResultVo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/7/17 19:35
 * @description
 */
@Getter
public class RpcRespDTO<T> {

    /**
     * 结果状态
     */
    private final RpcStatus status;
    /**
     * 返回码
     */
    private final String code;
    /**
     * 返回信息
     */
    private final String message;
    /**
     * 返回的数据
     */
    private final T data;

    @AllArgsConstructor
    @Getter
    public enum RpcStatus {

        SUCCESS("成功"),
        FAILURE("失败"),
        EXCEPTION("异常"),
        ;

        private final String message;
    }

    private RpcRespDTO(RpcStatus status, String code, String message, T data) {
        this.status = status;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static <T> RpcRespDTO<T> success(T data) {
        return new RpcRespDTO<>(RpcStatus.SUCCESS, null, null, data);
    }

    private static <T> RpcRespDTO<T> failure(String code, String message) {
        return new RpcRespDTO<>(RpcStatus.FAILURE, code, message, null);
    }

    public static <T> RpcRespDTO<T> paramsErrorFailure(String message) {
        return RpcRespDTO.failure(ExceptionEnum.PARAMS_ERROR.getCode(), message);
    }

    public static <T> RpcRespDTO<T> failure(ResponseBaseVo<T> rpcResp) {
        String code = rpcResp == null ? StringUtils.EMPTY : String.valueOf(rpcResp.getCode());
        String message = rpcResp == null ? "接口请求失败" : rpcResp.getMsg();
        return RpcRespDTO.failure(code, message);
    }

    public static <T> RpcRespDTO<T> failure(ResultVo<T> rpcResp) {
        String code = rpcResp == null ? StringUtils.EMPTY : String.valueOf(rpcResp.getCode());
        String message = rpcResp == null ? "接口请求失败" : rpcResp.getMsg();
        return RpcRespDTO.failure(code, message);
    }

    public static <T> RpcRespDTO<T> exception(String message) {
        return new RpcRespDTO<>(RpcStatus.EXCEPTION, null, message, null);
    }
}
