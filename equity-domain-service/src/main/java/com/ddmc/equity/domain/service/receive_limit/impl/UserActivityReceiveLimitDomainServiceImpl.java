package com.ddmc.equity.domain.service.receive_limit.impl;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.domain.dto.receive_limit.UserActivityReceiveLimitCacheDTO;
import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.domain.entity.receive_limit.ReceiveLimitConvertEntity;
import com.ddmc.equity.domain.service.receive_limit.UserActivityReceiveLimitDomainService;
import com.ddmc.equity.domain.service.receive_limit.UserActivityReceiveLimitMapperProxy;
import com.ddmc.equity.domain.valueobject.receive_limit.OperateActivityReceiveLimitResp;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.repository.dao.UserActivityReceiveLimitDO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/29 17:19
 * @description
 */
@Slf4j
@Service
public class UserActivityReceiveLimitDomainServiceImpl implements UserActivityReceiveLimitDomainService {

    @Resource
    private UserActivityReceiveLimitMapperProxy userActivityReceiveLimitMapperProxy;
    @Autowired
    private RedisCache redisCache;

    @Override
    public boolean insertReceiveLimitDO(@NotNull UserActivityReceiveLimitDO receiveLimitDO) {
        return userActivityReceiveLimitMapperProxy.insert(receiveLimitDO) == 1;
    }

    @Override
    public UserActivityReceiveLimitDO queryByUserIdAndDate(@NotNull String userId, @NotNull Long activityId, @NotNull Date date) {
        return userActivityReceiveLimitMapperProxy.queryByDate(userId, activityId, date);
    }

    @Override
    public UserActivityReceiveLimitDO queryByReceiveLimitId(@NotNull String userId, @NotNull Long activityId, @NotNull Long id) {
        return userActivityReceiveLimitMapperProxy.queryByReceiveLimitId(id, userId, activityId);
    }

    @Override
    public OperateActivityReceiveLimitResp deductReceiveLimit(@NotNull String userId, @NotNull Long activityId,
                                                              @NotNull ReceiveLimitRuleDTO receiveLimitRuleDTO) {
        Date now = new Date();
        UserActivityReceiveLimitDO receiveLimitDO = queryByUserIdAndDate(userId, activityId, now);
        // 如果 DB 没有，则初始化一条到 BD 并扣减
        if (Objects.isNull(receiveLimitDO)) {
            return initDbAndDeductReceiveLimit(userId, activityId, receiveLimitRuleDTO);
        }

        int deductResult = userActivityReceiveLimitMapperProxy.deduct(receiveLimitDO.getId(), userId, activityId,
                1L, receiveLimitDO.getVersion());
        if (deductResult > 0) {
            receiveLimitDO.setReceiveCount(receiveLimitDO.getReceiveCount() + 1L);
            resetReceiveLimitCache(receiveLimitDO);
            return OperateActivityReceiveLimitResp.builder()
                    .operateResult(Boolean.TRUE)
                    .receiveLimitId(receiveLimitDO.getId())
                    .build();
        }
        log.warn("deductReceiveLimit deduct failure. userId={}, activityId={}, receiveLimitDO={}, deductResult={}",
                userId, activityId, JSON.toJSONString(receiveLimitDO), deductResult);
        // 扣减活动频次失败，需要重置 redis 缓存值
        resetReceiveLimitCache(receiveLimitDO);
        return OperateActivityReceiveLimitResp.builder().build();
    }

    @Override
    public OperateActivityReceiveLimitResp releaseReceiveLimit(@NotNull String userId, @NotNull Long activityId,
                                                               @NotNull Long receiveLimitId) {
        UserActivityReceiveLimitDO receiveLimitDO = queryByReceiveLimitId(userId, activityId, receiveLimitId);
        if (Objects.isNull(receiveLimitDO)) {
            log.warn("releaseReceiveLimit receiveLimitDO is null. activityId={}, receiveLimitId={}", activityId, receiveLimitId);
            return OperateActivityReceiveLimitResp.builder().build();
        }

        int releaseResult = userActivityReceiveLimitMapperProxy.release(receiveLimitDO.getId(), userId, activityId,
                1L, receiveLimitDO.getVersion());
        if (releaseResult > 0) {
            receiveLimitDO.setReceiveCount(receiveLimitDO.getReceiveCount() - 1L);
            resetReceiveLimitCache(receiveLimitDO);
            return OperateActivityReceiveLimitResp.builder()
                    .operateResult(Boolean.TRUE)
                    .receiveLimitId(receiveLimitDO.getId())
                    .build();
        }
        log.warn("releaseReceiveLimit release failure. activityId={}, receiveLimitDO={}, releaseResult={}", activityId,
                JSON.toJSONString(receiveLimitDO), releaseResult);
        return OperateActivityReceiveLimitResp.builder().build();
    }

    @Override
    public Map<Long /* activityId */, UserActivityReceiveLimitCacheDTO> getActivityReceiveLimitCacheMul(String userId, List<Long> activityIds) {
        if (StringUtils.isBlank(userId) || CollectionUtils.isEmpty(activityIds)) {
            return null;
        }

        Map<String /* cacheKey */, Long /* activityId */> cacheKeyMap = Maps.newHashMap();
        activityIds.forEach(e -> cacheKeyMap.put(getCacheKey(userId, e), e));
        Map<String /* cacheKey */, String /* cacheVal */> cacheResult = redisCache.getMulStrValue(cacheKeyMap.keySet());

        Date nowDate = new Date();
        Map<Long /* activityId */, UserActivityReceiveLimitCacheDTO> receiveLimitMap = Maps.newHashMap();
        cacheResult.forEach((k, v) -> {
            Long activityId = cacheKeyMap.get(k);
            if (Objects.isNull(activityId)) {
                log.warn("getActivityReceiveLimitCacheMul cache activityId is null. cacheKey={}", k);
                return;
            }
            UserActivityReceiveLimitCacheDTO receiveLimitCacheDTO = ReceiveLimitConvertEntity.convertToReceiveLimitCacheDTO(v);
            if (Objects.isNull(receiveLimitCacheDTO) || nowDate.after(receiveLimitCacheDTO.getEndTime())) {
                log.warn("getActivityReceiveLimitCacheMul receiveLimitCacheDTO isNull or isEnd. activityId={}, cacheKey={}, cacheVal={}",
                        activityId, k, v);
                return;
            }
            receiveLimitMap.put(activityId, receiveLimitCacheDTO);
        });
        return receiveLimitMap;
    }

    private OperateActivityReceiveLimitResp initDbAndDeductReceiveLimit(@NotNull String userId, @NotNull Long activityId,
                                                                        @NotNull ReceiveLimitRuleDTO receiveLimitRuleDTO) {
        UserActivityReceiveLimitDO receiveLimitDO = ReceiveLimitConvertEntity.convertToUserActivityReceiveLimitDO(userId,
                activityId, receiveLimitRuleDTO);
        if (Objects.isNull(receiveLimitDO)) {
            log.warn("initDbAndDeductReceiveLimit receiveLimitDO is null. userId={}, activityId={}, receiveLimitRuleDTO={}",
                    userId, activityId, JSON.toJSONString(receiveLimitRuleDTO));
            return OperateActivityReceiveLimitResp.builder().build();
        }
        boolean saveResult = insertReceiveLimitDO(receiveLimitDO);
        log.info("initDbAndDeductReceiveLimit insertReceiveLimitDO end. userId={}, activityId={}, receiveLimitDO={}, saveResult={}",
                userId, activityId, JSON.toJSONString(receiveLimitDO), saveResult);
        if (saveResult) {
            resetReceiveLimitCache(receiveLimitDO);
            return OperateActivityReceiveLimitResp.builder()
                    .operateResult(Boolean.TRUE)
                    .receiveLimitId(receiveLimitDO.getId())
                    .build();
        }
        return OperateActivityReceiveLimitResp.builder().build();
    }

    private UserActivityReceiveLimitCacheDTO getReceiveLimitCache(@NotNull String userId, @NotNull Long activityId) {
        String cacheKey = getCacheKey(userId, activityId);
        String cacheValue = redisCache.getValueWithMaster(cacheKey);
        return ReceiveLimitConvertEntity.convertToReceiveLimitCacheDTO(cacheValue);
    }

    private void resetReceiveLimitCache(UserActivityReceiveLimitDO userActivityReceiveLimitDO) {
        Date now = new Date();
        Date endTime = userActivityReceiveLimitDO.getEndTime();
        if (now.after(endTime)) {
            log.warn("resetReceiveLimitCache currTime gt endTime. userActivityReceiveLimitDO={}",
                    JSON.toJSONString(userActivityReceiveLimitDO));
            return;
        }

        UserActivityReceiveLimitCacheDTO receiveLimitCacheDTO = UserActivityReceiveLimitCacheDTO.builder()
                .receiveLimitId(userActivityReceiveLimitDO.getId())
                .limitCount(userActivityReceiveLimitDO.getLimitCount())
                .receiveCount(userActivityReceiveLimitDO.getReceiveCount())
                .endTime(endTime)
                .build();
        // todo liuruiyu 终身限制的频次 redis 过期时间设置不要超过 2 years
        String cacheKey = getCacheKey(userActivityReceiveLimitDO.getUserId(), userActivityReceiveLimitDO.getActivityId());
        redisCache.setValue(cacheKey, JSON.toJSONString(receiveLimitCacheDTO), DateUtil.buildExpireSec(now, endTime), TimeUnit.SECONDS);
        log.info("resetReceiveLimitCache end. cacheKey={}, receiveLimitCache={}", cacheKey, JSON.toJSONString(receiveLimitCacheDTO));
    }

    private @NotNull String getCacheKey(@NotNull String userId, @NotNull Long activityId) {
        return String.format(CacheKeyConstants.USER_ACTIVITY_RECEIVE_LIMIT_KEY, userId, activityId);
    }
}
