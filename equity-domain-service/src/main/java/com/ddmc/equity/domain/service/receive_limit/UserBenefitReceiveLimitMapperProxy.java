package com.ddmc.equity.domain.service.receive_limit;

import com.ddmc.equity.infra.repository.dao.UserReceiveBenefitLimitDO;

import java.util.Date;

/**
 * 用户权益领取限制表 Mapper 代理接口
 * 用于新老表切换过程中的数据操作代理
 */
public interface UserBenefitReceiveLimitMapperProxy {

    /**
     * 查询单条记录
     *
     * @param userId     用户 ID
     * @param activityId 活动 ID
     * @param strategyId 策略 ID
     * @param benefitId  权益 ID
     * @param date       查询时间
     * @param id         记录 ID
     * @return 领取限制记录
     */
    UserReceiveBenefitLimitDO queryByDate(String userId, Long activityId, Long strategyId, Long benefitId,
                                          Date date, Long id);

    /**
     * 插入记录
     *
     * @param receiveLimitDO 领取限制记录
     * @return 是否成功
     */
    int insert(UserReceiveBenefitLimitDO receiveLimitDO);

    /**
     * 冻结权益限制
     *
     * @param id         记录 ID
     * @param userId     用户 ID
     * @param activityId 活动 ID
     * @param strategyId 策略 ID
     * @param benefitId  权益 ID
     * @param preVersion 版本号
     * @return 影响行数
     */
    Integer freezeBenefitLimit(Long id, String userId, Long activityId, Long strategyId, Long benefitId,
                               Long preVersion);

    /**
     * 扣减权益限制
     *
     * @param id         记录 ID
     * @param userId     用户 ID
     * @param activityId 活动 ID
     * @param strategyId 策略 ID
     * @param benefitId  权益 ID
     * @param preVersion 版本号
     * @return 影响行数
     */
    Integer deductBenefitLimit(Long id, String userId, Long activityId, Long strategyId, Long benefitId,
                               Long preVersion);

    /**
     * 释放权益限制
     *
     * @param id         记录 ID
     * @param userId     用户 ID
     * @param activityId 活动 ID
     * @param strategyId 策略 ID
     * @param benefitId  权益 ID
     * @param preVersion 版本号
     * @return 影响行数
     */
    Integer releaseBenefitLimit(Long id, String userId, Long activityId, Long strategyId, Long benefitId,
                                Long preVersion);
}