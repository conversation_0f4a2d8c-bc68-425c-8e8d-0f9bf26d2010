package com.ddmc.equity.domain.valueobject.benefit_limit;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.Nullable;

import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FreezeBenefitLimitResp implements Serializable {
    private static final long serialVersionUID = 5012965853686068918L;

    @ApiModelProperty("冻结结果，true:可以进行后续操作 ｜ false: 频次冻结不通过")
    @Builder.Default
    private Boolean freezeResult = Boolean.FALSE;

    @ApiModelProperty("频次流水号，目前讨论由总次数+已领取次数+回退次数字符串拼接而成")
    @Builder.Default
    private String serialNumber = Strings.EMPTY;

    /**
     * 20230216 @卢永党 要求尽量返回freezeId
     */
    @ApiModelProperty("冻结数据库成功更新后的ID，失败为null")
    @Nullable
    private Long freezeId;

    @ApiModelProperty("冻结前的冻结数量")
    private Long freezeCountBeforeFreeze;
}
