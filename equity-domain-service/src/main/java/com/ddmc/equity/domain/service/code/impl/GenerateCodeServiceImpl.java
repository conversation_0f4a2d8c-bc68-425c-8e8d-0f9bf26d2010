package com.ddmc.equity.domain.service.code.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.GenerateCodeTypeEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.domain.entity.code.DrawCodeGenerateEntity;
import com.ddmc.equity.domain.entity.code.GenerateCodeConvertEntity;
import com.ddmc.equity.domain.service.code.GenerateCodeService;
import com.ddmc.equity.infra.repository.dao.GenerateCodeDO;
import com.ddmc.equity.infra.repository.dao.mapper.GenerateCodeMapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/6 16:20
 * @description
 */
@Slf4j
@Service
public class GenerateCodeServiceImpl extends ServiceImpl<GenerateCodeMapper, GenerateCodeDO> implements GenerateCodeService {

    /**
     * 重试次数
     */
    private final static int RETRY_COUNT = 5;

    /**
     * 使用 v1 方式生成抽签码
     */
    @Value("${equity.generate.draw.codes.way.v1:true}")
    private Boolean generateDrawCodesWayV1;

    @Override
    public List<String> batchGenerateAndInsertDrawCode(Integer count) {
        String codeType = GenerateCodeTypeEnum.DRAW_CODE.getType();
        int times = 0;
        do {
            List<String> drawCodes = batchGenerateAndInsertDrawCode(codeType, count);
            if (CollectionUtils.isNotEmpty(drawCodes)) {
                return drawCodes;
            }
            times++;
        } while (times < RETRY_COUNT);

        log.warn("batchGenerateAndInsertDrawCode failure. times={}, retryCount={}", times, RETRY_COUNT);
        Map<String, Object> eventData = Maps.newHashMap();
        eventData.put("times", times);
        eventData.put("retryCount", RETRY_COUNT);
        CsossUtils.logEvent(MonitorConstants.BATCH_GENERATE_AND_INSERT_CODE_FAILURE, "draw_code", MonitorConstants.SUCCESS, eventData);
        throw AdminExceptionBuilder.build(ExceptionEnum.GENERATE_AND_INSERT_CODE_FAILURE);
    }

    private List<String> batchGenerateAndInsertDrawCode(String codeType, Integer count) {
        List<String> drawCodes = generateDrawCodes(count);
        try {
            List<GenerateCodeDO> generateCodeDOList = GenerateCodeConvertEntity.createGenerateCodeDOList(codeType, drawCodes);
            this.saveBatch(generateCodeDOList);
            return drawCodes;
        } catch (Exception e) {
            log.error("batchGenerateAndInsertDrawCode exception. drawCodes={}", JSON.toJSONString(drawCodes), e);
            return null;
        }
    }

    private List<String> generateDrawCodes(Integer count) {
        if (Boolean.TRUE.equals(generateDrawCodesWayV1)) {
            CsossUtils.logEvent(MonitorConstants.GENERATE_DRAW_CODES_WAY, "batchGenerateDrawCodesV1");
            return DrawCodeGenerateEntity.batchGenerateDrawCodesV1(count);
        }
        CsossUtils.logEvent(MonitorConstants.GENERATE_DRAW_CODES_WAY, "batchGenerateDrawCodeOld");
        return DrawCodeGenerateEntity.batchGenerateDrawCode(count);
    }
}
