package com.ddmc.equity.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ReceiveSceneBenefitDTO {

    /**
     * 场景 code
     */
    private String sceneCode;
    /**
     * 领取成功的权益集合
     */
    @NotNull
    @Builder.Default
    private List<ReceiveBenefitDTO> successList = Lists.newArrayList();
    /**
     * 领取失败的权益集合
     */
    @NotNull
    @Builder.Default
    private List<ReceiveBenefitDTO> failList = Lists.newArrayList();
    /**
     * 领取处理中的权益集合
     */
    @NotNull
    @Builder.Default
    private List<ReceiveBenefitDTO> processList = Lists.newArrayList();
}
