package com.ddmc.equity.domain.converter.benefit;

import com.ddmc.equity.dto.customer.ReceiveSceneBenefitReqDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.control.DeepClone;
import org.mapstruct.factory.Mappers;

@Mapper(mappingControl = DeepClone.class, unmappedTargetPolicy = ReportingPolicy.IGNORE,
        imports = {StringUtils.class, Lists.class})public interface BenefitDeepCopyConverter {
    BenefitDeepCopyConverter INSTANCE = Mappers.getMapper(BenefitDeepCopyConverter.class);

    ReceiveSceneBenefitReqDTO deepCopy(ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO);
}
