package com.ddmc.equity.domain.service.draw_code.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.SubAccountStatusEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.service.draw_code.DrawCodeAccountService;
import com.ddmc.equity.enums.DrawCodeSourceEnum;
import com.ddmc.equity.infra.repository.dao.DrawCodeAccountDO;
import com.ddmc.equity.infra.repository.dao.mapper.DrawCodeAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/5/30 16:12
 * @description
 */
@Slf4j
@Service
public class DrawCodeAccountServiceImpl extends ServiceImpl<DrawCodeAccountMapper, DrawCodeAccountDO> implements DrawCodeAccountService {

    @Override
    public void batchInsertAccountDO(List<DrawCodeAccountDO> accountDOList) {
        Assert.mustTrue(CollectionUtils.isNotEmpty(accountDOList), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户不能为空");
        accountDOList.forEach(e -> {
            Assert.mustTrue(StringUtils.isNotBlank(e.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户 userId 不能为空");
            Assert.mustTrue(DrawCodeSourceEnum.isContain(e.getSource()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户来源异常");
            Assert.mustTrue(SubAccountStatusEnum.isContain(e.getStatus()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户状态异常");
            Assert.mustTrue(StringUtils.isNotBlank(e.getLastReqNo()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户 lastReqNo 不能为空");
        });
        this.saveBatch(accountDOList);
    }

    @Override
    public boolean updateAccountsStatus(String userId, List<Long> accountIds, Integer sourceStatus, Integer targetStatus, String reqNo) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户 userId 不能为空");
        Assert.mustTrue(CollectionUtils.isNotEmpty(accountIds), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户 accountIds 不能为空");
        Assert.mustTrue(SubAccountStatusEnum.isContain(sourceStatus), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户原始状态异常");
        Assert.mustTrue(SubAccountStatusEnum.isContain(targetStatus), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户目标状态异常");

        DrawCodeAccountDO update = new DrawCodeAccountDO();
        update.setStatus(targetStatus);
        update.setLastReqNo(reqNo);
        return this.update(update, Wrappers.<DrawCodeAccountDO>lambdaUpdate()
                .eq(DrawCodeAccountDO::getUserId, userId)
                .in(DrawCodeAccountDO::getId, accountIds)
                .eq(DrawCodeAccountDO::getStatus, sourceStatus));
    }

    @Override
    public DrawCodeAccountDO queryAccountByUserIdAndId(String userId, Long accountId) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户 userId 不能为空");
        Assert.notNull(accountId, ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户 accountId 不能为空");

        Wrapper<DrawCodeAccountDO> wrapper = Wrappers.<DrawCodeAccountDO>lambdaQuery()
                .eq(DrawCodeAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(DrawCodeAccountDO::getUserId, userId)
                .eq(DrawCodeAccountDO::getId, accountId);
        return this.getOne(wrapper);
    }

    @Override
    public List<DrawCodeAccountDO> queryAccountsByUserIdAndDrawCodes(String userId, List<String> drawCodes) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询抽签码权益子账户 userId 不能为空");

        Wrapper<DrawCodeAccountDO> wrapper = Wrappers.<DrawCodeAccountDO>lambdaQuery()
                .eq(DrawCodeAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(DrawCodeAccountDO::getUserId, userId)
                .in(CollectionUtils.isNotEmpty(drawCodes), DrawCodeAccountDO::getDrawCode, drawCodes);
        return this.list(wrapper);
    }

    @Override
    public List<DrawCodeAccountDO> queryAccountsByUserIdAndReqNo(String userId, String reqNo) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询抽签码权益子账户 userId 不能为空");
        Assert.mustTrue(StringUtils.isNotBlank(reqNo), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询抽签码权益子账户业务流水号不能为空");

        Wrapper<DrawCodeAccountDO> wrapper = Wrappers.<DrawCodeAccountDO>lambdaQuery()
                .eq(DrawCodeAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(DrawCodeAccountDO::getUserId, userId)
                .eq(DrawCodeAccountDO::getLastReqNo, reqNo);
        return this.list(wrapper);
    }

    @Override
    public List<DrawCodeAccountDO> queryAccountsByUserId(String userId, Long benefitId, List<Integer> sources,
                                                         List<Integer> statuses, Date startDate, Date endDate) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询抽签码权益子账户 userId 不能为空");

        Wrapper<DrawCodeAccountDO> wrapper = Wrappers.<DrawCodeAccountDO>lambdaQuery()
                .eq(DrawCodeAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(DrawCodeAccountDO::getUserId, userId)
                .eq(Objects.nonNull(benefitId), DrawCodeAccountDO::getBenefitId, benefitId)
                .in(CollectionUtils.isNotEmpty(sources), DrawCodeAccountDO::getSource, sources)
                .in(CollectionUtils.isNotEmpty(statuses), DrawCodeAccountDO::getStatus, statuses)
                .ge(Objects.nonNull(startDate), DrawCodeAccountDO::getCreateTime, startDate)
                .le(Objects.nonNull(endDate), DrawCodeAccountDO::getCreateTime, endDate)
                .orderByAsc(DrawCodeAccountDO::getCreateTime);
        return this.list(wrapper);
    }
}
