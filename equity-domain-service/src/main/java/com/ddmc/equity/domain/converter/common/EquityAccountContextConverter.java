package com.ddmc.equity.domain.converter.common;

import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.config.MapstructBaseMapperConfig;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessContext;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/7/1 17:27
 * @description 场景处理上下文转权益账户上下文转换器
 */
@Mapper(config = MapstructBaseMapperConfig.class)
public interface EquityAccountContextConverter {

    EquityAccountContextConverter INSTANCE = Mappers.getMapper(EquityAccountContextConverter.class);

    /**
     * 将场景处理上下文转换为权益账户上下文
     *
     * @param context 场景处理上下文，包含场景相关的业务数据
     * @return EquityAccountContext 权益账户上下文，用于权益账户操作
     */
    EquityAccountContext convertToEquityAccountContext(SceneActionProcessContext context);
}
