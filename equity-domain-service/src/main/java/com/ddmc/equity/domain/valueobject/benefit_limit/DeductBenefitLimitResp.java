package com.ddmc.equity.domain.valueobject.benefit_limit;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DeductBenefitLimitResp implements Serializable {
    private static final long serialVersionUID = 5012965853686068918L;

    @ApiModelProperty("扣减结果，true:可以进行后续操作 ｜ false: 频次校验不通过")
    @Builder.Default
    private Boolean deductResult = Boolean.FALSE;

    @ApiModelProperty("频次流水号，目前讨论由总次数+已领取次数+回退次数字符串拼接而成")
    @Builder.Default
    private String serialNumber = Strings.EMPTY;
}
