package com.ddmc.equity.domain.service.rule.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.common.exception.ApiBusinessException;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.domain.service.rule.EquityRuleService;
import com.ddmc.equity.infra.repository.dao.EquityRuleDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityRuleMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class EquityRuleServiceImpl implements EquityRuleService {

    @Resource
    private EquityRuleMapper equityRuleMapper;
    @Autowired
    private TransactionUtil transactionUtil;

    @Override
    public List<EquityRuleDO> queryAllUnOffline() {

        return equityRuleMapper.selectList(null);
    }

    @Override
    public List<EquityRuleDO> queryAllUnOfflineByStrategyIds(List<Long> strategyList) {
        QueryWrapper<EquityRuleDO> queryWrapper = new QueryWrapper();
        queryWrapper.in("strategy_id", strategyList);
        return equityRuleMapper.selectList(queryWrapper);
    }

    @Override
    public List<EquityRuleDO> queryEquityRuleDOListByStrategyId(Long strategyId) {
        QueryWrapper<EquityRuleDO> queryWrapper = new QueryWrapper();
        queryWrapper.eq("strategy_id", strategyId);
        return equityRuleMapper.selectList(queryWrapper);
    }

    /**
     * 批量保存策略规则
     *
     * @param equityRuleDOS 规则
     * @return 是否保存成功
     */
    @Override
    public Boolean batchSave(List<EquityRuleDO> equityRuleDOS) {
        //判空
        if (CollectionUtils.isEmpty(equityRuleDOS)) {
            return false;
        }
        equityRuleMapper.insertBatchSomeColumn(equityRuleDOS);
        return true;
    }


    /**
     * 批量保存策略规则
     *
     * @param list 规则
     * @return 是否保存成功
     */
    @Override
    public Long batchInsertOrUpdate(List<EquityRuleDO> list) {
        //判空
        if (CollectionUtils.isEmpty(list)) {
            return 0l;
        }
        try {
            return equityRuleMapper.batchInsertOrUpdate(list);
        } catch (Exception e) {
            log.error("EquityRuleServiceImpl.batchSave.e", e);
            return 0l;
        }
    }

    /**
     * 查询所有的规则
     *
     * @param isContainsDeleted 是否包含被删除的数据
     * @param idIsOrderByDesc   是否按照id倒序
     * @return List
     */
    public List<EquityRuleDO> selectAllRules(boolean isContainsDeleted, boolean idIsOrderByDesc) throws RuntimeException {
        //查询参数拼接
        QueryWrapper<EquityRuleDO> queryWrapper = new QueryWrapper<>();
        //不包含已删除的数据
        if (!isContainsDeleted) {
            queryWrapper.eq("is_delete", CommonEnum.INTEGER_BOOL.NO.getCode());
        }
        //是否按照id倒叙
        if (idIsOrderByDesc) {
            queryWrapper.orderByDesc("id");
        }
        //查询
        return equityRuleMapper.selectList(queryWrapper);
    }

    /**
     * 根据主键id
     * 查询规则
     *
     * @param ruleId 规则id
     * @return 规则
     */
    @Override
    public EquityRuleDO selectById(Long ruleId) {
        if (Objects.isNull(ruleId)) {
            return null;
        }
        return equityRuleMapper.selectById(ruleId);
    }

    /**
     * 规则更新
     *
     * @param equityRuleDO 规则
     * @return 更新结果
     */
    @Override
    public boolean update(EquityRuleDO equityRuleDO) {
        if (Objects.isNull(equityRuleDO)) {
            return false;
        }
        int result = equityRuleMapper.updateById(equityRuleDO);
        return result == 1;
    }

    @Override
    public void batchInsertAndUpdate(List<EquityRuleDO> insertDo, List<EquityRuleDO> updateDo) {
        //判空
        if (CollectionUtils.isEmpty(insertDo) && CollectionUtils.isEmpty(updateDo)) {
            return;
        }
        try {
            transactionUtil.transactional(s -> {
                if (!CollectionUtils.isEmpty(insertDo)) {
                    equityRuleMapper.insertBatchSomeColumn(insertDo);
                }
                if (!CollectionUtils.isEmpty(updateDo)) {
                    equityRuleMapper.updateBatch(updateDo);
                }
            });
        } catch (Exception e) {
            log.error("EquityRuleServiceImpl.batchSave.e", e);
            throw new ApiBusinessException(ExceptionEnum.SAVE_RULE_FAILED);
        }
    }

    @Override
    public List<EquityRuleDO> queryAllUnOfflineByStrategyIdsAndType(
            Collection<Long> strategyList,
            @NotNull Integer ruleType) {
        QueryWrapper<EquityRuleDO> queryWrapper = new QueryWrapper();
        // queryWrapper.in("status", Arrays.asList(1, 0));
        queryWrapper.in("strategy_id", strategyList);
        queryWrapper.eq("rule_type", ruleType);
        return equityRuleMapper.selectList(queryWrapper);
    }

    @Override
    public boolean save(EquityRuleDO equityRuleDO) {
        return equityRuleMapper.insert(equityRuleDO) == 1 ? true : false;
    }

    /**
     * 根据id列表获取策略规则
     *
     * @param strategyIds
     * @param typeEnum
     * @return
     */
    @Override
    public List<EquityRuleDO> getEquityRulesByStrategyId(List<Long> strategyIds, RuleTypeEnum typeEnum) {
        if (CollectionUtils.isEmpty(strategyIds)) {
            return Lists.newArrayList();
        }
        QueryWrapper<EquityRuleDO> queryWrapper = new QueryWrapper();
        // queryWrapper.in("status", Arrays.asList(1, 0));
        queryWrapper.in("strategy_id", strategyIds);
        if (Objects.nonNull(typeEnum)) {
            queryWrapper.eq("rule_type", typeEnum.getCode());
        }
        return equityRuleMapper.selectList(queryWrapper);
    }

    @Override
    public void batchDelete(List<EquityRuleDO> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return;
        }
        for (EquityRuleDO ruleDO : ruleList) {
            UpdateWrapper<EquityRuleDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", ruleDO.getId()).eq("is_delete", 0).set("is_delete", 1).set("aux_key", ruleDO.getId());
            ruleDO.setIsDelete(1);
            ruleDO.setAuxKey(ruleDO.getId());
            equityRuleMapper.update(ruleDO, updateWrapper);
        }
    }

    @Override
    public List<EquityRuleDO> batchUpdate(List<EquityRuleDO> ruleList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return ruleList;
        }

        equityRuleMapper.updateBatch(ruleList);
        return ruleList;

    }

    @Override
    public StockLimitRuleDTO getBenefitStockLimitRule(Long strategyId) {
        if (Objects.isNull(strategyId)) {
            log.warn("getBenefitStockLimitRule args isNull. strategyId={}", strategyId);
            return null;
        }

        LambdaQueryWrapper<EquityRuleDO> wrapper = Wrappers.<EquityRuleDO>lambdaQuery()
                .eq(EquityRuleDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(EquityRuleDO::getStrategyId, strategyId)
                .eq(EquityRuleDO::getRuleType, RuleTypeEnum.STOCK_LIMIT_RULE.getCode())
                .last("LIMIT 1");
        EquityRuleDO ruleDO = equityRuleMapper.selectOne(wrapper);
        return Objects.isNull(ruleDO) ? null :
                JsonUtil.parseObject(ruleDO.getRuleValue(), StockLimitRuleDTO.class);
    }
}
