package com.ddmc.equity.domain.entity.common;

import com.ddmc.equity.domain.converter.common.EngineContextConverter;
import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.engine.*;
import com.ddmc.equity.domain.dto.rule.RuleFilterActInfoDTO;
import com.ddmc.equity.dto.customer.*;
import com.ddmc.equity.enums.ActivityFilterTypeEnum;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@Builder
public class EngineContextEntity {

    /**
     * 数据转换
     *
     * @param consultSceneBenefitReqDTO 请求
     * @return EngineContextDTO
     */
    public EngineContextDTO convertToEngineContextDTO(ConsultSceneBenefitReqDTO consultSceneBenefitReqDTO) {
        EngineContextDTO engineContextDTO = EngineContextConverter.INSTANCE.convertToEngineContextDTO(consultSceneBenefitReqDTO.getBaseRequestDTO());
        engineContextDTO.setActFilterType(ActivityFilterTypeEnum.getByType(consultSceneBenefitReqDTO.getActivityFilterType()));
        engineContextDTO.setPageId(consultSceneBenefitReqDTO.getPageId());
        engineContextDTO.setAppId(consultSceneBenefitReqDTO.getAppId());
        engineContextDTO.setSource(consultSceneBenefitReqDTO.getSource());
        engineContextDTO.setExternalIdList(consultSceneBenefitReqDTO.getExternalIdList());
        engineContextDTO.setExternalType(consultSceneBenefitReqDTO.getExternalType());
        return engineContextDTO;
    }

    public EngineContextDTO convertReceiveReqToEngineContextDTO(ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO,
                                                                ActivityFilterTypeEnum effectActivity) {
        EngineContextDTO engineContextDTO = EngineContextConverter.INSTANCE.convertToEngineContextDTO(receiveSceneBenefitReqDTO.getBaseRequestDTO());
        engineContextDTO.setPageId(receiveSceneBenefitReqDTO.getPageId());
        engineContextDTO.setAppId(receiveSceneBenefitReqDTO.getAppId());
        engineContextDTO.setSource(receiveSceneBenefitReqDTO.getSource());
        engineContextDTO.setActFilterType(effectActivity);
        engineContextDTO.setSendTicketScene(receiveSceneBenefitReqDTO.getSendTicketScene());
        // 20230312 去除批量领取功能
        engineContextDTO.setBatchActivityId(null);
        engineContextDTO.setIsTicketIsRead(receiveSceneBenefitReqDTO.getIsTicketIsRead());
        // 指定需要领取的 activityIds 和 strategyIds 咨询，可以减少不必要的过滤日志
        List<ReceiveBenefitReqDTO> receiveBenefitReqDTOList = receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS();
        List<Long> activityIdList = CollectionUtils.isEmpty(receiveBenefitReqDTOList) ? null : receiveBenefitReqDTOList.stream()
                .map(ReceiveBenefitReqDTO::getActivityId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Long> strategyIdList = CollectionUtils.isEmpty(receiveBenefitReqDTOList) ? null : receiveBenefitReqDTOList.stream()
                .map(ReceiveBenefitReqDTO::getStrategyId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        engineContextDTO.setActivityIdList(activityIdList);
        engineContextDTO.setStrategyIdList(strategyIdList);
        engineContextDTO.setIsReceive(Boolean.TRUE);
        engineContextDTO.setNeedUnableReceiveReason(receiveSceneBenefitReqDTO.getNeedUnableReceiveReason());
        return engineContextDTO;
    }

    public static void copyEngineResultContext(EngineResultContextDTO source, EngineResultContextDTO target) {
        if (Objects.isNull(source) || Objects.isNull(target)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(source.getUnableReceiveBenefitDTOList())) {
            target.getUnableReceiveBenefitDTOList().addAll(source.getUnableReceiveBenefitDTOList());
        }
    }

    public static List<UnableReceiveBenefitDTO> convertToUnableReceiveBenefitDTOList(ActivityEngineContextDTO engineContextDTO,
                                                                                     RuleFilterActInfoDTO ruleFilterActInfoDTO,
                                                                                     List<StrategyCacheDto> strategies) {
        if (CollectionUtils.isEmpty(strategies)) {
            return null;
        }
        return strategies.stream().map(strategy -> {
            Long strategyId = strategy.getStrategyId();
            List<BenefitIdWithConfDto> benefits = strategy.getStrategyBenefitGroup().get(strategyId);
            if (CollectionUtils.isEmpty(benefits)) {
                return null;
            }
            return benefits.stream().map(benefit ->
                    convertToUnableReceiveBenefitDTO(engineContextDTO, ruleFilterActInfoDTO, strategy, benefit)
            ).collect(Collectors.toList());
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private static UnableReceiveBenefitDTO convertToUnableReceiveBenefitDTO(ActivityEngineContextDTO engineContextDTO,
                                                                            RuleFilterActInfoDTO ruleFilterActInfoDTO,
                                                                            StrategyCacheDto strategy,
                                                                            BenefitIdWithConfDto benefit) {
        EquityBenefitDO benefitDO = benefit.getBenefitDO();
        return UnableReceiveBenefitDTO.builder()
                .activityId(engineContextDTO.getActivityId())
                .externalType(engineContextDTO.getExternalType())
                .externalId(engineContextDTO.getExternalId())
                .strategyId(strategy.getStrategyId())
                .strategyExternalId(strategy.getExternalId())
                .benefitGroupId(benefit.getBenefitGroupId())
                .benefitId(benefit.getId())
                .benefitType(Optional.ofNullable(benefitDO).map(EquityBenefitDO::getBenefitType).orElse(null))
                .benefitValue(Optional.ofNullable(benefitDO).map(EquityBenefitDO::getBenefitValue).orElse(null))
                .unableReceiveReasonCode(ruleFilterActInfoDTO.getUnableReceiveReasonCode())
                .build();
    }

    public static List<UnableReceiveBenefitDTO> convertToUnableReceiveBenefitDTOList(StrategyEngineContextDTO engineContextDTO,
                                                                                     RuleFilterActInfoDTO ruleFilterActInfoDTO,
                                                                                     List<BenefitIdWithConfDto> benefits) {
        if (CollectionUtils.isEmpty(benefits)) {
            return null;
        }
        return benefits.stream().map(benefit ->
                convertToUnableReceiveBenefitDTO(engineContextDTO, ruleFilterActInfoDTO, benefit)
        ).collect(Collectors.toList());
    }

    public static UnableReceiveBenefitDTO convertToUnableReceiveBenefitDTO(StrategyEngineContextDTO engineContextDTO,
                                                                           RuleFilterActInfoDTO ruleFilterActInfoDTO,
                                                                           BenefitIdWithConfDto benefit) {
        EquityBenefitDO benefitDO = benefit.getBenefitDO();
        return UnableReceiveBenefitDTO.builder()
                .activityId(engineContextDTO.getActivityId())
                .externalType(engineContextDTO.getExternalType())
                .externalId(engineContextDTO.getExternalId())
                .strategyId(engineContextDTO.getStrategyId())
                .strategyExternalId(engineContextDTO.getStrategyExternalId())
                .benefitGroupId(benefit.getBenefitGroupId())
                .benefitId(benefit.getId())
                .benefitType(Optional.ofNullable(benefitDO).map(EquityBenefitDO::getBenefitType).orElse(null))
                .benefitValue(Optional.ofNullable(benefitDO).map(EquityBenefitDO::getBenefitValue).orElse(null))
                .unableReceiveReasonCode(ruleFilterActInfoDTO.getUnableReceiveReasonCode())
                .build();
    }

    public static UnableReceiveBenefitDTO convertToUnableReceiveBenefitDTO(BenefitGroupEngineContextDTO engineContextDTO,
                                                                           RuleFilterActInfoDTO ruleFilterActInfoDTO,
                                                                           BenefitIdWithConfDto benefit) {
        EquityBenefitDO benefitDO = benefit.getBenefitDO();
        return UnableReceiveBenefitDTO.builder()
                .activityId(engineContextDTO.getActivityId())
                .externalType(engineContextDTO.getExternalType())
                .externalId(engineContextDTO.getExternalId())
                .strategyId(engineContextDTO.getStrategyId())
                .strategyExternalId(engineContextDTO.getStrategyExternalId())
                .benefitGroupId(engineContextDTO.getBenefitGroupId())
                .benefitId(engineContextDTO.getBenefitId())
                .benefitType(Optional.ofNullable(benefitDO).map(EquityBenefitDO::getBenefitType).orElse(null))
                .benefitValue(Optional.ofNullable(benefitDO).map(EquityBenefitDO::getBenefitValue).orElse(null))
                .unableReceiveReasonCode(ruleFilterActInfoDTO.getUnableReceiveReasonCode())
                .build();
    }


    public EngineContextDTO convertUniversalReceiveReqToEngineContextDTO(UniversalReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO,
                                                                         ActivityFilterTypeEnum effectActivity) {
        EngineContextDTO engineContextDTO = EngineContextConverter.INSTANCE.convertToEngineContextDTO(receiveSceneBenefitReqDTO.getBaseRequestDTO());
        engineContextDTO.setPageId(receiveSceneBenefitReqDTO.getPageId());
        engineContextDTO.setAppId(receiveSceneBenefitReqDTO.getAppId());
        engineContextDTO.setSource(receiveSceneBenefitReqDTO.getSource());
        engineContextDTO.setActFilterType(effectActivity);
        engineContextDTO.setSendTicketScene(receiveSceneBenefitReqDTO.getSendTicketScene());
        List<UniversalReceiveBenefitReqDTO> receiveBenefitReqDTOList = receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS();
        List<Long> activityIdList = receiveBenefitReqDTOList.stream()
                .map(UniversalReceiveBenefitReqDTO::getActivityId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Long> strategyIdList = receiveBenefitReqDTOList.stream()
                .map(UniversalReceiveBenefitReqDTO::getStrategyId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<String> strategyExternalIdList = receiveBenefitReqDTOList.stream()
                .map(UniversalReceiveBenefitReqDTO::getStrategyExternalId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        engineContextDTO.setStrategyExternalIdList(strategyExternalIdList);
        engineContextDTO.setActivityIdList(activityIdList);
        engineContextDTO.setStrategyIdList(strategyIdList);
        engineContextDTO.setIsReceive(Boolean.TRUE);
        engineContextDTO.setNeedUnableReceiveReason(receiveSceneBenefitReqDTO.getNeedUnableReceiveReason());
        return engineContextDTO;
    }
}
