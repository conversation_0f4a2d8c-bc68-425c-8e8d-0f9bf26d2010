package com.ddmc.equity.domain.service.ticketPackage.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.entity.account.TicketPackageAccountConvertEntity;
import com.ddmc.equity.domain.service.ticketPackage.TicketPackageAccountRecordDomainService;
import com.ddmc.equity.infra.repository.dao.TicketPackageAccountRecordDO;
import com.ddmc.equity.infra.repository.dao.mapper.TicketPackageAccountRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Slf4j
@Service
public class TicketPackageAccountRecordDomainServiceImpl extends ServiceImpl<TicketPackageAccountRecordMapper, TicketPackageAccountRecordDO>
        implements TicketPackageAccountRecordDomainService {

    @Override
    public void insertAccountRecordDO(TicketPackageAccountRecordDO accountRecordDO) {
        Assert.notNull(accountRecordDO, ExceptionEnum.ILLEGAL_ARGS.getCode(), "权益子账户操作流水不能为空");
        Assert.mustTrue(StringUtils.isNotBlank(accountRecordDO.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "权益子账户操作流水 userId 不能为空");
        this.save(accountRecordDO);
    }

    @Override
    public boolean updateAccountRecordStatusAndRpcResult(String userId, Long accountRecordId, Long accountId, Integer status,
                                                         EquityRpcDto equityRpcDto, Map<String, Object> ruleLimitInfoMap) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "权益子账户操作流水 userId 不能为空");
        Assert.notNull(accountRecordId, ExceptionEnum.ILLEGAL_ARGS.getCode(), "权益子账户操作流水 accountRecordId 不能为空");
        Assert.mustTrue(StatusEnum.isContain(status), ExceptionEnum.ILLEGAL_ARGS.getCode(), "权益子账户操作流水状态异常");

        TicketPackageAccountRecordDO update = TicketPackageAccountConvertEntity.createUpdateAccountRecordDO(accountRecordId, accountId,
                status, equityRpcDto, ruleLimitInfoMap);
        return this.update(update, Wrappers.<TicketPackageAccountRecordDO>lambdaUpdate()
                .eq(TicketPackageAccountRecordDO::getUserId, userId)
                .eq(TicketPackageAccountRecordDO::getId, accountRecordId));
    }

    @Override
    public TicketPackageAccountRecordDO queryAccountRecordByUniqueKeyAndStatuses(String userId, Integer operateType,
                                                                                 String reqNo, List<Integer> statuses) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "余额权益子账户操作流水 userId 不能为空");
        Assert.mustTrue(OperateTypeEnum.contains(operateType), ExceptionEnum.ILLEGAL_ARGS.getCode(), "余额权益子账户操作流水操作类型异常");
        Assert.mustTrue(StringUtils.isNotBlank(reqNo), ExceptionEnum.ILLEGAL_ARGS.getCode(), "余额权益子账户操作流水 reqNo 不能为空");

        Wrapper<TicketPackageAccountRecordDO> wrapper = Wrappers.<TicketPackageAccountRecordDO>lambdaQuery()
                .eq(TicketPackageAccountRecordDO::getUserId, userId)
                .eq(TicketPackageAccountRecordDO::getOperateType, operateType)
                .eq(TicketPackageAccountRecordDO::getReqNo, reqNo)
                .in(CollectionUtils.isNotEmpty(statuses), TicketPackageAccountRecordDO::getStatus, statuses);
        return this.getOne(wrapper);
    }

    @Override
    public List<TicketPackageAccountRecordDO> queryAccountRecordByStrategyIdAndStatus(String userId, Long strategyId, Long benefitId, Integer operateType, Long freezeId, Integer status) {
        Wrapper<TicketPackageAccountRecordDO> wrapper = Wrappers.<TicketPackageAccountRecordDO>lambdaQuery()
                .eq(TicketPackageAccountRecordDO::getUserId, userId)
                .eq(TicketPackageAccountRecordDO::getStrategyId, strategyId)
                .eq(TicketPackageAccountRecordDO::getBenefitId, benefitId)
                .eq(TicketPackageAccountRecordDO::getOperateType, operateType)
                .eq(TicketPackageAccountRecordDO::getFreezeReceiveLimitId, freezeId)
                .eq(TicketPackageAccountRecordDO::getStatus, status)
                .orderByDesc(TicketPackageAccountRecordDO::getCreateTime);
        return this.list(wrapper);
    }

    @Override
    public boolean updateAccountRecordFreezeId(String userId, Long id, Long freezeId) {
        TicketPackageAccountRecordDO update = new TicketPackageAccountRecordDO();
        update.setId(id);
        update.setFreezeReceiveLimitId(freezeId);
        update.setUserId(userId);
        return this.update(update, Wrappers.<TicketPackageAccountRecordDO>lambdaUpdate()
                .eq(TicketPackageAccountRecordDO::getUserId, userId)
                .eq(TicketPackageAccountRecordDO::getId, id));
    }

    @Override
    public List<TicketPackageAccountRecordDO> queryTicketPackageAccountRecords(String userId, List<Long> activityIds,
                                                                               List<String> ticketPackageIds,
                                                                               List<Integer> operateTypes,
                                                                               List<Integer> statuses,
                                                                               Date startDate, Date endDate) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询权益子账户操作流水列表 userId 不能为空");

        Wrapper<TicketPackageAccountRecordDO> wrapper = Wrappers.<TicketPackageAccountRecordDO>lambdaQuery()
                .eq(TicketPackageAccountRecordDO::getUserId, userId)
                .in(CollectionUtils.isNotEmpty(activityIds), TicketPackageAccountRecordDO::getActivityId, activityIds)
                .in(CollectionUtils.isNotEmpty(ticketPackageIds), TicketPackageAccountRecordDO::getTicketPackageId, ticketPackageIds)
                .in(CollectionUtils.isNotEmpty(operateTypes), TicketPackageAccountRecordDO::getOperateType, operateTypes)
                .in(CollectionUtils.isNotEmpty(statuses), TicketPackageAccountRecordDO::getStatus, statuses)
                .ge(Objects.nonNull(startDate), TicketPackageAccountRecordDO::getCreateTime, startDate)
                .le(Objects.nonNull(endDate), TicketPackageAccountRecordDO::getCreateTime, endDate);
        return this.list(wrapper);
    }
}
