package com.ddmc.equity.domain.valueobject.benefit;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class BenefitListFilterReqVO implements Serializable {
    private static final long serialVersionUID = -3349976376852249820L;

    @Builder.Default
    private Integer page = 1;
    @Builder.Default
    private Integer pageSize = 20;

    private Long id;

    private String name;

    private List<Integer> expireTypes;

    private List<Integer> benefitTypes;

    private String creator;

    private String updater;

    private List<Integer> statuses;
}
