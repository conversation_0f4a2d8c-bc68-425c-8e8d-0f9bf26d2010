package com.ddmc.equity.domain.service.decoration.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.dto.decoration.UserDecorationCacheDTO;
import com.ddmc.equity.domain.service.decoration.UserDecorationCacheDomainService;
import com.ddmc.equity.enums.DecorationWearStatusEnum;
import com.ddmc.equity.infra.cache.redis.EquityCountRedisCache;
import com.ddmc.equity.infra.repository.dao.UserWearingDecorationDO;
import com.ddmc.equity.infra.repository.dao.mapper.UserWearingDecorationMapper;
import com.google.common.collect.Lists;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户装饰缓存域服务实现
 *
 * <AUTHOR>
 * @date 2025/03/20
 */
@Service
@Slf4j
public class UserDecorationCacheDomainServiceImpl implements UserDecorationCacheDomainService {

    /**
     * 用户装饰缓存默认30天过期
     */
    @Value("${user.decoration.cache.ttl.day:30}")
    private Long userDecorationCacheTtlDay;

    /**
     * 长期有效日期字符串
     * 用于表示装饰的长期有效期限，默认为 2099-12-31 23:59:59
     * 当装饰的过期时间等于此值时，表示该装饰为长期有效
     * 在返回给前端时，会将此值转换为 null，表示永不过期
     */
    @Value("${user.decoration.longTermValidDateStr:2099-12-31 23:59:59}")
    private String longTermValidDateStr;

    @Setter(onMethod_ = {@Autowired})
    private EquityCountRedisCache countRedisCache;

    @Setter(onMethod_ = {@Autowired})
    private UserWearingDecorationMapper userWearingDecorationMapper;

    @Override
    public void refreshCacheByUser(UserWearingDecorationDO userWearingDecorationDO) {
        //用户主动操作刷新缓存
        this.refreshCache(userWearingDecorationDO);
        CsossUtils.logEventWithSpan(MonitorConstants.USER_DECORATION_CACHE, "refreshCacheByUser_success");
    }

    @Override
    public void refreshCacheByJob(UserWearingDecorationDO userWearingDecorationDO) {
        //JOb执行刷新缓存
        this.refreshCache(userWearingDecorationDO);
        CsossUtils.logEventWithSpan(MonitorConstants.USER_DECORATION_CACHE, "refreshCacheByJob_success");
    }

    @Override
    public List<UserDecorationCacheDTO> queryCacheList(List<String> uidList, Integer decorationType) {
        if (CollectionUtils.isEmpty(uidList)) {
            return Lists.newArrayList();
        }
        List<String> cacheKeyList = uidList.stream().filter(StringUtils::isNotBlank).map(uid -> this.cacheKey(uid, decorationType)).collect(Collectors.toList());
        List<String> valueList = this.countRedisCache.mGetAll(cacheKeyList);
        if (CollectionUtils.isNotEmpty(valueList)) {
            return valueList.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(jsonString -> JsonUtil.toJavaObject(jsonString, UserDecorationCacheDTO.class))
                    .collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public List<UserWearingDecorationDO> queryNeedCacheList(Integer shardingIndex, List<String> userIds, Long lastId, Integer limit) {
        if (Objects.isNull(shardingIndex) || Objects.isNull(lastId) || Objects.isNull(limit)) {
            log.error("queryNeedCacheList shardingIndex or lastId or limit isNull shardingIndex={}, lastId={}, limit={}", shardingIndex, lastId, limit);
            return null;
        }
        Wrapper<UserWearingDecorationDO> wrapper = Wrappers.<UserWearingDecorationDO>lambdaQuery()
                .and(w -> w.apply("sharding_index = {0}", shardingIndex))
                .in(CollectionUtils.isNotEmpty(userIds), UserWearingDecorationDO::getUserId, userIds)
                .gt(UserWearingDecorationDO::getId, lastId)
                .orderByAsc(UserWearingDecorationDO::getId)
                .last("limit " + limit);
        return this.userWearingDecorationMapper.selectList(wrapper);
    }

    /**
     * 刷新缓存
     *
     * @param userWearingDecorationDO 用户佩戴装饰记录
     */
    private void refreshCache(UserWearingDecorationDO userWearingDecorationDO) {
        //
        String key = this.cacheKey(userWearingDecorationDO.getUserId(), userWearingDecorationDO.getDecorationType());
        //取消佩戴
        if (Objects.equals(DecorationWearStatusEnum.UNWEARING.getCode(), userWearingDecorationDO.getWearStatus())) {
            this.countRedisCache.deleteKey(key);
        } else {
            //佩戴
            String decorationName = userWearingDecorationDO.getDecorationName();
            String decorationImage = userWearingDecorationDO.getDecorationImage();
            Long expireTime = this.getExpireTimeFromUserWearingDecorationDO(userWearingDecorationDO);
            //buildCache
            UserDecorationCacheDTO value = this.buildCacheValue(userWearingDecorationDO.getUserId(), decorationName, decorationImage, expireTime);
            Long ttl = this.getCacheTtl(userWearingDecorationDO);
            if (ttl > 0) {
                //refreshCache
                this.countRedisCache.setValue(key, JsonUtil.toJsonString(value), ttl, TimeUnit.SECONDS);
            } else {
                //缓存已过期
                this.countRedisCache.deleteKey(key);
            }
        }
    }

    /**
     * 缓存键
     *
     * @param userId         用户id
     * @param decorationType 装饰类型
     * @return {@link String }
     */
    private String cacheKey(String userId, Integer decorationType) {
        return String.format(CacheKeyConstants.USER_CURRENT_DECORATION_KEY_TEMPLATE, userId, decorationType);
    }

    /**
     * 构建缓存值
     *
     * @param userId     用户id
     * @param name       名字
     * @param image      图像
     * @param expireTime 到期时间
     * @return {@link UserDecorationCacheDTO }
     */
    private UserDecorationCacheDTO buildCacheValue(String userId, String name, String image, Long expireTime) {
        UserDecorationCacheDTO cacheValue = new UserDecorationCacheDTO();
        cacheValue.setUid(userId);
        cacheValue.setName(name);
        cacheValue.setImage(image);
        cacheValue.setExpireTime(expireTime);
        return cacheValue;
    }

    /**
     * 从用户佩戴装饰物中获取过期时间
     *
     * @param userWearingDecorationDO 用户佩戴装饰做
     * @return {@link Long }
     */
    private Long getExpireTimeFromUserWearingDecorationDO(UserWearingDecorationDO userWearingDecorationDO) {
        return Optional.of(userWearingDecorationDO)
                .map(UserWearingDecorationDO::getExpireTime)
                .map(Date::toInstant)
                .map(Instant::getEpochSecond)
                .orElse(null);
    }

    /**
     * 获取缓存TTL
     *
     * @param userWearingDecorationDO 用户佩戴装饰做
     * @return {@link Long } linux时间戳精确到秒级
     */
    public Long getCacheTtl(UserWearingDecorationDO userWearingDecorationDO) {
        long nowTime = Instant.now().getEpochSecond();
        //挂件失效时间
        return Optional.of(userWearingDecorationDO)
                .map(UserWearingDecorationDO::getExpireTime)
                .map(Date::toInstant)
                .map(Instant::getEpochSecond)
                .map(expireTime -> {
                    long longTermTime = LocalDateTime.parse(this.longTermValidDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                            .atZone(ZoneId.systemDefault())
                            .toEpochSecond();
                    //是否永久有效
                    return expireTime.equals(longTermTime) ? this.randomTtl() : (expireTime - nowTime);
                })
                .map(timeToLive -> Math.max(timeToLive, 0L))
                .orElse(0L);
    }

    /**
     * 基于30天+1min的随机过期时间
     *
     * @return {@link Long }
     */
    private Long randomTtl() {
        long fixedTime = this.userDecorationCacheTtlDay * 86400L;
        return fixedTime + RandomUtil.randomLong(0, 59);
    }

}
