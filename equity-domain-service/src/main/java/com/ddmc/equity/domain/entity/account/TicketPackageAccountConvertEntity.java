package com.ddmc.equity.domain.entity.account;

import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.dto.business.account.TicketPackageAccountRecordDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.repository.dao.TicketPackageAccountDO;
import com.ddmc.equity.infra.repository.dao.TicketPackageAccountRecordDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.SubAccountRecordDTO;
import com.ddmc.equity.model.dto.TicketPackageAccountInfoDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


public class TicketPackageAccountConvertEntity extends AccountConvertEntity {

    public static TicketPackageAccountRecordDO createAccountRecordDO(int operateType, EquityAccountContext equityAccountContext) {
        TicketPackageAccountRecordDO accountRecordDO = new TicketPackageAccountRecordDO();
        createSubAccountRecordBaseFieldDO(accountRecordDO, operateType, equityAccountContext);
        accountRecordDO.setTicketPackageId(equityAccountContext.getEquityValue());
        return accountRecordDO;
    }

    public static TicketPackageAccountDO createAccountDO(EquityAccountContext equityAccountContext) {
        TicketPackageAccountDO accountDO = new TicketPackageAccountDO();
        createSubAccountBaseFieldDO(accountDO, equityAccountContext);
        accountDO.setTicketPackageId(equityAccountContext.getEquityValue());
        return accountDO;
    }

    public static TicketPackageAccountRecordDO createUpdateAccountRecordDO(Long accountRecordId, Long accountId, Integer status,
                                                                           EquityRpcDto equityRpcDto, Map<String, Object> ruleLimitInfoMap) {
        TicketPackageAccountRecordDO update = new TicketPackageAccountRecordDO();
        fillUpdateBaseAccountRecordDO(update, accountRecordId, accountId, status, equityRpcDto, ruleLimitInfoMap);
        update.setUserTicketPackageId(Objects.nonNull(equityRpcDto) ? equityRpcDto.getValue() : null);
        return update;
    }

    public static SubAccountRecordDTO convertToSubAccountRecordDTO(TicketPackageAccountRecordDO recordDO) {
        SubAccountRecordDTO subAccountRecordDTO = new SubAccountRecordDTO();
        fillSubAccountRecordDTO(subAccountRecordDTO, recordDO);
        subAccountRecordDTO.setBenefitValue(recordDO.getTicketPackageId());
        return subAccountRecordDTO;
    }


    public static List<AccountInfoDTO> convertToAccountInfoDTOList(List<TicketPackageAccountDO> accountDOList) {
        if (CollectionUtils.isEmpty(accountDOList)) {
            return null;
        }
        return accountDOList.stream().map(TicketPackageAccountConvertEntity::convertToAccountInfoDTO).collect(Collectors.toList());
    }

    private static AccountInfoDTO convertToAccountInfoDTO(TicketPackageAccountDO accountDO) {
        TicketPackageAccountInfoDTO ticketPackageaccountInfoDTO = TicketPackageAccountInfoDTO.builder()
                .ticketPackageId(accountDO.getTicketPackageId())
                .build();
        AccountInfoDTO accountInfoDTO = convertToBaseAccountInfoDTO(accountDO);
        accountInfoDTO.setBenefitType(BenefitTypeEnum.TICKET_PACKAGE.getId());
        accountInfoDTO.setTicketPackageAccountInfoDTO(ticketPackageaccountInfoDTO);
        return accountInfoDTO;
    }

    public static List<TicketPackageAccountRecordDTO> convertToTicketPackageAccountRecordDTOList(List<TicketPackageAccountRecordDO> recordDOList) {
        if (CollectionUtils.isEmpty(recordDOList)) {
            return null;
        }
        return recordDOList.stream().map(TicketPackageAccountConvertEntity::convertToTicketPackageAccountRecordDTO).collect(Collectors.toList());
    }

    private static TicketPackageAccountRecordDTO convertToTicketPackageAccountRecordDTO(TicketPackageAccountRecordDO recordDO) {
        return TicketPackageAccountRecordDTO.builder()
                .activityId(recordDO.getActivityId())
                .strategyId(recordDO.getStrategyId())
                .benefitGroupId(recordDO.getBenefitGroupId())
                .benefitId(recordDO.getBenefitId())
                .accountId(recordDO.getAccountId())
                .userId(recordDO.getUserId())
                .operateType(recordDO.getOperateType())
                .status(recordDO.getStatus())
                .appId(recordDO.getAppId())
                .pageId(recordDO.getPageId())
                .source(recordDO.getSource())
                .reqNo(recordDO.getReqNo())
                .createTime(recordDO.getCreateTime())
                .ticketPackageId(recordDO.getTicketPackageId())
                .userTicketPackageId(recordDO.getUserTicketPackageId())
                .build();
    }
}
