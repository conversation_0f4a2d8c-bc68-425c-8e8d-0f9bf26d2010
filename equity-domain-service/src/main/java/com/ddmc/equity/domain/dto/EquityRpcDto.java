package com.ddmc.equity.domain.dto;

import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcResponseExtDTO;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class EquityRpcDto {

    /**
     * 远程调用发放状态
     */
    private StatusEnum statusEnum;
    /**
     * 返回 code
     */
    private String code;
    /**
     * 返回 msg
     */
    private String message;
    /**
     * 三方返回值。领券活动返回用户领取后的用户券 id；券膨胀返回用户膨胀后的券 userTicketId
     */
    private String value;
    /**
     * 外部 rpc 调用请求出参拓展信息
     */
    private UniversalAccountRpcResponseExtDTO rpcResponseExtDTO;

    public static EquityRpcDto processing(ExceptionEnum exceptionEnum) {
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        equityRpcDto.setStatusEnum(StatusEnum.PROCESSING);
        equityRpcDto.setCode(exceptionEnum.getCode());
        equityRpcDto.setMessage(exceptionEnum.getMessage());
        return equityRpcDto;
    }

    public static EquityRpcDto success(String value) {
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        equityRpcDto.setStatusEnum(StatusEnum.SUCCESS);
        equityRpcDto.setCode(ExceptionEnum.SUCCESS.getCode());
        equityRpcDto.setMessage(ExceptionEnum.SUCCESS.getMessage());
        equityRpcDto.setValue(value);
        return equityRpcDto;
    }

    public static EquityRpcDto success(String value, UniversalAccountRpcResponseExtDTO rpcResponseExtDTO) {
        EquityRpcDto equityRpcDto = success(value);
        equityRpcDto.setRpcResponseExtDTO(rpcResponseExtDTO);
        return equityRpcDto;
    }

    public static EquityRpcDto fail(ExceptionEnum exceptionEnum) {
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        equityRpcDto.setStatusEnum(StatusEnum.FAIL);
        equityRpcDto.setCode(exceptionEnum.getCode());
        equityRpcDto.setMessage(exceptionEnum.getMessage());
        return equityRpcDto;
    }

    public static EquityRpcDto fail(String code, String msg) {
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        equityRpcDto.setStatusEnum(StatusEnum.FAIL);
        equityRpcDto.setCode(code);
        equityRpcDto.setMessage(msg);
        return equityRpcDto;
    }
}
