package com.ddmc.equity.domain.service.coupon_expand;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ddmc.equity.domain.dto.benefit.ActivityCouponExpandAccountCollectInDTO;
import com.ddmc.equity.domain.dto.benefit.ActivityCouponExpandAccountCollectOutDTO;
import com.ddmc.equity.infra.repository.dao.CouponExpandAccountCollectDO;
import com.ddmc.equity.infra.repository.dao.CouponExpandAccountDO;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/7/4 10:57
 * @description
 */
public interface CouponExpandAccountCollectDomainService extends IService<CouponExpandAccountCollectDO> {

    /**
     * 创建券膨胀权益子账户聚合表记录
     *
     * @param accountDO DB_DO
     */
    void insertAccountCollectDO(CouponExpandAccountDO accountDO);


    /**
     * 分页查询
     *
     * @param in
     * @return
     */
    ActivityCouponExpandAccountCollectOutDTO getActivityExpandList(ActivityCouponExpandAccountCollectInDTO in);
}
