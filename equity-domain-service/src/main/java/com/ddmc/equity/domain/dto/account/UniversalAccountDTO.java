package com.ddmc.equity.domain.dto.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/22 18:00
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("用户通用账户")
public class UniversalAccountDTO {

    @ApiModelProperty("用户 id")
    private String userId;

    @ApiModelProperty("通用账户 id")
    private Long accountId;

    @ApiModelProperty("活动 id")
    private Long activityId;

    /**
     * @see com.ddmc.equity.enums.BenefitTypeEnum
     */
    @ApiModelProperty("权益类型")
    private Integer benefitType;

    @ApiModelProperty("总权益数量")
    private Integer totalCount;

    @ApiModelProperty("可使用权益数量")
    private Integer availableCount;

    @ApiModelProperty("已过期权益数量")
    private Integer expireCount;
}


