package com.ddmc.equity.domain.service.equityAccount;

import com.ddmc.equity.account.UniversalAccountRecordContext;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.QueryUniversalAccountRecordsReqDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRecordInnerExtDTO;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;

import java.util.List;

public interface UniversalAccountRecordDomainService {

    void add(UniversalAccountRecordDO universalAccountRecordDO);

    void update(UniversalAccountRecordDO universalAccountRecordDO);

    UniversalAccountRecordDO getOneByUniqueKey(UniversalAccountRecordDO universalAccountRecordDO);

    /**
     * 通过 userId + sceneCode + 操作类型 + 业务流水号 + 辅助键查询唯一的权益账户操作记录
     *
     * @param userId      用户 id
     * @param sceneCode   场景 code。如果为空，则查询 sceneCode = '' 的操作记录（直塞的场景下 sceneCode 为空）
     * @param operateType 操作类型
     * @param reqNo       业务流水号
     * @param auxKey      辅助键
     * @return 唯一的权益账户操作记录
     */
    UniversalAccountRecordDO queryOneByUniqueKey(String userId, String sceneCode, Integer operateType,
                                                 String reqNo, String auxKey);

    /**
     * 通过 userId + strategyId + benefitId + 操作类型 + 业务流水号 + 辅助键 + 状态列表查询权益账户操作记录列表
     *
     * @param userId      用户 id
     * @param strategyId  策略 id。如果为空，则查询 strategyId = 0L 的操作记录（直塞的场景下 strategyId 为空）
     * @param benefitId   权益 id
     * @param operateType 操作类型
     * @param reqNo       业务流水号。可以不指定
     * @param auxKey      辅助键。可以不指定
     * @param statuses    流水状态列表。可以不指定
     * @return 权益账户操作记录列表
     */
    List<UniversalAccountRecordDO> queryExistAccountRecords(String userId, Long strategyId, Long benefitId,
                                                            Integer operateType, String reqNo, String auxKey,
                                                            List<Integer> statuses);

    /**
     * 更新通用账户操作记录状态和 rpc 结果
     *
     * @param userId          用户 id
     * @param accountRecordId 账户操作记录 id
     * @param status          状态
     * @param accountId       账户 id
     * @param equityRpc       外部 rpc 调用结果
     */
    boolean updateStatusAndRpcResult(String userId, Long accountRecordId, Integer status, Long accountId,
                                     EquityRpcDto equityRpc);

    /**
     * 更新通用账户操作记录状态和 rpc 结果
     *
     * @param userId          用户 id
     * @param accountRecordId 账户操作记录 id
     * @param status          状态
     * @param accountId       账户 id
     * @param equityRpc       外部 rpc 调用结果
     * @param relatedReqNo    关联另一笔冻结记录的业务流水号
     */
    boolean updateRecordStatusAndRpcResult(String userId, Long accountRecordId, Integer status, Long accountId,
                                           EquityRpcDto equityRpc, String relatedReqNo);

    /**
     * 更新通用账户操作记录内部唯一流水号、活动维度领取频次限制表 id、权益维度领取频次限制表 id
     *
     * @param userId                 用户 id
     * @param accountRecordId        记录 id
     * @param innerReqNo             内部唯一流水号
     * @param activityReceiveLimitId 活动维度领取频次限制表 id
     * @param benefitReceiveLimitId  权益维度领取频次限制表 id
     */
    boolean updateInnerReqNoAndRuleLimitInfo(String userId, Long accountRecordId, String innerReqNo,
                                             Long activityReceiveLimitId, Long benefitReceiveLimitId);

    /**
     * 更新通用账户操作记录内部拓展信息
     *
     * @param userId          用户 id
     * @param accountRecordId 账户操作记录 id
     * @param innerExtDTO     内部拓展信息
     */
    boolean updateInnerExt(String userId, Long accountRecordId, UniversalAccountRecordInnerExtDTO innerExtDTO);

    /**
     * 查询用户权益通用账户操作记录列表
     *
     * @param req 请求参数
     * @return 用户权益通用账户操作记录列表
     */
    List<UniversalAccountRecordDO> queryUniversalAccountRecords(QueryUniversalAccountRecordsReqDTO req);

    /**
     * 分页查询用户权益通用账户操作记录列表
     *
     * @param req 请求参数
     * @return 用户权益通用账户操作记录列表
     */
    PageListRespDTO<UniversalAccountRecordDO> pageQueryUniversalAccountRecords(QueryUniversalAccountRecordsReqDTO req);

    PageListRespDTO<UniversalAccountRecordDO> queryUniversalAccountRecordsForPage(UniversalAccountRecordContext context);
}
