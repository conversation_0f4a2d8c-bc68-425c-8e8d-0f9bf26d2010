package com.ddmc.equity.domain.entity.account;

import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.repository.dao.EmptyPrizeAccountDO;
import com.ddmc.equity.infra.repository.dao.EmptyPrizeAccountRecordDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.SubAccountRecordDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/25 17:16
 * @description
 */
public class EmptyPrizeAccountConvertEntity extends AccountConvertEntity {

    public static EmptyPrizeAccountRecordDO createAccountRecordDO(int operateType, EquityAccountContext equityAccountContext) {
        EmptyPrizeAccountRecordDO accountRecordDO = new EmptyPrizeAccountRecordDO();
        createSubAccountRecordBaseFieldDO(accountRecordDO, operateType, equityAccountContext);
        return accountRecordDO;
    }

    public static EmptyPrizeAccountDO createAccountDO(EquityAccountContext equityAccountContext) {
        EmptyPrizeAccountDO accountDO = new EmptyPrizeAccountDO();
        createSubAccountBaseFieldDO(accountDO, equityAccountContext);
        return accountDO;
    }

    public static EmptyPrizeAccountRecordDO createUpdateAccountRecordDO(Long accountRecordId, Long accountId, Integer status,
                                                                        EquityRpcDto equityRpcDto, Map<String, Object> ruleLimitInfoMap) {
        EmptyPrizeAccountRecordDO update = new EmptyPrizeAccountRecordDO();
        fillUpdateBaseAccountRecordDO(update, accountRecordId, accountId, status, equityRpcDto, ruleLimitInfoMap);
        return update;
    }

    public static SubAccountRecordDTO convertToSubAccountRecordDTO(EmptyPrizeAccountRecordDO recordDO) {
        SubAccountRecordDTO subAccountRecordDTO = new SubAccountRecordDTO();
        fillSubAccountRecordDTO(subAccountRecordDTO, recordDO);
        return subAccountRecordDTO;
    }

    public static List<AccountInfoDTO> convertToAccountInfoDTOList(List<EmptyPrizeAccountDO> accountDOList) {
        if (CollectionUtils.isEmpty(accountDOList)) {
            return null;
        }
        return accountDOList.stream().map(EmptyPrizeAccountConvertEntity::convertToAccountInfoDTO).collect(Collectors.toList());
    }

    private static AccountInfoDTO convertToAccountInfoDTO(EmptyPrizeAccountDO accountDO) {
        AccountInfoDTO accountInfoDTO = convertToBaseAccountInfoDTO(accountDO);
        accountInfoDTO.setBenefitType(BenefitTypeEnum.EMPTY_PRIZE.getId());
        return accountInfoDTO;
    }
}
