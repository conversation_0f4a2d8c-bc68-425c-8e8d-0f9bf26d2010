package com.ddmc.equity.domain.entity.account;

import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.domain.dto.account.UniversalAccountDTO;
import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.ddmc.equity.dto.customer.account.UniversalUseBenefitTypeDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.userpoint.api.request.DecreaseRequestReq;
import com.ddmc.userpoint.api.request.IncreaseRequestReq;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/1/19 15:41
 * @description
 */
public class PointAccountConvertEntity {

    public static IncreaseRequestReq convertToIncreaseRequest(EquityAccountContext equityAccountContext) {
        IncreaseRequestReq req = new IncreaseRequestReq();
        req.setRequestId(equityAccountContext.getSerialNumber());
        // 当前请求时间（单位 ms）
        req.setRequestTime(System.currentTimeMillis());
        req.setUserId(equityAccountContext.getUid());
        // 发放积分场景
        req.setFromType(equityAccountContext.getSendPointScene());
        // 发放积分描述（如果为空，使用积分侧配置的默认文案）
        req.setDescription(equityAccountContext.getSendPointDesc());
        // 发放积分来源
        req.setSource(equityAccountContext.getSendPointSource());
        // 发放积分关联标识。设置成业务流水号
        req.setOrderNumber(equityAccountContext.getSerialNumber());
        // 发放积分数（正整数）
        req.setPoint(UniversalAccountAmountCalEntity.getDoRpcSendAmountInteger(equityAccountContext));
        return req;
    }

    public static DecreaseRequestReq convertToDecreaseRequest(UniversalUseBenefitTypeDTO useBenefitReq) {
        BaseRequestDTO baseRequestDTO = useBenefitReq.getBaseRequestDTO();
        DecreaseRequestReq req = new DecreaseRequestReq();
        req.setRequestId(useBenefitReq.getReqNo());
        // 当前请求时间（单位 ms）
        req.setRequestTime(System.currentTimeMillis());
        req.setUserId(baseRequestDTO.getUserId());
        // 使用积分场景
        req.setFromType(useBenefitReq.getUsePointScene());
        // 使用积分描述（如果为空，使用积分侧配置的默认文案）
        req.setDescription(useBenefitReq.getUsePointDesc());
        // 使用积分来源
        req.setSource(useBenefitReq.getUsePointSource());
        // 使用积分关联标识。设置成业务流水号
        req.setOrderNumber(useBenefitReq.getReqNo());
        req.setPoint(useBenefitReq.getCount());
        return req;
    }

    public static UniversalAccountDTO convertToPointAccountDTO(String userId, Integer userTotalPoint) {
        // 如果未查询到用户可用积分，则默认返回可用积分为 null
        return UniversalAccountDTO.builder()
                .userId(userId)
                .benefitType(BenefitTypeEnum.POINT.getId())
                .totalCount(userTotalPoint)
                .availableCount(userTotalPoint)
                .expireCount(0)
                .build();
    }
}
