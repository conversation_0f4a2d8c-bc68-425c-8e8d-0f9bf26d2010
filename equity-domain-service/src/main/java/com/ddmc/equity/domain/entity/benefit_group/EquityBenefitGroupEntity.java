package com.ddmc.equity.domain.entity.benefit_group;

import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.converter.benefit_group.BenefitGroupConverter;
import com.ddmc.equity.domain.dto.benefit.StrategyBenefitGroupRulesDTO;
import com.ddmc.equity.domain.dto.benefit_group.BaseInfoDTO;
import com.ddmc.equity.domain.dto.benefit_group.BenefitGroupInfoDTO;
import com.ddmc.equity.domain.dto.rule.UserTagInfoSaveDTO;
import com.ddmc.equity.domain.dto.rule.condition.UserTagInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.UserTagRuleDTO;
import com.ddmc.equity.domain.valueobject.benefit_group.BenefitGroupListFilterReqVO;
import com.ddmc.equity.dto.business.BenefitGroupBusinessDTO;
import com.ddmc.equity.dto.business.BenefitGroupListReqDTO;
import com.ddmc.equity.dto.business.BenefitGroupSaveReqDTO;
import com.ddmc.equity.dto.business.UniversalBenefitDTO;
import com.ddmc.equity.dto.business.provide.ProvideBenefitDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitGroupDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitGroupRuleDO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Getter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
@Slf4j
public class EquityBenefitGroupEntity extends EquityBenefitGroupDO implements Serializable {
    private static final long serialVersionUID = -8794090308421892630L;

    @NotNull
    public static BenefitGroupListFilterReqVO getBenefitGroupFilterByListReq(@NotNull BenefitGroupListReqDTO req) {
        return null;
    }

    @NotNull
    public static List<BenefitGroupBusinessDTO> listBenefitGroupBusinessDTO(@NotNull List<EquityBenefitGroupDO> list) {
        return null;
    }

    public static EquityBenefitGroupDO getBenefitGroupDOBySaveReq(BenefitGroupSaveReqDTO req) {
        return BenefitGroupConverter.INSTANCE.srd2e(req);
    }

    public EquityBenefitGroupDO convertToEquityBenefitGroupDO(Long strategyId, List<ProvideBenefitDTO> benefitDTOList, String adminId, String adminName, List<EquityBenefitDO> needCreateBenefitList, EquityBenefitGroupDO equityBenefitGroupDO) {
        if (CollectionUtils.isEmpty(benefitDTOList)) {
            return null;
        }
        if (Objects.isNull(equityBenefitGroupDO)) {
            equityBenefitGroupDO = new EquityBenefitGroupDO();
        }
        equityBenefitGroupDO.setStrategyId(strategyId);
        BenefitGroupInfoDTO benefitGroupInfoDTO = new BenefitGroupInfoDTO();
        List<BaseInfoDTO> baseList = Lists.newArrayList();
        for (ProvideBenefitDTO provideBenefitDTO : benefitDTOList) {
            //不存在的权益
            if (Objects.isNull(provideBenefitDTO.getBenefitId())) {
                Long benefitId = filterEquityBenefitDOId(provideBenefitDTO.getBenefitName(), provideBenefitDTO.getBenefitType(), provideBenefitDTO.getBenefitValue(), needCreateBenefitList);
                if (Objects.isNull(benefitId)) {
                    continue;
                }
                BaseInfoDTO baseInfoDTO = new BaseInfoDTO();
                baseInfoDTO.setId(benefitId);
                if (StringUtils.isBlank(provideBenefitDTO.getWeight())) {
                    baseInfoDTO.setWeight(Constants.ZERO_TEXT);
                } else {
                    baseInfoDTO.setWeight(provideBenefitDTO.getWeight());
                }
                baseList.add(baseInfoDTO);
                continue;
            }
            //已存在的权益数据
            BaseInfoDTO baseInfoDTO = new BaseInfoDTO();
            baseInfoDTO.setId(provideBenefitDTO.getBenefitId());
            baseInfoDTO.setWeight(provideBenefitDTO.getWeight());
            baseList.add(baseInfoDTO);
        }
        benefitGroupInfoDTO.setBaseList(baseList);
        equityBenefitGroupDO.setBenefitInfo(JsonUtil.toJsonString(benefitGroupInfoDTO));
        String operateId = StringUtils.isBlank(adminId) ? Constants.DEFAULT_ADMIN_ID : adminId;
        String operateName = StringUtils.isBlank(adminName) ? Constants.DEFAULT_ADMIN_ID : adminName;
        equityBenefitGroupDO.setEditAdminId(operateId);
        equityBenefitGroupDO.setEditAdminName(operateName);
        equityBenefitGroupDO.setOpAdminId(operateId);
        equityBenefitGroupDO.setOpAdminName(operateName);

        return equityBenefitGroupDO;

    }

    private Long filterEquityBenefitDOId(String benefitName, Integer benefitType, String benefitValue, List<EquityBenefitDO> needCreateBenefitList) {
        if (CollectionUtils.isEmpty(needCreateBenefitList)) {
            return null;
        }
        for (EquityBenefitDO equityBenefitDO : needCreateBenefitList) {
            if (!equityBenefitDO.getName().equals(benefitName)) {
                continue;
            }
            if (!equityBenefitDO.getBenefitType().equals(benefitType)) {
                continue;
            }
            if (!equityBenefitDO.getBenefitValue().equals(benefitValue)) {
                continue;
            }
            return equityBenefitDO.getId();
        }
        return null;
    }


    public static EquityBenefitGroupDO dto2do(List<UniversalBenefitDTO> benefitDTOList, String adminId, String adminName) {
        if (CollectionUtils.isEmpty(benefitDTOList)) {
            return null;
        }
        EquityBenefitGroupDO equityBenefitGroupDO = new EquityBenefitGroupDO();
        BenefitGroupInfoDTO benefitGroupInfoDTO = new BenefitGroupInfoDTO();
        List<BaseInfoDTO> baseList = Lists.newArrayList();
        for (UniversalBenefitDTO provideBenefitDTO : benefitDTOList) {
            BaseInfoDTO baseInfoDTO = new BaseInfoDTO();
            baseInfoDTO.setId(provideBenefitDTO.getBenefitId());
            baseInfoDTO.setWeight(StringUtils.defaultIfBlank(provideBenefitDTO.getWeight(), null));
            baseList.add(baseInfoDTO);
        }
        benefitGroupInfoDTO.setBaseList(baseList);
        equityBenefitGroupDO.setBenefitInfo(JsonUtil.toString(benefitGroupInfoDTO));
        String operateId = StringUtils.isBlank(adminId) ? Constants.DEFAULT_ADMIN_ID : adminId;
        String operateName = StringUtils.isBlank(adminName) ? Constants.DEFAULT_ADMIN_ID : adminName;
        equityBenefitGroupDO.setEditAdminId(operateId);
        equityBenefitGroupDO.setEditAdminName(operateName);
        equityBenefitGroupDO.setOpAdminId(operateId);
        equityBenefitGroupDO.setOpAdminName(operateName);

        return equityBenefitGroupDO;

    }

    public static List<UserTagInfoSaveDTO> convertToRuleTagInfoSaves(Long benefitGroupId, Long benefitId,
                                                                     StrategyBenefitGroupRulesDTO benefitGroupRulesDTO) {
        List<UserTagInfoDTO> userTagInfos = Optional.ofNullable(benefitGroupRulesDTO)
                .map(StrategyBenefitGroupRulesDTO::getUserTagR)
                .map(e -> e.get(benefitGroupId))
                .map(e -> e.get(benefitId))
                .map(UserTagRuleDTO::getUserTagInfos)
                .orElse(null);
        return CollectionUtils.isEmpty(userTagInfos) ? null : userTagInfos.stream()
                .map(e -> UserTagInfoSaveDTO.builder().ruleId(e.getRuleId()).ruleName(e.getRuleName()).build())
                .collect(Collectors.toList());
    }

    public static Map</* benefitId */Long, UserTagRuleDTO> convertToUserTagRuleDTOMap(EquityBenefitGroupRuleDO groupRuleDO,
                                                                                      Map<Long /* benefitId */, UserTagRuleDTO> userTagRuleMap) {
        if (Objects.isNull(groupRuleDO)) {
            return userTagRuleMap;
        }
        Long strategyId = groupRuleDO.getStrategyId();
        Long benefitGroupId = groupRuleDO.getBenefitGroupId();
        Long benefitId = groupRuleDO.getBenefitId();
        String ruleValue = groupRuleDO.getRuleValue();
        UserTagRuleDTO userTagRuleDTO = JsonUtil.parseObject(ruleValue, UserTagRuleDTO.class);
        if (Objects.isNull(userTagRuleDTO)) {
            log.warn("convertToUserTagRuleDTOMap userTagRuleDTO is null strategyId={}, benefitGroupId={}, benefitId={}, ruleValue={}",
                    strategyId, benefitGroupId, benefitId, ruleValue);
            return userTagRuleMap;
        }
        userTagRuleMap = MapUtils.isEmpty(userTagRuleMap) ? Maps.newHashMap() : userTagRuleMap;
        userTagRuleMap.put(benefitId, userTagRuleDTO);
        return userTagRuleMap;
    }
}
