package com.ddmc.equity.domain.converter.scene_action;

import com.ddmc.equity.common.config.MapstructBaseMapperConfig;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionBizDataDTO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/7/18 19:33
 * @description
 */
@Mapper(config = MapstructBaseMapperConfig.class)
public interface SceneActionBizDataConverter {

    SceneActionBizDataConverter INSTANCE = Mappers.getMapper(SceneActionBizDataConverter.class);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
            nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS
    )
    void merge(@MappingTarget SceneActionBizDataDTO target, SceneActionBizDataDTO source);
}
