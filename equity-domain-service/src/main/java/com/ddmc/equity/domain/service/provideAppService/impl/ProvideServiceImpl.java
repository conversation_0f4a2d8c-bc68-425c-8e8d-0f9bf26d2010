package com.ddmc.equity.domain.service.provideAppService.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csoss.monitor.api.common.AttributeKey;
import com.csoss.monitor.api.common.Attributes;
import com.csoss.monitor.api.internal.Instrumentation;
import com.csoss.monitor.api.metrics.Metrics;
import com.csoss.monitor.api.trace.Span;
import com.csoss.monitor.api.trace.Traces;
import com.csoss.monitor.sdk.resource.AttributeKeys;
import com.ddmc.equity.common.constant.ActivityConfigConstants;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.*;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.exception.ApiBusinessException;
import com.ddmc.equity.common.util.*;
import com.ddmc.equity.domain.dto.SendMqDTO;
import com.ddmc.equity.domain.dto.benefit_group.BaseInfoDTO;
import com.ddmc.equity.domain.dto.benefit_group.BenefitGroupInfoDTO;
import com.ddmc.equity.domain.dto.rule.condition.CityRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.domain.entity.activity.EquityActivityEntity;
import com.ddmc.equity.domain.entity.benefit.EquityBenefitEntity;
import com.ddmc.equity.domain.entity.benefit_group.EquityBenefitGroupEntity;
import com.ddmc.equity.domain.entity.rule.EquityRuleEntity;
import com.ddmc.equity.domain.entity.scene.EquitySceneEntity;
import com.ddmc.equity.domain.entity.strategy.EquityStrategyEntity;
import com.ddmc.equity.domain.service.activity.EquityActivityDomainService;
import com.ddmc.equity.domain.service.benefit.EquityBenefitDomainService;
import com.ddmc.equity.domain.service.benefitMapping.BenefitMappingService;
import com.ddmc.equity.domain.service.benefit_group.EquityBenefitGroupDomainService;
import com.ddmc.equity.domain.service.provideAppService.ProvideService;
import com.ddmc.equity.domain.service.rule.EquityRuleService;
import com.ddmc.equity.domain.service.scene.EquitySceneDomainService;
import com.ddmc.equity.domain.service.strategy.EquityStrategyDomainService;
import com.ddmc.equity.dto.business.provide.ProvideActivityDTO;
import com.ddmc.equity.dto.business.provide.ProvideBenefitDTO;
import com.ddmc.equity.dto.business.provide.ProvideSaveReqDTO;
import com.ddmc.equity.dto.business.provide.ProvideStrategyDTO;
import com.ddmc.equity.dto.business.rule.*;
import com.ddmc.equity.enums.*;
import com.ddmc.equity.infra.repository.dao.*;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitStockFlowMapper;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitStockPlanMapper;
import com.ddmc.promocore.admin.vo.ActivityVO;
import com.ddmc.promocore.admin.vo.PrizeVO;
import com.ddmc.promoequity.client.EquityAdminClient;
import com.ddmc.station.client.ServiceStationClient;
import com.ddmc.voucherprod.client.TicketAdminClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProvideServiceImpl implements ProvideService {

    @Autowired
    private EquitySceneDomainService equitySceneDomainService;
    @Autowired
    private EquityActivityDomainService equityActivityDomainService;
    @Autowired
    private EquityStrategyDomainService equityStrategyDomainService;
    @Autowired
    private EquityRuleService equityRuleService;
    @Autowired
    private EquityBenefitGroupDomainService equityBenefitGroupDomainService;
    @Autowired
    private TransactionUtil transactionUtil;
    @Autowired
    private EquityBenefitDomainService equityBenefitDomainService;

    @Resource
    private EquityBenefitStockFlowMapper equityBenefitStockFlowMapper;

    @Resource
    private EquityBenefitStockPlanMapper equityBenefitStockPlanMapper;

    @Autowired
    private BenefitMappingService benefitMappingService;

    @Resource
    private ProvideService self;

    @Resource
    private TicketAdminClient ticketAdminClient;
    @Resource
    private EquityAdminClient equityAdminClient;
    @Resource
    private ServiceStationClient serviceStationClient;

    /**
     * 供给保存
     *
     * @param req ProvideSaveReqDTO
     * @return Long
     */
    @Override
    public boolean saveOrUpdate(ProvideSaveReqDTO req) {
        //参数校验
        checkParam(req);
        //查询场景
        EquitySceneDO equitySceneDO = equitySceneDomainService.getSceneByCode(req.getSceneCode());
        //场景
        EquitySceneEntity equitySceneEntity = new EquitySceneEntity();
        //场景校验
        equitySceneEntity.checkSceneIsExistAndIsAlreadyPublish(equitySceneDO);
        try {
            List<SendMqDto> mqDtoList = new ArrayList<>();
            boolean flag = transactionUtil.transactional(s -> {
                List<SendMqDto> currSendMqs = saveActivityList(req.getSceneCode(), req.getActivityDTOList(), req.getAdminId(), req.getAdminName());
                if (CollectionUtils.isNotEmpty(currSendMqs)) {
                    mqDtoList.addAll(currSendMqs);
                }
            });
            if (CollectionUtils.isNotEmpty(mqDtoList)) {
                mqDtoList.forEach(x -> sendMq(x.getSendMqDTOList(), x.getInsertDo(), x.getActivityId()));
            }
            return flag;
        } catch (Exception e) {
            log.error("provideService saveOrUpdate exception. req={}", JsonUtil.toJsonString(req), e);
            return false;
        }
    }

    @Override
    public Boolean saveOrUpdateByPromoCore(ActivityVO activityDTO, String sceneCode, boolean useInputLimitCity,
                                           Map<Long /* oldEquityId */ , List<String>> ticketLimitCityMap,
                                           Map<Long /* oldEquityId */, List<ProvideBenefitDTO>> provideBenefitMap) {
        List<PrizeVO> prizeDTOList = activityDTO.getPrizes();
        if (CollectionUtils.isEmpty(prizeDTOList)) {
            //TODO 打点
            log.error("同步活动奖品数据数据异常,奖品数据不存在,activityId:{}", activityDTO.getActivityId());
            throw AdminExceptionBuilder.build(ExceptionEnum.ACTIVITY_DATA_ERROR);
        }
        log.debug("开始解析活动奖品数据,activityId:{},state:{}", activityDTO.getActivityId(), activityDTO.getState());
        ProvideSaveReqDTO provideSaveReqDTO = createProvideSaveReqDTO(sceneCode);
        //创建活动
        EquityActivityEntity equityActivityEntity = new EquityActivityEntity();
        EquityActivityDO equityActivityDO = equityActivityDomainService.queryByThirdTypeAndId(ExternalTypeEnum.GAME.getType(), activityDTO.getActivityId());
        ProvideActivityDTO provideActivityDTO = equityActivityEntity.createEquityActivityDO(ExternalTypeEnum.GAME.getType(), activityDTO, equityActivityDO);
        EquityStrategyEntity equityStrategyEntity = equityActivityEntity.buildStrategyEntity();
        //查询数据库已有策略，key为为玩法奖品ID
        Map<String, EquityStrategyDO> equityStrategyDOMap = getActivityEquityStrategyDOMap(provideActivityDTO.getActivityId());
        List<ProvideStrategyDTO> provideStrategyDTOList = useInputLimitCity ?
                equityStrategyEntity.createProvideStrategyDTOListWithLimitCityList(activityDTO, provideActivityDTO.getActivityId(), equityStrategyDOMap, ticketLimitCityMap, provideBenefitMap)
                : equityStrategyEntity.createProvideStrategyDTOList(activityDTO, provideActivityDTO.getActivityId(), equityStrategyDOMap, equityAdminClient, ticketAdminClient, serviceStationClient);
        provideActivityDTO.setStrategyDTOList(provideStrategyDTOList);
        //如果有中国银行的人群，需要特殊处理
        if (provideStrategyDTOList.stream().anyMatch(ps -> ps.getRuleDTO() != null && ps.getRuleDTO().getOldSysBOCUserDTO() != null)) {
            provideActivityDTO.setSendType(ActivitySendTypeEnum.SPECIAL_PRIORITY.getCode());
        }

        provideSaveReqDTO.setActivityDTOList(Collections.singletonList(provideActivityDTO));
        log.info("解析活动奖品数据结束,activityId:{},开始调用权益同步活动信息,provideSaveReqDTO:{}", activityDTO.getActivityId(), JSON.toJSONString(provideSaveReqDTO));
        return self.saveOrUpdate(provideSaveReqDTO);
    }

    /****
     * 获取权益下的所有的策略Map key位外部ID
     * @param activityId Long
     * @return Map
     */
    private Map<String, EquityStrategyDO> getActivityEquityStrategyDOMap(Long activityId) {
        Map<String, EquityStrategyDO> equityStrategyDOMap = new HashMap<>();
        if (Objects.nonNull(activityId)) {
            List<EquityStrategyDO> equityStrategyDOList = equityStrategyDomainService.queryEquityStrategyDOByActivityId(activityId);
            if (!CollectionUtils.isEmpty(equityStrategyDOList)) {
                equityStrategyDOMap = equityStrategyDOList.stream().collect(Collectors.toMap(EquityStrategyDO::getExternalId, a -> a));
            }
        }
        return equityStrategyDOMap;
    }


    private ProvideSaveReqDTO createProvideSaveReqDTO(@NotNull String sceneCode) {
        ProvideSaveReqDTO provideSaveReqDTO = new ProvideSaveReqDTO();
        provideSaveReqDTO.setSceneCode(sceneCode);
        return provideSaveReqDTO;
    }

    /**
     * 保存信息
     *
     * @param sceneCode    场景code
     * @param activityDTOS 活动集合
     * @param adminId      操作人id
     * @param adminName    操作人名称
     */
    private List<SendMqDto> saveActivityList(String sceneCode, List<ProvideActivityDTO> activityDTOS, String adminId, String adminName) {
        List<SendMqDto> mqDtoList = new ArrayList<>();
        for (ProvideActivityDTO provideActivityDTO : activityDTOS) {
            //活动转换
            EquityActivityEntity activityEntity = new EquityActivityEntity();
            EquityActivityDO equityActivityDO = activityEntity.convertToDO(provideActivityDTO, sceneCode, adminId, adminName);
            equityActivityDO.setSceneCode(sceneCode);
            Long activityId = equityActivityDomainService.insertOrUpdate(equityActivityDO);
            for (ProvideStrategyDTO provideStrategyDTO : provideActivityDTO.getStrategyDTOList()) {
                //策略组装
                EquityStrategyEntity equityStrategyEntity = new EquityStrategyEntity();
                EquityStrategyDO equityStrategyDO = equityStrategyEntity.convertToDO(provideStrategyDTO, activityId, adminId, adminName);
                //策略保存
                Long strategyId = equityStrategyDomainService.insertOrUpdate(equityStrategyDO);
                //权益保存
                EquityBenefitEntity equityBenefitEntity = new EquityBenefitEntity();
                //待保存权益数据
                List<EquityBenefitDO> needCreateBenefitList = equityBenefitEntity.convertToNeedCreateBenefitList(provideStrategyDTO.getBenefitDTOList());
                //过滤出真实需要保存的数据
                List<EquityBenefitDO> insertBenefitList = filterRealNeedSaveOrUpdate(needCreateBenefitList);
                //权益保存
                equityBenefitDomainService.batchInsert(insertBenefitList);
                //权益组
                List<ProvideBenefitDTO> benefitDTOList = provideStrategyDTO.getBenefitDTOList();
                EquityBenefitGroupEntity benefitGroupEntity = new EquityBenefitGroupEntity();
                EquityBenefitGroupDO equityBenefitGroupDO = equityBenefitGroupDomainService.getEquityBenefitGroupDOByStrategyId(strategyId);
                equityBenefitGroupDO = benefitGroupEntity.convertToEquityBenefitGroupDO(strategyId, benefitDTOList, adminId, adminName, needCreateBenefitList, equityBenefitGroupDO);
                equityBenefitGroupDomainService.insertOrUpdate(equityBenefitGroupDO);
                //规则
                RuleDTO ruleDTO = provideStrategyDTO.getRuleDTO();
                //规则数据组装
                EquityRuleEntity equityRuleEntity = EquityRuleEntity.builder().build();
                List<EquityRuleDO> equityRuleDOS = equityRuleEntity.convertRuleReqToRuleDOList(activityId, strategyId, ruleDTO, adminId, adminName);
                //规则保存
                List<EquityRuleDO> equityRuleDOList = equityRuleService.queryEquityRuleDOListByStrategyId(strategyId);
                List<SendMqDTO> sendMqDTOList = new ArrayList<>();
                Pair<List<EquityRuleDO>, List<EquityRuleDO>> pair = compareList(equityRuleDOList, equityRuleDOS, sendMqDTOList);
                log.info("规则保存结束 创建/更新列表信息 activityId:{} pair:{} ", activityId, pair);
                equityRuleService.batchInsertAndUpdate(pair.getFirst(), pair.getSecond());
                // 如果规则不存在了，则需要删除
                List<EquityRuleDO> deleteEquityRules = getDeleteEquityRules(equityRuleDOList, equityRuleDOS);
                equityRuleService.batchDelete(deleteEquityRules);
                //新增同步更新，修改异步更新，新增是同一个表，不存在dal 问题
                sendMq(new ArrayList<>(), pair.getFirst(), activityId);
                mqDtoList.add(new SendMqDto(sendMqDTOList, new ArrayList<>(), activityId));
                benefitMappingService.addMapping(needCreateBenefitList, strategyId, provideStrategyDTO.getBenefitDTOList());
            }
        }
        return mqDtoList;
    }

    @Data
    public static class SendMqDto {

        public SendMqDto(List<SendMqDTO> sendMqDTOList, List<EquityRuleDO> insertDo, Long activityId) {
            this.sendMqDTOList = sendMqDTOList;
            this.insertDo = insertDo;
            this.activityId = activityId;
        }

        private List<SendMqDTO> sendMqDTOList;
        private List<EquityRuleDO> insertDo;
        private Long activityId;
    }

    private void sendMq(List<SendMqDTO> sendMqDTOList, List<EquityRuleDO> insertDo, Long activityId) {

        try {
            for (SendMqDTO x : sendMqDTOList) {
                if (!sendUpdateMQ(activityId, x.getPairMap(), x.getEquityRuleDO())) {
                    log.error("发送库存变更消息异常, strategyId:{} RuleType:{},变更数据:{}", x.getEquityRuleDO().getStrategyId(), x.getEquityRuleDO().getRuleType(), JSON.toJSON(x.getPairMap()));
                    throw new ApiBusinessException(ExceptionEnum.CACHE_UPDATE_ERROR);
                }
            }
            for (EquityRuleDO x : insertDo) {
                if (Objects.equals(RuleTypeEnum.STOCK_LIMIT_RULE.getCode(), x.getRuleType())
                        || Objects.equals(RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode(), x.getRuleType())) {
                    if (!sendInsertMQ(activityId, x)) {
                        log.error("发送库存变更消息异常, strategyId:{} RuleType:{},变更数据:{}", x.getStrategyId(), x.getRuleType(), JSON.toJSON(x));
                        logEventForOperateBenefit("equity_benefit_add", "error", x);
                        throw new ApiBusinessException(ExceptionEnum.CACHE_INSERT_ERROR);
                    }
                }
            }
        } catch (Exception ex) {
            Map<String, Object> map = new HashMap();
            map.put("sendMqDTOList", sendMqDTOList);
            map.put("insertDo", insertDo);
            map.put("activityId", activityId);
            logEventForOperateBenefit("equity_benefit_update", "error", map);
        }
    }

    private static void logEventForOperateBenefit(
            String type, @NotNull String name, @NotNull Object object) {
        Span span = Traces.spanBuilder(type, Instrumentation.EVENT_NEW).startSpan();
        Metrics.newCounter(type, Instrumentation.EVENT_NEW).build().once(Attributes.of(AttributeKey.stringKey("name"), name));
        Attributes attributes = Attributes.of(AttributeKey.stringKey("name"), name,
                AttributeKey.stringKey(AttributeKeys.METRIC_STATUS), "1");
        span.metricsMapping(type, attributes);
        span.addEvent("event_data", Attributes.of(AttributeKey.stringKey("context"), JSON.toJSONString(object)));
        span.end();
    }

    private Pair<List<EquityRuleDO>, List<EquityRuleDO>> compareList(List<EquityRuleDO> sourceList, List<EquityRuleDO> targetList, List<SendMqDTO> sendMqDTOList) {
        List<EquityRuleDO> insertList = new ArrayList<>();
        List<EquityRuleDO> updateList = new ArrayList<>();
        Map<Integer, EquityRuleDO> sourceMap = sourceList.stream().collect(Collectors.toMap(EquityRuleDO::getRuleType, a -> a));
        for (EquityRuleDO x : targetList) {
            Map<String, Pair<Object, Object>> pairMap = null;
            EquityRuleDO dbEquityRuleDO = sourceMap.get(x.getRuleType());
            if (Objects.nonNull(dbEquityRuleDO)) {
                x.setId(dbEquityRuleDO.getId());
                updateList.add(x);
                if (Objects.equals(RuleTypeEnum.STOCK_LIMIT_RULE.getCode(), x.getRuleType())) {
                    StockLimitDTO stockLimitDTOSource = JSON.parseObject(sourceMap.get(x.getRuleType()).getRuleValue(), StockLimitDTO.class);
                    StockLimitDTO stockLimitDTOTarget = JSON.parseObject(x.getRuleValue(), StockLimitDTO.class);
                    pairMap = BeanUtil.beanHavingChangeAndReturn(stockLimitDTOSource, stockLimitDTOTarget, ActivityConfigConstants.STOCK_LIMIT_RULE_COLUMN_LIST);
                } else if (Objects.equals(RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode(), x.getRuleType())) {
                    ReceiveLimitDTO receiveLimitDTOSource = JSON.parseObject(sourceMap.get(x.getRuleType()).getRuleValue(), ReceiveLimitDTO.class);
                    ReceiveLimitDTO receiveLimitDTOTarget = JSON.parseObject(x.getRuleValue(), ReceiveLimitDTO.class);
                    pairMap = BeanUtil.beanHavingChangeAndReturn(receiveLimitDTOSource, receiveLimitDTOTarget, ActivityConfigConstants.RECEIVE_LIMIT_RULE_COLUMN_LIST);
                } else if (Objects.equals(RuleTypeEnum.CITY_RULE.getCode(), x.getRuleType())) {
                    CityRuleDTO cityRuleDTOSource = JSON.parseObject(sourceMap.get(x.getRuleType()).getRuleValue(), CityRuleDTO.class);
                    CityRuleDTO cityRuleDTOTarget = JSON.parseObject(x.getRuleValue(), CityRuleDTO.class);
                    pairMap = BeanUtil.beanHavingChangeAndReturn(cityRuleDTOSource, cityRuleDTOTarget, ActivityConfigConstants.CITY_RULE_COLUMN_LIST);
                }
                if (MapUtils.isNotEmpty(pairMap)) {
                    SendMqDTO sendMqDTO = new SendMqDTO();
                    sendMqDTO.setEquityRuleDO(x);
                    sendMqDTO.setPairMap(pairMap);
                    sendMqDTOList.add(sendMqDTO);
                }
            } else {
                insertList.add(x);
            }

        }
        return Pair.of(insertList, updateList);
    }

    /***
     * 发送MQ
     * key 权益类型
     * Pair<Object,Object> first : 原先值 second: 修改后的值
     *
     * @param pairMap Map
     */
    private boolean sendUpdateMQ(Long activityId, Map<String, Pair<Object, Object>> pairMap, EquityRuleDO equityRuleDO) {
        boolean isUpdateStockOnly = false;
        boolean isUpdatePlanStockOnly = false;
        boolean isUpdateStockAndPlanStock = false;
        boolean hasTotalStockUpdate = pairMap.containsKey("totalStock");
        boolean hasDayStockUpdate = pairMap.containsKey("dayStock");
        if (hasTotalStockUpdate && !hasDayStockUpdate) {
            isUpdateStockOnly = true;
        } else if (!hasTotalStockUpdate && hasDayStockUpdate) {
            isUpdatePlanStockOnly = true;
        } else if (hasTotalStockUpdate) {
            isUpdateStockAndPlanStock = true;
        }
        if (!isUpdateStockOnly && !isUpdatePlanStockOnly && !isUpdateStockAndPlanStock) {
            log.info("不需要更新总库存、计划库存 activityId:{} strategyId:{} pairMap:{}", activityId, equityRuleDO.getStrategyId(), pairMap);
            return true;
        }
        List<Long> benefitIds = getBenefitIdsByRuleDO(equityRuleDO);
        if (CollectionUtils.isEmpty(benefitIds)) {
            log.error("未查询到任何关联权益信息 activityId:{} equityRuleDO:{} pairMap:{}", activityId, equityRuleDO, pairMap);
            return false;
        }
        if (isUpdateStockOnly || isUpdateStockAndPlanStock) {
            Pair<Object, Object> diff = pairMap.get("totalStock");
            if (StringUtils.isNumeric(diff.getFirst().toString())
                    && StringUtils.isNumeric(diff.getSecond().toString())) {
                long before;
                long after;
                try {
                    before = Long.parseLong(diff.getFirst().toString());
                    after = Long.parseLong(diff.getSecond().toString());
                } catch (Exception e) {
                    log.error("解析变更值出错 activityId:{} strategyId:{} pairMap:{}", activityId, equityRuleDO.getStrategyId(), pairMap);
                    return false;
                }
                if (Objects.equals(before, after)) {
                    log.info("不需要变更 activityId:{} strategyId:{} pairMap:{}", activityId, equityRuleDO.getStrategyId(), pairMap);
                    return true;
                }
                if (before > 0L && after > 0L && after > before) {
                    List<EquityBenefitStockFlowDO> flowList = Lists.newArrayList();
                    for (Long benefitId : benefitIds) {
                        EquityBenefitStockFlowDO equityBenefitStockFlowDO = new EquityBenefitStockFlowDO();
                        equityBenefitStockFlowDO.setActivityId(activityId);
                        equityBenefitStockFlowDO.setStrategyId(equityRuleDO.getStrategyId());
                        equityBenefitStockFlowDO.setBenefitId(benefitId);
                        equityBenefitStockFlowDO.setPlanStockId(0L);// 更新总库存不需要这个字段
                        equityBenefitStockFlowDO.setIsHandle(CommonEnum.INTEGER_BOOL.NO.getCode());
                        equityBenefitStockFlowDO.setOperationType(StockOperationEnum.EXPEND.getOperation());
                        equityBenefitStockFlowDO.setPlanDateType(PlanDateTypeEnum.UNDEFINED.getType());// 更新总库存不需要这个字段
                        equityBenefitStockFlowDO.setReqNo(Strings.EMPTY);
                        equityBenefitStockFlowDO.setPlanOperationNum(0L);
                        equityBenefitStockFlowDO.setOperationNum(after - before);
                        equityBenefitStockFlowDO.setIsDelete(CommonEnum.INTEGER_BOOL.NO.getCode());
                        equityBenefitStockFlowDO.setCreateTime(new Date());
                        equityBenefitStockFlowDO.setUpdateTime(new Date());
                        equityBenefitStockFlowDO.setLoadDb(0);
                        equityBenefitStockFlowDO.setLoadCache(0);
                        flowList.add(equityBenefitStockFlowDO);
                    }
                    equityBenefitStockFlowMapper.insertBatchSomeColumn(flowList);
                }
            }
        }
        if (isUpdatePlanStockOnly || isUpdateStockAndPlanStock) {
            Pair<Object, Object> diff = pairMap.get("dayStock");
            if (StringUtils.isNumeric(diff.getFirst().toString())
                    && StringUtils.isNumeric(diff.getSecond().toString())) {
                long before;
                long after;
                try {
                    before = Long.parseLong(diff.getFirst().toString());
                    after = Long.parseLong(diff.getSecond().toString());
                } catch (Exception e) {
                    log.error("解析变更值出错 activityId:{} strategyId:{} pairMap:{}", activityId, equityRuleDO.getStrategyId(), pairMap);
                    return false;
                }
                if (Objects.equals(before, after)) {
                    log.info("不需要变更 activityId:{} strategyId:{} pairMap:{}", activityId, equityRuleDO.getStrategyId(), pairMap);
                    return true;
                }
                if (before > 0L && after > 0L && after > before) {
                    for (Long benefitId : benefitIds) {
                        // 计算影响范围
                        List<EquityBenefitStockPlanDO> equityBenefitStockPlanDOList = equityBenefitStockPlanMapper.selectList(new LambdaQueryWrapper<EquityBenefitStockPlanDO>()
                                        .eq(EquityBenefitStockPlanDO::getStrategyId, equityRuleDO.getStrategyId())
                                        .eq(EquityBenefitStockPlanDO::getBenefitId, benefitId)
                                        .eq(EquityBenefitStockPlanDO::getActivityId, activityId)
                                        .last(" and STR_TO_DATE(plan_date, '%Y%m%d') >= '" + DateUtil.format_yyyyMMdd(new Date()) + "'")
//                               .eq(EquityBenefitStockPlanDO::getPlanDate, DateUtil.format_yyyyMMdd(new Date()))
                        );
                        if (CollectionUtils.isNotEmpty(equityBenefitStockPlanDOList)) {
                            List<EquityBenefitStockFlowDO> equityBenefitStockFlowDOList = equityBenefitStockPlanDOList.stream().map(planDO -> {
                                EquityBenefitStockFlowDO equityBenefitStockFlowDO = new EquityBenefitStockFlowDO();
                                equityBenefitStockFlowDO.setActivityId(activityId);
                                equityBenefitStockFlowDO.setStrategyId(equityRuleDO.getStrategyId());
                                equityBenefitStockFlowDO.setBenefitId(benefitId);
                                equityBenefitStockFlowDO.setPlanStockId(planDO.getId());
                                equityBenefitStockFlowDO.setIsHandle(CommonEnum.INTEGER_BOOL.NO.getCode());
                                equityBenefitStockFlowDO.setOperationType(StockOperationEnum.EXPEND_PLAN.getOperation());
                                equityBenefitStockFlowDO.setPlanDateType(PlanDateTypeEnum.DAY.getType());
                                equityBenefitStockFlowDO.setReqNo(Strings.EMPTY);
                                equityBenefitStockFlowDO.setPlanOperationNum(0L); // 扩大库存不用
                                equityBenefitStockFlowDO.setOperationNum(after - before);
                                equityBenefitStockFlowDO.setCreateTime(new Date());
                                equityBenefitStockFlowDO.setUpdateTime(new Date());
                                equityBenefitStockFlowDO.setLoadDb(0);
                                equityBenefitStockFlowDO.setLoadCache(0);
                                equityBenefitStockFlowDO.setIsDelete(CommonEnum.INTEGER_BOOL.NO.getCode());
                                return equityBenefitStockFlowDO;
                            }).collect(Collectors.toList());
                            equityBenefitStockFlowMapper.insertBatchSomeColumn(equityBenefitStockFlowDOList);
                            return true;
                        } else {
                            log.error("查询不到需扩容的计划库存 activityId:{} equityRuleDO:{} pairMap:{}", activityId, equityRuleDO, pairMap);
                            throw new RuntimeException("查询计划库存表为空, 数据不一致");
                        }
                    }

                }
            }
        }
        log.info("不支持的操作模式");
        return false;
    }

    /***
     * 规则对象
     * @param equityRuleDO EquityRuleDO
     * @return Boolean
     */
    private Boolean sendInsertMQ(Long activityId, EquityRuleDO equityRuleDO) {
        // 初始化库存与计划库存
        // 2023.3.2 改为直接插流水表，包在一个事务里
        if (RuleTypeEnum.STOCK_LIMIT_RULE.getCode().equals(equityRuleDO.getRuleType())) {
            StockLimitRuleDTO stockLimitRuleDTO;
            try {
                stockLimitRuleDTO = JSON.parseObject(equityRuleDO.getRuleValue(), StockLimitRuleDTO.class);
            } catch (Exception e) {
                log.error("解析库存限制出错 activityId:{} equityRuleDO:{}", activityId, equityRuleDO, e);
                return false;
            }
            if (stockLimitRuleDTO.getTotalStock() <= 0L) {
                log.error("总库存设置小于0 activityId:{} equityRuleDO:{} stockLimitRuleDTO:{}", activityId, equityRuleDO, stockLimitRuleDTO);
                return false;
            }
            List<Long> benefitIds = getBenefitIdsByRuleDO(equityRuleDO);
            if (CollectionUtils.isEmpty(benefitIds)) {
                return false;
            }
            Integer planDateType = stockLimitRuleDTO.getPlanDateType();
            List<EquityBenefitStockFlowDO> flowList = Lists.newArrayList();
            for (Long benefitId : benefitIds) {
                // 插流水表
                EquityBenefitStockFlowDO equityBenefitStockFlowDO = new EquityBenefitStockFlowDO();
                equityBenefitStockFlowDO.setActivityId(activityId);
                equityBenefitStockFlowDO.setStrategyId(stockLimitRuleDTO.getStrategyId());
                equityBenefitStockFlowDO.setBenefitId(benefitId);
                equityBenefitStockFlowDO.setPlanStockId(0L);// 初始化总库存不需要这个字段
                equityBenefitStockFlowDO.setIsHandle(CommonEnum.INTEGER_BOOL.NO.getCode());
                equityBenefitStockFlowDO.setOperationType(StockOperationEnum.INIT.getOperation());
                equityBenefitStockFlowDO.setReqNo(Strings.EMPTY);
                if (PlanDateTypeEnum.DAY.getType().equals(planDateType)) {
                    equityBenefitStockFlowDO.setPlanDateType(planDateType);
                    equityBenefitStockFlowDO.setPlanOperationNum(stockLimitRuleDTO.getDayStock());
                } else if (PlanDateTypeEnum.WEEK.getType().equals(planDateType)) {
                    equityBenefitStockFlowDO.setPlanDateType(planDateType);
                    equityBenefitStockFlowDO.setPlanOperationNum(stockLimitRuleDTO.getWeekStock());
                } else if (PlanDateTypeEnum.MONTH.getType().equals(planDateType)) {
                    equityBenefitStockFlowDO.setPlanDateType(planDateType);
                    equityBenefitStockFlowDO.setPlanOperationNum(stockLimitRuleDTO.getMonthStock());
                } else if (PlanDateTypeEnum.YEAR.getType().equals(planDateType)) {
                    equityBenefitStockFlowDO.setPlanDateType(planDateType);
                    equityBenefitStockFlowDO.setPlanOperationNum(stockLimitRuleDTO.getYearStock());
                } else {
                    equityBenefitStockFlowDO.setPlanDateType(PlanDateTypeEnum.UNDEFINED.getType());
                    equityBenefitStockFlowDO.setPlanOperationNum(0L);
                }
                equityBenefitStockFlowDO.setOperationNum(Long.valueOf(stockLimitRuleDTO.getTotalStock()));
                equityBenefitStockFlowDO.setCreateTime(new Date());
                equityBenefitStockFlowDO.setUpdateTime(new Date());
                equityBenefitStockFlowDO.setIsDelete(CommonEnum.INTEGER_BOOL.NO.getCode());
                equityBenefitStockFlowDO.setLoadDb(0);
                equityBenefitStockFlowDO.setLoadCache(0);
                flowList.add(equityBenefitStockFlowDO);
            }
            // 如果插流水表失败，直接抛出异常回滚了
            equityBenefitStockFlowMapper.insertBatchSomeColumn(flowList);
            return true;
        }
        return true;
    }

    @Nullable
    private List<Long> getBenefitIdsByRuleDO(EquityRuleDO equityRuleDO) {
        List<EquityBenefitGroupDO> benefitGroupDOS = equityBenefitGroupDomainService.getEquityBenefitGroupDOListByStrategyId(equityRuleDO.getStrategyId());
        if (CollectionUtils.isEmpty(benefitGroupDOS)) {
            log.error("权益没有关联权益组 equityRuleDO:{} benefitGroupDOS:{}", equityRuleDO, benefitGroupDOS);
            return null;
        }
        List<String> benefitInfoList = benefitGroupDOS.stream().map(EquityBenefitGroupDO::getBenefitInfo).collect(Collectors.toList());
        List<BenefitGroupInfoDTO> benefitInfoDTOList = Lists.newArrayList();
        for (String benefitInfo : benefitInfoList) {
            try {
                benefitInfoDTOList.add(JSON.parseObject(benefitInfo, BenefitGroupInfoDTO.class));
            } catch (Exception e) {
                log.error("解析权益信息出错", e);
            }
        }
        if (CollectionUtils.isEmpty(benefitInfoDTOList)) {
            log.error("权益没有关联权益组 equityRuleDO:{} benefitGroupDOS:{}", equityRuleDO, benefitGroupDOS);
            return null;
        }
        List<Long> benefitIds = Lists.newArrayList();
        for (BenefitGroupInfoDTO benefitGroupInfoDTO : benefitInfoDTOList) {
            if (CollectionUtils.isNotEmpty(benefitGroupInfoDTO.getBaseList())) {
                benefitIds.addAll(benefitGroupInfoDTO.getBaseList().stream().map(BaseInfoDTO::getId).collect(Collectors.toList()));
            }
        }
        return benefitIds;
    }

    /**
     * 过滤出
     *
     * @param needCreateBenefitList 需要保存的权益
     * @return 真实需要保存的
     */
    private List<EquityBenefitDO> filterRealNeedSaveOrUpdate(List<EquityBenefitDO> needCreateBenefitList) {
        List<EquityBenefitDO> filterList = new ArrayList<>();
        if (CollectionUtils.isEmpty(needCreateBenefitList)) {
            return Lists.newArrayList();
        }
        needCreateBenefitList.forEach(x -> {
            EquityBenefitDO equityBenefitDO = equityBenefitDomainService.getBenefitByUni(x.getBenefitType(),
                    x.getBenefitValue(), null);
            if (Objects.isNull(equityBenefitDO)) {
                filterList.add(x);
            } else {
                x.setId(equityBenefitDO.getId());
            }
        });
        return filterList;
    }

    /**
     * 判断券权益是否已存在
     *
     * @param benefitValue 权益值
     * @param alreadyExist 表中权益信息
     * @return 是否已存在
     */
    private boolean benefitIsExist(String benefitValue, List<EquityBenefitDO> alreadyExist) {
        if (Objects.isNull(benefitValue) || CollectionUtils.isEmpty(alreadyExist)) {
            return false;
        }
        for (EquityBenefitDO equityBenefitDO : alreadyExist) {
            if (equityBenefitDO.getBenefitValue().equals(benefitValue)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 参数校验
     *
     * @param req 请求参数
     */
    private void checkParam(ProvideSaveReqDTO req) {
        Assert.notNull(req, ExceptionEnum.PARAMS_ERROR);
        Assert.notBlank(req.getSceneCode(), ExceptionEnum.PARAMS_ERROR);
        Assert.notEmpty(req.getActivityDTOList(), ExceptionEnum.PARAMS_ERROR);
        List<ProvideActivityDTO> activityDTOS = req.getActivityDTOList();
        for (ProvideActivityDTO provideActivityDTO : activityDTOS) {
            Assert.notBlank(provideActivityDTO.getActivityName(), ExceptionEnum.PARAMS_ERROR);
            Assert.mustTrue(ActivitySendTypeEnum.contains(provideActivityDTO.getSendType()), ExceptionEnum.ACTIVITY_SEND_TYPE_IS_ILLEGAL);
            Assert.notNull(provideActivityDTO.getStartTime(), ExceptionEnum.PARAMS_ERROR);
            Assert.notNull(provideActivityDTO.getEndTime(), ExceptionEnum.PARAMS_ERROR);
            Assert.mustTrue(ActivityStatusEnum.contains(provideActivityDTO.getStatus()), ExceptionEnum.PARAMS_ERROR);
            List<ProvideStrategyDTO> strategyDTOList = provideActivityDTO.getStrategyDTOList();
            Assert.notEmpty(strategyDTOList, ExceptionEnum.PARAMS_ERROR);
            for (ProvideStrategyDTO provideStrategyDTO : strategyDTOList) {
                Assert.notBlank(provideStrategyDTO.getStrategyName(), ExceptionEnum.PARAMS_ERROR);
                Assert.mustTrue(StrategyStatusEnum.contains(provideStrategyDTO.getStatus()), ExceptionEnum.PARAMS_ERROR);
                Assert.notEmpty(provideStrategyDTO.getBenefitDTOList(), ExceptionEnum.PARAMS_ERROR);
                //规则校验
                validateRuleDTO(provideStrategyDTO);
                //权益集合
                List<ProvideBenefitDTO> benefitDTOList = provideStrategyDTO.getBenefitDTOList();
                //是否存在重复的权益
                strategyBenefitIsRepeat(benefitDTOList);
                for (ProvideBenefitDTO provideBenefitDTO : benefitDTOList) {
                    //非全部发放
                    if (!(ActivitySendTypeEnum.WHOLE.getCode().equals(provideActivityDTO.getSendType()) || ActivitySendTypeEnum.SPECIAL_PRIORITY.getCode().equals(provideActivityDTO.getSendType()))) {
                        Assert.mustTrue(NumberUtils.isCreatable(provideBenefitDTO.getWeight()), ExceptionEnum.BENEFIT_WEIGHT_IS_ILLEGAL);
                        Assert.mustTrue(BigDecimalUtil.isGtZero(new BigDecimal(provideBenefitDTO.getWeight())), ExceptionEnum.BENEFIT_WEIGHT_IS_ILLEGAL);
                    }
                    //空的权益id
                    if (Objects.isNull(provideBenefitDTO.getBenefitId())) {
                        Assert.mustTrue(BenefitTypeEnum.contains(provideBenefitDTO.getBenefitType()), ExceptionEnum.BENEFIT_TYPE_IS_NOT_EXIST);
                        Assert.notBlank(provideBenefitDTO.getBenefitValue(), ExceptionEnum.BENEFIT_VALUE_IS_NULL);
                        Assert.notBlank(provideBenefitDTO.getBenefitName(), ExceptionEnum.BENEFIT_NAME_IS_NULL);
                        //非券的话，值必须是数字
                        if (!BenefitTypeEnum.TICKET.getId().equals(provideBenefitDTO.getBenefitType())) {
                            Assert.mustTrue(NumberUtils.isCreatable(provideBenefitDTO.getBenefitValue()), ExceptionEnum.BENEFIT_VALUE_IS_ILLEGAL);
                            Assert.mustTrue(BigDecimalUtil.isGtZero(new BigDecimal(provideBenefitDTO.getBenefitValue())), ExceptionEnum.BENEFIT_VALUE_IS_ILLEGAL);
                        }
                        continue;
                    }
                    EquityBenefitDO equityBenefitDO = equityBenefitDomainService.queryById(provideBenefitDTO.getBenefitId());
                    Assert.notNull(equityBenefitDO, ExceptionEnum.BENEFIT_IS_NOT_EXIST);
                }

            }
        }
    }

    /**
     * 是否存在重复的权益
     *
     * @param benefitDTOList 权益信息
     */
    private void strategyBenefitIsRepeat(List<ProvideBenefitDTO> benefitDTOList) {
        Set<Long> benefitIdSet = new HashSet<>();
        Set<String> benefitValueSet = new HashSet<>();
        for (ProvideBenefitDTO provideBenefitDTO : benefitDTOList) {
            if (Objects.nonNull(provideBenefitDTO.getBenefitId())) {
                benefitIdSet.add(provideBenefitDTO.getBenefitId());
                continue;
            }
            if (StringUtils.isNotBlank(provideBenefitDTO.getBenefitValue())) {
                benefitValueSet.add(provideBenefitDTO.getBenefitValue().trim());
            }
        }
        int filterSize = benefitIdSet.size() + benefitValueSet.size();
        if (filterSize != benefitDTOList.size()) {
            throw new ApiBusinessException(ExceptionEnum.STRATEGY_EXIST_SAME_BENEFIT);
        }
    }

    /**
     * 规则校验
     *
     * @param provideStrategyDTO 规则信息
     */
    private void validateRuleDTO(ProvideStrategyDTO provideStrategyDTO) {
        if (Objects.isNull(provideStrategyDTO.getRuleDTO())) {
            return;
        }
        RuleDTO ruleDTO = provideStrategyDTO.getRuleDTO();
        //ab
        ABTestDTO abTestDTO = ruleDTO.getAbTestDTO();
        if (Objects.nonNull(abTestDTO) && StringUtils.isNotBlank(abTestDTO.getLayerId())) {
            Assert.notEmpty(abTestDTO.getChoosePutGroup(), ExceptionEnum.PARAMS_ERROR);
        }
        //app版本
        AppVersionDTO appVersionDTO = ruleDTO.getAppVersionDTO();
        if (Objects.nonNull(appVersionDTO) && Objects.nonNull(appVersionDTO.getJoinType())) {
            Assert.mustTrue(AppVersionJoinTypeEnum.isContains(appVersionDTO.getJoinType()), ExceptionEnum.PARAMS_ERROR);
            List<AppVersionValueDTO> appVersionRuleValueVOList = appVersionDTO.getAppVersionList();
            Assert.notEmpty(appVersionRuleValueVOList, ExceptionEnum.PARAMS_ERROR);
        }
        //会员身份
        VipIdentityDTO vipIdentityDTO = ruleDTO.getVipIdentityDTO();
        if (Objects.nonNull(vipIdentityDTO) && Objects.nonNull(vipIdentityDTO.getUserStatus())) {
            Assert.mustTrue(VipIdentityEnum.contains(vipIdentityDTO.getUserStatus()), ExceptionEnum.VIP_IDENTITY_TYPE_IS_NOT_EXIST);
        }
        //频次限制
        ReceiveLimitDTO receiveLimitDTO = ruleDTO.getReceiveLimitDTO();
        if (Objects.nonNull(receiveLimitDTO)) {
            if (Objects.nonNull(receiveLimitDTO.getLimitType())) {
                provideStrategyDTO.setHasReceiveLimit(Constants.ONE);
                Assert.mustTrue(StrategyBenefitReceiveLimitTypeEnum.isContain(receiveLimitDTO.getLimitType()), ExceptionEnum.RECEIVE_STRATEGY_BENEFIT_LIMIT_TYPE_NOT_EXIST);
                //频次类型
                if (StrategyBenefitReceiveLimitTypeEnum.FREQUENCY.getType().equals(receiveLimitDTO.getLimitType())) {
                    Assert.mustTrue(FrequencyDateTypeEnum.isContain(receiveLimitDTO.getFrequencyDateType()), ExceptionEnum.FREQUENCY_TYPE_IS_NOT_EXIST);
                    Assert.mustTrue((Objects.nonNull(receiveLimitDTO.getFrequencyDateTypeValue()) && receiveLimitDTO.getFrequencyDateTypeValue() > 0), ExceptionEnum.RECEIVE_STRATEGY_BENEFIT_DATE_TYPE_VALUE_IS_ILLEGAL);
                }
                //固定日期
                if (StrategyBenefitReceiveLimitTypeEnum.FIX_DATE.getType().equals(receiveLimitDTO.getLimitType())) {
                    Assert.mustTrue(!CollectionUtils.isEmpty(receiveLimitDTO.getFixDateList()), ExceptionEnum.FIX_DATE_IS_NULL);
                }
            }
        }
        //库存限制
        StockLimitDTO stockLimitDTO = ruleDTO.getStockLimitDTO();
        if (Objects.nonNull(stockLimitDTO)) {
            //总库存
            if (Objects.nonNull(stockLimitDTO.getTotalStock())) {
                Assert.mustTrue(stockLimitDTO.getTotalStock() > 0, ExceptionEnum.STOCK_NUM_IS_ILLEGAL);
            }
            //每日库存
            if (Objects.nonNull(stockLimitDTO.getDayStock())) {
                Assert.mustTrue(stockLimitDTO.getDayStock() > 0, ExceptionEnum.STOCK_NUM_IS_ILLEGAL);
            }

            if (Objects.nonNull(stockLimitDTO.getMonthStock())) {
                Assert.mustTrue(stockLimitDTO.getMonthStock() > 0, ExceptionEnum.STOCK_NUM_IS_ILLEGAL);
            }

            if (Objects.nonNull(stockLimitDTO.getWeekStock())) {
                Assert.mustTrue(stockLimitDTO.getWeekStock() > 0, ExceptionEnum.STOCK_NUM_IS_ILLEGAL);
            }

            if (Objects.nonNull(stockLimitDTO.getYearStock())) {
                Assert.mustTrue(stockLimitDTO.getYearStock() > 0, ExceptionEnum.STOCK_NUM_IS_ILLEGAL);
            }
            provideStrategyDTO.setHasStockLimit(Constants.ONE);
        }
    }

    private List<EquityRuleDO> getDeleteEquityRules(List<EquityRuleDO> sourceEquityRules, List<EquityRuleDO> targetEquityRules) {
        if (CollectionUtils.isEmpty(sourceEquityRules)) {
            return null;
        }
        Map<Integer, EquityRuleDO> targetEquityRuleMap = CollectionUtils.isEmpty(targetEquityRules) ? Maps.newHashMap() : targetEquityRules.stream()
                .collect(Collectors.toMap(EquityRuleDO::getRuleType, Function.identity(), (var1, var2) -> var1));
        // 如果目标规则中没有对应的 ruleType，则表示对应的规则需要删除
        return sourceEquityRules.stream().filter(e -> !targetEquityRuleMap.containsKey(e.getRuleType())).collect(Collectors.toList());
    }
}
