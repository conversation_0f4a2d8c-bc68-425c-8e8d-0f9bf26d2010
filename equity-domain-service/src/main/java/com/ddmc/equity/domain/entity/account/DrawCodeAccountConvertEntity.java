package com.ddmc.equity.domain.entity.account;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.enums.SubAccountStatusEnum;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.ddmc.equity.dto.customer.account.AccountDirectBenefitReqDTO;
import com.ddmc.equity.dto.customer.account.AccountDistributeBenefitReqDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.enums.DirectProvideBenefitExtKeys;
import com.ddmc.equity.enums.DrawCodeAccountExtKeys;
import com.ddmc.equity.enums.DrawCodeSourceEnum;
import com.ddmc.equity.infra.repository.dao.DrawCodeAccountDO;
import com.ddmc.equity.infra.repository.dao.DrawCodeAccountRecordDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.DrawCodeAccountInfoDTO;
import com.ddmc.equity.model.vo.CommentPostTopicMessageVO;
import com.ddmc.equity.model.vo.HistoryTryEatCommentVO;
import com.ddmc.equity.model.vo.VipPaySuccessTopicMessageVO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/5/31 09:51
 * @description
 */
@Slf4j
public class DrawCodeAccountConvertEntity {

    private static DrawCodeAccountDO createAccountDO(EquityAccountContext equityAccountContext, String drawCode) {
        Map<String, Object> extMap = equityAccountContext.getExtMap();
        DrawCodeAccountDO drawCodeAccountDO = new DrawCodeAccountDO();
        drawCodeAccountDO.setBenefitId(equityAccountContext.getBenefitId());
        drawCodeAccountDO.setUserId(equityAccountContext.getUid());
        drawCodeAccountDO.setDrawCode(drawCode);
        drawCodeAccountDO.setSource(equityAccountContext.getDrawCodeSource());
        drawCodeAccountDO.setStatus(SubAccountStatusEnum.UNUSED.getCode());
        drawCodeAccountDO.setLastReqNo(equityAccountContext.getSerialNumber());
        drawCodeAccountDO.setExt(MapUtils.isEmpty(extMap) ? null : JSON.toJSONString(extMap));
        return drawCodeAccountDO;
    }

    public static List<DrawCodeAccountDO> createProvideAccountDOList(EquityAccountContext equityAccountContext, List<String> drawCodes) {
        return drawCodes.stream().map(e -> DrawCodeAccountConvertEntity.createAccountDO(equityAccountContext, e))
                .collect(Collectors.toList());
    }

    public static DrawCodeAccountRecordDO createAccountRecordDO(Integer operateType, EquityAccountContext equityAccountContext) {
        DrawCodeAccountRecordDO drawCodeAccountRecordDO = new DrawCodeAccountRecordDO();
        drawCodeAccountRecordDO.setBenefitId(equityAccountContext.getBenefitId());
        drawCodeAccountRecordDO.setAccountId(equityAccountContext.getAccountId());
        drawCodeAccountRecordDO.setUserId(equityAccountContext.getUid());
        drawCodeAccountRecordDO.setOperateType(operateType);
        drawCodeAccountRecordDO.setStatus(StatusEnum.INIT.getCode());
        drawCodeAccountRecordDO.setAppId(equityAccountContext.getAppId());
        drawCodeAccountRecordDO.setPageId(equityAccountContext.getPageId());
        drawCodeAccountRecordDO.setReqNo(equityAccountContext.getSerialNumber());
        drawCodeAccountRecordDO.setAuxKey(equityAccountContext.getSerialNumber());
        return drawCodeAccountRecordDO;
    }

    public static AccountDistributeBenefitReqDTO createAccountDistributeBenefitReq(VipPaySuccessTopicMessageVO message, String appId,
                                                                                   Integer distributeCount) {
        String userId = message.getUid();
        Integer drawCodeSource = DrawCodeSourceEnum.VIP_OPEN_CARD.getCode();
        Map<String, Object> extMap = Maps.newHashMap();
        extMap.put(DrawCodeAccountExtKeys.VIP_ORDER_NUMBER, message.getOrderNum());
        return AccountDistributeBenefitReqDTO.builder()
                .baseRequestDTO(BaseRequestDTO.builder().userId(userId).build())
                .appId(appId)
                .benefitType(BenefitTypeEnum.DRAW_CODE.getId())
                .reqNo(drawCodeSource + Constants.UNDERLINE + userId)
                .drawCodeSource(drawCodeSource)
                .distributeCount(distributeCount)
                .extMap(extMap)
                .build();
    }

    public static AccountDistributeBenefitReqDTO createAccountDistributeBenefitReq(HistoryTryEatCommentVO historyTryEatComment,
                                                                                   String appId) {
        return createAccountDistributeBenefitReq(historyTryEatComment.getUid(), historyTryEatComment.getCommentId(), appId, 1);
    }

    public static AccountDistributeBenefitReqDTO createAccountDistributeBenefitReq(CommentPostTopicMessageVO message,
                                                                                   String appId, Integer distributeCount) {
        return createAccountDistributeBenefitReq(message.getUid(), message.getCommentId(), appId, distributeCount);
    }

    private static AccountDistributeBenefitReqDTO createAccountDistributeBenefitReq(String userId, String commentId,
                                                                                    String appId, Integer distributeCount) {
        Integer drawCodeSource = DrawCodeSourceEnum.TRY_EAT_COMMENT.getCode();
        Map<String, Object> extMap = Maps.newHashMap();
        extMap.put(DrawCodeAccountExtKeys.COMMENT_ID, commentId);
        return AccountDistributeBenefitReqDTO.builder()
                .baseRequestDTO(BaseRequestDTO.builder().userId(userId).build())
                .appId(appId)
                .benefitType(BenefitTypeEnum.DRAW_CODE.getId())
                .reqNo(drawCodeSource + Constants.UNDERLINE + commentId)
                .drawCodeSource(drawCodeSource)
                .distributeCount(distributeCount)
                .extMap(extMap)
                .build();
    }

    public static List<AccountInfoDTO> convertToAccountInfoDTOList(List<DrawCodeAccountDO> accountDOList) {
        if (CollectionUtils.isEmpty(accountDOList)) {
            return null;
        }
        return accountDOList.stream().map(DrawCodeAccountConvertEntity::convertToAccountInfoDTO).collect(Collectors.toList());
    }

    private static AccountInfoDTO convertToAccountInfoDTO(DrawCodeAccountDO accountDO) {
        DrawCodeAccountInfoDTO drawCodeAccountInfoDTO = DrawCodeAccountInfoDTO.builder()
                .drawCode(accountDO.getDrawCode())
                .drawCodeSource(accountDO.getSource())
                .extMap(JsonUtil.parseToMap(accountDO.getExt()))
                .build();
        return AccountInfoDTO.builder()
                .benefitId(accountDO.getBenefitId())
                .accountId(accountDO.getId())
                .userId(accountDO.getUserId())
                .benefitType(BenefitTypeEnum.DRAW_CODE.getId())
                .status(accountDO.getStatus())
                .createTime(accountDO.getCreateTime())
                .drawCodeAccountInfoDTO(drawCodeAccountInfoDTO)
                .lastReqNo(accountDO.getLastReqNo())
                .build();
    }

    public static AccountDistributeBenefitReqDTO createAccountDistributeBenefitReq(AccountDirectBenefitReqDTO req) {
        BaseRequestDTO baseRequestDTO = req.getBaseRequestDTO();
        Map<String, Object> reqExtMap = req.getExtMap();
        Integer drawCodeSource = MapUtils.getInteger(reqExtMap, DirectProvideBenefitExtKeys.DRAW_CODE_SOURCE);
        Map<String, Object> drawCodeExtMap = Maps.newHashMap();
        Long missionInstanceId = MapUtils.getLong(reqExtMap, DirectProvideBenefitExtKeys.MISSION_INSTANCE_ID);
        drawCodeExtMap.put(DrawCodeAccountExtKeys.MISSION_INSTANCE_ID, missionInstanceId);

        return AccountDistributeBenefitReqDTO.builder()
                .baseRequestDTO(baseRequestDTO)
                .appId(req.getAppId())
                .pageId(req.getPageId())
                .source(req.getSource())
                .benefitId(req.getBenefitId())
                .benefitType(BenefitTypeEnum.DRAW_CODE.getId())
                .reqNo(req.getReqNo())
                .drawCodeSource(drawCodeSource)
                .distributeCount(NumberUtils.convertToInteger(req.getSendAmount()))
                .extMap(drawCodeExtMap)
                .build();
    }
}
