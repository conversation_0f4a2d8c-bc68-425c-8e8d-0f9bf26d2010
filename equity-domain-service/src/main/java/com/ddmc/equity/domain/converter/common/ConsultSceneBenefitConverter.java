package com.ddmc.equity.domain.converter.common;

import com.ddmc.equity.dto.customer.ConsultSceneBenefitReqDTO;
import com.ddmc.equity.dto.customer.ReceiveSceneBenefitReqDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface ConsultSceneBenefitConverter {
    ConsultSceneBenefitConverter INSTANCE = Mappers.getMapper(ConsultSceneBenefitConverter.class);

//    @Mapping(target = "appId", source = "appId")
//    @Mapping(target = "pageId", source = "pageId")
//    @Mapping(target = "source", source = "source")
//    ConsultSceneBenefitReqDTO receiveDTOToConsultDTO(ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO);
}
