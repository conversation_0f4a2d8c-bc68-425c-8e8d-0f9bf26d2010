package com.ddmc.equity.domain.entity.account;

import com.ddmc.equity.account.DirectAccountContext;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.dto.account.OperateAccountDetailDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRecordInnerExtDTO;
import com.ddmc.equity.domain.dto.account_deduct.DeductAccountConfigDTO;
import com.ddmc.equity.domain.dto.account_deduct.DeductOperateAccountDetailDTO;
import com.ddmc.equity.domain.dto.account_deduct.FetchDeductOperateAccountDetailsReqDTO;
import com.ddmc.equity.domain.dto.account_expire.ExpireOperateAccountDetailDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;
import com.ddmc.ugc.commons.util.UUIDUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/8/1 16:14
 * @description
 */
public class OperateAccountConvertEntity {

    public static List<Long> getQueryUserAccountsUseActivityIds(Long useActivityId) {
        // useActivityId = 0L，表示所有活动通用的权益账户
        // 如果未指定 useActivityId，则只能查询 useActivityId = 0L 的权益账户列表；如果指定了 useActivityId，则需要查询包含 useActivityId = 0L 的权益账户列表；
        return Objects.isNull(useActivityId) ? Lists.newArrayList(0L) :
                Lists.newArrayList(0L, useActivityId);
    }

    public static FetchDeductOperateAccountDetailsReqDTO convertToFetchDeductOperateAccountDetailsReqDTO(
            DirectAccountContext accountContext, DeductAccountConfigDTO deductAccountConfigDTO) {
        return FetchDeductOperateAccountDetailsReqDTO.builder()
                .userId(accountContext.getUserId())
                .useActivityIds(getQueryUserAccountsUseActivityIds(accountContext.getUseActivityId()))
                .benefitType(accountContext.getBenefitType())
                .deductCount(accountContext.getOperateCount())
                .pageLimit(deductAccountConfigDTO.getFetchPageLimit())
                .maxLoopTimes(deductAccountConfigDTO.getFetchMaxLoopTimes())
                .build();
    }

    public static UniversalAccountRecordDO convertToDeductRecordDO(DirectAccountContext accountContext,
                                                                   List<DeductOperateAccountDetailDTO> deductOperateAccountDetails) {
        List<OperateAccountDetailDTO> operateAccountDetails = convertToDeductOperateAccountDetails(deductOperateAccountDetails);
        return OperateAccountConvertEntity.convertToOperateRecordDO(accountContext, operateAccountDetails);
    }

    public static UniversalAccountRecordDO convertToExpireRecordDO(UniversalAccountDetailDO detailDO,
                                                                   String applicationName) {
        Integer benefitType = detailDO.getBenefitType();
        String source = convertToExpireSource(benefitType);
        String reqNo = UUIDUtils.getUUID("expire_");
        UniversalAccountRecordDO recordDO = new UniversalAccountRecordDO();
        recordDO.setUserId(detailDO.getUserId());
        recordDO.setAccountId(detailDO.getAccountId());
        recordDO.setSceneCode(detailDO.getSceneCode());
        recordDO.setActivityId(detailDO.getActivityId());
        recordDO.setStrategyId(detailDO.getStrategyId());
        recordDO.setBenefitGroupId(detailDO.getBenefitGroupId());
        recordDO.setBenefitId(detailDO.getBenefitId());
        recordDO.setBenefitType(benefitType);
        recordDO.setBenefitValue(detailDO.getBenefitValue());
        recordDO.setUseActivityId(detailDO.getUseActivityId());

        recordDO.setOperateType(OperateTypeEnum.EXPIRE.getCode());
        recordDO.setStatus(StatusEnum.INIT.getCode());
        // 操作过期全部可用数量
        recordDO.setOperateCount(detailDO.getAvailableCount());
        recordDO.setReqNo(reqNo);
        recordDO.setAuxKey(reqNo);
        // 内部流水号必须要设置，因为数据库有唯一键
        recordDO.setInnerSerialNumber(UUIDUtils.getUUID());

        recordDO.setAppId(applicationName);
        recordDO.setPageId(null);
        recordDO.setSource(source);
        recordDO.setInnerExt(null);
        recordDO.setRpcRequestExt(null);
        recordDO.setRpcResponseExt(null);
        recordDO.setRpcCode(null);
        recordDO.setRpcMsg(null);
        recordDO.setFreezeReceiveLimitId(null);
        recordDO.setRelatedReqNo(null);

        return recordDO;
    }

    public static DirectAccountContext convertToExpireAccountContext(String userId, String applicationName,
                                                                     List<UniversalAccountDetailDO> accountDetails) {
        UniversalAccountDetailDO detailDO = accountDetails.get(0);
        Integer benefitType = detailDO.getBenefitType();
        String source = convertToExpireSource(benefitType);
        String reqNo = UUIDUtils.getUUID("expire_");
        int operateCount = accountDetails.stream().mapToInt(UniversalAccountDetailDO::getAvailableCount).sum();
        DirectAccountContext directAccountContext = new DirectAccountContext();
        directAccountContext.setUserId(userId);
        directAccountContext.setBenefitType(benefitType);
        directAccountContext.setOperateType(OperateTypeEnum.EXPIRE.getCode());
        directAccountContext.setOperateCount(operateCount);
        directAccountContext.setReqNo(reqNo);
        directAccountContext.setAppId(applicationName);
        directAccountContext.setPageId(null);
        directAccountContext.setSource(source);
        return directAccountContext;
    }

    public static UniversalAccountRecordDO convertToExpireRecordDO(DirectAccountContext accountContext,
                                                                   List<ExpireOperateAccountDetailDTO> expireOperateAccountDetails) {
        List<OperateAccountDetailDTO> operateAccountDetails = convertToExpireOperateAccountDetails(expireOperateAccountDetails);
        UniversalAccountRecordDO recordDO = convertToOperateRecordDO(accountContext, operateAccountDetails);
        recordDO.setStatus(StatusEnum.SUCCESS.getCode());
        return recordDO;
    }

    private static String convertToExpireSource(Integer benefitType) {
        if (Objects.equals(benefitType, BenefitTypeEnum.GOLDEN_BEAN.getId())) {
            // source 用于 C 端文案展示
            return "金豆过期";
        }
        return null;
    }

    private static UniversalAccountRecordDO convertToOperateRecordDO(DirectAccountContext accountContext,
                                                                     List<OperateAccountDetailDTO> operateAccountDetails) {
        UniversalAccountRecordDO recordDO = new UniversalAccountRecordDO();
        recordDO.setUserId(accountContext.getUserId());
        recordDO.setAccountId(null);
        recordDO.setSceneCode(accountContext.getSceneCode());
        recordDO.setActivityId(null);
        recordDO.setStrategyId(null);
        recordDO.setBenefitGroupId(null);
        recordDO.setBenefitId(null);
        recordDO.setBenefitType(accountContext.getBenefitType());
        recordDO.setBenefitValue(null);
        recordDO.setUseActivityId(accountContext.getUseActivityId());

        recordDO.setOperateType(accountContext.getOperateType());
        recordDO.setStatus(StatusEnum.INIT.getCode());
        recordDO.setOperateCount(accountContext.getOperateCount());
        recordDO.setReqNo(accountContext.getReqNo());
        recordDO.setAuxKey(accountContext.getReqNo());
        recordDO.setInnerSerialNumber(UUIDUtils.getUUID());

        recordDO.setAppId(accountContext.getAppId());
        recordDO.setPageId(accountContext.getPageId());
        recordDO.setSource(accountContext.getSource());
        recordDO.setInnerExt(JsonUtil.toString(convertToRecordInnerExtDTO(operateAccountDetails)));
        recordDO.setRpcRequestExt(null);
        recordDO.setRpcResponseExt(null);
        recordDO.setRpcCode(null);
        recordDO.setRpcMsg(null);
        recordDO.setFreezeReceiveLimitId(null);
        recordDO.setRelatedReqNo(null);

        return recordDO;
    }

    private static List<OperateAccountDetailDTO> convertToDeductOperateAccountDetails(List<DeductOperateAccountDetailDTO> deductOperateAccountDetails) {
        if (CollectionUtils.isEmpty(deductOperateAccountDetails)) {
            return null;
        }
        return deductOperateAccountDetails.stream()
                .map(e -> OperateAccountDetailDTO.builder()
                        .accountDetailId(e.getAccountDetailId())
                        .operateCount(e.getDeduceCount())
                        .build()
                ).collect(Collectors.toList());
    }

    public static List<ExpireOperateAccountDetailDTO> convertToExpireOperateAccountDetailDTOList(List<UniversalAccountDetailDO> detailDOList) {
        if (CollectionUtils.isEmpty(detailDOList)) {
            return null;
        }
        return detailDOList.stream().map(e -> ExpireOperateAccountDetailDTO.builder()
                .accountDetailId(e.getId())
                .accountId(e.getAccountId())
                .availableCount(e.getAvailableCount())
                .expireCount(e.getAvailableCount())
                .build()
        ).collect(Collectors.toList());
    }

    private static List<OperateAccountDetailDTO> convertToExpireOperateAccountDetails(List<ExpireOperateAccountDetailDTO> expireOperateAccountDetails) {
        if (CollectionUtils.isEmpty(expireOperateAccountDetails)) {
            return null;
        }
        return expireOperateAccountDetails.stream()
                .map(e -> OperateAccountDetailDTO.builder()
                        .accountDetailId(e.getAccountDetailId())
                        .operateCount(e.getExpireCount())
                        .build()
                ).collect(Collectors.toList());
    }

    public static UniversalAccountRecordInnerExtDTO convertToDeductRecordInnerExtDTO(List<DeductOperateAccountDetailDTO> deductOperateAccountDetails) {
        return convertToRecordInnerExtDTO(convertToDeductOperateAccountDetails(deductOperateAccountDetails));
    }

    private static UniversalAccountRecordInnerExtDTO convertToRecordInnerExtDTO(List<OperateAccountDetailDTO> operateAccountDetails) {
        if (CollectionUtils.isEmpty(operateAccountDetails)) {
            return null;
        }
        return UniversalAccountRecordInnerExtDTO.builder()
                .operateDetailDTOList(operateAccountDetails)
                .build();
    }
}
