package com.ddmc.equity.domain.service.core;

import com.ddmc.equity.account.DirectAccountContext;
import com.ddmc.equity.account.UniversalAccountRecordContext;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.dto.customer.account.AccountDistributeBenefitReqDTO;
import com.ddmc.equity.dto.customer.account.AccountDistributeBenefitRespDTO;
import com.ddmc.equity.dto.customer.account.QueryUserAccountsReqDTO;
import com.ddmc.equity.dto.customer.account.UserAccountDTO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/5/30 15:29
 * @description
 */
public interface EquityAccountCoreService {

    /**
     * 发放权益
     *
     * @param req 发放权益 req
     * @return 发放权益 resp
     */
    AccountDistributeBenefitRespDTO distributeBenefit(AccountDistributeBenefitReqDTO req);

    /**
     * 获取用户权益账户记录
     *
     * @param req 入参
     * @return 用户权益账户记录
     */
    List<UserAccountDTO> queryUserAccounts(QueryUserAccountsReqDTO req);

    List<UserAccountDTO> queryAndFillUserAccounts(QueryUserAccountsReqDTO req, Integer accountType);

    Boolean universalUseBenefitType(DirectAccountContext directAccountContext);

    Boolean universalUseBenefitDetail(DirectAccountContext directAccountContext);

    /**
     * 直塞发放权益（没有活动规则）
     *
     * @param directAccountContext 上下文
     * @return 发放结果
     */
    ReceiveBenefitResDTO receiveDirectBenefit(DirectAccountContext directAccountContext);

    PageListRespDTO<UniversalAccountRecordDO> queryBenefitRecordDTOS(UniversalAccountRecordContext context);
}
