package com.ddmc.equity.domain.entity.account;

import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcResponseExtDTO;
import com.ddmc.equity.dto.customer.ReceiveExternalInfoDTO;
import com.ddmc.vip.app.request.internal_api.ExchangeVipReqDTO;
import com.ddmc.vip.app.request.internal_api.ExternalActivityDTO;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/4/24 17:35
 * @description
 */
public class VipDaysAccountConvertEntity {

    public static ExchangeVipReqDTO convertToExchangeVipReqDTO(EquityAccountContext equityAccountContext) {
        ExchangeVipReqDTO req = new ExchangeVipReqDTO();
        req.setCityNumber(equityAccountContext.getCityCode());
        req.setStationId(equityAccountContext.getStationId());
        req.setLongitude(equityAccountContext.getLongitude());
        req.setLatitude(equityAccountContext.getLatitude());
        req.setUid(equityAccountContext.getUid());
        req.setExchangeReqNo(equityAccountContext.getSerialNumber());
        req.setExchangeVipDays(UniversalAccountAmountCalEntity.getDoRpcSendAmountLong(equityAccountContext));
        // 发放会员天数时的来源
        req.setSource(equityAccountContext.getSendVipDaysScene());
        // 消耗权益数量。比如积分兑换活动，每次兑换消耗的积分数
        req.setSourceExchangeExpandValue(equityAccountContext.getConsumeBenefitAmount());
        // 发放会员天数时的活动信息
        req.setExternalActivityDTOList(convertToExternalActivityDTOList(equityAccountContext));
        return req;
    }

    private static List<ExternalActivityDTO> convertToExternalActivityDTOList(EquityAccountContext equityAccountContext) {
        ReceiveExternalInfoDTO receiveExternalInfoDTO = equityAccountContext.getReceiveExternalInfoDTO();
        ExternalActivityDTO externalActivityDTO = new ExternalActivityDTO();
        externalActivityDTO.setBizAppId(equityAccountContext.getAppId());
        externalActivityDTO.setActivityType(Objects.isNull(receiveExternalInfoDTO) ? null : receiveExternalInfoDTO.getActivityType());
        externalActivityDTO.setActivityTypeName(Objects.isNull(receiveExternalInfoDTO) ? null : receiveExternalInfoDTO.getActivityTypeName());
        externalActivityDTO.setActivityId(equityAccountContext.getExternalId());
        externalActivityDTO.setActivityName(equityAccountContext.getActivityName());
        externalActivityDTO.setInviter(Objects.isNull(receiveExternalInfoDTO) ? null : receiveExternalInfoDTO.getInviter());
        return Lists.newArrayList(externalActivityDTO);
    }

    public static UniversalAccountRpcResponseExtDTO convertToAccountRpcResponseExtDTO(EquityRpcDto equityRpcDto) {
        return UniversalAccountRpcResponseExtDTO.builder()
                .vipChargeOrderNumber(equityRpcDto.getValue())
                .build();
    }
}
