package com.ddmc.equity.domain.service.benefit_stock.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.csoss.monitor.api.trace.Span;
import com.csoss.monitor.api.trace.Traces;
import com.csoss.monitor.sdk.resource.Status;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.ddmc.equity.common.config.PulsarApolloConfig;
import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.PlanDateTypeEnum;
import com.ddmc.equity.common.enums.StockOperationEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.common.util.LongUtils;
import com.ddmc.equity.common.util.MapUtils;
import com.ddmc.equity.common.util.RedisLuaUtil;
import com.ddmc.equity.common.util.ThreadsUtils;
import com.ddmc.equity.domain.dto.NumberRangeDTO;
import com.ddmc.equity.domain.dto.benefit_stock.BenefitStockLimitInfo;
import com.ddmc.equity.domain.dto.benefit_stock.BenefitStockLimitReq;
import com.ddmc.equity.domain.dto.benefit_stock.QueryStockStatusByRedisMulReq;
import com.ddmc.equity.domain.dto.benefit_stock.QueryStockStatusByRedisMulResp;
import com.ddmc.equity.domain.dto.benefit_stock.RedisMulQueryStockKey;
import com.ddmc.equity.domain.dto.benefit_stock.StockOperationBaseParam;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.domain.service.benefit_group.EquityBenefitGroupRuleDomainService;
import com.ddmc.equity.domain.service.benefit_stock.EquityBenefitStockDomainService;
import com.ddmc.equity.domain.service.benefit_stock.StockOperationContext;
import com.ddmc.equity.domain.service.equityBenefitStockFlow.EquityBenefitStockFlowService;
import com.ddmc.equity.domain.service.rule.EquityRuleService;
import com.ddmc.equity.dto.customer.benefit_stock.QueryBenefitStockReqDTO;
import com.ddmc.equity.dto.customer.benefit_stock.QueryBenefitStockRespDTO;
import com.ddmc.equity.enums.ActivityStatusEnum;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.mq.pulsar.producer.PulsarProducer;
import com.ddmc.equity.infra.repository.dao.EquityActivityDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockFlowDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockPlanDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityActivityMapper;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitStockFlowMapper;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitStockMapper;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitStockPlanMapper;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.pulsar.client.api.MessageId;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EquityBenefitStockDomainServiceImpl implements EquityBenefitStockDomainService {

    @ApolloJsonValue("${balance.stock.compare.diff.number.range:{\"lower\":-4,\"upper\":64}}")
    private NumberRangeDTO balanceStockCompareDiffNumberRange;

    private static final Random RANDOM = new Random();

    @Resource
    private EquityBenefitStockMapper equityBenefitStockMapper;

    @Resource
    private EquityBenefitStockFlowMapper equityBenefitStockFlowMapper;

    @Resource
    private EquityBenefitStockPlanMapper equityBenefitStockPlanMapper;

    @Resource
    private EquityActivityMapper equityActivityMapper;

    @Resource
    private RedisCache redisCache;

    @Resource
    private RedisLuaUtil redisLuaUtil;

    @Resource
    private PulsarProducer producer;

    @Resource
    private PulsarApolloConfig pulsarApolloConfig;

    @Resource
    private StockOperationContext stockOperationContext;

    @Autowired
    private EquityBenefitStockFlowService equityBenefitStockFlowService;

    @Resource
    private EquityBenefitGroupRuleDomainService equityBenefitGroupRuleDomainService;

    @Resource
    private EquityRuleService equityRuleService;

    @Value("${force.refresh.activity.stock:false}")
    private boolean forceRefresh;

    @Override
    public List<EquityBenefitStockDO> queryStockByStrategyIds(Collection<Long> strategyIds) {
        return equityBenefitStockMapper.selectList(new LambdaQueryWrapper<EquityBenefitStockDO>()
                .in(EquityBenefitStockDO::getStrategyId, strategyIds)
        );
    }

    @Override
    public void scanFlowTableJob(Long cursor, long batchNumber, Set<Integer> operationTypes, List<Long> includeIds, List<Long> excludeIds) {
        Assert.notEmpty(operationTypes, ExceptionEnum.PARAMS_ERROR);
        for (Integer operationType : operationTypes) {
            StockOperationEnum stockOperationEnum = StockOperationEnum.getByOperation(operationType);
            Assert.notNull(stockOperationEnum, ExceptionEnum.PARAMS_ERROR);
        }
        while (true) {
            // 应该扫策略表，因为如果扫库存策略表，没法判断哪些库存容量为无限的策略上线了
            // 不能扫权益组表，权益组表里没状态。。
            LambdaQueryWrapper<EquityBenefitStockFlowDO> queryWrapperDraft = new QueryWrapper<EquityBenefitStockFlowDO>()
                    .lambda()
                    .gt(EquityBenefitStockFlowDO::getId, cursor)
                    .eq(EquityBenefitStockFlowDO::getIsHandle, CommonEnum.INTEGER_BOOL.NO.getCode())
                    .in(EquityBenefitStockFlowDO::getOperationType, operationTypes)
                    .eq(EquityBenefitStockFlowDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                    .in(CollectionUtils.isNotEmpty(includeIds), EquityBenefitStockFlowDO::getActivityId, includeIds)
                    .notIn(CollectionUtils.isNotEmpty(excludeIds), EquityBenefitStockFlowDO::getActivityId, excludeIds)
                    .last(String.format("limit %d", batchNumber));

            List<EquityBenefitStockFlowDO> equityBenefitStockFlowDOList = equityBenefitStockFlowMapper.selectList(queryWrapperDraft);
            int size = CollectionUtils.size(equityBenefitStockFlowDOList);
            log.info("scanFlowTableJob 库存流水表数据条数. size={}", size);
            XxlJobLogger.log("scanFlowTableJob 库存流水表数据条数. size={}", size);
            if (0 == size) {
                break;
            }
            List<Long> mightFailedIds = stockOperationContext.handleFlowList(equityBenefitStockFlowDOList);
            if (CollectionUtils.isNotEmpty(mightFailedIds)) {
                log.warn("scanFlowTableJob 库存流水表扫描转运出错 ids. mightFailedIds={}", mightFailedIds);
                XxlJobLogger.log("scanFlowTableJob 库存流水表扫描转运出错 ids. mightFailedIds={}", mightFailedIds);
                CsossUtils.logEvent(MonitorConstants.HANDLE_INIT_AND_EXPEND_FLOW, "handle_flow_fail");
            }
            List<Long> handleIds = equityBenefitStockFlowDOList.stream().map(EquityBenefitStockFlowDO::getId)
                    .filter(v -> !mightFailedIds.contains(v))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(handleIds)) {
                int updateResult = equityBenefitStockFlowMapper.update(null, new LambdaUpdateWrapper<EquityBenefitStockFlowDO>()
                        .set(EquityBenefitStockFlowDO::getIsHandle, CommonEnum.INTEGER_BOOL.YES.getCode())
                        .in(EquityBenefitStockFlowDO::getId, handleIds));
                log.info("scanFlowTableJob 库存流水表扫描转运完成 ids. handleIds={} ", handleIds);
                XxlJobLogger.log("scanFlowTableJob 库存流水表扫描转运完成 ids. handleIds:{} ", handleIds);
                if (updateResult <= 0) {
                    log.warn("scanFlowTableJob 更新库存流水表为已处理失败. handleIds={} ", handleIds);
                    XxlJobLogger.log("scanFlowTableJob 更新库存流水表为已处理失败. handleIds:{} ", handleIds);
                    CsossUtils.logEvent(MonitorConstants.HANDLE_INIT_AND_EXPEND_FLOW, "update_flow_handled_fail");
                }
            }
            if (size < batchNumber) {
                log.info("scanFlowTableJob 库存流水表数据遍历完成.");
                XxlJobLogger.log("scanFlowTableJob 库存流水表数据遍历完成.");
                break;
            }
            cursor = equityBenefitStockFlowDOList.get(size - 1).getId();
        }
    }

    @Override
    public void generatePlanStockNextTime(Long cursor, long batchNumber, String date, List<Long> includeIds, List<Long> excludeIds) {
        Date now = new Date();
        if (StringUtils.isNotBlank(date)) {
            now = DateUtil.parse_yyyyMMdd(date);
        }
        Date tomorrow = DateUtil.getTomorrow(now);
        String tomorrowStr = DateUtil.format_yyyyMMdd(tomorrow);
        // 应该扫活动表。因为活动id是计划库存表的shardingKey，如果没有shardingKey，会全表扫描，会影响数据库 @郝文海
        while (true) {
            LambdaQueryWrapper<EquityActivityDO> queryWrapperDraft = new QueryWrapper<EquityActivityDO>()
                    .lambda()
                    .gt(EquityActivityDO::getId, cursor)
//                    .le(EquityActivityDO::getStartTime, tomorrow)
                    .ge(EquityActivityDO::getEndTime, tomorrow)
                    .eq(EquityActivityDO::getStatus, ActivityStatusEnum.PUBLISHED.getStatus())
                    .eq(EquityActivityDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                    .in(CollectionUtils.isNotEmpty(includeIds), EquityActivityDO::getId, includeIds)
                    .notIn(CollectionUtils.isNotEmpty(excludeIds), EquityActivityDO::getId, excludeIds)
                    .last(String.format("limit %d", batchNumber));

            log.info("构建查询条件 queryWrapperDraft:{} tomorrow:{}", queryWrapperDraft, tomorrow);

            List<EquityActivityDO> equityActivityDOS = equityActivityMapper.selectList(queryWrapperDraft);
            int size = CollectionUtils.size(equityActivityDOS);
            log.info("活动表数据条数：size:{} equityActivityDOS:{}", size, JSON.toJSONString(equityActivityDOS));
            XxlJobLogger.log("活动表数据条数：{} equityActivityDOS:{}", size, JSON.toJSONString(equityActivityDOS));
            if (0 == size) {
                break;
            }
            LambdaQueryWrapper<EquityBenefitStockPlanDO> planQueryWrapperDraft = new QueryWrapper<EquityBenefitStockPlanDO>()
                    .lambda()
                    .in(EquityBenefitStockPlanDO::getActivityId, equityActivityDOS.stream().map(EquityActivityDO::getId).collect(Collectors.toSet()))
                    .in(EquityBenefitStockPlanDO::getPlanDate, Lists.newArrayList(DateUtil.format_yyyyMMdd(now), tomorrowStr))
                    .eq(EquityBenefitStockPlanDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode());
            List<EquityBenefitStockPlanDO> equityBenefitStockPlanDOS = equityBenefitStockPlanMapper.selectList(planQueryWrapperDraft);

            log.info("查询出计划库存条目 equityBenefitStockPlanDOS:{}", JSON.toJSONString(equityBenefitStockPlanDOS));
            XxlJobLogger.log("查询出计划库存条目 equityBenefitStockPlanDOS:{}", JSON.toJSONString(equityBenefitStockPlanDOS));
            if (CollectionUtils.isNotEmpty(equityBenefitStockPlanDOS)) {
                for (EquityBenefitStockPlanDO equityBenefitStockPlanDO : equityBenefitStockPlanDOS) {
                    // 检查和创建 T+1 库存
                    Long strategyId = equityBenefitStockPlanDO.getStrategyId();
                    Long benefitId = equityBenefitStockPlanDO.getBenefitId();
                    Integer planDateType = equityBenefitStockPlanDO.getPlanDateType();

                    StockLimitRuleDTO stockLimitRuleDTO = getStockLimitRuleDTOFromDB(strategyId, benefitId);
                    if (null == stockLimitRuleDTO) {
                        // 解析未匹配计划库存规则
                        log.error("解析未匹配计划库存规则  strategyId:{}", strategyId);
                    } else {
                        if (PlanDateTypeEnum.DAY.getType().equals(planDateType)) {
                            try {
                                // 无脑插就行了 实际上C端通过Lua消耗时，是和总库存一起同时消耗的
                                insertNewPlanCache(tomorrowStr, strategyId, benefitId, planDateType, stockLimitRuleDTO.getDayStock(), 3);
                                //
                                insertNewPlan(equityBenefitStockPlanDO, stockLimitRuleDTO.getDayStock(), tomorrowStr);
                            } catch (Exception e) {
                                log.error("创建明日计划库存失败 strategyId:{} benefitId:{} stockLimitRuleDTO:{}", strategyId, benefitId, stockLimitRuleDTO, e);
                            }
                        }
                    }
                }
            }


            if (size < batchNumber) {
                log.info("活动表数据遍历完成 size:{}", size);
                XxlJobLogger.log("活动表数据遍历完成 size:{}", size);
                break;
            }
            cursor = equityActivityDOS.get(size - 1).getId();
        }

//        while (true) {
//            LambdaQueryWrapper<EquityBenefitStockPlanDO> queryWrapperDraft = new QueryWrapper<EquityBenefitStockPlanDO>()
//                    .lambda()
//                    .gt(EquityBenefitStockPlanDO::getId, cursor)
//                    .eq(EquityBenefitStockPlanDO::getPlanDate, DateUtil.format_yyyyMMdd(now))
//                    .eq(EquityBenefitStockPlanDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
//                    .last(String.format("limit %d", batchNumber));
//            List<EquityBenefitStockPlanDO> equityBenefitStockPlanDOS = equityBenefitStockPlanMapper.selectList(queryWrapperDraft);
//            int size = CollectionUtils.size(equityBenefitStockPlanDOS);
//            log.info("计划库存今日表数据条数：{}", size);
//            XxlJobLogger.log("计划库存今日表数据条数：{}", size);
//            if (0 == size) {
//                break;
//            }
//
//            // 主逻辑
//            Set<Long> needCheckTomorrowIfValidActivityIds = equityBenefitStockPlanDOS.stream().map(EquityBenefitStockPlanDO::getActivityId).collect(Collectors.toSet());
////            Date tomorrow = DateUtil.getTomorrow(now);
//            List<EquityActivityDO> activityDOS = equityActivityMapper.selectList(new LambdaQueryWrapper<EquityActivityDO>()
//                    .in(EquityActivityDO::getId, needCheckTomorrowIfValidActivityIds)
//                    .gt(EquityActivityDO::getEndTime, tomorrow)
//                    .eq(EquityActivityDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode()));
//            if (CollectionUtils.isEmpty(activityDOS)) {
//                // 活动均已过期
//                continue;
//            }
//            List<Long> validActivity = activityDOS.stream().map(EquityActivityDO::getId).collect(Collectors.toList());
//            equityBenefitStockPlanDOS.removeIf(v -> !validActivity.contains(v.getActivityId()));
//            if (CollectionUtils.isEmpty(equityBenefitStockPlanDOS)) {
//                // 需初始化计划的均已过期
//                continue;
//            }
//            // 明日
//            for (EquityBenefitStockPlanDO equityBenefitStockPlanDO : equityBenefitStockPlanDOS) {
//                // 检查和创建 T+1 库存
//                Long strategyId = equityBenefitStockPlanDO.getStrategyId();
//                Long benefitId = equityBenefitStockPlanDO.getBenefitId();
//                Integer planDateType = equityBenefitStockPlanDO.getPlanDateType();
//
//                StockLimitRuleDTO stockLimitRuleDTO = equityRuleService.getStockLimitRule(strategyId, planDateType);
//                if (null == stockLimitRuleDTO) {
//                    // 解析未匹配计划库存规则
//                    log.error("解析未匹配计划库存规则  strategyId:{}", strategyId);
//                } else {
//                    if (PlanDateTypeEnum.DAY.getType().equals(planDateType)) {
//                        try {
//                            // 无脑插就行了 实际上C端通过Lua消耗时，是和总库存一起同时消耗的
//                            insertNewPlanCache(tomorrowStr, strategyId, benefitId, planDateType, stockLimitRuleDTO.getDayStock(), 3);
//                            //
//                            insertNewPlan(equityBenefitStockPlanDO, stockLimitRuleDTO.getDayStock(), tomorrowStr);
//                        } catch (Exception e) {
//                            log.error("创建明日计划库存失败 strategyId:{} benefitId:{} stockLimitRuleDTO:{}", strategyId, benefitId, stockLimitRuleDTO, e);
//                        }
//                    }
//                }
//            }
//
//            if (size < batchNumber) {
//                log.info("计划库存今日表数据遍历完成");
//                XxlJobLogger.log("计划库存今日表数据遍历完成");
//                break;
//            }
//            cursor = equityBenefitStockPlanDOS.get(size - 1).getId();
//        }
    }

    private Boolean insertNewPlan(EquityBenefitStockPlanDO msg, Long supplierCount, String nowStr) {
        if (supplierCount > 0) {
            EquityBenefitStockPlanDO mightExist = equityBenefitStockPlanMapper.selectOne(new LambdaQueryWrapper<EquityBenefitStockPlanDO>()
                    .eq(EquityBenefitStockPlanDO::getStrategyId, msg.getStrategyId())
                    .eq(EquityBenefitStockPlanDO::getBenefitId, msg.getBenefitId())
                    .eq(EquityBenefitStockPlanDO::getPlanDateType, msg.getPlanDateType())
                    .eq(EquityBenefitStockPlanDO::getPlanDate, nowStr)
                    .eq(EquityBenefitStockPlanDO::getActivityId, msg.getActivityId())
                    .last("limit 1")
            );
            if (null != mightExist) {
                // 初始化的行为不更新
                return true;
            }
            // 插plan表
            EquityBenefitStockPlanDO equityBenefitStockPlanDO = new EquityBenefitStockPlanDO();
            equityBenefitStockPlanDO.setActivityId(msg.getActivityId());
            equityBenefitStockPlanDO.setStrategyId(msg.getStrategyId());
            equityBenefitStockPlanDO.setBenefitId(msg.getBenefitId());
            equityBenefitStockPlanDO.setStockCount(supplierCount);
            equityBenefitStockPlanDO.setBalanceCount(supplierCount);
            equityBenefitStockPlanDO.setPlanDateType(msg.getPlanDateType());
            equityBenefitStockPlanDO.setPlanDate(nowStr);
            equityBenefitStockPlanDO.setUseCount(0L);
            equityBenefitStockPlanDO.setVersion(0L);
            int stockPlanRow;
            try {
                stockPlanRow = equityBenefitStockPlanMapper.insert(equityBenefitStockPlanDO);
            } catch (DuplicateKeyException e) {
                log.error("出现重复插入的主键，疑似已经处理过了，确认后可无视 equityBenefitStockPlanDO:{}", equityBenefitStockPlanDO, e);
                stockPlanRow = 1;
            }
            if (stockPlanRow <= 0) {
                log.error("插入权益库存计划信息失败 msg:{} equityBenefitStockPlanDO:{}", msg, equityBenefitStockPlanDO);
                return false;
            }

        }
        return true;
    }

    private void insertNewPlanCache(String dateStr, Long strategyId, Long benefitId,
                                    Integer planDateType,
                                    Long supplierCount,
                                    long expireDayOffset) {
        if (supplierCount > 0) {
            String planBalanceCacheKey = String.format(
                    CacheKeyConstants.PLAN_BALANCE_COUNT_KEY, strategyId, benefitId, planDateType, dateStr);
            String planStockCacheKey = String.format(
                    CacheKeyConstants.PLAN_STOCK_COUNT_KEY, strategyId, benefitId, planDateType, dateStr);
            log.info("写入Redis key planBalanceCacheKey:{} planStockCacheKey:{}", planBalanceCacheKey, planStockCacheKey);
            XxlJobLogger.log("写入Redis key planBalanceCacheKey:{} planStockCacheKey:{}", planBalanceCacheKey, planStockCacheKey);
            try {

                if (redisCache.exist(planBalanceCacheKey) && redisCache.exist(planStockCacheKey)) {
                    log.info("写入Redis key 已存在 planBalanceCacheKey:{} planStockCacheKey:{}", planBalanceCacheKey, planStockCacheKey);
                    XxlJobLogger.log("写入Redis key 已存在 planBalanceCacheKey:{} planStockCacheKey:{}", planBalanceCacheKey, planStockCacheKey);
                    return;
                }
            } catch (Exception e) {
                log.error("查询计划缓存是否存在失败", e);
            }

            // 初始化场景下，都是trySet
            expireDayOffset = expireDayOffset > 0 ? expireDayOffset : 2;
            int timeToLive = (int) (Duration.ofDays(expireDayOffset).getSeconds() + RANDOM.nextInt(300));
            redisCache.insertNewCache(planBalanceCacheKey, planStockCacheKey, supplierCount, timeToLive, TimeUnit.SECONDS);
            log.info("写入Redis key 完成 planBalanceCacheKey:{} planStockCacheKey:{}", planBalanceCacheKey, planStockCacheKey);
            XxlJobLogger.log("写入Redis key 完成 planBalanceCacheKey:{} planStockCacheKey:{}", planBalanceCacheKey, planStockCacheKey);
        }
    }

    @Override
    public @Nullable Boolean deductStock(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                         @NotNull Long num, @NotNull String auxKey,
                                         @NotNull StockLimitRuleDTO stockLimitRuleDTO) {
        Date now = new Date();
        String lockKey = String.format(CacheKeyConstants.STOCK_DO_DEDUCTION_OPERATION_LOCK_KEY, auxKey);
        boolean isLocked = false;
        try {
            isLocked = redisCache.lock(lockKey, 10, TimeUnit.SECONDS);
            if (!isLocked) {
                log.warn("deductStock 未获取到锁 lockKey={}", lockKey);
                return Boolean.FALSE;
            }
            return doDeductionOperationRedis(activityId, strategyId, benefitId, num, stockLimitRuleDTO, auxKey, now);
        } catch (Exception e) {
            log.error("deductStock 任务上锁失败或执行异常 lockKey={}", lockKey, e);
            return Boolean.FALSE;
        } finally {
            redisCache.unlock(lockKey, isLocked);
        }
    }

    @Override
    public @Nullable Boolean releaseStock(@NotNull Long activityId, @NotNull Long strategyId,
                                          @NotNull Long benefitId, @NotNull Long num, @NotNull String auxKey,
                                          @NotNull StockLimitRuleDTO stockLimitRuleDTO) {
        Date now = new Date();
        String lockKey = String.format(CacheKeyConstants.STOCK_DO_RELEASE_OPERATION_LOCK_KEY, auxKey);
        boolean isLocked = false;
        try {
            isLocked = redisCache.lock(lockKey, 45, TimeUnit.SECONDS);
            if (!isLocked) {
                log.warn("releaseStock 未获取到锁 lockKey={}", lockKey);
                return Boolean.FALSE;
            }
            return doReleaseOperationRedis(activityId, strategyId, benefitId, num, stockLimitRuleDTO, auxKey, now);
        } catch (Exception e) {
            log.error("releaseStock 任务上锁失败或执行异常 lockKey={}", lockKey, e);
            return Boolean.FALSE;
        } finally {
            redisCache.unlock(lockKey, isLocked);
        }
    }

    /**
     * 扣减库存，权益组维度（具体到权益组下的权益维度）
     * <p>
     * 用 lua 脚本保证计划库存和总库存的同时扣减
     * 在缓存中更新库存并发消息更新 DB
     */
    private Boolean doDeductionOperationRedis(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                              @NotNull Long num, @NotNull StockLimitRuleDTO stockLimitRuleDTO,
                                              @NotNull String auxKey, @NotNull Date date) {
        if (num < 0) {
            log.error("doDeductionOperationRedis 扣减库存，扣减数必须为正数. activityId={}, strategyId={}, benefitId={}",
                    activityId, strategyId, benefitId);
            return null;
        }
        String dateStr = DateUtil.format_yyyyMMdd(date);
        boolean hasStockLimit;
        String rdsStockCount = String.format(CacheKeyConstants.STOCK_COUNT_KEY, strategyId, benefitId);
        try {
            hasStockLimit = redisCache.exist(rdsStockCount);
        } catch (Exception e) {
            log.error("doDeductionOperationRedis 查询 redis 失败，无法确定库存状态. activityId={}, strategyId={}, benefitId={}",
                    activityId, strategyId, benefitId, e);
            return null;
        }
        Integer planDateType = ObjectUtils.defaultIfNull(stockLimitRuleDTO.getPlanDateType(), PlanDateTypeEnum.UNDEFINED.getType());
        boolean hasPlanStockLimit = PlanDateTypeEnum.isHasPlanStockLimit(planDateType);
        // 无总库存和计划库存限制，直接返回成功
        if (!hasStockLimit && !hasPlanStockLimit) {
            return Boolean.TRUE;
        }

        // 扣减 redis 中总库存和计划库存
        String balanceStockCacheKey = getBalanceStockCacheKey(strategyId, benefitId);
        String planBalanceStockCacheKey = hasPlanStockLimit ? getPlanBalanceStockCacheKey(strategyId, benefitId, planDateType, dateStr) : null;
        Span decrByUntilToZeroTwoKeyLuaSpan = Traces.spanBuilder("DecrByUntilToZeroTwoKeyLua").startSpan();
        decrByUntilToZeroTwoKeyLuaSpan.setAttribute("balanceStockCacheKey", balanceStockCacheKey);
        decrByUntilToZeroTwoKeyLuaSpan.setAttribute("planBalanceStockCacheKey", StringUtils.defaultIfBlank(planBalanceStockCacheKey, StringUtils.EMPTY));
        decrByUntilToZeroTwoKeyLuaSpan.setAttribute("num", num);
        boolean result;
        try {
            // 执行 lua 脚本同步扣减总库存和计划库存
            result = redisLuaUtil.decrByUntilToZeroTwoKey(balanceStockCacheKey, planBalanceStockCacheKey, num);
            decrByUntilToZeroTwoKeyLuaSpan.setAttribute("result", result);
        } catch (Exception e) {
            log.error("doDeductionOperationRedis 扣减库存失败. activityId={}, strategyId={}, benefitId={}", activityId, strategyId, benefitId, e);
            decrByUntilToZeroTwoKeyLuaSpan.recordException(e);
            return Boolean.FALSE;
        } finally {
            decrByUntilToZeroTwoKeyLuaSpan.end();
        }
        if (!Boolean.TRUE.equals(result)) {
            return Boolean.FALSE;
        }

        // 异步扣减 BD 中总库存和计划库存
        ThreadsUtils.runAsync(() ->
                        sendStockDeductMsg(activityId, strategyId, benefitId, num, auxKey, planDateType, dateStr),
                ThreadsUtils.getOrdinaryThreadPoll(), Constants.CHANGE_STOCK_MSG);
        return Boolean.TRUE;
    }

    private void sendStockDeductMsg(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                    @NotNull Long num, @NotNull String auxKey, Integer planDateType, String dateStr) {
        StockOperationBaseParam activityStockChangedMsg = StockOperationBaseParam.builder()
                .stockOperationType(StockOperationEnum.DEDUCT.getOperation())
                .stockOperation(num)
                .activityId(activityId)
                .strategyId(strategyId)
                .benefitId(benefitId)
                .planDateType(planDateType)
                .reqNo(auxKey)
                .dateStr(dateStr)
                .build();
        MessageId messageId = producer.syncSendOrderlyWithSchema(pulsarApolloConfig.getActivityStockChangeTopic(),
                activityStockChangedMsg, StockOperationBaseParam.class);
        log.info("sendStockDeductMsg success. messageId={}, msg={}", JSON.toJSONString(messageId), JSON.toJSONString(activityStockChangedMsg));
        CsossUtils.logEvent(MonitorConstants.SEND_SYNC_STOCK_MSG, MonitorConstants.SUCCESS, "0", JSON.toJSONString(messageId));
    }

    /**
     * 释放库存，权益组维度（具体到权益组下的权益维度）
     * <p>
     * 用 lua 脚本保证计划库存和总库存的同时扣减
     * 在缓存中更新库存并发消息更新 DB
     */
    private Boolean doReleaseOperationRedis(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                            @NotNull Long num, @NotNull StockLimitRuleDTO stockLimitRuleDTO,
                                            @NotNull String auxKey, @NotNull Date date) {
        if (num < 0) {
            log.error("doReleaseOperationRedis 扣减数必须为正数. activityId={}, strategyId={}, benefitId={}", activityId, strategyId, benefitId);
            return null;
        }
        String dateStr = DateUtil.format_yyyyMMdd(date);
        boolean hasStockLimit;
        String rdsStockCount = String.format(CacheKeyConstants.STOCK_COUNT_KEY, strategyId, benefitId);
        try {
            hasStockLimit = redisCache.exist(rdsStockCount);
        } catch (Exception e) {
            log.error("doReleaseOperationRedis 查询 redis 失败，无法确定库存状态. activityId={}, strategyId={}, benefitId={}",
                    activityId, strategyId, benefitId, e);
            return null;
        }
        Integer planDateType = ObjectUtils.defaultIfNull(stockLimitRuleDTO.getPlanDateType(), PlanDateTypeEnum.UNDEFINED.getType());
        boolean hasPlanStockLimit = PlanDateTypeEnum.isHasPlanStockLimit(planDateType);
        // 无总库存和计划库存限制，直接返回成功
        if (!hasStockLimit && !hasPlanStockLimit) {
            return Boolean.TRUE;
        }

        // 释放 redis 中总库存和计划库存
        String stockCacheKey = getStockCacheKey(strategyId, benefitId);
        String balanceStockCacheKey = getBalanceStockCacheKey(strategyId, benefitId);
        String planStockCacheKey = hasPlanStockLimit ? getPlanStockCacheKey(strategyId, benefitId, planDateType, dateStr) : null;
        String planBalanceStockCacheKey = hasPlanStockLimit ? getPlanBalanceStockCacheKey(strategyId, benefitId, planDateType, dateStr) : null;
        Boolean result;
        try {
            // 执行 lua 脚本同步释放总库存和计划库存
            result = redisLuaUtil.incrByUntilToFillFourKey(stockCacheKey, balanceStockCacheKey,
                    planStockCacheKey, planBalanceStockCacheKey, num);
        } catch (Exception e) {
            log.error("doReleaseOperationRedis 释放库存失败. activityId={}, strategyId={}, benefitId={}", activityId, strategyId, benefitId, e);
            return Boolean.FALSE;
        }
        if (!Boolean.TRUE.equals(result)) {
            return Boolean.FALSE;
        }

        // 异步释放 BD 中总库存和计划库存
        ThreadsUtils.runAsync(() ->
                        sendStockReleaseMsg(activityId, strategyId, benefitId, num, auxKey, planDateType, dateStr),
                ThreadsUtils.getOrdinaryThreadPoll(), Constants.CHANGE_STOCK_MSG);
        return Boolean.TRUE;
    }

    private void sendStockReleaseMsg(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                     @NotNull Long num, @NotNull String auxKey, Integer planDateType, String dateStr) {
        StockOperationBaseParam activityStockChangedMsg = StockOperationBaseParam.builder()
                .stockOperationType(StockOperationEnum.RELEASE.getOperation())
                .stockOperation(num)
                .strategyId(strategyId)
                .benefitId(benefitId)
                .planDateType(planDateType)
                .reqNo(auxKey)
                .dateStr(dateStr)
                .activityId(activityId)
                .build();
        MessageId messageId = producer.syncSendOrderlyWithSchema(pulsarApolloConfig.getActivityStockChangeTopic(),
                activityStockChangedMsg, StockOperationBaseParam.class);
        log.info("sendStockReleaseMsg success. messageId={}, msg={}", JSON.toJSONString(messageId), JSON.toJSONString(activityStockChangedMsg));
        CsossUtils.logEvent(MonitorConstants.SEND_SYNC_STOCK_MSG, MonitorConstants.SUCCESS, "0", JSON.toJSONString(messageId));
    }

    @Override
    public boolean queryStockStatusByRedis(Long strategyId, Long benefitGroupId, Long benefitId,
                                           @NotNull Integer planDateType, @NotNull Date date) {
        BenefitStockLimitReq benefitStockLimitReq = BenefitStockLimitReq.builder()
                .strategyId(strategyId).benefitId(benefitId).planDateType(planDateType)
                .build();
        QueryStockStatusByRedisMulReq req = QueryStockStatusByRedisMulReq.builder()
                .reqList(Lists.newArrayList(benefitStockLimitReq))
                .build();
        QueryStockStatusByRedisMulResp resp = this.queryStockStatusByRedisMul(req);
        log.info("queryStockStatusByRedis end. req={}, resp={}", JSON.toJSONString(req), JSON.toJSONString(resp));
        return Objects.nonNull(resp) && resp.getStockInfo().values().stream().allMatch(Boolean.TRUE::equals);
    }

    @Override
    public QueryStockStatusByRedisMulResp queryStockStatusByRedisMul(QueryStockStatusByRedisMulReq req) {
        Map<String, RedisMulQueryStockKey> mulQueryStockKeyMap = getMulQueryStockKeyMap(req);
        if (MapUtils.isEmpty(mulQueryStockKeyMap)) {
            return QueryStockStatusByRedisMulResp.builder().build();
        }

        Map<String, @Nullable Long> mulQueryResultMap = batchGetCacheKeyValue(mulQueryStockKeyMap.keySet());
        // 加一个库存查询失败打点 如果遇到查询为 null 的情况报警
        mulQueryStockKeyMap.keySet().forEach(key -> {
            Long value = mulQueryResultMap.get(key);
            if (Objects.nonNull(value) && value >= 0L) {
                CsossUtils.logEvent(MonitorConstants.GET_STOCK, MonitorConstants.SUCCESS);
                return;
            }
            log.error("queryStockStatusByRedisMul 查询库存不存在 key={}, value={}", key, value);
            CsossUtils.logEvent(MonitorConstants.GET_STOCK, MonitorConstants.FAIL);
        });

        if (MapUtils.isEmpty(mulQueryResultMap)) {
            return QueryStockStatusByRedisMulResp.builder().build();
        }
        return getQueryStockStatusByRedisMulResp(mulQueryStockKeyMap, mulQueryResultMap);
    }

    @Override
    public EquityBenefitStockPlanDO queryBenefitStockPlanDO(@NotNull Long activityId, @NotNull Long strategyId,
                                                            @NotNull Long benefitId, @NotNull Integer planDateType,
                                                            @NotNull String dateStr) {
        Wrapper<EquityBenefitStockPlanDO> wrapper = Wrappers.<EquityBenefitStockPlanDO>lambdaQuery()
                .eq(EquityBenefitStockPlanDO::getActivityId, activityId)
                .eq(EquityBenefitStockPlanDO::getStrategyId, strategyId)
                .eq(EquityBenefitStockPlanDO::getBenefitId, benefitId)
                .eq(EquityBenefitStockPlanDO::getPlanDateType, planDateType)
                .eq(EquityBenefitStockPlanDO::getPlanDate, dateStr)
                .last("limit 1");
        return equityBenefitStockPlanMapper.selectOne(wrapper);
    }

    @Override
    public EquityBenefitStockDO getBenefitStock(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId) {
        Wrapper<EquityBenefitStockDO> wrapper = Wrappers.<EquityBenefitStockDO>lambdaQuery()
                .eq(EquityBenefitStockDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(EquityBenefitStockDO::getActivityId, activityId)
                .eq(EquityBenefitStockDO::getStrategyId, strategyId)
                .eq(EquityBenefitStockDO::getBenefitId, benefitId);
        return equityBenefitStockMapper.selectOne(wrapper);
    }

    @Override
    public EquityBenefitStockPlanDO getBenefitPlanStock(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                                        @NotNull Integer planDateType, @NotNull String planDate) {
        Wrapper<EquityBenefitStockPlanDO> wrapper = Wrappers.<EquityBenefitStockPlanDO>lambdaQuery()
                .eq(EquityBenefitStockPlanDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(EquityBenefitStockPlanDO::getActivityId, activityId)
                .eq(EquityBenefitStockPlanDO::getStrategyId, strategyId)
                .eq(EquityBenefitStockPlanDO::getBenefitId, benefitId)
                .eq(EquityBenefitStockPlanDO::getPlanDateType, planDateType)
                .eq(EquityBenefitStockPlanDO::getPlanDate, planDate);
        return equityBenefitStockPlanMapper.selectOne(wrapper);
    }

    @Override
    public void checkAndRefreshRedisStock(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                          Date actEndTime) {
        String stockCacheKey = getStockCacheKey(strategyId, benefitId);
        String balanceStockCacheKey = getBalanceStockCacheKey(strategyId, benefitId);
        // DB 中库存不存在，打点告警
        Map<String, Object> dataMap = getEventDataMap(activityId, strategyId, benefitId);
        EquityBenefitStockDO benefitStock = this.getBenefitStock(activityId, strategyId, benefitId);
        if (Objects.isNull(benefitStock)) {
            log.warn("checkAndRefreshRedisStock stock_db_is_null. activityId={}, strategyId={}, benefitId={}", activityId, strategyId, benefitId);
            CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "stock_db_is_null", Status.SUCCESS, dataMap);
            return;
        }
        Long dbStockCount = benefitStock.getStockCount();
        Long dbBalanceStockCount = benefitStock.getBalanceCount();
        Long unHandledCount = equityBenefitStockFlowService.sumUnHandledFlowOperationCount(activityId, strategyId, benefitId);
        unHandledCount = ObjectUtils.defaultIfNull(unHandledCount, 0L);

        // 如果 redis 不存在库存值，重新初始化库存值，并打点告警
        Date now = new Date();
        // 如果活动未设置结束时间，则 Redis 过期时间默认为次年的第一天结束时间
        Date endDate = ObjectUtils.defaultIfNull(actEndTime, org.apache.commons.lang3.time.DateUtils.addDays(DateUtil.getEndOfYear(now), 1));
        long timeToLive = DateUtil.buildExpireSecNotRandom(now, endDate);
        Long redisStockCount = redisCache.getLongValueWithMaster(stockCacheKey);
        Long redisBalanceStockCount = redisCache.getLongValueWithMaster(balanceStockCacheKey);
        boolean hasRedisStock = Objects.nonNull(redisStockCount);
        boolean hasRedisBalanceStock = Objects.nonNull(redisBalanceStockCount);
        if (!hasRedisStock) {
            log.warn("checkAndRefreshRedisStock stock_redis_is_null. activityId={}, strategyId={}, benefitId={}, " +
                    "dbStockCount={}", activityId, strategyId, benefitId, dbStockCount);
            CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "stock_redis_is_null", Status.SUCCESS, dataMap);
            redisCache.setIfAbsent(stockCacheKey, dbStockCount, Duration.ofSeconds(timeToLive));
        }
        if (!hasRedisBalanceStock) {
            log.warn("checkAndRefreshRedisStock balance_stock_redis_is_null. activityId={}, strategyId={}, benefitId={}, " +
                    "dbBalanceStockCount={}, unHandledCount={}", activityId, strategyId, benefitId, dbBalanceStockCount, unHandledCount);
            CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "balance_stock_redis_is_null", Status.SUCCESS, dataMap);
            redisCache.setIfAbsent(balanceStockCacheKey, dbBalanceStockCount - unHandledCount, Duration.ofSeconds(timeToLive));
        }

        // 比较 db 和 redis 库存值是否相同：总库存需要相等；剩余库存允许一定差值（-4 <= dbStock - redisStock <= 64），差值不在配置范围内则告警
        if (hasRedisStock && !Objects.equals(redisStockCount, dbStockCount)) {
            log.warn("checkAndRefreshRedisStock stock_db_and_redis_diff. activityId={}, strategyId={}, benefitId={}, " +
                    "dbStockCount={}, redisStockCount={}", activityId, strategyId, benefitId, dbStockCount, redisStockCount);
            CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "stock_db_and_redis_diff", Status.SUCCESS, dataMap);
            if (forceRefresh) {
                redisCache.setValue(stockCacheKey, String.valueOf(dbStockCount), timeToLive, TimeUnit.SECONDS);
                CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "force_refresh_stock_redis", Status.SUCCESS, dataMap);
            }
        }
        if (hasRedisBalanceStock && !LongUtils.isNumberBetween(dbBalanceStockCount - unHandledCount - redisBalanceStockCount, balanceStockCompareDiffNumberRange)) {
            log.warn("checkAndRefreshRedisStock balance_stock_db_and_redis_diff. activityId={}, strategyId={}, benefitId={}, " +
                            "dbBalanceStockCount={}, redisBalanceStockCount={}, unHandledCount={}", activityId, strategyId, benefitId,
                    dbBalanceStockCount, redisBalanceStockCount, unHandledCount);
            CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "balance_stock_db_and_redis_diff", Status.SUCCESS, dataMap);
            if (forceRefresh) {
                redisCache.setValue(balanceStockCacheKey, String.valueOf(dbBalanceStockCount - unHandledCount), timeToLive, TimeUnit.SECONDS);
                CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "force_refresh_balance_redis", Status.SUCCESS, dataMap);
            }
        }

        // 如果 redis 库存值有效期时间不足，则重新设置过期时间，并打点告警
        if (hasRedisStock) {
            redisCache.checkAndExtendExpireTimeWithMaster(stockCacheKey, timeToLive);
        }
        if (hasRedisBalanceStock) {
            redisCache.checkAndExtendExpireTimeWithMaster(balanceStockCacheKey, timeToLive);
        }
    }

    @Override
    public void checkAndRefreshRedisPlanStock(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                              Integer planDateType, @NotNull Date planDate) {
        if (!PlanDateTypeEnum.isHasPlanStockLimit(planDateType)) {
            // 计划库存不存在
            return;
        }
        String planDateStr = DateUtil.format_yyyyMMdd(planDate);
        String planStockCacheKey = getPlanStockCacheKey(strategyId, benefitId, planDateType, planDateStr);
        String planBalanceStockCacheKey = getPlanBalanceStockCacheKey(strategyId, benefitId, planDateType, planDateStr);
        // DB 中库存不存在，打点告警
        Map<String, Object> dataMap = getEventDataMap(activityId, strategyId, benefitId);
        EquityBenefitStockPlanDO benefitPlanStock = this.getBenefitPlanStock(activityId, strategyId, benefitId, planDateType, planDateStr);
        if (Objects.isNull(benefitPlanStock)) {
            log.warn("checkAndRefreshRedisPlanStock plan_stock_db_is_null. activityId={}, strategyId={}, benefitId={}", activityId, strategyId, benefitId);
            CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "plan_stock_db_is_null", Status.SUCCESS, dataMap);
            return;
        }
        Long dbPlanStockCount = benefitPlanStock.getStockCount();
        Long dbPlanBalanceStockCount = benefitPlanStock.getBalanceCount();
        Long unHandledCount = equityBenefitStockFlowService.sumUnHandledFlowPlanOperationCount(activityId, strategyId, benefitId, benefitPlanStock.getId());
        unHandledCount = ObjectUtils.defaultIfNull(unHandledCount, 0L);

        // 如果 redis 不存在库存值，重新初始化库存值，并打点告警
        // 计划库存过期时间设置为：计划库存时间 + 1day 的结束时间
        Date expireEndTime = DateUtil.getEndOfDay(org.apache.commons.lang3.time.DateUtils.addDays(planDate, 1));
        long timeToLive = DateUtil.buildExpireSecNotRandom(new Date(), expireEndTime);
        Long redisPlanStockCount = redisCache.getLongValueWithMaster(planStockCacheKey);
        Long redisPlanBalanceStockCount = redisCache.getLongValueWithMaster(planBalanceStockCacheKey);
        boolean hasRedisStock = Objects.nonNull(redisPlanStockCount);
        boolean hasRedisBalanceStock = Objects.nonNull(redisPlanBalanceStockCount);
        if (!hasRedisStock) {
            log.warn("checkAndRefreshRedisPlanStock plan_stock_redis_is_null. activityId={}, strategyId={}, benefitId={}, " +
                    "dbPlanStockCount={}", activityId, strategyId, benefitId, dbPlanStockCount);
            CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "plan_stock_redis_is_null", Status.SUCCESS, dataMap);
            redisCache.setIfAbsent(planStockCacheKey, dbPlanStockCount, Duration.ofSeconds(timeToLive));
        }
        if (!hasRedisBalanceStock) {
            log.warn("checkAndRefreshRedisPlanStock plan_balance_stock_redis_is_null. activityId={}, strategyId={}, benefitId={}, " +
                    "dbPlanBalanceStockCount={}, unHandledCount={}", activityId, strategyId, benefitId, dbPlanBalanceStockCount, unHandledCount);
            CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "plan_balance_stock_redis_is_null", Status.SUCCESS, dataMap);
            redisCache.setIfAbsent(planBalanceStockCacheKey, dbPlanBalanceStockCount - unHandledCount, Duration.ofSeconds(timeToLive));
        }

        // 比较 db 和 redis 库存值是否相同：总计划库存需要相等；剩余计划库存允许一定差值（-4 <= dbStock - redisStock <= 64），差值不在配置范围内则告警
        if (hasRedisStock && !Objects.equals(redisPlanStockCount, dbPlanStockCount)) {
            log.warn("checkAndRefreshRedisPlanStock plan_stock_db_and_redis_diff. activityId={}, strategyId={}, benefitId={}, " +
                    "dbPlanStockCount={}, redisPlanStockCount={}", activityId, strategyId, benefitId, dbPlanStockCount, redisPlanStockCount);
            CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "plan_stock_db_and_redis_diff", Status.SUCCESS, dataMap);
            if (forceRefresh) {
                redisCache.setValue(planStockCacheKey, String.valueOf(dbPlanStockCount), timeToLive, TimeUnit.SECONDS);
                CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "force_refresh_plan_balance_redis", Status.SUCCESS, dataMap);
            }
        }
        if (hasRedisBalanceStock && !LongUtils.isNumberBetween(dbPlanBalanceStockCount - unHandledCount - redisPlanBalanceStockCount, balanceStockCompareDiffNumberRange)) {
            log.warn("checkAndRefreshRedisPlanStock plan_balance_stock_db_and_redis_diff. activityId={}, strategyId={}, benefitId={}, " +
                            "dbPlanBalanceStockCount={}, redisPlanBalanceStockCount={}, unHandledCount={}", activityId, strategyId, benefitId,
                    dbPlanBalanceStockCount, redisPlanBalanceStockCount, unHandledCount);
            CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "plan_balance_stock_db_and_redis_diff", Status.SUCCESS, dataMap);
            if (forceRefresh) {
                redisCache.setValue(planBalanceStockCacheKey, String.valueOf(dbPlanBalanceStockCount - unHandledCount), timeToLive, TimeUnit.SECONDS);
                CsossUtils.logEvent(MonitorConstants.CHECK_AND_REFRESH_REDIS_STOCK, "force_refresh_plan_balance_redis", Status.SUCCESS, dataMap);
            }
        }

        // 如果 redis 库存值有效期时间不足，则重新设置过期时间，并打点告警
        if (hasRedisStock) {
            redisCache.checkAndExtendExpireTimeWithMaster(planStockCacheKey, timeToLive);
        }
        if (hasRedisBalanceStock) {
            redisCache.checkAndExtendExpireTimeWithMaster(planBalanceStockCacheKey, timeToLive);
        }
    }

    @Override
    public QueryBenefitStockRespDTO queryBenefitStock(QueryBenefitStockReqDTO req) {
        if (req == null || CollectionUtils.isEmpty(req.getBenefitStockQueryItemList())) {
            log.error("queryBenefitStock req is null or benefitStockQueryItemList is empty");
            return QueryBenefitStockRespDTO.builder().build();
        }

        // 构建需要查询的 Redis key 集合
        List<QueryBenefitStockReqDTO.BenefitStockQueryItem> benefitStockQueryItemList = req.getBenefitStockQueryItemList();
        Set<String> mulQueryStockKeys = buildMulQueryStockKeys(benefitStockQueryItemList);

        // 批量查询 Redis
        Map<String, Long> mulQueryStockMap = batchGetCacheKeyValue(mulQueryStockKeys);

        // 构造返回结果
        return QueryBenefitStockRespDTO.builder()
                .benefitStockInfoList(buildBenefitStockInfos(benefitStockQueryItemList, mulQueryStockMap))
                .build();
    }

    private Set<String> buildMulQueryStockKeys(
            List<QueryBenefitStockReqDTO.BenefitStockQueryItem> benefitStockQueryItemList
    ) {
        Set<String> mulQueryStockKeys = Sets.newHashSet();

        if (CollectionUtils.isEmpty(benefitStockQueryItemList)) {
            return mulQueryStockKeys;
        }

        benefitStockQueryItemList.forEach(benefitStockQueryItem -> {
            Long strategyId = benefitStockQueryItem.getStrategyId();
            Long benefitId = benefitStockQueryItem.getBenefitId();

            // 添加总库存和剩余库存的 key
            mulQueryStockKeys.add(getStockCacheKey(strategyId, benefitId));
            mulQueryStockKeys.add(getBalanceStockCacheKey(strategyId, benefitId));

            Integer planDateType = benefitStockQueryItem.getPlanDateType();
            // 如果有计划库存，添加计划库存相关的 key
            if (PlanDateTypeEnum.isHasPlanStockLimit(planDateType)) {
                String dateStr = benefitStockQueryItem.getDateStr();
                // 如果 dateStr 为空，默认为当前日期
                dateStr = org.apache.commons.lang3.StringUtils.isBlank(dateStr) ? DateUtil.format_yyyyMMdd(new Date()) : dateStr;

                mulQueryStockKeys.add(getPlanStockCacheKey(strategyId, benefitId, planDateType, dateStr));
                mulQueryStockKeys.add(getPlanBalanceStockCacheKey(strategyId, benefitId, planDateType, dateStr));
            }
        });

        return mulQueryStockKeys;
    }

    private List<QueryBenefitStockRespDTO.BenefitStockInfo> buildBenefitStockInfos(
            List<QueryBenefitStockReqDTO.BenefitStockQueryItem> benefitStockQueryItemList,
            Map<String, Long> mulQueryStockMap
    ) {
        if (CollectionUtils.isEmpty(benefitStockQueryItemList)) {
            return Lists.newArrayList();
        }

        Map<String, Long> finalMulQueryStockMap = org.apache.commons.collections4.MapUtils.emptyIfNull(mulQueryStockMap);
        return benefitStockQueryItemList.stream()
                .map(benefitStockQueryItem -> {
                    Long strategyId = benefitStockQueryItem.getStrategyId();
                    Long benefitId = benefitStockQueryItem.getBenefitId();
                    Integer planDateType = benefitStockQueryItem.getPlanDateType();
                    String dateStr = benefitStockQueryItem.getDateStr();
                    // 如果 dateStr 为空，默认为当前日期
                    dateStr = org.apache.commons.lang3.StringUtils.isBlank(dateStr) ? DateUtil.format_yyyyMMdd(new Date()) : dateStr;

                    // 获取总库存和剩余库存
                    String stockKey = getStockCacheKey(strategyId, benefitId);
                    String balanceStockKey = getBalanceStockCacheKey(strategyId, benefitId);
                    Long stockCount = finalMulQueryStockMap.get(stockKey);
                    Long balanceCount = finalMulQueryStockMap.get(balanceStockKey);
                    Long planStockCount = null;
                    Long planBalanceCount = null;
                    // 如果有计划库存，获取计划库存相关信息
                    if (PlanDateTypeEnum.isHasPlanStockLimit(planDateType)) {
                        String planStockKey = getPlanStockCacheKey(strategyId, benefitId, planDateType, dateStr);
                        String planBalanceStockKey = getPlanBalanceStockCacheKey(strategyId, benefitId, planDateType, dateStr);
                        planStockCount = finalMulQueryStockMap.get(planStockKey);
                        planBalanceCount = finalMulQueryStockMap.get(planBalanceStockKey);
                    }

                    // 构造结果对象
                    return QueryBenefitStockRespDTO.BenefitStockInfo.builder()
                            .strategyId(strategyId)
                            .benefitId(benefitId)
                            .planDateType(planDateType)
                            .dateStr(dateStr)
                            .stockCount(stockCount)
                            .balanceCount(balanceCount)
                            .planStockCount(planStockCount)
                            .planBalanceCount(planBalanceCount)
                            .build();
                })
                .collect(Collectors.toList());
    }

    private Map<String, RedisMulQueryStockKey> getMulQueryStockKeyMap(QueryStockStatusByRedisMulReq req) {
        if (Objects.isNull(req) || CollectionUtils.isEmpty(req.getReqList())) {
            return null;
        }
        String dateYmd = DateUtil.format_yyyyMMdd(req.getDate());
        Map<String /* key */, RedisMulQueryStockKey> mulQueryStockKeyMap = Maps.newHashMap();
        req.getReqList().forEach(benefitStockLimitReq -> {
            Long strategyId = benefitStockLimitReq.getStrategyId();
            Long benefitId = benefitStockLimitReq.getBenefitId();
            Integer planDateType = ObjectUtils.defaultIfNull(benefitStockLimitReq.getPlanDateType(), PlanDateTypeEnum.UNDEFINED.getType());
            RedisMulQueryStockKey mulPlanQueryKey = getRedisMulQueryStockKey(strategyId, benefitStockLimitReq.getBenefitGroupId(),
                    benefitId, planDateType, dateYmd);
            String stockCacheKey = getStockCacheKey(strategyId, benefitId);
            String balanceStockCacheKey = getBalanceStockCacheKey(strategyId, benefitId);
            mulQueryStockKeyMap.put(stockCacheKey, mulPlanQueryKey);
            mulQueryStockKeyMap.put(balanceStockCacheKey, mulPlanQueryKey);

            if (!PlanDateTypeEnum.isHasPlanStockLimit(planDateType)) {
                return;
            }
            // 计划库存存在，需要查询 redis 中的计划库存值
            String planStockCacheKey = getPlanStockCacheKey(strategyId, benefitId, planDateType, dateYmd);
            String planBalanceStockCacheKey = getPlanBalanceStockCacheKey(strategyId, benefitId, planDateType, dateYmd);
            mulQueryStockKeyMap.put(planStockCacheKey, mulPlanQueryKey);
            mulQueryStockKeyMap.put(planBalanceStockCacheKey, mulPlanQueryKey);
        });
        return mulQueryStockKeyMap;
    }

    private Map<String /* key */, @Nullable Long> batchGetCacheKeyValue(Set<String> cacheKeys) {
        Map<String /* key */, @Nullable Long> mulQueryResult = Maps.newHashMap();
        if (CollectionUtils.isEmpty(cacheKeys)) {
            return mulQueryResult;
        }
        // 分页查询，每次多个 100 个 key
        Lists.partition(Lists.newArrayList(cacheKeys), 100).forEach(sub -> {
            Map<String /* key */, @Nullable Long> mGetResultPart = redisCache.getMulLongValue(sub);
            if (MapUtils.isEmpty(mGetResultPart)) {
                return;
            }
            mulQueryResult.putAll(mGetResultPart);
        });
        return mulQueryResult;
    }

    private QueryStockStatusByRedisMulResp getQueryStockStatusByRedisMulResp(Map<String, RedisMulQueryStockKey> mulQueryStockKeyMap,
                                                                             Map<String, @Nullable Long> mulQueryResultMap) {
        ListMultimap<RedisMulQueryStockKey, Long /* mul count，正常情况下是 4 个，且都不为空则 true */> resultMap = ArrayListMultimap.create();
        mulQueryResultMap.forEach((k, v) -> resultMap.put(mulQueryStockKeyMap.get(k), v));

        Map<BenefitStockLimitInfo, Boolean /* true: has stock */> stockMap = Maps.newHashMap();
        resultMap.keySet().forEach(key -> stockMap.put(getBenefitStockLimitInfo(key), allHasStock(resultMap, key)));
        return QueryStockStatusByRedisMulResp.builder().stockInfo(stockMap).build();
    }

    private boolean allHasStock(ListMultimap<RedisMulQueryStockKey, Long> resultMap, RedisMulQueryStockKey key) {
        List<Long> allStockCounts = getAllStockCounts(resultMap, key);
        int allStockCountSize = CollectionUtils.size(allStockCounts);
        boolean allHasStock = allStockCounts.stream().allMatch(v -> v > 0L);
        // Redis 中不存在对应库存值，咨询时当作有库存处理，后续领取时扣减库存失败。前面查询 redis 成功后，已经做了异常情况（不存在对应库存值）打点
        // 有计划库存，四个库存值：总库存（非计划库存）、剩余库存（非计划库存）、总计划库存（每日库存）、剩余计划库存（每日库存）
        if (allHasStock && PlanDateTypeEnum.isHasPlanStockLimit(key.getPlanDateType()) && allStockCountSize <= 4) {
            return true;
        }
        // 无计划库存，两个库存值：总库存（非计划库存）、剩余库存（非计划库存）
        return allHasStock && allStockCountSize <= 2;
    }

    private List<Long> getAllStockCounts(ListMultimap<RedisMulQueryStockKey, Long> resultMap, RedisMulQueryStockKey key) {
        return resultMap.get(key).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private RedisMulQueryStockKey getRedisMulQueryStockKey(Long strategyId, Long benefitGroupId, Long benefitId,
                                                           Integer planDateType, String dateYmd) {
        return RedisMulQueryStockKey.builder()
                .strategyId(strategyId)
                .benefitGroupId(benefitGroupId)
                .benefitId(benefitId)
                .planDateType(planDateType)
                .dateYmd(dateYmd)
                .build();
    }

    private BenefitStockLimitInfo getBenefitStockLimitInfo(RedisMulQueryStockKey redisMulQueryStockKey) {
        return BenefitStockLimitInfo.builder()
                .strategyId(redisMulQueryStockKey.getStrategyId())
                .benefitGroupId(redisMulQueryStockKey.getBenefitGroupId())
                .benefitId(redisMulQueryStockKey.getBenefitId())
                .build();
    }

    private String getStockCacheKey(@NotNull Long strategyId, @NotNull Long benefitId) {
        return String.format(CacheKeyConstants.STOCK_COUNT_KEY, strategyId, benefitId);
    }

    private String getBalanceStockCacheKey(@NotNull Long strategyId, @NotNull Long benefitId) {
        return String.format(CacheKeyConstants.BALANCE_COUNT_KEY, strategyId, benefitId);
    }

    private String getPlanStockCacheKey(@NotNull Long strategyId, @NotNull Long benefitId,
                                        @NotNull Integer planDateType, @NotNull String dateYmd) {
        return String.format(CacheKeyConstants.PLAN_STOCK_COUNT_KEY, strategyId, benefitId, planDateType, dateYmd);
    }

    private String getPlanBalanceStockCacheKey(@NotNull Long strategyId, @NotNull Long benefitId,
                                               @NotNull Integer planDateType, @NotNull String dateYmd) {
        return String.format(CacheKeyConstants.PLAN_BALANCE_COUNT_KEY, strategyId, benefitId, planDateType, dateYmd);
    }

    private Map<String, Object> getEventDataMap(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId) {
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("activityId", activityId);
        dataMap.put("strategyId", strategyId);
        dataMap.put("benefitId", benefitId);
        return dataMap;
    }

    private StockLimitRuleDTO getStockLimitRuleDTOFromDB(Long strategyId, Long benefitId) {
        // 1 先查询权益组规则中是否有库存规则
        StockLimitRuleDTO stockLimitRuleDTO = equityBenefitGroupRuleDomainService.getBenefitStockLimitRule(strategyId, benefitId);
        if (stockLimitRuleDTO != null) {
            return stockLimitRuleDTO;
        }

        // 2 如果权益组规则中没有库存规则，则查询策略规则
        return equityRuleService.getBenefitStockLimitRule(strategyId);
    }
}
