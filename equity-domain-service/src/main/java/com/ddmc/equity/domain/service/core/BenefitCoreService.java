package com.ddmc.equity.domain.service.core;

import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.domain.dto.OperateBenefitResultDTO;
import com.ddmc.equity.domain.dto.UserAccountStatusReqDTO;
import com.ddmc.equity.domain.dto.UserBenefitInfoDTO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;

import java.util.List;

public interface BenefitCoreService {

    /**
     * 权益操作
     * @param equityAccountContext 请求上下文
     * @return 操作结果
     */
    OperateBenefitResultDTO operateBenefit(EquityAccountContext equityAccountContext);

    /**
     * 用户可用权益信息查询
     * @param userId 用户id
     * @param benefitTypeList 权益类型
     * @return 权益信息
     */
    List<UserBenefitInfoDTO> queryCurrentActiveBenefitsInfo(String userId, List<Integer> benefitTypeList);

    /**
     * 查询权益状态
     *
     * @param userAccountStatusReqDTO 请求
     * @return 结果
     */
    AccountInfoDTO queryUserBenefitAccountStatus(UserAccountStatusReqDTO userAccountStatusReqDTO);

    /**
     * 根据场景 code + 活动 id + 策略类型查询权益信息列表
     *
     * @param sceneCode    场景 code
     * @param activityId   活动 id
     * @param strategyType 策略类型
     * @return 权益信息列表
     */
    List<FullBenefitInfoDTO> getFullBenefitInfosByStrategyType(String sceneCode, Long activityId, Integer strategyType);

    /**
     * 根据活动 + 策略类型查询权益信息列表
     *
     * @param activity     活动
     * @param strategyType 策略类型
     * @return 权益信息列表
     */
    List<FullBenefitInfoDTO> getFullBenefitInfosByStrategyType(SceneActivityCacheDto activity, Integer strategyType);

    /**
     * 根据 activityId + strategyId + benefitGroupId + benefitId 查询权益信息
     *
     * @param activityId     活动 id
     * @param strategyId     策略 id
     * @param benefitGroupId 权益组 id
     * @param benefitId      权益 id
     * @return 权益信息
     */
    FullBenefitInfoDTO getOneFullBenefitInfoDTO(Long activityId, Long strategyId, Long benefitGroupId, Long benefitId);
}
