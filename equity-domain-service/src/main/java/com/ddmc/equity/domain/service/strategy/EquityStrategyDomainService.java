package com.ddmc.equity.domain.service.strategy;


import com.ddmc.equity.domain.valueobject.strategy.StrategyListFilterReqVO;
import com.ddmc.equity.domain.valueobject.strategy.StrategyListFilterRespVO;
import com.ddmc.equity.infra.repository.dao.EquityStrategyDO;

import java.util.Collection;
import java.util.List;

public interface EquityStrategyDomainService {

    StrategyListFilterRespVO getListedStrategiesRespByFilter(StrategyListFilterReqVO getListFilterReq);

    /**
     * 保存数据
     *
     * @param strategyDO
     * @return 返回受影响的数据
     */
    int save(EquityStrategyDO strategyDO);

    Long insertOrUpdate(EquityStrategyDO equityStrategyDO);

    boolean updateBatch(List<EquityStrategyDO> equityStrategyDOList);

    /**
     * 软删除
     *
     * @param strategyId
     * @return
     */
    int deleteById(Long strategyId);

    List<EquityStrategyDO> queryAllUnOffline(int limitCount);


    List<Long> queryAllUnOfflineStrategyIds(int limitCount);

    List<EquityStrategyDO> queryAllUnOfflineByActivityIdList(List<Long> activityList);

    List<EquityStrategyDO> queryAllUnOffline();

    List<EquityStrategyDO> queryAllUnOfflineByActivityId(Long activityId);

    List<EquityStrategyDO> queryEquityStrategyDOByActivityId(Long activityId);

    EquityStrategyDO queryByIdTryCacheFirst(Long strategyId);

    List<Long> queryAllUnOfflineIds(Collection<Long> ids);

    void offlineByActivityId(Long activityId);

    /**
     * 根据ID查询策略
     *
     * @param strategyId
     * @return
     */
    EquityStrategyDO getStrategyById(Long strategyId);

    EquityStrategyDO queryByActivityIdAndExternalId(Long activityId, String externalId);

    int update(EquityStrategyDO equityStrategyDO);

    /**
     * 根据策略 ids 查询策略列表
     *
     * @param strategyIds 策略 ids
     * @param statuses    状态列表。如果不指定，则查询所有状态
     * @return 策略列表
     */
    List<EquityStrategyDO> queryStrategiesByIds(List<Long> strategyIds, List<Integer> statuses);
}
