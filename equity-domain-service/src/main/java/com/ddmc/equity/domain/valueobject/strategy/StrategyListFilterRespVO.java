package com.ddmc.equity.domain.valueobject.strategy;

import com.ddmc.equity.infra.repository.dao.EquityActivityDO;
import com.ddmc.equity.infra.repository.dao.EquityStrategyDO;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class StrategyListFilterRespVO implements Serializable {
    private static final long serialVersionUID = -8868341093397678707L;

    @ApiModelProperty("策略列表")
    @NotNull
    @Builder.Default
    private List<EquityStrategyDO> list = Lists.newArrayList();

    @ApiModelProperty("策略记录总数")
    @NotNull
    @Builder.Default
    private Long total = 0L;
}
