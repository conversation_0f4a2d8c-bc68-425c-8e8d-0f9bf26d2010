package com.ddmc.equity.domain.service.benefit;

import com.ddmc.equity.dto.business.BenefitReceiveReqDTO;
import com.ddmc.equity.dto.business.BenefitReceiveRspDTO;
import com.ddmc.equity.infra.repository.dao.BenefitReceiveStatisticsDO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/28
 */
public interface BenefitReceiveDomainService {

    List<BenefitReceiveStatisticsDO> queryBenefitReceiveDetail(BenefitReceiveReqDTO benefitReceiveReqDTO);
}
