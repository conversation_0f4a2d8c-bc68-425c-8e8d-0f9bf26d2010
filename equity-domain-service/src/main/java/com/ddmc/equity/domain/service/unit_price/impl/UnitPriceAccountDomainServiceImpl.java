package com.ddmc.equity.domain.service.unit_price.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.service.unit_price.UnitPriceAccountDomainService;
import com.ddmc.equity.infra.repository.dao.UnitPriceAccountDO;
import com.ddmc.equity.infra.repository.dao.mapper.UnitPriceAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/16 11:30
 * @description
 */
@Slf4j
@Service
public class UnitPriceAccountDomainServiceImpl extends ServiceImpl<UnitPriceAccountMapper, UnitPriceAccountDO>
        implements UnitPriceAccountDomainService {

    @Override
    public void insertAccountDO(UnitPriceAccountDO accountDO) {
        Assert.mustTrue(StringUtils.isNotBlank(accountDO.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "单价促销品权益子账户 userId 不能为空");
        this.save(accountDO);
    }

    @Override
    public List<UnitPriceAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds, List<Integer> statuses, Date startDate, Date endDate) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询单价促销品权益子账户 userId 不能为空");

        Wrapper<UnitPriceAccountDO> wrapper = Wrappers.<UnitPriceAccountDO>lambdaQuery()
                .eq(UnitPriceAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(UnitPriceAccountDO::getUserId, userId)
                .in(CollectionUtils.isNotEmpty(activityIds), UnitPriceAccountDO::getActivityId, activityIds)
                .in(CollectionUtils.isNotEmpty(statuses), UnitPriceAccountDO::getStatus, statuses)
                .ge(Objects.nonNull(startDate), UnitPriceAccountDO::getCreateTime, startDate)
                .le(Objects.nonNull(endDate), UnitPriceAccountDO::getCreateTime, endDate)
                .orderByDesc(UnitPriceAccountDO::getId);
        return this.list(wrapper);
    }
}
