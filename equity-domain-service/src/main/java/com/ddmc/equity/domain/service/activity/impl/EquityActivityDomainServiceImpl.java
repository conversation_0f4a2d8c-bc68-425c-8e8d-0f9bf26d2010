package com.ddmc.equity.domain.service.activity.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ddmc.equity.common.constant.ActivityConfigConstants;
import com.ddmc.equity.enums.ActivityInitStockEnum;
import com.ddmc.equity.enums.ActivityStatusEnum;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.exception.ApiBusinessException;
import com.ddmc.equity.common.util.BeanUtil;
import com.ddmc.equity.common.util.LongUtils;
import com.ddmc.equity.domain.service.activity.EquityActivityDomainService;
import com.ddmc.equity.domain.valueobject.activity.ActivityListFilterReqVO;
import com.ddmc.equity.domain.valueobject.activity.ActivityListFilterRespVO;
import com.ddmc.equity.enums.ActivityStatusEnum;
import com.ddmc.equity.infra.repository.dao.EquityActivityDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityActivityMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EquityActivityDomainServiceImpl implements EquityActivityDomainService {

    @Resource
    private EquityActivityMapper equityActivityMapper;


    @Override
    public Boolean updateActivityStartTimeById(Long activityId, Date startTime) {
        return equityActivityMapper.updateActivityStartTimeById(activityId, startTime) > 0;
    }

    @Override
    public @NotNull List<EquityActivityDO> listBySceneCode(@NotNull String sceneCode) {
        List<EquityActivityDO> result = equityActivityMapper.selectList(new LambdaQueryWrapper<EquityActivityDO>()
                .eq(EquityActivityDO::getSceneCode, sceneCode));
        if (CollectionUtils.isNotEmpty(result)) {
            return result;
        }
        return Collections.emptyList();
    }

    // cr: 未到结束时间，且状态非新建且下线
    @Override
    public @NotNull List<EquityActivityDO> listUnOfflineBySceneCode(@NotNull String sceneCode) {
        Date now = new Date();
        List<EquityActivityDO> result = equityActivityMapper.selectList(new LambdaQueryWrapper<EquityActivityDO>()
                .eq(EquityActivityDO::getSceneCode, sceneCode)
                .le(EquityActivityDO::getEndTime, now)
                .eq(EquityActivityDO::getStatus, ActivityStatusEnum.PUBLISHED.getStatus()));
        if (CollectionUtils.isNotEmpty(result)) {
            return result;
        }
        return Collections.emptyList();
    }


    @Override
    public ActivityListFilterRespVO getListedActivitiesRespByFilter(ActivityListFilterReqVO filter) {
        @NotNull QueryWrapper<EquityActivityDO> wrapper = getActivityListQueryWrapper(filter);
        IPage<EquityActivityDO> iPage = new Page(filter.getPage(), filter.getPageSize());
        iPage = equityActivityMapper.selectPage(iPage, wrapper);
        if (iPage == null) {
            return null;
        }
        List<EquityActivityDO> records = iPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new ActivityListFilterRespVO();
        } else {
            return ActivityListFilterRespVO.builder().list(records).total(iPage.getTotal()).build();
        }
    }

    @Override
    public void save(EquityActivityDO needSaveDO) {
        if (LongUtils.isTrue(needSaveDO.getId())) {
            equityActivityMapper.updateById(needSaveDO);
        } else {
            if (Objects.isNull(needSaveDO.getExternalId())) {
                needSaveDO.setExternalId(UUID.randomUUID().toString().trim().replace("-", ""));
            }
            equityActivityMapper.insert(needSaveDO);
        }
    }

    @Override
    public Long insertOrUpdate(EquityActivityDO activityEntity) {
        int result = 1;
        if (Objects.isNull(activityEntity)) {
            throw new ApiBusinessException(ExceptionEnum.COMMON_ERROR);
        }
        QueryWrapper<EquityActivityDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("external_id", activityEntity.getExternalId()).eq("external_type", activityEntity.getExternalType());
        EquityActivityDO activityEntityDb = equityActivityMapper.selectOne(queryWrapper);
        if (Objects.isNull(activityEntityDb)) {
            result = equityActivityMapper.insert(activityEntity);
        } else {
            activityEntity.setId(activityEntityDb.getId());
            if (BeanUtil.beanHavingChangeAndSet(activityEntity, activityEntityDb, ActivityConfigConstants.ACTIVITY_COLUMN_LIST)) {
                result = equityActivityMapper.updateById(activityEntity);
            }
        }
        if (result != 1) {
            throw new ApiBusinessException(ExceptionEnum.COMMON_ERROR);
        }
        return activityEntity.getId();
    }

    @Override
    public EquityActivityDO queryByThirdTypeAndId(Integer externalType, String externalId) {
        return equityActivityMapper.selectOne(new LambdaQueryWrapper<EquityActivityDO>()
                .eq(EquityActivityDO::getExternalType, externalType)
                .eq(EquityActivityDO::getExternalId, externalId));
    }

    @Override
    public int offlineById(Long id) {
        return equityActivityMapper.update(null, new LambdaUpdateWrapper<EquityActivityDO>()
                .set(EquityActivityDO::getStatus, ActivityStatusEnum.OFFLINE.getStatus())
                .eq(EquityActivityDO::getId, id));
    }


    /**
     * 原子化更新活动状态
     *
     * @param activityId
     * @param oldStatus
     * @param newStatus
     * @return
     */
    @Override
    public int updateActivityStatusByIdAndStatus(Long activityId, ActivityStatusEnum oldStatus, ActivityStatusEnum newStatus) {
        if (Objects.isNull(activityId) || Objects.isNull(oldStatus) || Objects.isNull(newStatus)) {
            return 0;
        }
        return equityActivityMapper.updateActivityStatusByIdAndStatus(activityId, oldStatus.getStatus(), newStatus.getStatus());
    }

    @Override
    public int updateActivityStatusByIdAndInitStatus(Long activityId) {
        if (Objects.isNull(activityId)) {
            return 0;
        }
        return equityActivityMapper.updateActivityStatusByIdAndInitStatus(activityId);
    }


    private static @NotNull QueryWrapper<EquityActivityDO> getActivityListQueryWrapper(ActivityListFilterReqVO req) {
        QueryWrapper<EquityActivityDO> wrapper = new QueryWrapper<>();
        wrapper.and(LongUtils.isTrue(req.getId()) || StringUtils.isNotEmpty(req.getName()),
                        e -> e.eq(LongUtils.isTrue(req.getId()), "id", req.getId())
                                .or(StringUtils.isNotEmpty(req.getName()),
                                        e1 -> e1.like("activity_name", req.getName())))
                .in(CollectionUtils.isNotEmpty(req.getStatuses()), "status", req.getStatuses())
                .in(CollectionUtils.isNotEmpty(req.getExternalTypes()), "external_type", req.getExternalTypes())
                .in(CollectionUtils.isNotEmpty(req.getSendTypes()), "send_type", req.getSendTypes())
                .eq(StringUtils.isNotEmpty(req.getCreator()), "op_admin_name", req.getCreator())
                .eq(StringUtils.isNotEmpty(req.getUpdater()), "edit_admin_name", req.getUpdater())
                .eq("is_delete", CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(StringUtils.isNotEmpty(req.getSceneCode()), "scene_code", req.getSceneCode());
        return wrapper.orderByDesc("create_time");
    }

    @Override
    public List<EquityActivityDO> queryAllUnOffline() {
        QueryWrapper<EquityActivityDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.ne("status", ActivityStatusEnum.OFFLINE.getStatus());
        return equityActivityMapper.selectList(queryWrapper);
    }

    @Override
    public List<EquityActivityDO> queryAllUnOfflineByIds(@NotNull Collection<Long> ids) {
        QueryWrapper<EquityActivityDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids);
        queryWrapper.ne("status", ActivityStatusEnum.OFFLINE.getStatus());
        return equityActivityMapper.selectList(queryWrapper);
    }

    @Override
    public Page<EquityActivityDO> queryAllUnOfflineByPage(int pageNo, int pageSize) {
        Page<EquityActivityDO> page = new Page<>(pageNo, pageSize);
        QueryWrapper<EquityActivityDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.ge("end_time",new Date());
        queryWrapper.eq("status", ActivityStatusEnum.PUBLISHED.getStatus());
        queryWrapper.last(" order by id desc ");
        return equityActivityMapper.selectPage(page, queryWrapper);
    }

    @Override
    public EquityActivityDO queryEquityActivityDOById(Long id) {
        return equityActivityMapper.selectById(id);
    }

    @Override
    public List<EquityActivityDO> queryEquityActivityDOByIdS(List<Long> ids) {
        QueryWrapper<EquityActivityDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids);
        return equityActivityMapper.selectList(queryWrapper);
    }

    /**
     * 查询时间区间内有效的活动，含新建和已发布
     *
     * @param sceneCode
     * @param start
     * @param end
     * @param excludeId
     * @return
     */
    @Override
    public List<EquityActivityDO> getEffectiveActivity(String sceneCode, Date start, Date end, Long excludeId) {
        List<Integer> status = Lists.newArrayList();
        status.add(ActivityStatusEnum.DRAFT.getStatus());
        status.add(ActivityStatusEnum.PUBLISHED.getStatus());
        return equityActivityMapper.getEffectiveStatusActivity(sceneCode, start, end, status, excludeId);
    }

    /**
     * 获取时间已经结束，但是状态非结束态的活动
     *
     * @param sceneCodes
     * @return
     */
    @Override
    public List<EquityActivityDO> getEndActivityAndNotOffline(List<String> sceneCodes) {
        List<Integer> status = Lists.newArrayList();
        status.add(ActivityStatusEnum.DRAFT.getStatus());
        status.add(ActivityStatusEnum.PUBLISHED.getStatus());

        QueryWrapper<EquityActivityDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("status", status);
        queryWrapper.isNotNull("end_time");
        queryWrapper.le("end_time", DateUtil.date());
        queryWrapper.in("scene_code", sceneCodes);
        return equityActivityMapper.selectList(queryWrapper);
    }


    @Override
    public List<EquityActivityDO> getActivityBySceneCodeAndStatusAndBegTimeAndEndTime(List<String> sceneCodeList, Date start) {
        QueryWrapper<EquityActivityDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", ActivityStatusEnum.DRAFT.getStatus());
        queryWrapper.le("start_time", start);
        queryWrapper.in("scene_code", sceneCodeList);
        return equityActivityMapper.selectList(queryWrapper);
    }

    @Override
    public List<EquityActivityDO> getActivityUnInitOnLineActivityBySceneCodes(List<String> sceneCodes) {
        QueryWrapper<EquityActivityDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", ActivityStatusEnum.PUBLISHED.getStatus());
        queryWrapper.in("scene_code", sceneCodes);
        queryWrapper.eq("init_status", ActivityInitStockEnum.UNINIT.getStatus());
        return equityActivityMapper.selectList(queryWrapper);
    }

    @Override
    public Integer updateEquityActivityDO(EquityActivityDO equityActivityDO) {
        return equityActivityMapper.updateById(equityActivityDO);
    }

    public Boolean updateById(EquityActivityDO equityActivityDO) {
        return equityActivityMapper.updateById(equityActivityDO) > 0;
    }

    @Override
    public List<Long> queryActivityIdsBySceneCode(String sceneCode, List<Integer> statuses) {
        if (StringUtils.isBlank(sceneCode)) {
            log.warn("queryActivityIdsBySceneCode sceneCode is null");
            return null;
        }
        Wrapper<EquityActivityDO> wrapper = Wrappers.<EquityActivityDO>lambdaQuery()
                .select(EquityActivityDO::getId)
                .eq(EquityActivityDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(EquityActivityDO::getSceneCode, sceneCode)
                .in(CollectionUtils.isNotEmpty(statuses), EquityActivityDO::getStatus, statuses);
        List<EquityActivityDO> activityDOList = equityActivityMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(activityDOList)) {
            return null;
        }
        return activityDOList.stream().map(EquityActivityDO::getId).collect(Collectors.toList());
    }

    @Override
    public List<Long> queryNonDraftActivityIdsBySceneCode(String sceneCode) {
        List<Integer> statuses = Lists.newArrayList(ActivityStatusEnum.PUBLISHED.getStatus(), ActivityStatusEnum.OFFLINE.getStatus());
        return this.queryActivityIdsBySceneCode(sceneCode, statuses);
    }

    @Override
    public List<Long> queryActivityIdsByExternal(Integer externalType, List<String> externalIds) {
        if (Objects.isNull(externalType) || CollectionUtils.isEmpty(externalIds)) {
            log.warn("queryActivityIdsByExternal externalType or externalIds is null");
            return null;
        }
        Wrapper<EquityActivityDO> wrapper = Wrappers.<EquityActivityDO>lambdaQuery()
                .select(EquityActivityDO::getId)
                .eq(EquityActivityDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(EquityActivityDO::getExternalType, externalType)
                .in(EquityActivityDO::getExternalId, externalIds);
        List<EquityActivityDO> activityDOList = equityActivityMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(activityDOList)) {
            return null;
        }
        return activityDOList.stream().map(EquityActivityDO::getId).collect(Collectors.toList());
    }
}
