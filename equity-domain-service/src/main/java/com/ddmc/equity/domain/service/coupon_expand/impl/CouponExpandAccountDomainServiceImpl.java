package com.ddmc.equity.domain.service.coupon_expand.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.service.coupon_expand.CouponExpandAccountDomainService;
import com.ddmc.equity.infra.repository.dao.CouponExpandAccountDO;
import com.ddmc.equity.infra.repository.dao.mapper.CouponExpandAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/26 16:45
 * @description
 */
@Slf4j
@Service
public class CouponExpandAccountDomainServiceImpl extends ServiceImpl<CouponExpandAccountMapper, CouponExpandAccountDO> implements CouponExpandAccountDomainService {

    @Override
    public void insertAccountDO(CouponExpandAccountDO accountDO) {
        Assert.mustTrue(StringUtils.isNotBlank(accountDO.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "券膨胀权益子账户 userId 不能为空");
        this.save(accountDO);
    }

    @Override
    public List<CouponExpandAccountDO> queryAccountsByUserIdAndReqNo(String userId, String reqNo) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询券膨胀权益子账户 userId 不能为空");
        Assert.mustTrue(StringUtils.isNotBlank(reqNo), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询券膨胀权益子账户业务流水号不能为空");

        Wrapper<CouponExpandAccountDO> wrapper = Wrappers.<CouponExpandAccountDO>lambdaQuery()
                .eq(CouponExpandAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(CouponExpandAccountDO::getUserId, userId)
                .eq(CouponExpandAccountDO::getLastReqNo, reqNo);
        return this.list(wrapper);
    }

    @Override
    public List<CouponExpandAccountDO> queryAccountsByUserId(String userId) {
        Wrapper<CouponExpandAccountDO> wrapper = Wrappers.<CouponExpandAccountDO>lambdaQuery().eq(CouponExpandAccountDO::getUserId,userId);
        return list(wrapper);
    }

    @Override
    public List<CouponExpandAccountDO> queryAccountRecordByUserIdAndActivityIdsAndBegTimeAndEndTime(String userId, List<Long> activityIds, Date begDate, Date endDate) {
        Wrapper<CouponExpandAccountDO> wrapper = Wrappers.<CouponExpandAccountDO>lambdaQuery().eq(CouponExpandAccountDO::getUserId,userId)
                .in(CollectionUtils.isNotEmpty(activityIds),CouponExpandAccountDO::getActivityId,activityIds)
                .ge(Objects.nonNull(begDate), CouponExpandAccountDO::getCreateTime, begDate)
                .le(Objects.nonNull(endDate), CouponExpandAccountDO::getCreateTime, endDate);
        return list(wrapper);
    }
}
