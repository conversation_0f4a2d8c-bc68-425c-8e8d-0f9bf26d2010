package com.ddmc.equity.domain.entity.benefitRule;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.common.util.IntegerUtil;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.dto.rule.condition.RfTypeRuleDTO;
import com.ddmc.equity.dto.business.UniversalBenefitDTO;
import com.ddmc.equity.dto.business.UniversalRuleDTO;
import com.ddmc.equity.dto.business.rule.ABTestDTO;
import com.ddmc.equity.dto.business.rule.AppVersionDTO;
import com.ddmc.equity.dto.business.rule.CitiesDTO;
import com.ddmc.equity.dto.business.rule.ReceiveLimitDTO;
import com.ddmc.equity.dto.business.rule.RfTypeDTO;
import com.ddmc.equity.dto.business.rule.StockLimitDTO;
import com.ddmc.equity.dto.business.rule.UserTagDTO;
import com.ddmc.equity.dto.business.rule.VipIdentityDTO;
import com.ddmc.equity.dto.business.rule.WhiteUserListDTO;
import com.ddmc.equity.enums.RuleScopeEnum;
import com.ddmc.equity.infra.repository.dao.EquityBenefitGroupRuleDO;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

@Data
@Builder
@Slf4j
public class BenefitGroupRuleEntity {

    public static List<EquityBenefitGroupRuleDO> buildBenefitGroupRuleDoListFromDTO(List<UniversalBenefitDTO> benefitDTOList, String opAdminId, String opAdminName) {
        List<EquityBenefitGroupRuleDO> equityRuleDOS = Lists.newArrayList();
        if (CollectionUtils.isEmpty(benefitDTOList)) {
            return equityRuleDOS;
        }

        for (UniversalBenefitDTO benefitDTO : benefitDTOList) {
            UniversalRuleDTO ruleDTO = benefitDTO.getRuleDTO();
            if (Objects.isNull(ruleDTO)) {
                continue;
            }
            //ab转换
            EquityBenefitGroupRuleDO abEquityRuleDO = convertAbTestRuleFromDTO(ruleDTO.getAbTestDTO());
            if (!Objects.isNull(abEquityRuleDO)) {
                abEquityRuleDO.setBenefitId(benefitDTO.getBenefitId());
                fillOpAdminInfo(abEquityRuleDO, opAdminId, opAdminName);
                equityRuleDOS.add(abEquityRuleDO);
            }
            //城市规则转换
            EquityBenefitGroupRuleDO cityEquityRuleDO = convertCityRuleFromDTO(ruleDTO.getCitiesDTO());
            if (!Objects.isNull(cityEquityRuleDO)) {
                cityEquityRuleDO.setBenefitId(benefitDTO.getBenefitId());
                fillOpAdminInfo(cityEquityRuleDO, opAdminId, opAdminName);
                equityRuleDOS.add(cityEquityRuleDO);
            }

            //app版本规则转换
            EquityBenefitGroupRuleDO appVersionEquityRuleDO = convertAppVersionRuleFromDTO(ruleDTO.getAppVersionDTO());
            if (!Objects.isNull(appVersionEquityRuleDO)) {
                appVersionEquityRuleDO.setBenefitId(benefitDTO.getBenefitId());
                fillOpAdminInfo(appVersionEquityRuleDO, opAdminId, opAdminName);
                equityRuleDOS.add(appVersionEquityRuleDO);
            }

            //人群规则转换
            EquityBenefitGroupRuleDO userTagEquityRuleDO = convertUserTagRuleFromDTO(ruleDTO.getUserTagDTO());
            if (!Objects.isNull(userTagEquityRuleDO)) {
                userTagEquityRuleDO.setBenefitId(benefitDTO.getBenefitId());
                fillOpAdminInfo(userTagEquityRuleDO, opAdminId, opAdminName);
                equityRuleDOS.add(userTagEquityRuleDO);
            }
            //用户白名单规则转换
            EquityBenefitGroupRuleDO whiteUserEquityRuleDO = convertWhiteUserRuleFromDTO(ruleDTO.getWhiteUserListDTO());
            if (!Objects.isNull(whiteUserEquityRuleDO)) {
                whiteUserEquityRuleDO.setBenefitId(benefitDTO.getBenefitId());
                fillOpAdminInfo(whiteUserEquityRuleDO, opAdminId, opAdminName);
                equityRuleDOS.add(whiteUserEquityRuleDO);
            }
            //会员身份规则转换
            EquityBenefitGroupRuleDO vipIdentityEquityRuleDO = convertVipIdentityRuleFromDTO(ruleDTO.getVipIdentityDTO());
            if (!Objects.isNull(vipIdentityEquityRuleDO)) {
                vipIdentityEquityRuleDO.setBenefitId(benefitDTO.getBenefitId());
                fillOpAdminInfo(vipIdentityEquityRuleDO, opAdminId, opAdminName);
                equityRuleDOS.add(vipIdentityEquityRuleDO);
            }

            //频次
            EquityBenefitGroupRuleDO receiveLimitEquityRuleDO = convertReceiveLimitRuleDO(ruleDTO.getReceiveLimitDTO());
            if (!Objects.isNull(receiveLimitEquityRuleDO)) {
                receiveLimitEquityRuleDO.setBenefitId(benefitDTO.getBenefitId());
                fillOpAdminInfo(receiveLimitEquityRuleDO, opAdminId, opAdminName);
                receiveLimitEquityRuleDO.setRuleScope(ObjectUtils.defaultIfNull(receiveLimitEquityRuleDO.getRuleScope(), RuleScopeEnum.CONSULT_AND_RECEIVE.getCode()));
                equityRuleDOS.add(receiveLimitEquityRuleDO);
            }
            //库存
            EquityBenefitGroupRuleDO stockLimitEquityRuleDO = convertStockLimitRuleDO(ruleDTO.getStockLimitDTO());
            if (!Objects.isNull(stockLimitEquityRuleDO)) {
                stockLimitEquityRuleDO.setBenefitId(benefitDTO.getBenefitId());
                fillOpAdminInfo(stockLimitEquityRuleDO, opAdminId, opAdminName);
                stockLimitEquityRuleDO.setRuleScope(ObjectUtils.defaultIfNull(stockLimitEquityRuleDO.getRuleScope(), RuleScopeEnum.CONSULT_AND_RECEIVE.getCode()));
                equityRuleDOS.add(stockLimitEquityRuleDO);
            }
            // RF 人群身份
            EquityBenefitGroupRuleDO rfTypeRuleDO = convertRfTypeRuleDO(ruleDTO.getRfTypeDTO());
            if (Objects.nonNull(rfTypeRuleDO)) {
                rfTypeRuleDO.setBenefitId(benefitDTO.getBenefitId());
                fillOpAdminInfo(rfTypeRuleDO, opAdminId, opAdminName);
                equityRuleDOS.add(rfTypeRuleDO);
            }
        }

        return equityRuleDOS;
    }

    private static EquityBenefitGroupRuleDO convertAbTestRuleFromDTO(ABTestDTO abTestRuleVO) {
        if (Objects.isNull(abTestRuleVO)) {
            return null;
        }
        if (StringUtils.isBlank(abTestRuleVO.getLayerId()) || CollectionUtils.isEmpty(abTestRuleVO.getDefaultGroup())
                || CollectionUtils.isEmpty(abTestRuleVO.getChoosePutGroup())) {
            return null;
        }
        EquityBenefitGroupRuleDO equityRuleDO = new EquityBenefitGroupRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.AB_TEST_RULE.getCode());
        equityRuleDO.setRuleScope(abTestRuleVO.getRuleScope());
        String ruleValue = JsonUtil.toJsonString(abTestRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityBenefitGroupRuleDO convertCityRuleFromDTO(CitiesDTO cityRuleVO) {
        if (Objects.isNull(cityRuleVO)
                || CollectionUtils.isEmpty(cityRuleVO.getCityCodeList())) {
            return null;
        }
        EquityBenefitGroupRuleDO equityRuleDO = new EquityBenefitGroupRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.CITY_RULE.getCode());
        equityRuleDO.setRuleScope(cityRuleVO.getRuleScope());
        String ruleValue = JsonUtil.toJsonString(cityRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityBenefitGroupRuleDO convertAppVersionRuleFromDTO(AppVersionDTO appVersionRuleVO) {
        if (Objects.isNull(appVersionRuleVO)) {
            return null;
        }
        if (CollectionUtils.isEmpty(appVersionRuleVO.getAppVersionList())) {
            return null;
        }
        EquityBenefitGroupRuleDO equityRuleDO = new EquityBenefitGroupRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.APP_VERSION_RULE.getCode());
        equityRuleDO.setRuleScope(appVersionRuleVO.getRuleScope());
        String ruleValue = JsonUtil.toJsonString(appVersionRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityBenefitGroupRuleDO convertUserTagRuleFromDTO(UserTagDTO userTagRuleVO) {
        if (Objects.isNull(userTagRuleVO)
                || CollectionUtils.isEmpty(userTagRuleVO.getRuleIds())) {
            return null;
        }
        EquityBenefitGroupRuleDO equityRuleDO = new EquityBenefitGroupRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.USER_TAG_RULE.getCode());
        equityRuleDO.setRuleScope(userTagRuleVO.getRuleScope());
        String ruleValue = JsonUtil.toJsonString(userTagRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityBenefitGroupRuleDO convertWhiteUserRuleFromDTO(WhiteUserListDTO whiteUserListRuleVO) {
        if (Objects.isNull(whiteUserListRuleVO)
                || CollectionUtils.isEmpty(whiteUserListRuleVO.getWhiteUserList())) {
            return null;
        }
        EquityBenefitGroupRuleDO equityRuleDO = new EquityBenefitGroupRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.WHITE_USER_LIST_RULE.getCode());
        equityRuleDO.setRuleScope(whiteUserListRuleVO.getRuleScope());
        String ruleValue = JsonUtil.toJsonString(whiteUserListRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityBenefitGroupRuleDO convertVipIdentityRuleFromDTO(VipIdentityDTO vipIdentityRuleVO) {
        if (Objects.isNull(vipIdentityRuleVO)
                || Objects.isNull(vipIdentityRuleVO.getUserStatus())) {
            return null;
        }
        EquityBenefitGroupRuleDO equityRuleDO = new EquityBenefitGroupRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.VIP_IDENTITY_RULE.getCode());
        equityRuleDO.setRuleScope(vipIdentityRuleVO.getRuleScope());
        String ruleValue = JsonUtil.toJsonString(vipIdentityRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityBenefitGroupRuleDO convertReceiveLimitRuleDO(ReceiveLimitDTO receiveLimitDTO) {

        if (Objects.isNull(receiveLimitDTO) || IntegerUtil.isFalse(receiveLimitDTO.getLimitType())) {
            return null;
        }
        EquityBenefitGroupRuleDO equityRuleDO = new EquityBenefitGroupRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode());
        equityRuleDO.setRuleScope(receiveLimitDTO.getRuleScope());
        String ruleValue = JsonUtil.toJsonString(receiveLimitDTO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityBenefitGroupRuleDO convertStockLimitRuleDO(StockLimitDTO stockLimitDTO) {

        if (Objects.isNull(stockLimitDTO) || IntegerUtil.isFalse(stockLimitDTO.getTotalStock())) {
            return null;
        }
        EquityBenefitGroupRuleDO equityRuleDO = new EquityBenefitGroupRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.STOCK_LIMIT_RULE.getCode());
        equityRuleDO.setRuleScope(stockLimitDTO.getRuleScope());
        String ruleValue = JsonUtil.toJsonString(stockLimitDTO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityBenefitGroupRuleDO convertRfTypeRuleDO(RfTypeDTO rfTypeDTO) {
        if (Objects.isNull(rfTypeDTO)) {
            return null;
        }
        if (CollectionUtils.isEmpty(rfTypeDTO.getRfFirstTypeNames()) && CollectionUtils.isEmpty(rfTypeDTO.getRfTypeNames())) {
            return null;
        }
        RfTypeRuleDTO rfTypeRuleDTO = RfTypeRuleDTO.builder()
                .rfFirstTypeNames(rfTypeDTO.getRfFirstTypeNames())
                .rfTypeNames(rfTypeDTO.getRfTypeNames())
                .build();
        EquityBenefitGroupRuleDO ruleDO = new EquityBenefitGroupRuleDO();
        ruleDO.setRuleType(RuleTypeEnum.RF_TYPE_RULE.getCode());
        ruleDO.setRuleValue(JSON.toJSONString(rfTypeRuleDTO));
        ruleDO.setRuleScope(ObjectUtils.defaultIfNull(rfTypeDTO.getRuleScope(), RuleScopeEnum.CONSULT_AND_RECEIVE.getCode()));
        return ruleDO;
    }

    private static void fillOpAdminInfo(EquityBenefitGroupRuleDO abEquityRuleDO, String opAdminId, String opAdminName) {
        if (!Objects.isNull(abEquityRuleDO)) {
            String addOpAdminId = StringUtils.isBlank(opAdminId) ? Constants.DEFAULT_ADMIN_ID : opAdminId;
            String addOpAdminName = StringUtils.isBlank(opAdminName) ? Constants.DEFAULT_ADMIN_ID : opAdminName;
            abEquityRuleDO.setOpAdminId(addOpAdminId);
            abEquityRuleDO.setOpAdminName(addOpAdminName);
            abEquityRuleDO.setEditAdminId(addOpAdminId);
            abEquityRuleDO.setEditAdminName(addOpAdminName);
            abEquityRuleDO.setIsDelete(Constants.STATUS_NO);
            abEquityRuleDO.setRuleScope(ObjectUtils.defaultIfNull(abEquityRuleDO.getRuleScope(), RuleScopeEnum.CONSULT_AND_RECEIVE.getCode()));
        }
    }
}
