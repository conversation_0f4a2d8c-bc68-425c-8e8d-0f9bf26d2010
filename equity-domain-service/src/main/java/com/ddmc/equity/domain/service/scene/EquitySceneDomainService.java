package com.ddmc.equity.domain.service.scene;

import com.ddmc.equity.domain.valueobject.scene.SceneListFilterReqVO;
import com.ddmc.equity.domain.valueobject.scene.SceneListFilterRespVO;
import com.ddmc.equity.infra.repository.dao.EquityActivityDO;
import com.ddmc.equity.infra.repository.dao.EquitySceneDO;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * 场景领域服务
 */
public interface EquitySceneDomainService {

    /**
     * 通过场景ID查询场景信息
     *
     * @param sceneId Long
     * @return EquitySceneDO
     */
    @Nullable EquitySceneDO getSceneById(@NotNull Long sceneId);

    /**
     * 通过场景Code查询场景信息
     *
     * @param sceneCode String
     * @return EquitySceneDO
     */
    @Nullable EquitySceneDO getSceneByCode(@NotNull String sceneCode);

    /**
     * 获取所有已发布的场景
     *
     * @return 已发布的场景列表
     */
    List<EquitySceneDO> getAllPublishedScenes();

    /**
     * 通过场景列表过滤器查询场景列表
     *
     * @param filter SceneListFilterReqVO
     * @return SceneListFilterRespVO
     */
    @Nullable SceneListFilterRespVO getListedScenesRespByFilter(@NotNull SceneListFilterReqVO filter);

    /**
     * 查询场景关联的全部活动包括失效活动，不包括被删除的活动
     *
     * @param sceneCode String
     * @return List
     */
    @Nullable List<EquityActivityDO> listLinkedUncheckedActivities(String sceneCode);

    /**
     * 查询场景关联的全部有效活动，不包括被删除的活动
     *
     * @param sceneCode String
     * @return List
     */
    @Nullable List<EquityActivityDO> listLinkedActiveActivities(String sceneCode);

    /**
     * 保存待保存的场景
     *
     * @param req EquitySceneDO
     */
    void save(EquitySceneDO req);
}
