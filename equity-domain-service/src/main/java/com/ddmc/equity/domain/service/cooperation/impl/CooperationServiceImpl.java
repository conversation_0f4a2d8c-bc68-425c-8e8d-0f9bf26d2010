package com.ddmc.equity.domain.service.cooperation.impl;

import com.ddmc.equity.common.apollo.cooperation.CooperationConfig;
import com.ddmc.equity.common.apollo.cooperation.CooperationConfigConstants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.util.CatMonitorUtil;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.service.cooperation.CooperationService;
import com.ddmc.utils.constant.CommonConstants;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CooperationServiceImpl implements CooperationService {
    @Autowired
    private CooperationConfigConstants cooperationConfigConstants;
    @Autowired
    private CatMonitorUtil catMonitorUtil;


    /**
     * 合作方是否存在
     *
     * @param channel 合作方
     * @return 是否存在
     */
    @Override
    public boolean cooperationChannelIsExist(Integer channel) {
        if (Objects.isNull(channel)) {
            return false;
        }
        List<CooperationConfig> cooperationConfigs = getConfigList();
        if (CollectionUtils.isEmpty(cooperationConfigs)) {
            return false;
        }
        Map<Integer, List<CooperationConfig>> cooperationMap = cooperationConfigs.stream().filter(cooperationConfig -> Objects.nonNull(cooperationConfig.getChannel())).collect(Collectors.groupingBy(CooperationConfig::getChannel));
        boolean isExist = cooperationMap.containsKey(channel);
        if (!isExist) {
            catMonitorUtil.logEventWithSpan(MonitorConstants.VALIDATE_COOPERATION_CONFIG, MonitorConstants.FAIL, CommonConstants.IS_SUCCESS_CODE, String.valueOf(channel));
        }
        return isExist;
    }


    /**
     * 获取 合作方配置
     *
     * @return 合作方配置
     */
    private List<CooperationConfig> getConfigList() {
        String configInfo = cooperationConfigConstants.getCooperationConfigInfo();
        if (StringUtils.isBlank(configInfo)) {
            return Lists.newArrayList();
        }
        try {
            return JsonUtil.toJavaObjectList(configInfo, CooperationConfig.class);
        } catch (Exception e) {
            catMonitorUtil.logEvent(MonitorConstants.COOPERATION_CONFIG_INIT, MonitorConstants.EXCEPTION);
            log.error("CooperationServiceImpl.getConfigList.e", e);
        }
        return Lists.newArrayList();
    }
}
