package com.ddmc.equity.domain.service.core.impl;

import com.alibaba.fastjson.JSON;
import com.csoss.monitor.api.common.AttributeKey;
import com.csoss.monitor.api.common.Attributes;
import com.csoss.monitor.api.internal.Instrumentation;
import com.csoss.monitor.api.metrics.Metrics;
import com.csoss.monitor.api.trace.Span;
import com.csoss.monitor.api.trace.Traces;
import com.csoss.monitor.sdk.resource.AttributeKeys;
import com.ddmc.equity.account.AccountStrategyContext;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.apollo.BenefitConstants;
import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.domain.dto.OperateBenefitResultDTO;
import com.ddmc.equity.domain.dto.UserAccountStatusReqDTO;
import com.ddmc.equity.domain.dto.UserBenefitInfoDTO;
import com.ddmc.equity.domain.entity.benefit.BenefitConvertEntity;
import com.ddmc.equity.domain.entity.common.BenefitOperateEntity;
import com.ddmc.equity.domain.service.core.BenefitCoreService;
import com.ddmc.equity.engine.benefit_group.BenefitSendHandlerContext;
import com.ddmc.equity.engine.strategy.StrategySendHandlerContext;
import com.ddmc.equity.enums.AccountType;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import com.ddmc.equity.model.dto.QueryEquityDto;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BenefitCoreServiceImpl implements BenefitCoreService {

    @Autowired
    private LocalCacheManager localCacheManager;
    @Autowired
    private StrategySendHandlerContext strategySendHandlerContext;
    @Autowired
    private BenefitSendHandlerContext benefitSendHandlerContext;
    @Autowired
    private BenefitConstants benefitConstants;
    @Autowired
    private RedisCache redisCache;

    /**
     * 权益操作
     *
     * @param equityAccountContext 请求上下文
     * @return 操作结果
     */
    @Override
    public OperateBenefitResultDTO operateBenefit(EquityAccountContext equityAccountContext) {
        CsossUtils.logEvent(MonitorConstants.OPERATE_BENEFIT_APP_ID, equityAccountContext.getAppId(), "1", null);
        //加锁
        String lockKey = String.format(CacheKeyConstants.OPERATE_BENEFIT_LOCK, equityAccountContext.getUid(), equityAccountContext.getOperateType(), equityAccountContext.getSerialNumber());
        boolean isLocked = false;
        try {
            //加锁
            isLocked = redisCache.lock(lockKey, 10, TimeUnit.SECONDS);
            if (!isLocked) {
                log.error("BenefitCoreServiceImpl.operateBenefit.lock.fail;req={}", JsonUtil.toJsonString(equityAccountContext));
                return OperateBenefitResultDTO.fail(ExceptionEnum.TOO_FREQUENTING);
            }
            log.info("BenefitCoreServiceImpl.operate.benefit.start;req={}", JsonUtil.toJsonString(equityAccountContext));
            //发放
            if (OperateTypeEnum.PROVIDE.getCode() == equityAccountContext.getOperateType()) {
                return logEventForOperateBenefit(() ->
                        issue(equityAccountContext), "PROVIDE", equityAccountContext);
//                return issue(equityAccountContext);
            }
            //使用权益
            if (OperateTypeEnum.USE.getCode() == equityAccountContext.getOperateType()) {
                return logEventForOperateBenefit(() ->
                        use(equityAccountContext), "USE", equityAccountContext);
//                return use(equityAccountContext);
            }
            //回退
            if (OperateTypeEnum.FALLBACK.getCode() == equityAccountContext.getOperateType()) {
                return logEventForOperateBenefit(() ->
                        fallBack(equityAccountContext), "FALLBACK", equityAccountContext);
//                return fallBack(equityAccountContext);
            }
            return OperateBenefitResultDTO.fail(ExceptionEnum.OPERATE_TYPE_IS_NOT_EXIST);
        } catch (Exception e) {
            log.error("BenefitCoreServiceImpl.operateBenefit.e;req={};e", JsonUtil.toJsonString(equityAccountContext), e);
            return OperateBenefitResultDTO.fail(ExceptionEnum.COMMON_ERROR);
        } finally {
            redisCache.unlock(lockKey, isLocked);
        }
    }

    private static <T> T logEventForOperateBenefit(
            @NotNull Supplier<T> action, @NotNull String name, @NotNull EquityAccountContext context) {
        String type = "OperateBenefit.Action";
        Span span = Traces.spanBuilder(type, Instrumentation.EVENT_NEW).startSpan();
        Metrics.newCounter(type, Instrumentation.EVENT_NEW).build().once(Attributes.of(AttributeKey.stringKey("name"), name));
        Attributes attributes = Attributes.of(AttributeKey.stringKey("name"), name,
                AttributeKey.stringKey(AttributeKeys.METRIC_STATUS), "1");
        span.metricsMapping(type, attributes);
        span.addEvent("event_data", Attributes.of(AttributeKey.stringKey("context"), JSON.toJSONString(context)));
        try {
            return action.get();
        } catch (Exception e) {
            span.recordException(e);
            throw e;
        } finally {
            span.end();
        }
    }

    /**
     * 权益回退
     *
     * @param equityAccountContext 请求上下文
     * @return 结果
     */
    private OperateBenefitResultDTO fallBack(EquityAccountContext equityAccountContext) {
        //操作声明
        AccountStrategyContext accountStrategyContext = AccountStrategyContext.builder(equityAccountContext.getAccountType());
        //使用
        boolean result = accountStrategyContext.fallbackEquity(equityAccountContext);
        return OperateBenefitResultDTO.result(result);
    }

    /**
     * 权益使用
     *
     * @param equityAccountContext 请求上下文
     * @return 结果
     */
    private OperateBenefitResultDTO use(EquityAccountContext equityAccountContext) {
        //操作声明
        AccountStrategyContext accountStrategyContext = AccountStrategyContext.builder(equityAccountContext.getAccountType());
        //使用
        boolean result = accountStrategyContext.useEquity(equityAccountContext);
        return OperateBenefitResultDTO.result(result);
    }

    /**
     * 发放
     *
     * @param equityAccountContext 请求上下文
     * @return 发放结果
     */
    private OperateBenefitResultDTO issue(EquityAccountContext equityAccountContext) {
        //操作声明
        AccountStrategyContext accountStrategyContext = AccountStrategyContext.builder(equityAccountContext.getAccountType());
        //发放
        boolean result = accountStrategyContext.provideEquity(equityAccountContext);
        return OperateBenefitResultDTO.result(result);
    }

    /**
     * 用户可用权益信息查询
     *
     * @param userId          用户id
     * @param benefitTypeList 权益类型
     * @return 权益信息
     */
    @Override
    public List<UserBenefitInfoDTO> queryCurrentActiveBenefitsInfo(String userId, List<Integer> benefitTypeList) {
        if (StringUtils.isBlank(userId)) {
            return Lists.newArrayList();
        }
        //权益类型不为空
        if (!CollectionUtils.isEmpty(benefitTypeList)) {
            benefitTypeList = benefitTypeList.stream().distinct().collect(Collectors.toList());
            return queryUserCurrentActiveBenefitsInfo(userId, benefitTypeList);
        }
        //可查询的列表
        List<Integer> benefitTypeConfigList = benefitConstants.getCurrentActiveBenefitQueryAllTypeList();
        if (CollectionUtils.isEmpty(benefitTypeConfigList)) {
            return Lists.newArrayList();
        }
        return queryUserCurrentActiveBenefitsInfo(userId, benefitTypeConfigList);
    }

    /**
     * 查询权益状态
     *
     * @param userAccountStatusReqDTO 请求
     * @return 结果
     */
    @Override
    public AccountInfoDTO queryUserBenefitAccountStatus(UserAccountStatusReqDTO userAccountStatusReqDTO) {
        //操作声明
        AccountStrategyContext accountStrategyContext = AccountStrategyContext.builder(userAccountStatusReqDTO.getAccountType());
        BenefitOperateEntity benefitOperateEntity = BenefitOperateEntity.builder().build();
        //参数转换
        EquityAccountContext equityAccountContext = benefitOperateEntity.convertUserAccountStatusReqDTOToEquityAccountContext(userAccountStatusReqDTO);
        //使用
        return accountStrategyContext.queryEquityAccountInfo(equityAccountContext);
    }

    @Override
    public List<FullBenefitInfoDTO> getFullBenefitInfosByStrategyType(String sceneCode, Long activityId, Integer strategyType) {
        SceneActivityCacheDto activity = localCacheManager.getSceneActivityCacheDtoByActivityId(activityId, sceneCode);
        if (Objects.isNull(activity)) {
            log.warn("getFullBenefitInfosByStrategyType activity is null. sceneCode={}, activityId={}, strategyType={}",
                    sceneCode, activityId, strategyType);
            return null;
        }
        return getFullBenefitInfosByStrategyType(activity, strategyType);
    }

    @Override
    public List<FullBenefitInfoDTO> getFullBenefitInfosByStrategyType(SceneActivityCacheDto activity, Integer strategyType) {
        Long activityId = activity.getActivityId();
        Integer activitySendType = activity.getSendType();
        List<StrategyCacheDto> strategies = getStrategiesByStrategyType(activity.getStrategyCacheDtoList(), strategyType);
        List<StrategyCacheDto> hitStrategies = strategySendHandlerContext.chooseStrategiesBySendType(strategies, activitySendType);
        if (CollectionUtils.isEmpty(hitStrategies)) {
            log.warn("getFullBenefitInfosByStrategyType hitStrategies is null. activityId={}, strategyType={}, activitySendType={}",
                    activityId, strategyType, activitySendType);
            return null;
        }

        return hitStrategies.stream().map(strategy -> {
            List<BenefitIdWithConfDto> allBenefits = strategy.getStrategyBenefitGroup().get(strategy.getStrategyId());
            List<BenefitIdWithConfDto> hitBenefits = benefitSendHandlerContext.chooseBenefitsBySendType(allBenefits, strategy.getSendType());
            if (CollectionUtils.isEmpty(hitBenefits)) {
                return null;
            }
            return hitBenefits.stream().map(benefit ->
                    BenefitConvertEntity.convertToFullBenefitInfoDTO(activity, strategy, benefit)
            ).collect(Collectors.toList());
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    @Override
    public FullBenefitInfoDTO getOneFullBenefitInfoDTO(Long activityId, Long strategyId, Long benefitGroupId, Long benefitId) {
        SceneActivityCacheDto activity = localCacheManager.getSceneActivityCacheDtoByActivityId(activityId, null);
        if (Objects.isNull(activity)) {
            log.warn("getOneFullBenefitInfoDTO activity is null. activityId={}", activityId);
            return null;
        }

        List<StrategyCacheDto> strategies = activity.getStrategyCacheDtoList();
        if (CollectionUtils.isEmpty(strategies)) {
            log.warn("getOneFullBenefitInfoDTO strategies is null. activityId={}", activityId);
            return null;
        }
        StrategyCacheDto strategy = strategies.stream()
                .filter(s -> Objects.equals(s.getStrategyId(), strategyId))
                .findFirst().orElse(null);
        if (Objects.isNull(strategy)) {
            log.warn("getOneFullBenefitInfoDTO strategy is null. activityId={}, strategyId={}", activityId, strategyId);
            return null;
        }

        List<BenefitIdWithConfDto> benefits = strategy.getStrategyBenefitGroup().get(strategyId);
        if (Objects.isNull(benefits)) {
            log.warn("getOneFullBenefitInfoDTO benefits is null. activityId={}, strategyId={}", activityId, strategyId);
            return null;
        }
        BenefitIdWithConfDto benefit = benefits.stream()
                .filter(b -> Objects.equals(b.getBenefitGroupId(), benefitGroupId))
                .filter(b -> Objects.equals(b.getId(), benefitId))
                .findFirst().orElse(null);
        if (Objects.isNull(benefit)) {
            log.warn("getOneFullBenefitInfoDTO benefit is null. activityId={}, strategyId={}, benefitGroupId={}, benefitId={}",
                    activityId, strategyId, benefitGroupId, benefitId);
            return null;
        }

        return BenefitConvertEntity.convertToFullBenefitInfoDTO(activity, strategy, benefit);
    }

    /**
     * 批量查询用户权益信息
     *
     * @param userId          用户id
     * @param benefitTypeList 权益类型
     * @return 当前可用权益
     */
    private List<UserBenefitInfoDTO> queryUserCurrentActiveBenefitsInfo(String userId, List<Integer> benefitTypeList) {
        List<UserBenefitInfoDTO> userBenefitInfoDTOS = Lists.newArrayList();
        for (Integer benefitType : benefitTypeList) {
            AccountType accountType = AccountType.getByBenefitType(benefitType);
            if (Objects.isNull(accountType)) {
                continue;
            }
            //实体转换
            AccountStrategyContext accountStrategyContext = AccountStrategyContext.builder(accountType.getAccountType());
            BenefitOperateEntity benefitOperateEntity = BenefitOperateEntity.builder().build();
            EquityAccountContext equityAccountContext = benefitOperateEntity.convertQueryBenefitReqToEquityAccountContext(userId, accountType);
            //账户查询
            QueryEquityDto queryEquityDto = accountStrategyContext.equityQuery(equityAccountContext);
            if (Objects.isNull(queryEquityDto)) {
                continue;
            }
            //实体转换
            UserBenefitInfoDTO userBenefitInfoDTO = benefitOperateEntity.convertQueryEquityDtoToUserBenefitInfoDTO(benefitType, queryEquityDto);
            userBenefitInfoDTOS.add(userBenefitInfoDTO);

        }
        return userBenefitInfoDTOS;
    }

    private List<StrategyCacheDto> getStrategiesByStrategyType(List<StrategyCacheDto> strategies, Integer strategyType) {
        if (CollectionUtils.isEmpty(strategies)) {
            return null;
        }
        return strategies.stream()
                .filter(e -> Objects.equals(e.getStrategyType(), strategyType))
                .collect(Collectors.toList());
    }
}
