package com.ddmc.equity.domain.service.equityBenefitStockFlow.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.exception.TransactionRollbackException;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.domain.dto.benefit_stock.UpdateStockFlowIsHandleParam;
import com.ddmc.equity.domain.service.equityBenefitStockFlow.EquityBenefitStockFlowService;
import com.ddmc.equity.infra.repository.dao.EquityBenefitStockFlowDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitStockFlowMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Service
public class EquityBenefitStockFlowServiceImpl implements EquityBenefitStockFlowService {

    @Resource
    private EquityBenefitStockFlowMapper equityBenefitStockFlowMapper;
    @Autowired
    private TransactionUtil transactionUtil;

    @Override
    public boolean updateEquityBenefitStockFlowById(EquityBenefitStockFlowDO equityBenefitStockFlowDO) {
        return equityBenefitStockFlowMapper.updateById(equityBenefitStockFlowDO) > 0;
    }

    /**
     * 批量写入
     *
     * @param list
     * @return
     */
    @Override
    public Long batchInsert(List<EquityBenefitStockFlowDO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0l;
        }
        return equityBenefitStockFlowMapper.batchInsert(list);
    }

    @Override
    public void insert(EquityBenefitStockFlowDO equityBenefitStockFlowDO) {
        equityBenefitStockFlowMapper.insert(equityBenefitStockFlowDO);
    }

    @Override
    public List<EquityBenefitStockFlowDO> queryIsHandleFlowDOList(@NotNull Set<Integer> operationTypes, Date startTime,
                                                                  @NotNull Long cursor, @NotNull Long batchLimit) {
        Wrapper<EquityBenefitStockFlowDO> wrapper = Wrappers.<EquityBenefitStockFlowDO>lambdaQuery()
                .eq(EquityBenefitStockFlowDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(EquityBenefitStockFlowDO::getIsHandle, CommonEnum.INTEGER_BOOL.NO.getCode())
                .in(EquityBenefitStockFlowDO::getOperationType, operationTypes)
                .gt(EquityBenefitStockFlowDO::getId, cursor)
                .ge(Objects.nonNull(startTime), EquityBenefitStockFlowDO::getCreateTime, startTime)
                .last(String.format("limit %d", batchLimit));
        return equityBenefitStockFlowMapper.selectList(wrapper);
    }

    @Override
    public boolean updateFlowHandledByFlowIds(@NotNull List<Long> flowIds) {
        return transactionUtil.transactionalAndCatch(t -> {
            LambdaUpdateWrapper<EquityBenefitStockFlowDO> wrapper = Wrappers.<EquityBenefitStockFlowDO>lambdaUpdate()
                    .set(EquityBenefitStockFlowDO::getIsHandle, CommonEnum.INTEGER_BOOL.YES.getCode())
                    .eq(EquityBenefitStockFlowDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                    .eq(EquityBenefitStockFlowDO::getIsHandle, CommonEnum.INTEGER_BOOL.NO.getCode())
                    .in(EquityBenefitStockFlowDO::getId, flowIds);
            int update = equityBenefitStockFlowMapper.update(null, wrapper);
            int flowIdsSize = CollectionUtils.size(flowIds);
            if (update <= 0 || update != flowIdsSize) {
                log.warn("updateFlowHandledByFlowIds update failure. flowIds={}, flowIdsSize={}, update={}", JSON.toJSONString(flowIds), flowIdsSize, update);
                throw new TransactionRollbackException(String.format("通过 flowIds 更新流水为已处理失败. flowIdsSize=%d, update=%d", flowIdsSize, update));
            }
        });
    }

    @Override
    public boolean updateFlowUnHandledByFlowIds(@NotNull List<Long> flowIds) {
        return transactionUtil.transactionalAndCatch(t -> {
            LambdaUpdateWrapper<EquityBenefitStockFlowDO> wrapper = Wrappers.<EquityBenefitStockFlowDO>lambdaUpdate()
                    .set(EquityBenefitStockFlowDO::getIsHandle, CommonEnum.INTEGER_BOOL.NO.getCode())
                    .eq(EquityBenefitStockFlowDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                    .eq(EquityBenefitStockFlowDO::getIsHandle, CommonEnum.INTEGER_BOOL.YES.getCode())
                    .in(EquityBenefitStockFlowDO::getId, flowIds);
            int update = equityBenefitStockFlowMapper.update(null, wrapper);
            int flowIdsSize = CollectionUtils.size(flowIds);
            if (update <= 0 || update != flowIdsSize) {
                log.warn("updateFlowUnHandledByFlowIds update failure. flowIds={}, flowIdsSize={}, update={}", JSON.toJSONString(flowIds), flowIdsSize, update);
                throw new TransactionRollbackException(String.format("通过 flowIds 更新流水为未处理失败. flowIdsSize=%d, update=%d", flowIdsSize, update));
            }
        });
    }

    @Override
    public int updateFlowIsHandleByParam(UpdateStockFlowIsHandleParam param) {
        if (Objects.isNull(param) || CollectionUtils.isEmpty(param.getActivityIds()) || Objects.isNull(param.getHandle())) {
            log.warn("updateFlowIsHandleByParam param or activityIds or handle is null. param={}", JSON.toJSONString(param));
            return 0;
        }
        Boolean handle = param.getHandle();
        List<Long> activityIds = param.getActivityIds();
        List<Long> strategyIds = param.getStrategyIds();
        List<Long> benefitIds = param.getBenefitIds();
        List<Long> operationTypes = param.getOperationTypes();
        Date endTime = param.getEndTime();
        LambdaUpdateWrapper<EquityBenefitStockFlowDO> wrapper = Wrappers.<EquityBenefitStockFlowDO>lambdaUpdate()
                .set(EquityBenefitStockFlowDO::getIsHandle, handle ? CommonEnum.INTEGER_BOOL.YES.getCode() : CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(EquityBenefitStockFlowDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(EquityBenefitStockFlowDO::getIsHandle, handle ? CommonEnum.INTEGER_BOOL.NO.getCode() : CommonEnum.INTEGER_BOOL.YES.getCode())
                .in(EquityBenefitStockFlowDO::getActivityId, activityIds)
                .in(CollectionUtils.isNotEmpty(strategyIds), EquityBenefitStockFlowDO::getStrategyId, strategyIds)
                .in(CollectionUtils.isNotEmpty(benefitIds), EquityBenefitStockFlowDO::getBenefitId, benefitIds)
                .in(CollectionUtils.isNotEmpty(operationTypes), EquityBenefitStockFlowDO::getOperationType, operationTypes)
                .le(Objects.nonNull(endTime), EquityBenefitStockFlowDO::getCreateTime, endTime);
        return equityBenefitStockFlowMapper.update(null, wrapper);
    }

    public Long sumUnHandledFlowOperationCount(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId) {
        return equityBenefitStockFlowMapper.sumUnHandledFlowOperationCount(activityId, strategyId, benefitId);
    }

    public Long sumUnHandledFlowPlanOperationCount(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId,
                                                   @NotNull Long planStockId) {
        return equityBenefitStockFlowMapper.sumUnHandledFlowPlanOperationCount(activityId, strategyId, benefitId, planStockId);
    }

    @Override
    public List<EquityBenefitStockFlowDO> queryLastFlowByOperationType(@NotNull Long activityId, @NotNull Long strategyId, @NotNull Long benefitId, List<Integer> operationType) {
        Wrapper<EquityBenefitStockFlowDO> wrapper = Wrappers.<EquityBenefitStockFlowDO>lambdaQuery()
                .eq(EquityBenefitStockFlowDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(EquityBenefitStockFlowDO::getActivityId, activityId)
                .eq(EquityBenefitStockFlowDO::getStrategyId, strategyId)
                .eq(EquityBenefitStockFlowDO::getBenefitId, benefitId)
                .in(EquityBenefitStockFlowDO::getOperationType, operationType);
        return equityBenefitStockFlowMapper.selectList(wrapper);
    }
}
