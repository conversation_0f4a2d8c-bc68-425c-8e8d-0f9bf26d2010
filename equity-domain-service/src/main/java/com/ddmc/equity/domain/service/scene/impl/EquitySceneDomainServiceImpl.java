package com.ddmc.equity.domain.service.scene.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.SceneStatusEnum;
import com.ddmc.equity.common.util.LongUtils;
import com.ddmc.equity.domain.service.activity.EquityActivityDomainService;
import com.ddmc.equity.domain.service.scene.EquitySceneDomainService;
import com.ddmc.equity.domain.valueobject.scene.SceneListFilterReqVO;
import com.ddmc.equity.domain.valueobject.scene.SceneListFilterRespVO;
import com.ddmc.equity.infra.repository.dao.EquityActivityDO;
import com.ddmc.equity.infra.repository.dao.EquitySceneDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquitySceneMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class EquitySceneDomainServiceImpl implements EquitySceneDomainService {
    @Resource
    private EquitySceneMapper equitySceneMapper;

    @Resource
    private EquityActivityDomainService equityActivityDomainService;


    @Override
    @Nullable
    public EquitySceneDO getSceneById(@NotNull Long sceneId) {
        return equitySceneMapper.selectById(sceneId);
    }

    @Override
    @Nullable
    public EquitySceneDO getSceneByCode(@NotNull String sceneCode) {
        return equitySceneMapper.selectOne(new LambdaQueryWrapper<EquitySceneDO>()
                .eq(EquitySceneDO::getSceneCode, sceneCode));
    }

    @Override
    public List<EquitySceneDO> getAllPublishedScenes() {
        Wrapper<EquitySceneDO> wrapper = Wrappers.<EquitySceneDO>lambdaQuery()
                .eq(EquitySceneDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(EquitySceneDO::getStatus, SceneStatusEnum.PUBLISHED.getId());
        return equitySceneMapper.selectList(wrapper);
    }


    /**
     * 主要是toB的场景使用，直查数据库
     *
     * @param filter SceneListFilterReqVO
     * @return SceneListFilterRespVO
     */
    @Override
    @Nullable
    public SceneListFilterRespVO getListedScenesRespByFilter(@NotNull SceneListFilterReqVO filter) {
        @NotNull QueryWrapper<EquitySceneDO> wrapper = getSceneListQueryWrapper(filter);
        IPage<EquitySceneDO> sceneDataPage = new Page<>(filter.getPage(), filter.getPageSize());
        sceneDataPage = equitySceneMapper.listScenesByCondition(sceneDataPage, wrapper);
        if (null == sceneDataPage) {
            return null;
        }
        List<EquitySceneDO> records = sceneDataPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new SceneListFilterRespVO();
        } else {
            return SceneListFilterRespVO.builder().list(records).total(sceneDataPage.getTotal()).build();
        }
    }

    @NotNull
    private static QueryWrapper<EquitySceneDO> getSceneListQueryWrapper(@NotNull SceneListFilterReqVO req) {
        QueryWrapper<EquitySceneDO> wrapper = new QueryWrapper<>();
        wrapper.and(LongUtils.isTrue(req.getId()) || StringUtils.isNotEmpty(req.getName()),
                        e -> e.eq(LongUtils.isTrue(req.getId()), "id", req.getId())
                                .or(StringUtils.isNotEmpty(req.getName()),
                                        e1 -> e1.like("scene_name", req.getName())))
                .in(CollectionUtils.isNotEmpty(req.getStatuses()), "status", req.getStatuses())
                .eq(StringUtils.isNotEmpty(req.getCreator()), "op_admin_name", req.getCreator())
                .eq(StringUtils.isNotEmpty(req.getUpdater()), "edit_admin_name", req.getUpdater())
                .eq(StringUtils.isNotEmpty(req.getSceneCode()), "scene_code", req.getSceneCode())
                .eq("is_delete", CommonEnum.INTEGER_BOOL.NO.getCode());

        return wrapper.orderByDesc("create_time");
    }


    /**
     * 通过sceneCode获取所有未删除的活动，包括失效活动
     *
     * @return List
     */
    @Override
    @Nullable
    public List<EquityActivityDO> listLinkedUncheckedActivities(String sceneCode) {
        if (StringUtils.isBlank(sceneCode)) {
            return Collections.emptyList();
        }
        EquitySceneDO equitySceneDO = getSceneByCode(sceneCode);
        if (null == equitySceneDO) {
            return Collections.emptyList();
        }
        if (CommonEnum.INTEGER_BOOL.YES.getCode().equals(equitySceneDO.getIsDelete())) {
            return Collections.emptyList();
        }
        List<EquityActivityDO> activities = equityActivityDomainService.listBySceneCode(sceneCode);
        if (CollectionUtils.isEmpty(activities)) {
            return Collections.emptyList();
        }
        return activities;
    }

    /**
     * 通过sceneCode获取所有生效中的活动，不包括失效活动
     *
     * @return List
     */
    @Override
    @Nullable
    public List<EquityActivityDO> listLinkedActiveActivities(String sceneCode) {
        if (StringUtils.isBlank(sceneCode)) {
            return Collections.emptyList();
        }
        EquitySceneDO equitySceneDO = getSceneByCode(sceneCode);
        if (null == equitySceneDO) {
            return Collections.emptyList();
        }
        if (CommonEnum.INTEGER_BOOL.YES.getCode().equals(equitySceneDO.getIsDelete())) {
            return Collections.emptyList();
        }
        List<EquityActivityDO> activities = equityActivityDomainService.listUnOfflineBySceneCode(sceneCode);
        if (CollectionUtils.isEmpty(activities)) {
            return Collections.emptyList();
        }
        return activities;
    }

    @Override
    public void save(EquitySceneDO needSaveDO) {
        if (LongUtils.isTrue(needSaveDO.getId())) {
            equitySceneMapper.updateById(needSaveDO);
        } else {
            equitySceneMapper.insert(needSaveDO);
        }
    }
}
