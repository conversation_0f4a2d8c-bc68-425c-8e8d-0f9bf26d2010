package com.ddmc.equity.domain.dto.benefit_stock;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.List;

/**
 * 库存不足提醒条件配置 DTO
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2024/1/24 16:23
 * @description 库存不足提醒条件
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class LowStockAlarmConditionDTO {

    /**
     * @see com.ddmc.equity.enums.SceneCodeEnum
     */
    @ApiModelProperty(value = "活动场景 code，用于标识不同的业务场景")
    private String sceneCode;

    @ApiModelProperty(value = "库存提醒规则列表，支持多个阈值配置不同的实现类")
    private List<LowStockAlarmRuleDTO> alarmRules;

    /**
     * 库存提醒规则配置 DTO
     *
     * <p>单个提醒规则的详细配置，包含触发条件、执行逻辑、通知方式等</p>
     */
    @Data
    @NoArgsConstructor
    @SuperBuilder
    @EqualsAndHashCode(callSuper = false)
    public static class LowStockAlarmRuleDTO {

        @ApiModelProperty(value = "提醒实现类名称")
        private String alarmClass;

        @ApiModelProperty(value = "需要过滤的活动名称列表（支持模糊匹配，类似 SQL LIKE '%关键词%'）")
        private List<String> excludeActNames;

        @ApiModelProperty(value = "剩余库存不足提醒阈值。当 balanceStock <= threshold 时，触发库存不足提醒")
        private List<Long> thresholds;

        @ApiModelProperty(value = "剩余库存百分比阈值。当 剩余库存/总库存 <= 百分比阈值 时，触发库存不足提醒。支持高精度小数，如：20.5 表示 20.5%")
        private List<BigDecimal> percentageThresholds;

        @ApiModelProperty(value = "一天内最大提醒次数，用于控制提醒频率，避免过度打扰")
        private Integer alarmMaxTimes;

        @ApiModelProperty(value = "跳转链接")
        private String jumpLink;

        @ApiModelProperty(value = "企微机器人 key，用于发送提醒消息到指定群组")
        private String wechatRobotKey;

        @ApiModelProperty(value = "工作台统一识别码")
        private Integer workbenchBizCode;
    }
}
