package com.ddmc.equity.domain.entity.rule;

import com.ddmc.equity.account.BenefitReceiveRuleContext;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitGroupRuleDO;
import com.ddmc.equity.model.dto.RuleCacheDTO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/20 13:44
 * @description
 */
public class RuleConvertEntity {

    public static <T> List<T> convertToFormatRuleDTOList(List<? extends RuleCacheDTO> ruleDTOList, Class<T> tClass) {
        if (CollectionUtils.isEmpty(ruleDTOList)) {
            return null;
        }
        return ruleDTOList.stream().map(e -> RuleConvertEntity.convertToFormatRuleDTO(e, tClass))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static <T> T convertToFormatRuleDTO(RuleCacheDTO ruleDTO, Class<T> tClass) {
        if (Objects.isNull(ruleDTO) || StringUtils.isBlank(ruleDTO.getRuleValue())) {
            return null;
        }
        return JsonUtil.parseObject(ruleDTO.getRuleValue(), tClass);
    }

    private static Map<Integer /* ruleType */, RuleCacheDTO> convertToRuleMap(List<? extends RuleCacheDTO> ruleDTOList) {
        if (CollectionUtils.isEmpty(ruleDTOList)) {
            return Maps.newHashMap();
        }
        return ruleDTOList.stream().collect(Collectors.toMap(RuleCacheDTO::getRuleType, Function.identity(), (v1, v2) -> v1));
    }

    public static BenefitReceiveRuleContext convertToBenefitReceiveRuleContext(List<? extends RuleCacheDTO> activityRuleList,
                                                                               List<? extends RuleCacheDTO> strategyRuleList,
                                                                               List<? extends RuleCacheDTO> benefitRuleList) {
        Map<Integer /* ruleType */, RuleCacheDTO> activityRulesMap = RuleConvertEntity.convertToRuleMap(activityRuleList);
        Map<Integer /* ruleType */, RuleCacheDTO> strategyRulesMap = RuleConvertEntity.convertToRuleMap(strategyRuleList);
        Map<Integer /* ruleType */, RuleCacheDTO> benefitRulesMap = RuleConvertEntity.convertToRuleMap(benefitRuleList);

        // 20230628 目前策略上配置的领取频次限制、领取库存限制，实际都是下沉到权益组维度
        RuleCacheDTO activityReceiveLimitRuleCacheDTO = activityRulesMap.get(RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode());
        RuleCacheDTO benefitReceiveLimitRuleCacheDTO = ObjectUtils.defaultIfNull(benefitRulesMap.get(RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode()),
                strategyRulesMap.get(RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode()));
        RuleCacheDTO activityStockLimitRuleCacheDTO = activityRulesMap.get(RuleTypeEnum.STOCK_LIMIT_RULE.getCode());
        RuleCacheDTO benefitStockLimitRuleCacheDTO = ObjectUtils.defaultIfNull(benefitRulesMap.get(RuleTypeEnum.STOCK_LIMIT_RULE.getCode()),
                strategyRulesMap.get(RuleTypeEnum.STOCK_LIMIT_RULE.getCode()));

        return BenefitReceiveRuleContext.builder()
                .hasActivityReceiveLimit(Objects.nonNull(activityReceiveLimitRuleCacheDTO))
                .activityReceiveLimitRuleDTO(RuleConvertEntity.convertToFormatRuleDTO(activityReceiveLimitRuleCacheDTO, ReceiveLimitRuleDTO.class))
                .hasBenefitReceiveLimit(Objects.nonNull(benefitReceiveLimitRuleCacheDTO))
                .benefitReceiveLimitRuleDTO(RuleConvertEntity.convertToFormatRuleDTO(benefitReceiveLimitRuleCacheDTO, ReceiveLimitRuleDTO.class))
                .hasActivityStockLimit(Objects.nonNull(activityStockLimitRuleCacheDTO))
                .activityStockLimitRuleDTO(RuleConvertEntity.convertToFormatRuleDTO(activityStockLimitRuleCacheDTO, StockLimitRuleDTO.class))
                .hasBenefitStockLimit(Boolean.FALSE)
                .hasBenefitStockLimit(Objects.nonNull(benefitStockLimitRuleCacheDTO))
                .benefitStockLimitRuleDTO(RuleConvertEntity.convertToFormatRuleDTO(benefitStockLimitRuleCacheDTO, StockLimitRuleDTO.class))
                .build();
    }

    public static @NotNull String getRuleUniqueKey(@NotNull EquityBenefitGroupRuleDO benefitGroupRuleDO) {
        return benefitGroupRuleDO.getStrategyId() + Constants.UNDERLINE + benefitGroupRuleDO.getBenefitGroupId()
                + Constants.UNDERLINE + benefitGroupRuleDO.getBenefitId() + Constants.UNDERLINE + benefitGroupRuleDO.getRuleType();
    }
}
