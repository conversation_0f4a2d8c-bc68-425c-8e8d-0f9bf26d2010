package com.ddmc.equity.domain.dto.engine;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/7/6 16:27
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class SceneEngineContextDTO extends EngineContextDTO {

    /**
     * 场景 code
     */
    private String sceneCode;
    /**
     * 场景上的发放类型。1-全部发放；2-随机发放；3-概率发放；4-优先级发放；
     *
     * @see com.ddmc.equity.enums.SceneSendTypeEnum
     */
    private Integer sceneSendType;
}
