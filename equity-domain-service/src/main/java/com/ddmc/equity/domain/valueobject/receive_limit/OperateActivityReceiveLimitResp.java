package com.ddmc.equity.domain.valueobject.receive_limit;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/29 17:24
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class OperateActivityReceiveLimitResp {

    /**
     * 操作结果，默认为操作失败
     */
    @Builder.Default
    private Boolean operateResult = Boolean.FALSE;
    /**
     * 成功为：用户单活动领取频次限制表 id；失败为：null
     */
    private Long receiveLimitId;
}
