package com.ddmc.equity.domain.converter.rule;

import com.ddmc.equity.domain.dto.rule.condition.CommunityUserRuleDTO;
import com.ddmc.equity.dto.business.rule.CommunityUserDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/3/25 19:58
 * @description
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CommunityUserRuleConverter {

    CommunityUserRuleConverter INSTANCE = Mappers.getMapper(CommunityUserRuleConverter.class);

    CommunityUserRuleDTO convertToCommunityUserRuleDTO(CommunityUserDTO communityUserDTO);
}
