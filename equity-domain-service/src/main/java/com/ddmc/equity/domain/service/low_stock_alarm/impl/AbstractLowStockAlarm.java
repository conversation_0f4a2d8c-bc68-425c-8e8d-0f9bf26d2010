package com.ddmc.equity.domain.service.low_stock_alarm.impl;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.DateUtil;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.common.util.RedisLuaUtil;
import com.ddmc.equity.domain.dto.benefit_stock.LowStockAlarmConditionDTO;
import com.ddmc.equity.domain.dto.benefit_stock.LowStockAlarmContextDTO;
import com.ddmc.equity.domain.service.low_stock_alarm.LowStockAlarm;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.rpc.workbench.WorkbenchNotificationProxy;
import com.ddmc.equity.mq.pulsar.producer.WechatRobotMsgProducer;
import com.ddmc.product.operation.workbench.request.notification.WorkbenchNotificationReachRequest;
import com.ddmc.product.operation.workbench.response.notification.WorkbenchNotificationReachResponse;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/1/24 10:51
 * @description
 */
@Slf4j
public abstract class AbstractLowStockAlarm implements LowStockAlarm {

    @Resource
    private RedisCache redisCache;
    @Resource
    private RedisLuaUtil redisLuaUtil;
    @Resource
    protected WechatRobotMsgProducer wechatRobotMsgProducer;
    @Resource
    protected WorkbenchNotificationProxy workbenchNotificationProxy;

    @Override
    public void lowStockAlarm(LowStockAlarmContextDTO contextDTO) {
        LowStockAlarmConditionDTO.LowStockAlarmRuleDTO lowStockAlarmRule = contextDTO.getLowStockAlarmRule();
        if (lowStockAlarmRule == null) {
            log.error("lowStockAlarm lowStockAlarmRule isNull context={}", JSON.toJSONString(contextDTO));
            CsossUtils.logEvent(MonitorConstants.LOW_STOCK_ALARM, "rule_is_null");
            return;
        }

        // 1 检查活动名称是否在排除列表中
        if (shouldExcludeByActName(contextDTO)) {
            return;
        }

        // 2 判断是否满足库存不足提醒条件
        String hitLowStockAlarmType = getHitLowStockAlarmType(contextDTO);
        if (StringUtils.isBlank(hitLowStockAlarmType)) {
            return;
        }

        // 3 同一 activityId + strategyId + benefitId + lowStockAlarmTimesKey 如果当天已经达到最大提醒次数，则不再发送提醒
        String lowStockAlarmTimesKey = getLowStockAlarmTimesKey(contextDTO, hitLowStockAlarmType);
        if (haveReachedAlarmMaxTimes(lowStockAlarmTimesKey, lowStockAlarmRule.getAlarmMaxTimes())) {
            log.info("lowStockAlarm haveReachedAlarmMaxTimes context={}, lowStockAlarmTimesKey={}",
                    contextDTO.toJsonStr(), lowStockAlarmTimesKey);
            return;
        }

        // 4 发送提醒
        sendAlarmMsg(contextDTO);

        // 5 提醒次数 Redis 计数
        setOrIncrAlarmTimes(lowStockAlarmTimesKey);
    }

    /**
     * 根据活动名称判断是否应该排除提醒
     *
     * @param contextDTO 提醒上下文
     */
    protected boolean shouldExcludeByActName(LowStockAlarmContextDTO contextDTO) {
        String activityName = Optional.ofNullable(contextDTO)
                .map(LowStockAlarmContextDTO::getActivityName)
                .orElse(null);
        List<String> excludeActNames = Optional.ofNullable(contextDTO)
                .map(LowStockAlarmContextDTO::getLowStockAlarmRule)
                .map(LowStockAlarmConditionDTO.LowStockAlarmRuleDTO::getExcludeActNames)
                .orElse(null);

        return StringUtils.isNotBlank(activityName) && CollectionUtils.isNotEmpty(excludeActNames) &&
                excludeActNames.stream()
                        .anyMatch(activityName::contains);
    }

    /**
     * 获取触发库存不足提醒的类型
     *
     * @param contextDTO 提醒上下文
     */
    protected String getHitLowStockAlarmType(LowStockAlarmContextDTO contextDTO) {
        // 检查是否触发该规则的告警（绝对数量阈值）
        String hitAbsoluteThreshold = getHitAbsoluteThreshold(contextDTO);
        if (StringUtils.isNotBlank(hitAbsoluteThreshold)) {
            return "hitAbsoluteThreshold_" + hitAbsoluteThreshold;
        }

        // 检查是否触发该规则的告警（百分比阈值）
        String hitPercentageThreshold = getHitPercentageThreshold(contextDTO);
        if (StringUtils.isNotBlank(hitPercentageThreshold)) {
            return "hitPercentageThreshold_" + hitPercentageThreshold;
        }
        return null;
    }

    /**
     * 检查是否触发绝对数量阈值提醒
     *
     * <p>检查剩余库存是否低于配置的绝对数量阈值。</p>
     * <p>支持同时检查普通库存和计划库存。</p>
     *
     * @param contextDTO 提醒上下文，包含库存信息
     * @return 触发的阈值字符串，未触发则返回 null
     */
    private String getHitAbsoluteThreshold(LowStockAlarmContextDTO contextDTO) {
        List<Long> thresholds = Optional.ofNullable(contextDTO)
                .map(LowStockAlarmContextDTO::getLowStockAlarmRule)
                .map(LowStockAlarmConditionDTO.LowStockAlarmRuleDTO::getThresholds)
                .orElse(null);
        if (CollectionUtils.isEmpty(thresholds)) {
            return null;
        }

        // 升序排序，优先判断更小的阈值
        List<Long> sortedThresholds = Lists.newArrayList(thresholds);
        sortedThresholds.sort(Comparator.naturalOrder());

        Long balanceStock = contextDTO.getBalanceStock();
        Long planBalanceStock = contextDTO.getPlanBalanceStock();
        return sortedThresholds.stream()
                .filter(threshold -> hitAbsoluteThreshold(balanceStock, threshold) ||
                        hitAbsoluteThreshold(planBalanceStock, threshold)
                )
                .findFirst()
                .map(String::valueOf)
                .orElse(null);
    }

    /**
     * 判断是否触发绝对数量阈值
     *
     * @param balanceStock 剩余库存
     * @param threshold    阈值
     */
    private boolean hitAbsoluteThreshold(Long balanceStock, Long threshold) {
        return balanceStock != null && threshold != null && balanceStock <= threshold;
    }

    /**
     * 检查是否触发百分比阈值提醒
     *
     * <p>检查剩余库存百分比是否低于配置的百分比阈值。</p>
     * <p>使用 BigDecimal 确保计算精度，支持同时检查普通库存和计划库存。</p>
     *
     * @param contextDTO 提醒上下文，包含库存信息
     * @return 触发的百分比阈值字符串，未触发则返回 null
     */
    private String getHitPercentageThreshold(LowStockAlarmContextDTO contextDTO) {
        List<BigDecimal> percentageThresholds = Optional.ofNullable(contextDTO)
                .map(LowStockAlarmContextDTO::getLowStockAlarmRule)
                .map(LowStockAlarmConditionDTO.LowStockAlarmRuleDTO::getPercentageThresholds)
                .orElse(null);
        if (CollectionUtils.isEmpty(percentageThresholds)) {
            return null;
        }

        // 升序排序，优先判断更小的百分比阈值
        List<BigDecimal> sortedThresholds = Lists.newArrayList(percentageThresholds);
        sortedThresholds.sort(Comparator.naturalOrder());

        Long balanceStock = contextDTO.getBalanceStock();
        Long stock = contextDTO.getStock();
        Long planBalanceStock = contextDTO.getPlanBalanceStock();
        Long planStock = contextDTO.getPlanStock();
        return sortedThresholds.stream()
                .filter(threshold -> hitPercentageThreshold(balanceStock, stock, threshold) ||
                        hitPercentageThreshold(planBalanceStock, planStock, threshold)
                )
                .findFirst()
                .map(BigDecimal::toString)
                .orElse(null);
    }

    /**
     * 检查库存百分比是否低于阈值
     *
     * @param balanceStock        剩余库存
     * @param totalStock          总库存
     * @param percentageThreshold 百分比阈值
     */
    private boolean hitPercentageThreshold(Long balanceStock, Long totalStock, BigDecimal percentageThreshold) {
        if (balanceStock == null || totalStock == null || totalStock <= 0) {
            return false;
        }

        if (percentageThreshold == null || percentageThreshold.compareTo(BigDecimal.ZERO) < 0 ||
                percentageThreshold.compareTo(new BigDecimal("100")) > 0) {
            return false;
        }

        // 计算剩余库存百分比，使用 BigDecimal 保证精度
        BigDecimal balanceStockDecimal = new BigDecimal(balanceStock);
        BigDecimal totalStockDecimal = new BigDecimal(totalStock);
        BigDecimal hundred = new BigDecimal("100");

        BigDecimal percentage = balanceStockDecimal
                .divide(totalStockDecimal, 4, RoundingMode.HALF_UP)
                .multiply(hundred);

        return percentage.compareTo(percentageThreshold) <= 0;
    }

    protected abstract String getLowStockAlarmTimesKey(LowStockAlarmContextDTO contextDTO, String hitLowStockAlarmType);

    /**
     * 设置或增加提醒次数计数
     *
     * @param key 缓存键
     */
    private void setOrIncrAlarmTimes(String key) {
        // 缓存值为提醒次数；缓存过期时间为当天结束时间；
        // 同一 activityId + strategyId + benefitId + lowStockAlarmTimesKey 如果当天已经达到最大提醒次数，则不再发送提醒
        Date nowDate = new Date();
        Date endOfDay = DateUtil.getEndOfDay(nowDate);
        long timeToLive = DateUtil.buildExpireSecNotRandom(nowDate, endOfDay);
        redisLuaUtil.setOrIncr(key, timeToLive);
    }

    /**
     * 判断是否已达到最大提醒次数
     *
     * @param key           缓存键
     * @param alarmMaxTimes 最大提醒次数
     */
    protected boolean haveReachedAlarmMaxTimes(String key, Integer alarmMaxTimes) {
        // 最大提醒次数默认为 1
        alarmMaxTimes = ObjectUtils.defaultIfNull(alarmMaxTimes, 1);
        String value = redisCache.getValue(key);
        // 如果当天已经达到最大提醒次数，则不再发送提醒
        return Objects.nonNull(value) && NumberUtils.convertToInteger(value) >= alarmMaxTimes;
    }

    /**
     * 发送库存不足提醒消息
     *
     * @param contextDTO 提醒上下文
     */
    protected void sendAlarmMsg(LowStockAlarmContextDTO contextDTO) {
        // 1 发送企微机器人消息
        sendWechatRobotMsg(contextDTO);

        // 2 发送工作台通知
        sendWorkbenchNotification(contextDTO);
    }

    /**
     * 发送企微机器人消息
     *
     * @param contextDTO 提醒上下文
     */
    protected void sendWechatRobotMsg(LowStockAlarmContextDTO contextDTO) {
        String robotKey = contextDTO.getLowStockAlarmRule().getWechatRobotKey();
        if (StringUtils.isBlank(robotKey)) {
            log.info("sendWechatRobotMsg robotKey is blank contextDTO={}", contextDTO.toJsonStr());
            return;
        }

        wechatRobotMsgProducer.sendMarkdownMsg(buildAlarmContent(contextDTO), robotKey);
        CsossUtils.logEvent(MonitorConstants.LOW_STOCK_ALARM_SEND_MSG, contextDTO.getSceneCode() + "_wechat_msg_success");
    }

    /**
     * 发送工作台通知
     *
     * @param contextDTO 提醒上下文
     */
    protected void sendWorkbenchNotification(LowStockAlarmContextDTO contextDTO) {
        Integer workbenchBizCode = contextDTO.getLowStockAlarmRule().getWorkbenchBizCode();
        if (workbenchBizCode == null) {
            log.info("sendWorkbenchNotification workbenchBizCode is null contextDTO={}", contextDTO.toJsonStr());
            return;
        }

        WorkbenchNotificationReachRequest req = buildWorkbenchNotificationRequest(contextDTO);
        if (req == null) {
            log.info("sendWorkbenchNotification req is null contextDTO={}", contextDTO.toJsonStr());
            return;
        }

        WorkbenchNotificationReachResponse resp = workbenchNotificationProxy.workbenchNotificationReach(req);
        if (resp == null || MapUtils.isNotEmpty(resp.getPersonCheckErrorMessage())) {
            log.error("sendWorkbenchNotification send failure context={}, req={}, resp={}",
                    JSON.toJSONString(contextDTO), JSON.toJSONString(req), JSON.toJSONString(resp));
            CsossUtils.logEvent(MonitorConstants.LOW_STOCK_ALARM_SEND_MSG, contextDTO.getSceneCode() + "_workbench_msg_failure");
            return;
        }
        CsossUtils.logEvent(MonitorConstants.LOW_STOCK_ALARM_SEND_MSG, contextDTO.getSceneCode() + "_workbench_msg_success");
    }

    /**
     * 构建提醒消息内容
     *
     * @param contextDTO 提醒上下文
     */
    protected String buildAlarmContent(LowStockAlarmContextDTO contextDTO) {
        return String.format("**[%s] 库存不足提醒**\n" +
                        "> 活动 id& 外部活动 id& 名称：<font color=\"comment\">%d/ %s/ %s</font>\n" +
                        "> 策略 id& 外部策略 id& 名称：<font color=\"comment\">%d/ %s/ %s</font>\n" +
                        "> 权益 id& 值& 名称：<font color=\"comment\">%d /%s / %s</font>\n" +
                        "%s" +
                        "> 活动时间：<font color=\"comment\">%s - %s</font>\n" +
                        "%s%s" +
                        "> 请及时补充！！！",
                contextDTO.buildDisplaySceneCode(),
                contextDTO.getActivityId(), contextDTO.buildExternal(), contextDTO.getActivityName(),
                contextDTO.getStrategyId(), contextDTO.buildStrategyExternal(), contextDTO.getStrategyName(),
                contextDTO.getBenefitId(), contextDTO.getBenefitValue(), contextDTO.getBenefitName(),
                contextDTO.buildStrategyTypeContentStr(),
                contextDTO.buildStartDateStr(), contextDTO.buildEndDateStr(),
                contextDTO.buildStockContentStr(), contextDTO.buildPlanStockContentStr());
    }

    protected WorkbenchNotificationReachRequest buildWorkbenchNotificationRequest(LowStockAlarmContextDTO contextDTO) {
        return null;
    }
}
