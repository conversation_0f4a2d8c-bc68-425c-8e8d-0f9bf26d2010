package com.ddmc.equity.domain.dto.account_deduct;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/7/16 14:45
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("查询扣减需要操作的账户明细列表 req")
public class FetchDeductOperateAccountDetailsReqDTO {

    @ApiModelProperty(value = "用户 id")
    private String userId;

    @ApiModelProperty("使用活动 ids")
    private List<Long> useActivityIds;

    /**
     * @see com.ddmc.equity.enums.BenefitTypeEnum
     */
    @ApiModelProperty(value = "权益类型")
    private Integer benefitType;

    @ApiModelProperty(value = "扣减数量")
    private Integer deductCount;

    @ApiModelProperty(value = "查询页数大小")
    private Integer pageLimit;

    @ApiModelProperty(value = "最大循环次数。如果超过 maxLoopTimes，则不再继续查询，扣减只进行总账户扣减")
    private Integer maxLoopTimes;
}
