package com.ddmc.equity.domain.dto.receive_limit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/29 17:34
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class UserActivityReceiveLimitCacheDTO {

    /**
     * tb_equity_user_activity_receive_limit 表 id
     */
    private Long receiveLimitId;
    /**
     * 限制领取数量
     */
    private Long limitCount;
    /**
     * 已领取数量
     */
    private Long receiveCount;
    /**
     * 结束时间
     */
    private Date endTime;
}
