package com.ddmc.equity.domain.valueobject.scene;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class SceneListFilterReqVO implements Serializable {
    private static final long serialVersionUID = -8317445512216937310L;
    @Builder.Default
    private Integer page = 1;
    @Builder.Default
    private Integer pageSize = 20;

    private Long id;

    private String sceneCode;

    private String name;

    private String creator;

    private String updater;

    private List<Integer> statuses;
}
