package com.ddmc.equity.domain.service.benefit_group.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.exception.ApiBusinessException;
import com.ddmc.equity.common.util.LongUtils;
import com.ddmc.equity.domain.service.benefit_group.EquityBenefitGroupDomainService;
import com.ddmc.equity.domain.valueobject.benefit_group.BenefitGroupListFilterReqVO;
import com.ddmc.equity.domain.valueobject.benefit_group.BenefitGroupListFilterRespVO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitGroupDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitGroupMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class EquityBenefitGroupDomainServiceImpl implements EquityBenefitGroupDomainService {

    @Resource
    private EquityBenefitGroupMapper equityBenefitGroupMapper;

    @Override
    public List<EquityBenefitGroupDO> getAllEquityBenefitGroupDO() {
        return equityBenefitGroupMapper.selectList(null);
    }

    @Override
    public List<EquityBenefitGroupDO> getAllEquityBenefitGroupDOByStrategyIds(List<Long> strategyIds) {
        if (CollectionUtils.isEmpty(strategyIds)) {
            return Lists.newArrayList();
        }
        QueryWrapper<EquityBenefitGroupDO> queryWrapper = new QueryWrapper();
        queryWrapper.in("strategy_id", strategyIds);
        return equityBenefitGroupMapper.selectList(queryWrapper);
    }

    @Override
    public List<EquityBenefitGroupDO> getEquityBenefitGroupDOListByStrategyId(Long strategyId) {
        QueryWrapper<EquityBenefitGroupDO> queryWrapper = new QueryWrapper();
        queryWrapper.eq("strategy_id", strategyId);
        return equityBenefitGroupMapper.selectList(queryWrapper);
    }

    @Override
    public EquityBenefitGroupDO getEquityBenefitGroupDOByStrategyId(Long strategyId) {
        QueryWrapper<EquityBenefitGroupDO> queryWrapper = new QueryWrapper();
        queryWrapper.in("strategy_id", strategyId);
        return equityBenefitGroupMapper.selectOne(queryWrapper);
    }

    @Override
    public List<EquityBenefitGroupDO> getEquityBenefitGroupDOListByStrategyIds(List<Long> strategyIds) {
        QueryWrapper<EquityBenefitGroupDO> queryWrapper = new QueryWrapper();
        queryWrapper.in("strategy_id", strategyIds);
        return equityBenefitGroupMapper.selectList(queryWrapper);
    }

    @Override
    public BenefitGroupListFilterRespVO getListedBenefitGroupsRespByFilter(BenefitGroupListFilterReqVO getListFilterReq) {
        return null;
    }

    @Override
    public int save(EquityBenefitGroupDO needSaveDO) {
        if (LongUtils.isTrue(needSaveDO.getId())) {
            return equityBenefitGroupMapper.updateById(needSaveDO);
        } else {
            return equityBenefitGroupMapper.insert(needSaveDO);
        }
    }

    @Override
    public void insert(EquityBenefitGroupDO equityBenefitGroupDO) {
        if (Objects.isNull(equityBenefitGroupDO)) {
            throw new ApiBusinessException(ExceptionEnum.SAVE_BENEFIT_GROUP_FAILED);
        }
        int result = equityBenefitGroupMapper.insert(equityBenefitGroupDO);
        if (result != 1) {
            throw new ApiBusinessException(ExceptionEnum.SAVE_BENEFIT_GROUP_FAILED);
        }
    }

    @Override
    public void insertOrUpdate(EquityBenefitGroupDO equityBenefitGroupDO) {
        equityBenefitGroupMapper.insertOrUpdate(equityBenefitGroupDO);
    }

    /**
     * 根据ID删除
     *
     * @param benefitGroupId
     * @return
     */
    @Override
    public int deleteById(Long benefitGroupId) {
        return equityBenefitGroupMapper.deleteById(benefitGroupId);
    }

    /**
     * 根据id列表查询
     *
     * @param benefitGroupIds
     * @return
     */
    @Override
    public List<EquityBenefitGroupDO> getBenefitGroupByIds(List<Long> benefitGroupIds) {
        if (CollectionUtils.isEmpty(benefitGroupIds)) {
            return Lists.newArrayList();
        }
        return equityBenefitGroupMapper.selectBatchIds(benefitGroupIds);
    }

    @Override
    public int update(EquityBenefitGroupDO equityBenefitGroupDO) {
        return equityBenefitGroupMapper.updateById(equityBenefitGroupDO);
    }


}
