package com.ddmc.equity.domain.service.provideAppService;

import com.ddmc.equity.dto.business.provide.ProvideBenefitDTO;
import com.ddmc.equity.dto.business.provide.ProvideSaveReqDTO;
import com.ddmc.promocore.admin.vo.ActivityVO;

import java.util.List;
import java.util.Map;

public interface ProvideService {

    /**
     * 供给保存
     *
     * @param req ProvideSaveReqDTO
     * @return Long
     */
    boolean saveOrUpdate(ProvideSaveReqDTO req);


    /**
     * 供给老玩法活动保存
     *
     * @param activityVO ActivityVO
     * @param sceneCode  String
     * @return boolean
     */
    Boolean saveOrUpdateByPromoCore(ActivityVO activityVO, String sceneCode, boolean useInputLimitCity,
                                    Map<Long /* oldEquityId */ , List<String>> ticketLimitCityMap,
                                    Map<Long /* oldEquityId */, List<ProvideBenefitDTO>> provideBenefitMap);
}
