package com.ddmc.equity.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/27 10:27
 * @description
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class ReqReceiveBenefitDTO {

    /**
     * 策略 id
     */
    private Long strategyId;
    /**
     * 主券用户券 id，为用户膨胀前的券 userTicketId
     */
    private String masterUserTicketId;
    /**
     * 权益组 id
     */
    private Long benefitGroupId;
    /**
     * 权益 id
     */
    private Long benefitId;
}
