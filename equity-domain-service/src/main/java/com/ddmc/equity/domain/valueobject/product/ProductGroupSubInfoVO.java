package com.ddmc.equity.domain.valueobject.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created on 2022/10/13.
 *
 * <AUTHOR>
 */
@Data
public class ProductGroupSubInfoVO implements Serializable {

    private String id;

    private Integer count;

    @JsonProperty("product_name")
    private String productName;

    private String name;

    @JsonProperty("origin_price")
    private String originPrice;

    private String price;

    @JsonProperty("small_image")
    private String smallImage;

    @JsonProperty("stock_number")
    private Integer stockNumber;

    private Integer status;

    private List<ProductDetailSizeVO> sizes;

    @JsonProperty("is_invoice")
    private Integer isInvoice;

    @JsonProperty("category_path")
    private String categoryPath;

    @JsonProperty("manage_category_path")
    private String manageCategoryPath;
}
