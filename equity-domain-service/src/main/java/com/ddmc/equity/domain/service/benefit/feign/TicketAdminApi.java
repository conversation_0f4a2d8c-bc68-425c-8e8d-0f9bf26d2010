package com.ddmc.equity.domain.service.benefit.feign;

import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2024/7/28
 */
@FeignClient(name = "vouchercore-service", path = "/vouchercore-service",
        url = "${feign.service.url.vouchercore:}", contextId = "ticketAdminApi")
public interface TicketAdminApi {

    // @汤伟强，确认场景号是否需要传

    @PostMapping("/admin/ticketBill/listTicketBill")
    JSONObject ticketBill(@RequestBody TreeMap<String, Object> param);

}
