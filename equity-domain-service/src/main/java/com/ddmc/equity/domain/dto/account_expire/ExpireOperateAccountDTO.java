package com.ddmc.equity.domain.dto.account_expire;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/7/16 15:56
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("过期需要操作的账户")
public class ExpireOperateAccountDTO {

    @ApiModelProperty("账户 id")
    private Long accountId;

    @ApiModelProperty("可用数量")
    private Integer availableCount;

    @ApiModelProperty("版本号")
    private Integer version;

    @ApiModelProperty("过期数量")
    private Integer expireCount;
}
