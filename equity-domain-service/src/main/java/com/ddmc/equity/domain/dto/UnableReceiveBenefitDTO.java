package com.ddmc.equity.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/9/18 11:32
 * @description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class UnableReceiveBenefitDTO {

    @ApiModelProperty(value = "活动 id")
    private Long activityId;

    @ApiModelProperty(value = "外部关联类型。1-玩法；")
    private Integer externalType;

    @ApiModelProperty(value = "外部关联 id（一般是外部关联方的活动 id，如 promo.activity.activityId）")
    private String externalId;

    @ApiModelProperty(value = "策略 id")
    private Long strategyId;

    @ApiModelProperty(value = "策略外部关联 id。如果是来源玩法的活动，则为活动 prizeId；如果是膨胀券活动，则为母券券模板 id；")
    private String strategyExternalId;

    @ApiModelProperty(value = "权益组 id")
    private Long benefitGroupId;

    @ApiModelProperty(value = "权益 id")
    private Long benefitId;

    /**
     * @see com.ddmc.equity.enums.BenefitTypeEnum
     */
    @ApiModelProperty("权益类型")
    private Integer benefitType;

    @ApiModelProperty("权益值")
    private String benefitValue;

    /**
     * @see com.ddmc.equity.enums.BenefitUnableReceiveReasonType
     */
    @ApiModelProperty("不能领取原因 code")
    private String unableReceiveReasonCode;
}
