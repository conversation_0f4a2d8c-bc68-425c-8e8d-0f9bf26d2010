package com.ddmc.equity.domain.service.coupon_expand.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.dto.benefit.ActivityCouponExpandAccountCollectInDTO;
import com.ddmc.equity.domain.dto.benefit.ActivityCouponExpandAccountCollectOutDTO;
import com.ddmc.equity.domain.entity.account.CouponExpandAccountConvertEntity;
import com.ddmc.equity.domain.service.coupon_expand.CouponExpandAccountCollectDomainService;
import com.ddmc.equity.infra.repository.dao.CouponExpandAccountCollectDO;
import com.ddmc.equity.infra.repository.dao.CouponExpandAccountDO;
import com.ddmc.equity.infra.repository.dao.mapper.CouponExpandAccountCollectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/7/4 10:58
 * @description
 */
@Slf4j
@Service
public class CouponExpandAccountCollectDomainServiceImpl extends ServiceImpl<CouponExpandAccountCollectMapper, CouponExpandAccountCollectDO>
        implements CouponExpandAccountCollectDomainService {


    @Autowired
    private CouponExpandAccountCollectMapper couponExpandAccountCollectMapper;

    @Override
    public void insertAccountCollectDO(CouponExpandAccountDO accountDO) {
        Assert.notNull(accountDO, ExceptionEnum.ILLEGAL_ARGS.getCode(), "券膨胀权益子账户不能为空");
        Assert.mustTrue(StringUtils.isNotBlank(accountDO.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "券膨胀权益子账户 userId 不能为空");
        CouponExpandAccountCollectDO accountCollectDO = CouponExpandAccountConvertEntity.convertToAccountCollectDO(accountDO);
        this.save(accountCollectDO);
    }


    /**
     * 分页查询
     *
     * @param in
     * @return
     */
    public ActivityCouponExpandAccountCollectOutDTO getActivityExpandList(ActivityCouponExpandAccountCollectInDTO in) {
        ActivityCouponExpandAccountCollectOutDTO out = new ActivityCouponExpandAccountCollectOutDTO();

        QueryWrapper<CouponExpandAccountCollectDO> wrapper = new QueryWrapper<>();
        wrapper.eq("activity_id", in.getActivityId());
        if (Objects.nonNull(in.getUserId())) {
            wrapper.eq("user_id", in.getUserId());
        }
        wrapper.orderByAsc("id");
        IPage<CouponExpandAccountCollectDO> iPage = new Page(in.getPage(), in.getPageSize());
        iPage = couponExpandAccountCollectMapper.selectPage(iPage, wrapper);

        if (iPage == null) return out;
        out.setList(iPage.getRecords());
        out.setTotal(iPage.getTotal());
        out.setPages(iPage.getPages());
        return out;
    }

}
