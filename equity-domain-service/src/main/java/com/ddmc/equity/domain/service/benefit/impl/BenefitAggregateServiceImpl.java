package com.ddmc.equity.domain.service.benefit.impl;

import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.exception.AdminBusinessException;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.common.util.LongUtils;
import com.ddmc.equity.common.util.business.BenefitUtil;
import com.ddmc.equity.domain.converter.benefit.BenefitConverter;
import com.ddmc.equity.domain.dto.ReceiveSceneBenefitDTO;
import com.ddmc.equity.domain.dto.ReqReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.entity.common.BenefitOperateEntity;
import com.ddmc.equity.domain.entity.common.EngineContextEntity;
import com.ddmc.equity.domain.service.benefit.BenefitAggregateService;
import com.ddmc.equity.domain.service.core.SceneActivityBenefitCoreService;
import com.ddmc.equity.domain.service.core.ThirdBenefitService;
import com.ddmc.equity.dto.customer.*;
import com.ddmc.equity.enums.ActivityFilterTypeEnum;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.infra.repository.dao.EquityBenefitMappingDO;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BenefitAggregateServiceImpl implements BenefitAggregateService {

    @Resource
    private ThirdBenefitService thirdBenefitService;

    @Resource
    private SceneActivityBenefitCoreService sceneActivityBenefitCoreService;

    @Resource
    private LocalCacheManager localCacheManager;

    @NotNull
    @Override
    public ReceiveSceneBenefitResDTO receiveSceneBenefit(@NotNull ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO) {
        // 20230312 不支持批量领取了
        receiveSceneBenefitReqDTO.setActivityId(null);

        // 如果是需要外调发放权益的特殊场景 请求参数中虚拟的权益id要和走新权益的分隔处理
        // 原领取逻辑不需要变
        @NotNull Pair<@NotNull ReceiveSceneBenefitReqDTO /* originalReq */ ,
                @Nullable ReceiveSceneBenefitReqDTO /* thirdReq */>
                diviedReqPair = thirdBenefitService.divideExtraBenefitByReq(receiveSceneBenefitReqDTO);
        receiveSceneBenefitReqDTO = diviedReqPair.getLeft();
        boolean needAppendThirdReceive = null != diviedReqPair.getRight();

        //参数转换
        // 20230312 特殊逻辑: 把老权益ID转化为新权益ID
        validateReceiveSceneBenefitParam(receiveSceneBenefitReqDTO);
        // 分割出需要单独外调的权益
        Pair<@NotNull List<BenefitDTO> /* success*/, @NotNull List<BenefitDTO> /* fail */>
                receiveExtraBenefitResult = needAppendThirdReceive ?
                thirdBenefitService.receiveExtraBenefitOfSpecScene(diviedReqPair.getRight()) : null;

        @NotNull ReceiveSceneBenefitResDTO receiveSceneBenefitResDTO = ReceiveSceneBenefitResDTO.builder().sceneCode(receiveSceneBenefitReqDTO.getSceneCode()).build();
        if (CollectionUtils.isNotEmpty(receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS())) {
            //实体声明
            EngineContextEntity engineContextEntity = EngineContextEntity.builder().build();
            //实体转换
            EngineContextDTO engineContextDTO = engineContextEntity.convertReceiveReqToEngineContextDTO(receiveSceneBenefitReqDTO, ActivityFilterTypeEnum.EFFECT_ACTIVITY);
            // 特殊逻辑：如果是特定场景配置的券，不需要天降
            if (BenefitUtil.isSpecSceneSendTicket(receiveSceneBenefitReqDTO.getSceneCode())) {
                engineContextDTO.setIsTicketIsRead(Boolean.TRUE);
                // 20230312 特殊逻辑：如果是领券中心场景，默认透传券的流水号
                engineContextDTO.setIsUseSerialNumberSave(CommonEnum.INTEGER_BOOL.YES.getCode());
            } else {
                engineContextDTO.setIsUseSerialNumberSave(receiveSceneBenefitReqDTO.getIsUseSerialNumberSave());
            }
            BenefitOperateEntity benefitOperateEntity = BenefitOperateEntity.builder().build();
            List<ReqReceiveBenefitDTO> userReceiveBenefitList = benefitOperateEntity.convertToReceiveBenefitDTOList(receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS());
            //权益领取
            try {
                ReceiveSceneBenefitDTO receiveSceneBenefitDTO = sceneActivityBenefitCoreService.receiveSceneBenefits(receiveSceneBenefitReqDTO.getReqNo().trim(),
                        receiveSceneBenefitReqDTO.getSceneCode(), engineContextDTO, userReceiveBenefitList);
                //返回结果组装
                receiveSceneBenefitResDTO = benefitOperateEntity.convertToReceiveSceneBenefitResDTO(receiveSceneBenefitDTO);
            } catch (Exception e) {
                log.error("领取过程出错 req:{}", receiveSceneBenefitReqDTO, e);
                if (needAppendThirdReceive) {
                    receiveSceneBenefitResDTO.getReceiveFailBenefitList().addAll(BenefitConverter.INSTANCE.rd2dEmpties(receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS()));
                } else {
                    throw e;
                }
            }

        }

        // 如果需要追加第三方领取结果，新增
        if (needAppendThirdReceive) {
            receiveSceneBenefitResDTO.getReceivedBenefitList().addAll(receiveExtraBenefitResult.getLeft());
            receiveSceneBenefitResDTO.getReceiveFailBenefitList().addAll(receiveExtraBenefitResult.getRight());
        }
        return receiveSceneBenefitResDTO;
    }

    /**
     * 查询幂等权益信息
     *
     * @param queryReceivedBenefitsReqDTO QueryReceivedBenefitsReqDTO
     * @return ReceiveSceneBenefitResDTO
     */
    @Override
    public @NotNull ReceiveSceneBenefitResDTO queryReceivedBenefits(QueryReceivedBenefitsReqDTO queryReceivedBenefitsReqDTO) {
        log.info("queryReceivedBenefits request uid={},sceneCode={},serialNumber={},activityIds={},strategyIds={}",
                queryReceivedBenefitsReqDTO.getUserId(), queryReceivedBenefitsReqDTO.getSceneCode(), queryReceivedBenefitsReqDTO.getSerialNumber(), queryReceivedBenefitsReqDTO.getActivityIds(), queryReceivedBenefitsReqDTO.getStrategyIds());
        @Nullable ReceiveSceneBenefitDTO receiveSceneBenefitDTO = sceneActivityBenefitCoreService.queryReceivedBenefits(queryReceivedBenefitsReqDTO);
        @NotNull ReceiveSceneBenefitResDTO receiveSceneBenefitResDTO = ReceiveSceneBenefitResDTO.builder()
                .sceneCode(queryReceivedBenefitsReqDTO.getSceneCode()).build();
        //返回结果组装
        BenefitOperateEntity benefitOperateEntity = BenefitOperateEntity.builder().build();
        if (null != receiveSceneBenefitDTO) {
            receiveSceneBenefitResDTO = benefitOperateEntity.convertToReceiveSceneBenefitResDTO(receiveSceneBenefitDTO);
        }
        return receiveSceneBenefitResDTO;
    }

    /**
     * 领取场景权益参数校验
     *
     * @param receiveSceneBenefitReqDTO 领取参数
     */
    private void validateReceiveSceneBenefitParam(ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO) {
        Assert.notNull(receiveSceneBenefitReqDTO, ExceptionEnum.PARAMS_ERROR);
        Assert.notBlank(receiveSceneBenefitReqDTO.getReqNo(), ExceptionEnum.REQ_NO_IS_NULL);
        Assert.notBlank(receiveSceneBenefitReqDTO.getSceneCode(), ExceptionEnum.SCENE_CODE_IS_NULL);
        Assert.mustTrue(receiveSceneBenefitReqDTO.getReqNo().trim().length() <= 64, ExceptionEnum.REQ_NO_TOO_LONG);
        Assert.notBlank(receiveSceneBenefitReqDTO.getAppId(), ExceptionEnum.SCENE_CODE_IS_NULL);
        BaseRequestDTO baseRequestDTO = receiveSceneBenefitReqDTO.getBaseRequestDTO();
        Assert.notNull(baseRequestDTO, ExceptionEnum.BASE_REQUEST_IS_NULL);
        Assert.notBlank(baseRequestDTO.getUserId(), ExceptionEnum.USER_ID_IS_NULL);
        List<ReceiveBenefitReqDTO> receiveBenefitReqDTOS = receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS();
        if (!CollectionUtils.isEmpty(receiveBenefitReqDTOS)) {
            for (ReceiveBenefitReqDTO receiveBenefitReqDTO : receiveBenefitReqDTOS) {
                if (Objects.equals(receiveBenefitReqDTO.getBenefitType(), BenefitTypeEnum.CASH_COUPON.getId())) {
                    Assert.notBlank(baseRequestDTO.getOpenId(), ExceptionEnum.OPEN_ID_IS_NULL);
                }
                if (StringUtils.isNotBlank(receiveBenefitReqDTO.getOldBenefitId()) || LongUtils.isFalse(receiveBenefitReqDTO.getBenefitId())) {
                    Assert.notBlank(receiveBenefitReqDTO.getOldBenefitId(), ExceptionEnum.BENEFIT_ID_IS_NULL);
                    //  20230312 这一层把老权益ID转化为新权益ID
                    try {
                        EquityBenefitMappingDO mappingDO = localCacheManager.getEquityBenefitMappingDOByMappingId(receiveBenefitReqDTO.getOldBenefitId(), receiveBenefitReqDTO.getActivityId());
                        if (null == mappingDO || LongUtils.isFalse(mappingDO.getBenefitId()) || LongUtils.isFalse(mappingDO.getStrategyId())) {
                            log.error("读取本地缓存获取老权益ID信息出错 mappingDO:{} oldBenefitId:{}, activityId:{}", mappingDO, receiveBenefitReqDTO.getOldBenefitId(), receiveBenefitReqDTO.getActivityId());
                            throw AdminExceptionBuilder.build(ExceptionEnum.BENEFIT_ID_IS_NULL);
                        }
                        receiveBenefitReqDTO.setBenefitId(mappingDO.getBenefitId());
                        receiveBenefitReqDTO.setStrategyId(mappingDO.getStrategyId());
                    } catch (Exception e) {
                        log.error("读取本地缓存获取老权益ID信息出错 oldBenefitId:{}, activityId:{}", receiveBenefitReqDTO.getOldBenefitId(), receiveBenefitReqDTO.getActivityId(), e);
                        throw AdminExceptionBuilder.build(ExceptionEnum.BENEFIT_ID_IS_NULL);
                    }
                }
                Assert.notNull(receiveBenefitReqDTO.getStrategyId(), ExceptionEnum.STRATEGY_ID_IS_NULL);
                Assert.mustTrue((Objects.nonNull(receiveBenefitReqDTO.getActivityId()) || StringUtils.isNotBlank(receiveBenefitReqDTO.getExternalId())),
                        ExceptionEnum.EXTERNAL_ID_AND_ACTIVITY_ID_IS_NULL);
            }
        }
    }

    /**
     * 领取主流程，先查后写
     *
     * @param receiveSceneBenefitReqDTO ReceiveSceneBenefitReqDTO
     * @return ReceiveSceneBenefitResDTO
     */
    @Override
    @NotNull
    public ReceiveSceneBenefitResDTO receiveSceneBenefitProcess(@NotNull ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO) {
        log.info("receiveSceneBenefitProcess request uid={},sceneCode={},reqNo={},sendTicketScene={},isUseSerialNumberSave={},receiveBenefitReqDTOS={}",
                receiveSceneBenefitReqDTO.getBaseRequestDTO() == null ? "null" : receiveSceneBenefitReqDTO.getBaseRequestDTO().getUserId(),
                receiveSceneBenefitReqDTO.getSceneCode(), receiveSceneBenefitReqDTO.getReqNo(), receiveSceneBenefitReqDTO.getSendTicketScene(), receiveSceneBenefitReqDTO.getIsUseSerialNumberSave(), receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS());
        // 领取权益时活动 id 兜底逻辑（如果 activityId 为空，通过外部关联类型和外部关联 id 反查 activityId 并设置）
        receiveBenefitActivityIdPocket(receiveSceneBenefitReqDTO);
        ReceiveSceneBenefitResDTO resp = ReceiveSceneBenefitResDTO.builder().sceneCode(receiveSceneBenefitReqDTO.getSceneCode()).build();

        Set<String> reqOldBenefitIds = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS())) {
            reqOldBenefitIds = receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS().stream().map(ReceiveBenefitReqDTO::getOldBenefitId)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        }

        // 幂等
        @Nullable QueryReceivedBenefitsReqDTO queryReceivedBenefitsReqDTO = getQueryReceivedBenefitsReqDTO(receiveSceneBenefitReqDTO);
        if (null != queryReceivedBenefitsReqDTO) {
            ReceiveSceneBenefitResDTO resDTO = queryReceivedBenefits(queryReceivedBenefitsReqDTO);
            if (CollectionUtils.isNotEmpty(resDTO.getReceivedBenefitList())) {
                resp.getReceivedBenefitList().addAll(resDTO.getReceivedBenefitList());
                // 去掉已经成功的记录
                removeSuccessRecord(receiveSceneBenefitReqDTO, resDTO);
            }
        }

        if (CollectionUtils.isNotEmpty(receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS())) {
            ReceiveSceneBenefitResDTO receiveData = receiveSceneBenefit(receiveSceneBenefitReqDTO);
            resp.getReceivedBenefitList().addAll(receiveData.getReceivedBenefitList());
            resp.getReceiveProcessBenefitList().addAll(receiveData.getReceiveProcessBenefitList());
            resp.getReceiveFailBenefitList().addAll(receiveData.getReceiveFailBenefitList());
        }
        if (CollectionUtils.isNotEmpty(resp.getReceivedBenefitList()) && CollectionUtils.isNotEmpty(reqOldBenefitIds)) {
            final Set<String> finalReqOldBenefitIds = reqOldBenefitIds;
            resp.getReceivedBenefitList().removeIf(benefitDTO ->
                    StringUtils.isNotBlank(benefitDTO.getOldBenefitId())
                            && !finalReqOldBenefitIds.contains(benefitDTO.getOldBenefitId()));
        }

        return resp;
    }

    /**
     * 去掉处理成功的权益ID请求(幂等用)
     *
     * @param receiveSceneBenefitReqDTO ReceiveSceneBenefitReqDTO
     * @param resDTO                    ReceiveSceneBenefitResDTO
     */
    private void removeSuccessRecord(
            @NotNull ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO,
            @NotNull ReceiveSceneBenefitResDTO resDTO) throws AdminBusinessException {
        receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS().removeIf(v -> {
            for (BenefitDTO benefitDTO : resDTO.getReceivedBenefitList()) {
                Long benefitId = getBenefitIdByOldBenefitId(v);
                if (Objects.equals(benefitDTO.getBenefitId(), benefitId)) {
                    return true;
                }
            }
            return false;
        });
    }

    /**
     * 通过玩法旧活动ID查找到权益2.0的权益ID
     *
     * @param v ReceiveBenefitReqDTO 请求体
     * @return Long benefitId
     */
    private Long getBenefitIdByOldBenefitId(@NotNull ReceiveBenefitReqDTO v) throws AdminBusinessException {
        Long benefitId = v.getBenefitId();
        if (StringUtils.isNotBlank(v.getOldBenefitId())) {
            try {
                EquityBenefitMappingDO mappingDO = localCacheManager.getEquityBenefitMappingDOByMappingId(v.getOldBenefitId(), v.getActivityId());
                if (null == mappingDO || LongUtils.isFalse(mappingDO.getBenefitId()) || LongUtils.isFalse(mappingDO.getStrategyId())) {
                    log.error("读取本地缓存获取老权益ID信息出错 mappingDO:{} oldBenefitId:{}, activityId:{}", mappingDO, v.getOldBenefitId(), v.getActivityId());
                    throw AdminExceptionBuilder.build(ExceptionEnum.BENEFIT_ID_IS_NULL);
                }
                benefitId = mappingDO.getBenefitId();
            } catch (Exception e) {
                log.error("读取本地缓存获取老权益ID信息出错 oldBenefitId:{}, activityId:{}", v.getOldBenefitId(), v.getActivityId(), e);
                throw AdminExceptionBuilder.build(ExceptionEnum.BENEFIT_ID_IS_NULL);
            }
        }
        return benefitId;
    }

    /**
     * 构建幂等用的请求体，如果是会员专享券不需要走幂等逻辑
     *
     * @param receiveSceneBenefitReqDTO ReceiveSceneBenefitReqDTO 原领取请求体
     * @return QueryReceivedBenefitsReqDTO | null
     */
    @Nullable
    private static QueryReceivedBenefitsReqDTO getQueryReceivedBenefitsReqDTO(@NotNull ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO) {
        if (CollectionUtils.isEmpty(receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS())) {
            return null;
        }
        // 过滤掉会员专享券
        List<ReceiveBenefitReqDTO> afterFilter = receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS().
                stream().filter(receiveBenefitReqDTO ->
                        !Objects.equals(Long.valueOf(Constants.ZERO), (receiveBenefitReqDTO.getActivityId()))
                                || StringUtils.isNotBlank(receiveBenefitReqDTO.getOldBenefitId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(afterFilter)) {
            // 领取会员专享券的 不幂等
            return null;
        }
        QueryReceivedBenefitsReqDTO queryReceivedBenefitsReqDTO = new QueryReceivedBenefitsReqDTO();
        queryReceivedBenefitsReqDTO.setSceneCode(receiveSceneBenefitReqDTO.getSceneCode());
        queryReceivedBenefitsReqDTO.setSerialNumber(receiveSceneBenefitReqDTO.getReqNo());
        queryReceivedBenefitsReqDTO.setUserId(receiveSceneBenefitReqDTO.getBaseRequestDTO().getUserId());
        Set<Long> strategyIds = afterFilter.stream().map(ReceiveBenefitReqDTO::getStrategyId).collect(Collectors.toSet());
        Set<Long> activityIds = afterFilter.stream().map(ReceiveBenefitReqDTO::getActivityId).collect(Collectors.toSet());
        queryReceivedBenefitsReqDTO.setStrategyIds(strategyIds);
        queryReceivedBenefitsReqDTO.setActivityIds(activityIds);
        return queryReceivedBenefitsReqDTO;
    }

    private void receiveBenefitActivityIdPocket(@NotNull ReceiveSceneBenefitReqDTO receiveSceneBenefitReqDTO) {
        if (CollectionUtils.isEmpty(receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS())) {
            return;
        }
        receiveSceneBenefitReqDTO.getReceiveBenefitReqDTOS().forEach(e -> {
            // 活动 id 不为空，无需兜底
            if (Objects.nonNull(e.getActivityId())) {
                return;
            }
            // 外部关联类型和外部关联 id 为空，无需兜底
            Integer externalType = e.getExternalType();
            String externalId = e.getExternalId();
            if (Objects.isNull(externalType) || StringUtils.isBlank(externalId)) {
                return;
            }
            e.setActivityId(localCacheManager.getActivityIdByExternalIdAndExternalType(externalId, externalType));
        });
    }
}
