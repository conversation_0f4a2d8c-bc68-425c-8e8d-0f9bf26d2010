package com.ddmc.equity.domain.valueobject.benefit_limit;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.logging.log4j.util.Strings;

import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ReleaseBenefitLimitResp implements Serializable {
    private static final long serialVersionUID = 5012965853686068918L;

    @ApiModelProperty("释放结果，true:释放成功 ｜ false: 释放失败")
    @Builder.Default
    private Boolean releaseResult = Boolean.FALSE;

    @ApiModelProperty("频次流水号，目前讨论由总次数+已领取次数+回退次数字符串拼接而成")
    @Builder.Default
    private String serialNumber = Strings.EMPTY;
}
