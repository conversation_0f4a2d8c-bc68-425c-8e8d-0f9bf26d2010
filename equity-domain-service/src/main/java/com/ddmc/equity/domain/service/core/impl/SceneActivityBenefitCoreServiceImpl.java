package com.ddmc.equity.domain.service.core.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.csoss.monitor.api.cat.Transaction;
import com.csoss.monitor.api.internal.Instrumentation;
import com.csoss.monitor.api.trace.Span;
import com.csoss.monitor.api.trace.Traces;
import com.ddmc.equity.account.AccountStrategyContext;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.apollo.RecallConstants;
import com.ddmc.equity.common.apollo.SceneBenefitOperateConstants;
import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.exception.ApiBusinessException;
import com.ddmc.equity.common.interceptor.annotation.MonitorSpan;
import com.ddmc.equity.common.util.CatMonitorUtil;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.common.util.MapUtils;
import com.ddmc.equity.common.util.ThreadsUtils;
import com.ddmc.equity.common.util.TimestampUtil;
import com.ddmc.equity.common.util.business.BenefitUtil;
import com.ddmc.equity.domain.dto.InitActivityCountReqDTO;
import com.ddmc.equity.domain.dto.ReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.dto.ReceiveSceneBenefitDTO;
import com.ddmc.equity.domain.dto.ReqReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.engine.EngineResultContextDTO;
import com.ddmc.equity.domain.dto.engine.SceneEngineContextDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.entity.activity.ActivityCacheRuleConvertEntity;
import com.ddmc.equity.domain.entity.common.BenefitOperateConvertEntity;
import com.ddmc.equity.domain.entity.common.BenefitOperateEntity;
import com.ddmc.equity.domain.entity.common.EngineContextEntity;
import com.ddmc.equity.domain.service.core.SceneActivityBenefitCoreService;
import com.ddmc.equity.domain.service.core.UniversalBenefitOperateCoreService;
import com.ddmc.equity.dto.customer.QueryReceivedBenefitsReqDTO;
import com.ddmc.equity.engine.scene.EquitySceneEngine;
import com.ddmc.equity.enums.ActivityFilterTypeEnum;
import com.ddmc.equity.enums.ActivityStatusEnum;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.enums.SceneCodeEnum;
import com.ddmc.equity.factor.RuleFactorContext;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.repository.dao.TicketAccountRecordDO;
import com.ddmc.equity.infra.repository.dao.mapper.TicketAccountRecordMapper;
import com.ddmc.equity.model.convert.EngineContextConvert;
import com.ddmc.equity.model.dto.ActivityCacheDto;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.equity.model.dto.SceneBenefitDataDto;
import com.ddmc.equity.model.dto.SceneCacheDTO;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import com.ddmc.equity.model.dto.StrategyRuleCacheDTO;
import com.ddmc.utils.constant.CommonConstants;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SceneActivityBenefitCoreServiceImpl implements SceneActivityBenefitCoreService {

    @Autowired
    private RuleFactorContext ruleFactorContext;
    @Autowired
    private LocalCacheManager localCacheManager;
    @Autowired
    private EquitySceneEngine equitySceneEngine;
    @Autowired
    private RecallConstants recallConstants;
    @Autowired
    private SceneBenefitOperateConstants sceneBenefitOperateConstants;
    @Autowired
    private CatMonitorUtil catMonitorUtil;
    @Autowired
    private RedisCache redisCache;
    @Resource
    private TicketAccountRecordMapper ticketAccountRecordMapper;
    @Autowired
    private UniversalBenefitOperateCoreService universalBenefitOperateCoreService;
    @Value("${spring.application.name}")
    private String applicationName;

    @Override
    public List<SceneActivityCacheDto> consultSceneBenefit(String sceneCode, EngineContextDTO engineContextDTO) {
        CsossUtils.logEvent(MonitorConstants.CONSULT_SCENE_BENEFIT_SCENE_CODE, sceneCode, "1", null);
        Span span = Traces.spanBuilder("ConsultSceneBenefit.Action", Instrumentation.EVENT_NEW).startSpan();
        try {
            // 缓存中根据场景 code 查询权益信息
            List<SceneActivityCacheDto> sceneActivityList = localCacheManager.getActivityCacheDtoListBySceneCode(sceneCode);
            span.setAttribute("sceneActivityList.size", CollectionUtils.size(sceneActivityList));
            if (CollectionUtils.isEmpty(sceneActivityList)) {
                log.warn("{}缓存中场景活动列表为空 sceneCode={}, engineContext={}", Constants.EQUITY_CONSULT_BENEFIT,
                        sceneCode, JSON.toJSONString(engineContextDTO));
                catMonitorUtil.logEventWithSpan(MonitorConstants.SCENE_CONSULT, MonitorConstants.SCENE_ACTIVITY_IS_NULL,
                        CommonConstants.IS_SUCCESS_CODE, sceneCode);
                return null;
            }

            // 场景活动过滤。如果存在活动 idList 或者 外部关联类型 + 外部关联 idList，需过滤
            List<SceneActivityCacheDto> filterSceneActivityList = filterActivity(sceneActivityList, engineContextDTO);
            span.setAttribute("filterSceneActivityIds.size", CollectionUtils.size(filterSceneActivityList));
            if (CollectionUtils.isEmpty(filterSceneActivityList)) {
                log.warn("{}过滤后的场景活动列表为空 sceneCode={}, engineContext={}", Constants.EQUITY_CONSULT_BENEFIT,
                        sceneCode, JSON.toJSONString(engineContextDTO));
                catMonitorUtil.logEventWithSpan(MonitorConstants.SCENE_CONSULT, MonitorConstants.FILTER_SCENE_ACTIVITY_IS_NULL,
                        CommonConstants.IS_SUCCESS_CODE, sceneCode);
                return null;
            }

            // 过滤出生效中的活动
            List<SceneActivityCacheDto> activeSceneActivityList = filterActiveActivityCacheList(filterSceneActivityList,
                    engineContextDTO.getActFilterType());
            Set<Long> activeSceneActivityIds = CollectionUtils.isEmpty(activeSceneActivityList) ? Sets.newHashSet() :
                    activeSceneActivityList.stream().map(SceneActivityCacheDto::getActivityId).collect(Collectors.toSet());
            span.setAttribute("activeSceneActivityList.size", CollectionUtils.size(activeSceneActivityList));
            span.setAttribute("activeSceneActivityList.ids", StringUtils.join(activeSceneActivityIds, ","));
            if (CollectionUtils.isEmpty(activeSceneActivityList)) {
                log.warn("{}场景有效活动列表为空 sceneCode={}, engineContext={}", Constants.EQUITY_CONSULT_BENEFIT,
                        sceneCode, JSON.toJSONString(engineContextDTO));
                catMonitorUtil.logEventWithSpan(MonitorConstants.SCENE_CONSULT, MonitorConstants.SCENE_ACTIVE_ACTIVITY_IS_NULL,
                        CommonConstants.IS_SUCCESS_CODE, sceneCode);
                return null;
            }

            // 拉取规则因子值
            RuleConditionFactorValueDTO ruleConditionFactorValue = ruleFactorContext.getRuleConditionFactorValue(engineContextDTO,
                    activeSceneActivityList);

            // 权益咨询引擎过滤。调用场景引擎
            SceneEngineContextDTO sceneEngineContextDTO = convertToSceneEngineContextDTO(sceneCode, engineContextDTO);
            List<SceneActivityCacheDto> consultResult = equitySceneEngine.consultSceneBenefit(activeSceneActivityList, sceneEngineContextDTO, ruleConditionFactorValue);
            // 需要把[场景引擎]返回结果上下文 copy 到 engineContextDTO
            EngineContextEntity.copyEngineResultContext(sceneEngineContextDTO.getResultContextDTO(), engineContextDTO.getResultContextDTO());
            // 设置 [权益咨询引擎过滤返回结果上下文]。因为在拉取规则因子的时候，可能已经获取过用户身份等其他，后面如果需要用到则直接复用
            fillEngineResultContextDTO(engineContextDTO, ruleConditionFactorValue);
            return consultResult;
        } catch (Exception e) {
            log.error("{}查询权益异常 sceneCode={}, engineContext={}", Constants.EQUITY_CONSULT_BENEFIT, sceneCode, JSON.toJSONString(engineContextDTO), e);
            span.recordException(e);
            throw e;
        } finally {
            span.end();
        }
    }

    @Override
    public List<SceneBenefitDataDto> recallBenefit(EngineContextDTO engineContextDTO) {
        //查询所有的场景
        List<SceneBenefitDataDto> sceneBenefitDataDtoList = localCacheManager.getAllSceneActivityCacheDto();
        //判空
        if (CollectionUtils.isEmpty(sceneBenefitDataDtoList)) {
            return Lists.newArrayList();
        }
        //判空
        if (CollectionUtils.isEmpty(sceneBenefitDataDtoList)) {
            return Lists.newArrayList();
        }
        //规则过滤
        List<StrategyRuleCacheDTO> strategyRuleCacheDTOList = Lists.newArrayList();
        //生效中的活动
        List<SceneActivityCacheDto> activeActivityList = Lists.newArrayList();
        //活动合并
        List<SceneBenefitDataDto> activeSceneBenefitDataDtoList = filterActivityAndMerge(sceneBenefitDataDtoList, strategyRuleCacheDTOList, engineContextDTO.getActFilterType(), activeActivityList);
        if (CollectionUtils.isEmpty(activeSceneBenefitDataDtoList)) {
            return Lists.newArrayList();
        }
        //拉取规则因子值
        RuleConditionFactorValueDTO ruleConditionFactorValueDTO = ruleFactorContext.getRuleConditionFactorValue(engineContextDTO, activeActivityList);
        //按照场景分组
        Map<String, List<SceneBenefitDataDto>> activeSceneMap = activeSceneBenefitDataDtoList.stream().filter(sceneBenefitDataDto -> StringUtils.isNotBlank(sceneBenefitDataDto.getSceneCode())).collect(Collectors.groupingBy(SceneBenefitDataDto::getSceneCode));
        List<CompletableFuture<Void>> asyncTasks = Lists.newArrayList();
        Map<String, List<SceneBenefitDataDto>> resultMap = new ConcurrentHashMap<>();
        //循环异步处理
        for (String key : activeSceneMap.keySet()) {
            CompletableFuture<Void> recallFuture = ThreadsUtils.runAsync(() -> recallAllSceneBenefit(key, activeSceneMap.get(key), engineContextDTO, ruleConditionFactorValueDTO, resultMap), ThreadsUtils.getCoreThreadPoll(), Constants.RECALL_ALL_SCENE_BENEFIT);
            asyncTasks.add(recallFuture);
        }
        ThreadsUtils.getCompletableFutureList(asyncTasks, recallConstants.getRecallSceneHandleWaitMsTime(), TimeUnit.MILLISECONDS);
        //结果中过滤
        return filterHitSceneBenefitListFromResult(resultMap);
    }


    /**
     * 结果中过滤
     *
     * @param resultMap 结果值
     * @return 命中的场景权益
     */
    private List<SceneBenefitDataDto> filterHitSceneBenefitListFromResult(Map<String, List<SceneBenefitDataDto>> resultMap) {
        //结果处理
        if (MapUtils.isEmpty(resultMap)) {
            return Lists.newArrayList();
        }
        List<SceneBenefitDataDto> hitList = Lists.newArrayList();
        //循环组装
        for (String key : resultMap.keySet()) {
            List<SceneBenefitDataDto> sceneList = resultMap.get(key);
            if (CollectionUtils.isEmpty(sceneList)) {
                continue;
            }
            hitList.addAll(sceneList);
        }
        return hitList;
    }

    /**
     * 召回全部场景权益
     *
     * @param sceneCode                   场景code
     * @param sceneBenefitDataDtos        场景权益集合
     * @param engineContextDTO            上下文
     * @param ruleConditionFactorValueDTO 规则
     * @param resultMap                   返回集合
     */
    private void recallAllSceneBenefit(String sceneCode, List<SceneBenefitDataDto> sceneBenefitDataDtos, EngineContextDTO engineContextDTO, RuleConditionFactorValueDTO ruleConditionFactorValueDTO, Map<String, List<SceneBenefitDataDto>> resultMap) {
        if (CollectionUtils.isEmpty(sceneBenefitDataDtos)) {
            return;
        }
        List<SceneBenefitDataDto> userHitSceneList = Lists.newArrayList();
        for (SceneBenefitDataDto sceneBenefitDataDto : sceneBenefitDataDtos) {
            //生效中的活动
            SceneEngineContextDTO sceneEngineContextDTO = convertToSceneEngineContextDTO(sceneCode, engineContextDTO);
            List<SceneActivityCacheDto> sceneActivityCacheDtoList = equitySceneEngine.consultSceneBenefit(sceneBenefitDataDto.getSceneActivityCacheDtoList(), sceneEngineContextDTO, ruleConditionFactorValueDTO);
            if (CollectionUtils.isEmpty(sceneActivityCacheDtoList)) {
                continue;
            }
            sceneBenefitDataDto.setSceneActivityCacheDtoList(sceneActivityCacheDtoList);
            userHitSceneList.add(sceneBenefitDataDto);
        }
        if (CollectionUtils.isEmpty(userHitSceneList)) {
            return;
        }
        resultMap.put(sceneCode, userHitSceneList);
    }


    /**
     * 活动合并
     *
     * @param sceneBenefitDataDtoList  场景活动集合
     * @param strategyRuleCacheDTOList 规则
     * @param actFilterType            过滤类型
     * @param activeActivityList       生效中的活动
     * @return 活动
     */
    private List<SceneBenefitDataDto> filterActivityAndMerge(List<SceneBenefitDataDto> sceneBenefitDataDtoList, List<StrategyRuleCacheDTO> strategyRuleCacheDTOList, ActivityFilterTypeEnum actFilterType, List<SceneActivityCacheDto> activeActivityList) {
        if (CollectionUtils.isEmpty(sceneBenefitDataDtoList)) {
            return Lists.newArrayList();
        }

        List<SceneBenefitDataDto> filterSceneBenefitDataDtoList = Lists.newArrayList();
        //循环合并
        for (SceneBenefitDataDto sceneBenefitDataDto : sceneBenefitDataDtoList) {
            if (CollectionUtils.isEmpty(sceneBenefitDataDto.getSceneActivityCacheDtoList())) {
                continue;
            }
            List<SceneActivityCacheDto> filterActivityList = filterActiveActivityCacheList(sceneBenefitDataDto.getSceneActivityCacheDtoList(), actFilterType);
            if (CollectionUtils.isEmpty(filterActivityList)) {
                continue;
            }
            activeActivityList.addAll(filterActivityList);
            List<StrategyRuleCacheDTO> filterRules = ActivityCacheRuleConvertEntity.getStrategyRules(filterActivityList);
            if (!CollectionUtils.isEmpty(filterRules)) {
                strategyRuleCacheDTOList.addAll(filterRules);
            }
            sceneBenefitDataDto.setSceneActivityCacheDtoList(filterActivityList);
            filterSceneBenefitDataDtoList.add(sceneBenefitDataDto);
        }
        return filterSceneBenefitDataDtoList;
    }

    /**
     * 通用领取
     *
     * @param reqNo                    业务流水号
     * @param sceneCode                场景 code
     * @param engineContextDTO         上下文
     * @param reqReceiveBenefitDTOList 领取的权益列表
     * @return
     */
    @Override
    public ReceiveSceneBenefitDTO receiveSceneBenefits(String reqNo, String sceneCode, EngineContextDTO engineContextDTO,
                                                       List<ReqReceiveBenefitDTO> reqReceiveBenefitDTOList) {
        Transaction transaction = CsossUtils.newTransaction("receiveSceneBenefits", sceneCode);
        String lockKey = String.format(CacheKeyConstants.RECEIVE_SCENE_BENEFIT_LOCK, engineContextDTO.getUserId(), sceneCode, reqNo);
        boolean isLocked = false;
        try {

            // 加锁
            isLocked = redisCache.lock(lockKey, 10, TimeUnit.SECONDS);
            if (!isLocked) {
                log.error("receiveSceneBenefits lock failure. reqNo={}, sceneCode={}, engineContext={}, reqReceiveBenefitDTOList={}",
                        reqNo, sceneCode, JSON.toJSONString(engineContextDTO), JSON.toJSONString(reqReceiveBenefitDTOList));
                throw new ApiBusinessException(ExceptionEnum.TOO_FREQUENTING);
            }
            log.info("receiveSceneBenefits start. reqNo={}, sceneCode={}, engineContext={}, reqReceiveBenefitDTOList={}",
                    reqNo, sceneCode, JSON.toJSONString(engineContextDTO), JSON.toJSONString(reqReceiveBenefitDTOList));

            // 权益咨询
            List<SceneActivityCacheDto> sceneActivityCacheDtoList;
            Span consultSceneBenefitSpan = Traces.spanBuilder("ReceiveSceneBenefits.ConsultSceneBenefit").startSpan();
            try {
                sceneActivityCacheDtoList = consultSceneBenefit(sceneCode, engineContextDTO);
            } finally {
                consultSceneBenefitSpan.end();
            }

            // 过滤出不能领取权益列表
            SceneActivityBenefitCoreServiceImpl self = (SceneActivityBenefitCoreServiceImpl) AopContext.currentProxy();
            List<ReceiveBenefitDTO> userUnableReceiveBenefits = self.filterUserUnableReceiveBenefits(reqReceiveBenefitDTOList,
                    engineContextDTO.getResultContextDTO());
            if (CollectionUtils.isNotEmpty(userUnableReceiveBenefits)) {
                // 如果不能领取权益列表不为空，则直接返回领取失败（结果包含不能领取权益列表）
                log.warn("receiveSceneBenefits userUnableReceiveBenefits is not null. reqNo={}, sceneCode={}, engineContext={}, reqReceiveBenefitDTOList={}",
                        reqNo, sceneCode, JSON.toJSONString(engineContextDTO), JSON.toJSONString(reqReceiveBenefitDTOList));
                return ReceiveSceneBenefitDTO.builder()
                        .sceneCode(sceneCode)
                        .failList(userUnableReceiveBenefits)
                        .build();
            }

            // 权益过滤，过滤出可以领取的权益列表
            Span filterActivityListSpan = Traces.spanBuilder("ReceiveSceneBenefits.FilterUserReceiveSceneActivityList").startSpan();
            List<ReceiveBenefitDTO> userReceiveBenefits;
            try {
                userReceiveBenefits = filterUserReceiveBenefits(sceneActivityCacheDtoList, reqReceiveBenefitDTOList);
            } finally {
                filterActivityListSpan.end();
            }

            // 权益领取
            @NotNull List<ReceiveBenefitDTO> receiveSuccessBenefitDTOList = Lists.newCopyOnWriteArrayList();
            @NotNull List<ReceiveBenefitDTO> receiveFailBenefitDTOList = Lists.newCopyOnWriteArrayList();
            @NotNull List<ReceiveBenefitDTO> receiveProcessBenefitDTOList = Lists.newCopyOnWriteArrayList();
            Span receiveBenefitsAsyncSpan = Traces.spanBuilder("ReceiveSceneBenefits.ReceiveBenefitsAsync").startSpan();
            try {
                receiveBenefitsAsync(reqNo, sceneCode, engineContextDTO, userReceiveBenefits,
                        receiveSuccessBenefitDTOList, receiveFailBenefitDTOList, receiveProcessBenefitDTOList);
            } finally {
                receiveBenefitsAsyncSpan.end();
            }

            // 结果数据组装
            return ReceiveSceneBenefitDTO.builder()
                    .sceneCode(sceneCode)
                    .successList(receiveSuccessBenefitDTOList)
                    .processList(receiveProcessBenefitDTOList)
                    .failList(receiveFailBenefitDTOList)
                    .build();
        } catch (InterruptedException e) {
            transaction.setStatus(e);
            log.error("receiveSceneBenefits interruptedException. reqNo={}, sceneCode={}, engineContext={}, reqReceiveBenefitDTOList={}",
                    reqNo, sceneCode, JSON.toJSONString(engineContextDTO), JSON.toJSONString(reqReceiveBenefitDTOList), e);
            Thread.currentThread().interrupt();
            throw new ApiBusinessException(ExceptionEnum.TRY_LOCK_INTERRUPT);
        } finally {
            transaction.complete();
            redisCache.unlock(lockKey, isLocked);
        }
    }

    @Override
    public ReceiveSceneBenefitDTO queryReceivedBenefits(QueryReceivedBenefitsReqDTO queryReceivedBenefitsReqDTO) {
        Set<Long> activityIds = Sets.newHashSet(queryReceivedBenefitsReqDTO.getActivityIds());
        Set<Long> strategyIds = Sets.newHashSet(queryReceivedBenefitsReqDTO.getStrategyIds());
        List<TicketAccountRecordDO> aboutRecords;
        Span getTicketAccountRecordDOSSpan = Traces.spanBuilder("GetTicketAccountRecordDOS").startSpan();
        try {
            aboutRecords = getTicketAccountRecordDOS(queryReceivedBenefitsReqDTO, activityIds, strategyIds);
        } finally {
            getTicketAccountRecordDOSSpan.end();
        }
        if (CollectionUtils.isEmpty(aboutRecords)) {
            return null;
        }
        //TODO 下一个版本去掉非流水号逻辑，流水号为必传
        // 按时间排序，拿最后一个状态作为终态(防止非流水号查询出的领->退->领->退)
        aboutRecords = aboutRecords.stream().sorted(Comparator.comparing(TicketAccountRecordDO::getUpdateTime, Comparator.reverseOrder())
                .thenComparing(TicketAccountRecordDO::getCreateTime, Comparator.reverseOrder())).collect(Collectors.toList());

        ListMultimap<Pair<Long /* benefitId */, Long /* strategyId */>, TicketAccountRecordDO> recordMap = ArrayListMultimap.create();
        for (TicketAccountRecordDO ticketAccountRecordDO : aboutRecords) {
            recordMap.put(Pair.of(ticketAccountRecordDO.getBenefitId(), ticketAccountRecordDO.getStrategyId()), ticketAccountRecordDO);
        }
        ReceiveSceneBenefitDTO resp = new ReceiveSceneBenefitDTO();
        resp.setSceneCode(queryReceivedBenefitsReqDTO.getSceneCode());

        // 返回成功的权益id
        List<ReceiveBenefitDTO> successList = Lists.newArrayList();

        Span buildRespSpan = Traces.spanBuilder("BuildResp").startSpan();
        for (Pair<Long /* benefitId */, Long /* strategyId */> idPair : recordMap.keySet()) {
            List<TicketAccountRecordDO> records = recordMap.get(idPair);
            TicketAccountRecordDO last = records.get(records.size() - 1);
            if (OperateTypeEnum.PROVIDE.getCode().equals(last.getOperateType())) {
                Long benefitId = idPair.getLeft();
                Long strategyId = idPair.getRight();
                ActivityCacheDto activityCacheDtoById = null;
                try {
                    activityCacheDtoById = localCacheManager.getActivityCacheDtoById(last.getActivityId());
                } catch (Exception e) {
                    log.error("读取本地缓存活动信息出错 last:{} ", last, e);
                }
                String oldBenefitId = Strings.EMPTY;
                try {
                    oldBenefitId = localCacheManager.getMappingIdByBenefitIdAndStrategyById(benefitId, strategyId);
                } catch (Exception e) {
                    log.error("读取本地缓存获取老权益ID信息出错 last:{} oldBenefitId:{}", last, oldBenefitId, e);
                }
                ReceiveBenefitDTO receiveBenefitDTO = ReceiveBenefitDTO.builder()
                        .benefitId(benefitId)
                        .activityId(last.getActivityId())
                        .strategyId(strategyId)
                        .accountRecordId(last.getId())
                        .benefitType(BenefitTypeEnum.TICKET.getId())
                        .benefitValue(last.getTicketTemplateId())
                        .thirdResNo(last.getUserTicketId())
                        .externalId(null == activityCacheDtoById ? Strings.EMPTY : activityCacheDtoById.getExternalId())
                        .benefitGroupId(last.getBenefitGroupId())
                        .oldBenefitId(oldBenefitId)
                        .build();
                successList.add(receiveBenefitDTO);
            }
        }
        buildRespSpan.end();
        resp.setSuccessList(successList);
        return resp;
    }

    @Nullable
    private List<TicketAccountRecordDO> getTicketAccountRecordDOS(
            @NotNull QueryReceivedBenefitsReqDTO queryReceivedBenefitsReqDTO,
            @NotNull Set<Long> activityIds, @NotNull Set<Long> strategyIds) {
        List<TicketAccountRecordDO> aboutRecords = Lists.newArrayList();
        LambdaQueryWrapper<TicketAccountRecordDO> cond = new LambdaQueryWrapper<TicketAccountRecordDO>()
                .eq(TicketAccountRecordDO::getUserId, queryReceivedBenefitsReqDTO.getUserId())
                .eq(TicketAccountRecordDO::getStatus, StatusEnum.SUCCESS.getCode());
        if (StringUtils.isNotBlank(queryReceivedBenefitsReqDTO.getSerialNumber())) {
            //auxKey 查询 理论上只能有一个
            cond = cond.eq(TicketAccountRecordDO::getReqNo, queryReceivedBenefitsReqDTO.getSerialNumber())
                    .eq(TicketAccountRecordDO::getAuxKey, queryReceivedBenefitsReqDTO.getSerialNumber())
                    .eq(TicketAccountRecordDO::getOperateType, OperateTypeEnum.PROVIDE.getCode());
        } else if (CollectionUtils.isEmpty(queryReceivedBenefitsReqDTO.getActivityIds())
                && CollectionUtils.isEmpty(queryReceivedBenefitsReqDTO.getStrategyIds())) {
            return null;
        } else {
            // 如果没有流水号，仅靠活动ID和策略ID也是能查的，但是需要排序并过滤fallback的记录
            if (CollectionUtils.isNotEmpty(activityIds)) {
                cond.in(TicketAccountRecordDO::getActivityId, activityIds);
            }
            if (CollectionUtils.isNotEmpty(strategyIds)) {
                cond.in(TicketAccountRecordDO::getStrategyId, strategyIds);
            }
        }
        aboutRecords = ticketAccountRecordMapper.selectList(cond);
        if (CollectionUtils.isEmpty(aboutRecords)) {
            // 没查到有效的记录
            return null;
        }
        return aboutRecords;
    }

    /**
     * 权益领取异步
     *
     * @param reqNo                        业务流水号
     * @param sceneCode                    场景 code
     * @param engineContextDTO             上下文
     * @param userReceiveBenefits          用户本次需要领取的权益列表
     * @param receiveSuccessBenefitDTOList 成功领取的权益列表
     * @param receiveFailBenefitDTOList    领取失败的权益列表
     * @param receiveProcessBenefitDTOList 处理中的权益列表
     */
    private void receiveBenefitsAsync(String reqNo, String sceneCode, EngineContextDTO engineContextDTO,
                                      List<ReceiveBenefitDTO> userReceiveBenefits,
                                      List<ReceiveBenefitDTO> receiveSuccessBenefitDTOList,
                                      List<ReceiveBenefitDTO> receiveFailBenefitDTOList,
                                      List<ReceiveBenefitDTO> receiveProcessBenefitDTOList) {
        if (CollectionUtils.isEmpty(userReceiveBenefits)) {
            log.warn("receiveSceneBenefits receiveBenefitsAsync userReceiveBenefits is null. reqNo={}, sceneCode={}, engineContext={}",
                    reqNo, sceneCode, JSON.toJSONString(engineContextDTO));
            throw new ApiBusinessException(ExceptionEnum.RECEIVE_BENEFIT_IS_NULL);
        }

        if (userReceiveBenefits.size() == 1) {
            receiveSingleSceneBenefit(reqNo, sceneCode, engineContextDTO, userReceiveBenefits.get(0),
                    receiveSuccessBenefitDTOList, receiveFailBenefitDTOList, receiveProcessBenefitDTOList);
            return;
        }

        List<CompletableFuture<Void>> asyncTasks = Lists.newArrayList();
        for (ReceiveBenefitDTO userReceiveBenefit : userReceiveBenefits) {
            CompletableFuture<Void> receiveFuture = ThreadsUtils.runAsync(() ->
                            receiveSingleSceneBenefit(reqNo, sceneCode, engineContextDTO, userReceiveBenefit,
                                    receiveSuccessBenefitDTOList, receiveFailBenefitDTOList, receiveProcessBenefitDTOList),
                    ThreadsUtils.getCoreThreadPoll(), Constants.RECEIVE_SCENE_BENEFIT);
            asyncTasks.add(receiveFuture);
        }
        // 同步等待
        ThreadsUtils.getCompletableFutureList(asyncTasks, sceneBenefitOperateConstants.getReceiveSceneBenefitWaitMsTime(), TimeUnit.MILLISECONDS);
    }

    /**
     * 权益领取
     *
     * @param reqNo                        业务流水号
     * @param sceneCode                    场景 code
     * @param engineContextDTO             上下文
     * @param userReceiveBenefit           用户本次需要领取的权益
     * @param receiveSuccessBenefitDTOList 成功领取的权益列表
     * @param receiveFailBenefitDTOList    领取失败的权益列表
     * @param receiveProcessBenefitDTOList 处理中的权益列表
     */
    private void receiveSingleSceneBenefit(String reqNo, String sceneCode, EngineContextDTO engineContextDTO,
                                           ReceiveBenefitDTO userReceiveBenefit,
                                           List<ReceiveBenefitDTO> receiveSuccessBenefitDTOList,
                                           List<ReceiveBenefitDTO> receiveFailBenefitDTOList,
                                           List<ReceiveBenefitDTO> receiveProcessBenefitDTOList) {
        // 设置 oldBenefitId
        userReceiveBenefit.setOldBenefitId(getOldBenefitId(sceneCode, userReceiveBenefit.getStrategyId(),
                userReceiveBenefit.getBenefitId()));

        // 转换成权益账户上下文
        EquityAccountContext equityAccountContext = BenefitOperateEntity.convertToEquityAccountContext(reqNo, sceneCode,
                engineContextDTO, userReceiveBenefit);

        // 指定领取场景领取权益
        AccountStrategyContext accountStrategyContext = AccountStrategyContext.builder(equityAccountContext.getAccountType());
        ReceiveBenefitResDTO receiveBenefitResDTO = accountStrategyContext.receiveSceneBenefit(equityAccountContext);

        // 领取返回结果为空，则失败
        if (Objects.isNull(receiveBenefitResDTO)) {
            userReceiveBenefit.setUnableReceiveReasonCode("网络异常");
            receiveFailBenefitDTOList.add(userReceiveBenefit);
            CsossUtils.logEventFail("receiveSceneBenefitFail", "netError", reqNo);
            return;
        }
        userReceiveBenefit.setAccountRecordId(receiveBenefitResDTO.getAccountRecordId());
        userReceiveBenefit.setThirdResNo(receiveBenefitResDTO.getThirdResNo());
        // 失败
        StatusEnum resultStatusEnum = receiveBenefitResDTO.getStatusEnum();
        if (Objects.isNull(resultStatusEnum) || StatusEnum.FAIL.equals(resultStatusEnum)) {
            userReceiveBenefit.setUnableReceiveReasonCode(receiveBenefitResDTO.getCode());
            receiveFailBenefitDTOList.add(userReceiveBenefit);
            CsossUtils.logEventFail("receiveSceneBenefitFail", receiveBenefitResDTO.getCode() + "_" + receiveBenefitResDTO.getMsg(), reqNo);
            return;
        }
        // 成功
        if (StatusEnum.SUCCESS.equals(resultStatusEnum)) {
            receiveSuccessBenefitDTOList.add(userReceiveBenefit);
            return;
        }
        // 处理中
        receiveProcessBenefitDTOList.add(userReceiveBenefit);
    }

    private String getOldBenefitId(String sceneCode, Long strategyId, Long benefitId) {
        if (StringUtils.isBlank(sceneCode) || Objects.isNull(strategyId) || Objects.isNull(benefitId)) {
            return null;
        }
        // 活动场景为领券活动，需要查询老权益 id
        List<String> hasMappingIdSceneCodes = Lists.newArrayList(SceneCodeEnum.RECEIVE_TICKET_ACTIVITY.getCode());
        if (!hasMappingIdSceneCodes.contains(sceneCode)) {
            return null;
        }
        try {
            return localCacheManager.getMappingIdByBenefitIdAndStrategyById(benefitId, strategyId);
        } catch (Exception e) {
            log.error("getOldBenefitId exception. strategyId={}, benefitId={}", strategyId, benefitId, e);
            return null;
        }
    }

    /**
     * 权益过滤，过滤出可以领取的权益列表
     */
    private List<ReceiveBenefitDTO> filterUserReceiveBenefits(List<SceneActivityCacheDto> sceneActivityCacheDtoList,
                                                              List<ReqReceiveBenefitDTO> reqReceiveBenefitDTOList) {
        if (CollectionUtils.isEmpty(reqReceiveBenefitDTOList)) {
            log.warn("receiveSceneBenefits filterUserReceiveBenefits reqReceiveBenefitDTOList is null");
            throw new ApiBusinessException(ExceptionEnum.RECEIVE_BENEFIT_IS_NULL);
        }
        if (CollectionUtils.isEmpty(sceneActivityCacheDtoList)) {
            log.warn("receiveSceneBenefits filterUserReceiveBenefits sceneActivityCacheDtoList is null. reqReceiveBenefitDTOList={}",
                    JSON.toJSONString(reqReceiveBenefitDTOList));
            throw new ApiBusinessException(ExceptionEnum.USER_CAN_NOT_RECEIVE_BENEFIT);
        }

        // 获取用户可以领取的权益列表，在咨询获取活动可以领取的权益列表里（sceneActivityCacheDtoList），且在用户传入可以领取的权益列表里（reqReceiveBenefitDTOList）
        List<ReceiveBenefitDTO> userReceiveBenefits = BenefitOperateEntity.convertToUserReceiveBenefits(sceneActivityCacheDtoList,
                reqReceiveBenefitDTOList);
        // 领取的数据为空，或者当前领取的权益发生变化
        if (CollectionUtils.size(userReceiveBenefits) < CollectionUtils.size(reqReceiveBenefitDTOList)) {
            log.warn("receiveSceneBenefits filterUserReceiveBenefits 当前领取的权益发生变化 userReceiveBenefits={}, reqReceiveBenefitDTOList:{}",
                    JSON.toJSONString(userReceiveBenefits), JSON.toJSONString(reqReceiveBenefitDTOList));
            throw new ApiBusinessException(ExceptionEnum.USER_HAS_NO_AUTH_RECEIVE_BENEFIT);
        }
        return userReceiveBenefits;
    }

    @Override
    public List<SceneActivityCacheDto> filterActiveActivityCacheList(List<SceneActivityCacheDto> activityCacheList,
                                                                     ActivityFilterTypeEnum actFilterType) {
        if (CollectionUtils.isEmpty(activityCacheList)) {
            return null;
        }
        Date currentDate = new Date();
        return activityCacheList.stream().map(sceneActivityCacheDto -> {
            // 1 判断当前活动是否生效（活动状态、活动时间）
            if (!currentActivityIsMeetActivityFilterRule(currentDate, sceneActivityCacheDto, actFilterType)) {
                return null;
            }

            // 2 判断过滤策略状态（暂无）

            // 3 判断过滤权益是否生效（权益状态、权益有效期）
            filterActiveStrategyBenefitList(sceneActivityCacheDto);

            // 4 如果策略关联的权益列表为空，则活动对应策略过滤不返回
            if (CollectionUtils.isEmpty(sceneActivityCacheDto.getStrategyCacheDtoList())) {
                return null;
            }
            return sceneActivityCacheDto;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public void initActivityCount(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> activities) {
        try {
            if (Objects.isNull(engineContextDTO) || CollectionUtils.isEmpty(activities)) {
                return;
            }
            List<Long> activityIds = engineContextDTO.getActivityIdList();
            Integer externalType = engineContextDTO.getExternalType();
            List<String> externalIds = engineContextDTO.getExternalIdList();
            activities.stream()
                    .filter(activity -> (CollectionUtils.isNotEmpty(activityIds) && activityIds.contains(activity.getActivityId()))
                            || (Objects.equals(externalType, activity.getExternalType()) && CollectionUtils.isNotEmpty(externalIds) && externalIds.contains(activity.getExternalId())))
                    .filter(activity -> {
                        Map<String, Object> extInfoMap = JsonUtil.parseToMap(activity.getExtInfo());
                        return MapUtils.isNotEmpty(extInfoMap) && NumberUtil.isInteger(String.valueOf(extInfoMap.get("initActivityCount"))) && NumberUtil.isLong(String.valueOf(extInfoMap.get("benefitId")));
                    })
                    .limit(1)
                    .forEach(activity -> {
                        InitActivityCountReqDTO initActivityCountReqDTO = BenefitOperateConvertEntity.convertToInitActivityCountReqDTO(engineContextDTO,
                                activity, applicationName);
                        universalBenefitOperateCoreService.initActivityCount(initActivityCountReqDTO);
                    });
        } catch (Exception e) {
            log.error("initActivityCount exception userId={}", engineContextDTO.getUserId(), e);
            CsossUtils.logEvent(MonitorConstants.INIT_ACTIVITY_COUNT, "exception");
        }

    }

    private void filterActiveStrategyBenefitList(SceneActivityCacheDto sceneActivityCacheDto) {
        List<StrategyCacheDto> originStrategyCacheDtoList = sceneActivityCacheDto.getStrategyCacheDtoList();
        if (CollectionUtils.isEmpty(originStrategyCacheDtoList)) {
            return;
        }
        originStrategyCacheDtoList.forEach(strategy -> {
            Long strategyId = strategy.getStrategyId();
            List<BenefitIdWithConfDto> originBenefitDtoList = strategy.getStrategyBenefitGroup().get(strategyId);
            if (CollectionUtils.isEmpty(originBenefitDtoList)) {
                return;
            }
            List<BenefitIdWithConfDto> activeBenefitDtoList = originBenefitDtoList.stream()
                    // 加载到 localCache 的时候已经判断过了，无效权益不加在到缓存。因为有效期是否过期需要实时判断，所以这里要再做一次
                    .filter(BenefitUtil::isEffectiveBenefit).collect(Collectors.toList());
            strategy.getStrategyBenefitGroup().put(strategyId, activeBenefitDtoList);
        });
        // 如果策略关联的权益列表为空，则活动对应策略过滤不返回
        List<StrategyCacheDto> resultStrategyCacheDtoList = originStrategyCacheDtoList.stream()
                .filter(strategy -> CollectionUtils.isNotEmpty(strategy.getStrategyBenefitGroup().get(strategy.getStrategyId())))
                .collect(Collectors.toList());
        sceneActivityCacheDto.setStrategyCacheDtoList(resultStrategyCacheDtoList);
    }

    /**
     * 当前活动是否满足过滤规则
     *
     * @param currentDate           当前日期
     * @param sceneActivityCacheDto 活动
     * @param actFilterType         活动过滤类型
     * @return 是否满足
     */
    private boolean currentActivityIsMeetActivityFilterRule(Date currentDate, SceneActivityCacheDto sceneActivityCacheDto,
                                                            ActivityFilterTypeEnum actFilterType) {
        Integer status = sceneActivityCacheDto.getStatus();
        Date startTime = sceneActivityCacheDto.getStartTime();
        Date endTime = sceneActivityCacheDto.getEndTime();
        boolean startDateIsBeforeNow = Objects.isNull(startTime) || TimestampUtil.sourceDateIsBeforeOrEqualTargetDate(startTime, currentDate);
        boolean endDateIsAfterNow = Objects.isNull(endTime) || TimestampUtil.sourceDateIsAfterOrEqualTargetDate(endTime, currentDate);
        // 状态为已开始的活动（生效中）
        boolean startedAct = ActivityStatusEnum.PUBLISHED.getStatus().equals(status) && startDateIsBeforeNow && endDateIsAfterNow;
        // 状态为未开始的活动（即将生效）
        boolean nowStartedAct = ActivityStatusEnum.PUBLISHED.getStatus().equals(status) && !startDateIsBeforeNow && endDateIsAfterNow;

        // 获取生效中的活动。如果 actFilterType 为空，默认获取生效中的活动
        if (Objects.isNull(actFilterType) || ActivityFilterTypeEnum.EFFECT_ACTIVITY.equals(actFilterType)) {
            return startedAct;
        }
        // 生效中和即将生效的活动
        if (ActivityFilterTypeEnum.EFFECT_AND_PENDING_ACTIVITY.equals(actFilterType)) {
            return startedAct || nowStartedAct;
        }
        return false;
    }

    private List<SceneActivityCacheDto> filterActivity(List<SceneActivityCacheDto> activityCacheDtoList, EngineContextDTO engineContextDTO) {
        List<Long> activityIds = engineContextDTO.getActivityIdList();
        Integer externalType = engineContextDTO.getExternalType();
        List<String> externalIds = engineContextDTO.getExternalIdList();
        return activityCacheDtoList.stream()
                // 根据活动 activityIds 过滤活动
                .filter(activity -> CollectionUtils.isEmpty(activityIds) || activityIds.contains(activity.getActivityId()))
                // 根据活动 externalType、externalIds 过滤活动
                .filter(activity -> {
                    if (Objects.isNull(externalType) || CollectionUtils.isEmpty(externalIds)) {
                        return true;
                    }
                    return Objects.equals(externalType, activity.getExternalType()) && externalIds.contains(activity.getExternalId());
                })
                // 过滤出符合条件的策略，重置活动中的策略列表
                .peek(e -> e.setStrategyCacheDtoList(filterStrategy(e.getStrategyCacheDtoList(), engineContextDTO)))
                // 过滤策略为空的活动
                .filter(e -> CollectionUtils.isNotEmpty(e.getStrategyCacheDtoList()))
                .collect(Collectors.toList());
    }

    private List<StrategyCacheDto> filterStrategy(List<StrategyCacheDto> strategyCacheDtoList, EngineContextDTO engineContextDTO) {
        List<Long> strategyIds = engineContextDTO.getStrategyIdList();
        List<String> strategyExternalIds = engineContextDTO.getStrategyExternalIdList();
        return strategyCacheDtoList.stream()
                // 根据策略 strategyIds 过滤策略
                .filter(strategy -> CollectionUtils.isEmpty(strategyIds) || strategyIds.contains(strategy.getStrategyId()))
                // 根据策略 strategyExternalIds 过滤策略
                .filter(strategy -> CollectionUtils.isEmpty(strategyExternalIds) || strategyExternalIds.contains(strategy.getExternalId()))
                // 过滤出符合条件的权益，重置策略中的 strategyBenefitGroup
                .peek(strategy -> strategy.setStrategyBenefitGroup(filterStrategyBenefitGroup(strategy, engineContextDTO)))
                // 过滤 strategyBenefitGroup 为空的策略
                .filter(strategy -> org.apache.commons.collections4.MapUtils.isNotEmpty(strategy.getStrategyBenefitGroup()))
                .collect(Collectors.toList());
    }

    private Map<Long, List<BenefitIdWithConfDto>> filterStrategyBenefitGroup(StrategyCacheDto strategy,
                                                                             EngineContextDTO engineContextDTO) {
        // 过滤权益
        Long strategyId = strategy.getStrategyId();
        Map<Long, List<BenefitIdWithConfDto>> strategyBenefitGroup = strategy.getStrategyBenefitGroup();
        List<BenefitIdWithConfDto> filterBenefits = filterBenefit(strategyBenefitGroup.get(strategyId), engineContextDTO);

        // 过滤后权益为空，则返回 null
        if (CollectionUtils.isEmpty(filterBenefits)) {
            return null;
        }

        // 过滤后权益不为空，则返回新的 strategyBenefitGroup
        Map<Long, List<BenefitIdWithConfDto>> newStrategyBenefitGroup = Maps.newHashMap();
        newStrategyBenefitGroup.put(strategyId, filterBenefits);
        return newStrategyBenefitGroup;
    }

    private List<BenefitIdWithConfDto> filterBenefit(List<BenefitIdWithConfDto> benefitIdWithConfDtoList,
                                                     EngineContextDTO engineContextDTO) {
        List<Long> benefitIds = engineContextDTO.getBenefitIdList();
        return benefitIdWithConfDtoList.stream()
                // 根据权益 benefitIds 过滤权益
                .filter(benefit -> CollectionUtils.isEmpty(benefitIds) || benefitIds.contains(benefit.getId()))
                .collect(Collectors.toList());
    }

    private SceneEngineContextDTO convertToSceneEngineContextDTO(String sceneCode, EngineContextDTO engineContextDTO) {
        SceneCacheDTO sceneCacheDTO = localCacheManager.getSceneCacheDTO(sceneCode);
        SceneEngineContextDTO sceneEngineContextDTO = EngineContextConvert.INSTANCE.convertToSceneEngineContextDTO(engineContextDTO);
        sceneEngineContextDTO.setSceneCode(sceneCode);
        if (Objects.nonNull(sceneCacheDTO)) {
            sceneEngineContextDTO.setSceneSendType(sceneCacheDTO.getSendType());
        }
        return sceneEngineContextDTO;
    }

    @MonitorSpan(name = "ReceiveSceneBenefits.FilterUserUnableReceiveBenefits")
    public List<ReceiveBenefitDTO> filterUserUnableReceiveBenefits(List<ReqReceiveBenefitDTO> reqReceiveBenefitDTOList,
                                                                   EngineResultContextDTO resultContextDTO) {
        if (CollectionUtils.isEmpty(reqReceiveBenefitDTOList)) {
            log.warn("receiveSceneBenefits filterUserUnableReceiveBenefits reqReceiveBenefitDTOList is null");
            return null;
        }
        List<UnableReceiveBenefitDTO> unableReceiveBenefitDTOList = resultContextDTO.getUnableReceiveBenefitDTOList();
        if (CollectionUtils.isEmpty(unableReceiveBenefitDTOList)) {
            return null;
        }
        return reqReceiveBenefitDTOList.stream()
                .map(reqReceiveBenefit -> BenefitOperateEntity.convertToUserUnableReceiveBenefit(reqReceiveBenefit, unableReceiveBenefitDTOList))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private void fillEngineResultContextDTO(EngineContextDTO engineContextDTO, RuleConditionFactorValueDTO ruleConditionFactorValue) {
        Integer userStatus = Optional.ofNullable(ruleConditionFactorValue).map(RuleConditionFactorValueDTO::getUserStatus).orElse(null);
        engineContextDTO.getResultContextDTO().setUserStatus(userStatus);
    }
}
