package com.ddmc.equity.domain.dto.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/23 15:07
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("通用权益账户详情-内部拓展信息")
public class UniversalAccountDetailInnerExtDTO {

    @ApiModelProperty("最后一次操作的流水号，目前记录的是发放时流水号")
    private String lastReqNo;

    @ApiModelProperty("城市 code")
    private String cityCode;

    @ApiModelProperty("站点 id")
    private String stationId;

    /**
     * @see com.ddmc.equity.enums.ExternalTypeEnum
     */
    @ApiModelProperty(value = "外部活动类型")
    private Integer externalType;

    @ApiModelProperty(value = "外部活动 id")
    private String externalId;

    @ApiModelProperty(value = "策略外部关联 id。如果是来源玩法的活动，则为活动 prizeId；如果是膨胀券活动，则为母券券模板 id；")
    private String strategyExternalId;
}
