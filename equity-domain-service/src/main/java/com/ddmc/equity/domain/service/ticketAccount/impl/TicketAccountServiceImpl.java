package com.ddmc.equity.domain.service.ticketAccount.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.service.ticketAccount.TicketAccountService;
import com.ddmc.equity.dto.business.PageListReqDTO;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.infra.repository.dao.TicketAccountDO;
import com.ddmc.equity.infra.repository.dao.mapper.TicketAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class TicketAccountServiceImpl implements TicketAccountService {

    @Autowired
    private TicketAccountMapper ticketAccountMapper;

    /**
     * 账户保存
     *
     * @param ticketAccountDO 账户
     * @return 是否成功
     */
    @Override
    public boolean save(TicketAccountDO ticketAccountDO) {
        if (Objects.isNull(ticketAccountDO)) {
            return false;
        }
        try {
            int result = ticketAccountMapper.insert(ticketAccountDO);
            return result == 1;
        } catch (Exception e) {
            log.error("TicketAccountServiceImpl.save.e;do={}", JsonUtil.toJsonString(ticketAccountDO), e);
        }
        return false;
    }

    @Override
    public Integer getUserTicketCountByActivityId(String userId, Long activityId, String ticketTemplateId) {
        QueryWrapper<TicketAccountDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId).eq("activity_id", activityId).eq("ticket_template_id", ticketTemplateId);
        List list = ticketAccountMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.size();
        }
        return 0;
    }

    @Override
    public List<TicketAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds,
                                                       List<Integer> statuses, Date startDate, Date endDate) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询优惠券权益子账户 userId 不能为空");

        Wrapper<TicketAccountDO> wrapper = Wrappers.<TicketAccountDO>lambdaQuery()
                .eq(TicketAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(TicketAccountDO::getUserId, userId)
                .in(CollectionUtils.isNotEmpty(activityIds), TicketAccountDO::getActivityId, activityIds)
                .in(CollectionUtils.isNotEmpty(statuses), TicketAccountDO::getStatus, statuses)
                .ge(Objects.nonNull(startDate), TicketAccountDO::getCreateTime, startDate)
                .le(Objects.nonNull(endDate), TicketAccountDO::getCreateTime, endDate)
                .orderByDesc(TicketAccountDO::getId);
        return ticketAccountMapper.selectList(wrapper);
    }

    @Override
    public PageListRespDTO<TicketAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds,
                                                                  List<Integer> statuses, Date startDate, Date endDate,
                                                                  PageListReqDTO pageListReq) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(),
                "分页查询优惠券权益子账户 userId 不能为空");

        Wrapper<TicketAccountDO> wrapper = Wrappers.<TicketAccountDO>lambdaQuery()
                .eq(TicketAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(TicketAccountDO::getUserId, userId)
                .in(CollectionUtils.isNotEmpty(activityIds), TicketAccountDO::getActivityId, activityIds)
                .in(CollectionUtils.isNotEmpty(statuses), TicketAccountDO::getStatus, statuses)
                .ge(Objects.nonNull(startDate), TicketAccountDO::getCreateTime, startDate)
                .le(Objects.nonNull(endDate), TicketAccountDO::getCreateTime, endDate)
                .orderByDesc(TicketAccountDO::getCreateTime);
        IPage<TicketAccountDO> page = new com.baomidou.mybatisplus.extension.plugins.pagination
                .Page<>(pageListReq.getPage(), pageListReq.getPageSize());

        IPage<TicketAccountDO> pageResult = ticketAccountMapper.selectPage(page, wrapper);
        if (Objects.isNull(pageResult)) {
            log.warn("queryAccountsByUserId pageResult is null userId={}, activityIds={}, statuses={}" +
                            ", startDate={}, endDate={}, pageListReq={}",
                    userId, activityIds, statuses, startDate, endDate, JSON.toJSONString(pageListReq));
            return PageListRespDTO.<TicketAccountDO>builder().build();
        }
        return PageListRespDTO.<TicketAccountDO>builder()
                .list(pageResult.getRecords())
                .total(page.getTotal())
                .build();
    }

    @Override
    public Integer getUserTicketCount(String userId, Long activityId, Long benefitId, Date startDate, Date endDate) {
        QueryWrapper<TicketAccountDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId).eq("activity_id", activityId).eq("benefit_id", benefitId)
                .ge("create_time", startDate).le("create_time", endDate);
        Integer count = ticketAccountMapper.selectCount(queryWrapper);
        if (count == null) {
            return 0;
        }
        return count;
    }
}
