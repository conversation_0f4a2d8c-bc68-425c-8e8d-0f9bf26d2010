package com.ddmc.equity.domain.service.balance;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ddmc.equity.dto.business.PageListReqDTO;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.infra.repository.dao.BalanceAccountDO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/16 11:20
 * @description
 */
public interface BalanceAccountDomainService extends IService<BalanceAccountDO> {

    /**
     * 创建权益子账户
     *
     * @param accountDO DB_DO
     */
    void insertAccountDO(BalanceAccountDO accountDO);

    /**
     * 通过 userId 查询权益子账户列表
     *
     * @param userId      用户 id
     * @param activityIds 活动 id。如果未指定活动 id，则查询所有
     * @param statuses    权益子账户状态。如果未指定状态，则查询所有
     * @param startDate   开始时间。可以指定开始时间（创建时间）查询，不指定则查询所有
     * @param endDate     结束时间。可以指定结束时间（创建时间）查询，不指定则查询所有
     * @return 权益子账户列表
     */
    List<BalanceAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds,
                                                List<Integer> statuses, Date startDate, Date endDate);

    /**
     * 通过 userId 分页查询权益子账户列表
     *
     * @param userId      用户 id
     * @param activityIds 活动 id。如果未指定活动 id，则查询所有
     * @param statuses    权益子账户状态。如果未指定状态，则查询所有
     * @param startDate   开始时间。可以指定开始时间（创建时间）查询，不指定则查询所有
     * @param endDate     结束时间。可以指定结束时间（创建时间）查询，不指定则查询所有
     * @param pageListReq 分页参数
     * @return 权益子账户列表
     */
    PageListRespDTO<BalanceAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds,
                                                            List<Integer> statuses, Date startDate, Date endDate,
                                                            PageListReqDTO pageListReq);

    /**
     * 查询用户获得的余额总金额
     *
     * @param userId      用户 id
     * @param activityIds 活动 id。如果未指定活动 id，则查询所有
     * @param startDate   开始时间。可以指定开始时间（创建时间）查询，不指定则查询所有
     * @param endDate     结束时间。可以指定结束时间（创建时间）查询，不指定则查询所有
     * @return 余额总金额
     */
    BigDecimal getTotalBalanceMoney(String userId, List<Long> activityIds, Date startDate, Date endDate);
}
