package com.ddmc.equity.domain.valueobject.product;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created on 2022/10/13.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductInfoActivityVO implements Serializable {

    private static final long serialVersionUID = -4962333390030505631L;

    public Integer type;

    @JsonProperty("type_name")
    public String  typeName;

    public String tag;

    @JsonProperty("is_unsold")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public Boolean isUnsold;
}

