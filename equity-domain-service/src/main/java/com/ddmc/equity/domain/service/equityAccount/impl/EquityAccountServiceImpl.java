package com.ddmc.equity.domain.service.equityAccount.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ddmc.equity.domain.service.equityAccount.EquityAccountService;
import com.ddmc.equity.infra.repository.dao.EquityAccountDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityAccountMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class EquityAccountServiceImpl implements EquityAccountService {

    @Resource
    private EquityAccountMapper equityAccountMapper;


    @Override
    public EquityAccountDO queryEquityAccountDOByUserIdAndAccountType(String uid, int accountType) {
        QueryWrapper<EquityAccountDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", uid).eq("account_type", accountType);
        return equityAccountMapper.selectOne(queryWrapper);
    }

    @Override
    public int createEquityAccountByDO(EquityAccountDO equityAccountDO) {
        return equityAccountMapper.insert(equityAccountDO);
    }
}
