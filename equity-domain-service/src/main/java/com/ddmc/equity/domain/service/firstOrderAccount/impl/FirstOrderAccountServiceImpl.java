package com.ddmc.equity.domain.service.firstOrderAccount.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ddmc.equity.common.enums.FirstOrderAccountStatus;
import com.ddmc.equity.domain.service.firstOrderAccount.FirstOrderAccountService;
import com.ddmc.equity.infra.repository.dao.FirstOrderAccountDO;
import com.ddmc.equity.infra.repository.dao.mapper.FirstOrderAccountMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class FirstOrderAccountServiceImpl implements FirstOrderAccountService {

    @Resource
    private FirstOrderAccountMapper firstOrderAccountMapper;

    @Override
    public int saveFirstOrderAccountDO(FirstOrderAccountDO firstOrderAccountDO) {
        return firstOrderAccountMapper.insert(firstOrderAccountDO);
    }

    @Override
    public FirstOrderAccountDO queryFirstOrderAccountDOByUserId(String userId) {
        QueryWrapper<FirstOrderAccountDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        return firstOrderAccountMapper.selectOne(queryWrapper);
    }

    @Override
    public int updateFirstOrderAccountDO(FirstOrderAccountDO firstOrderAccountDO) {
        return firstOrderAccountMapper.updateById(firstOrderAccountDO);
    }


    @Override
    public boolean useAccount(String uid, String lastReqNo) {
        UpdateWrapper<FirstOrderAccountDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("user_id", uid);
        updateWrapper.eq("status", FirstOrderAccountStatus.INIT.getCode());
        updateWrapper.set("status", FirstOrderAccountStatus.USE.getCode());
        if (StringUtils.isNotBlank(lastReqNo)) {
            updateWrapper.set("last_req_no", lastReqNo);
        }
        return firstOrderAccountMapper.update(null, updateWrapper) != 0;
    }

    @Override
    public boolean fallBackAccount(String uid, Long accountId, String lastReqNo) {
        UpdateWrapper<FirstOrderAccountDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("user_id", uid);
        updateWrapper.eq("id", accountId);
        updateWrapper.eq("status", FirstOrderAccountStatus.USE.getCode());
        updateWrapper.set("status", FirstOrderAccountStatus.INIT.getCode());
        if (StringUtils.isNotBlank(lastReqNo)) {
            updateWrapper.set("last_req_no", lastReqNo);
        }
        return firstOrderAccountMapper.update(null, updateWrapper) != 0;
    }
}
