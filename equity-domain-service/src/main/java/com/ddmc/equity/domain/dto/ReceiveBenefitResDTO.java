package com.ddmc.equity.domain.dto;

import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcResponseExtDTO;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class ReceiveBenefitResDTO {

    /**
     * 状态
     */
    private StatusEnum statusEnum;
    /**
     * 错误码
     */
    private String code;
    /**
     * 错误信息
     */
    private String msg;
    /**
     * 三方返回值。发放优惠券后返回用户领取后的 userTicketId；券膨胀返回用户膨胀后的券 userTicketId；发放优惠券包后返回用户领取后的 userPackageTicketId；
     */
    private String thirdResNo;
    /**
     * 外部 rpc 调用请求出参拓展信息
     */
    private UniversalAccountRpcResponseExtDTO rpcResponseExtDTO;
    /**
     * 权益子账户操作流水 id
     */
    private Long accountRecordId;
    /**
     * 权益账户明细 id
     */
    private Long accountDetailId;

    public static ReceiveBenefitResDTO fail(ExceptionEnum exceptionEnum) {
        ReceiveBenefitResDTO receiveBenefitResDTO = new ReceiveBenefitResDTO();
        receiveBenefitResDTO.setCode(exceptionEnum.getCode());
        receiveBenefitResDTO.setMsg(exceptionEnum.getMessage());
        receiveBenefitResDTO.setStatusEnum(StatusEnum.FAIL);
        return receiveBenefitResDTO;
    }

    public static ReceiveBenefitResDTO processing(ExceptionEnum exceptionEnum) {
        ReceiveBenefitResDTO receiveBenefitResDTO = new ReceiveBenefitResDTO();
        receiveBenefitResDTO.setCode(exceptionEnum.getCode());
        receiveBenefitResDTO.setMsg(exceptionEnum.getMessage());
        receiveBenefitResDTO.setStatusEnum(StatusEnum.PROCESSING);
        return receiveBenefitResDTO;
    }

    public static ReceiveBenefitResDTO success(EquityRpcDto equityRpcDto, Long accountRecordId) {
        ReceiveBenefitResDTO receiveBenefitResDTO = new ReceiveBenefitResDTO();
        receiveBenefitResDTO.setCode(ExceptionEnum.SUCCESS.getCode());
        receiveBenefitResDTO.setMsg(ExceptionEnum.SUCCESS.getMessage());
        receiveBenefitResDTO.setStatusEnum(StatusEnum.SUCCESS);
        receiveBenefitResDTO.setThirdResNo(Objects.isNull(equityRpcDto) ? null : equityRpcDto.getValue());
        receiveBenefitResDTO.setRpcResponseExtDTO(Objects.isNull(equityRpcDto) ? null : equityRpcDto.getRpcResponseExtDTO());
        receiveBenefitResDTO.setAccountRecordId(accountRecordId);
        return receiveBenefitResDTO;
    }

    public static ReceiveBenefitResDTO success(EquityRpcDto equityRpcDto, Long accountRecordId, Long accountDetailId) {
        ReceiveBenefitResDTO receiveBenefitResDTO = success(equityRpcDto, accountRecordId);
        receiveBenefitResDTO.setAccountDetailId(accountDetailId);
        return receiveBenefitResDTO;
    }

    public static ReceiveBenefitResDTO fail(String code, String message) {
        ReceiveBenefitResDTO receiveBenefitResDTO = new ReceiveBenefitResDTO();
        receiveBenefitResDTO.setCode(code);
        receiveBenefitResDTO.setMsg(message);
        receiveBenefitResDTO.setStatusEnum(StatusEnum.FAIL);
        return receiveBenefitResDTO;
    }
}
