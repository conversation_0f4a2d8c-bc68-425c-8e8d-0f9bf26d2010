package com.ddmc.equity.domain.service.coupon_expand.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.service.coupon_expand.CouponExpandAccountRecordDomainService;
import com.ddmc.equity.infra.repository.dao.CouponExpandAccountRecordDO;
import com.ddmc.equity.infra.repository.dao.mapper.CouponExpandAccountRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/26 16:45
 * @description
 */
@Slf4j
@Service
public class CouponExpandAccountRecordDomainServiceImpl extends ServiceImpl<CouponExpandAccountRecordMapper, CouponExpandAccountRecordDO> implements CouponExpandAccountRecordDomainService {

    @Override
    public void insertAccountRecordDO(CouponExpandAccountRecordDO accountRecordDO) {
        Assert.notNull(accountRecordDO, ExceptionEnum.ILLEGAL_ARGS.getCode(), "券膨胀权益子账户操作流水不能为空");
        Assert.mustTrue(StringUtils.isNotBlank(accountRecordDO.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "券膨胀权益子账户操作流水 userId 不能为空");
        this.save(accountRecordDO);
    }

    @Override
    public boolean updateAccountRecordStatusAndRpcResult(String userId, Long accountRecordId, Long accountId,
                                                         Integer status, EquityRpcDto equityRpcDto,
                                                         Map<String, Object> ruleLimitInfoMap) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "券膨胀权益子账户操作流水 userId 不能为空");
        Assert.notNull(accountRecordId, ExceptionEnum.ILLEGAL_ARGS.getCode(), "券膨胀权益子账户操作流水 accountRecordId 不能为空");
        Assert.mustTrue(StatusEnum.isContain(status), ExceptionEnum.ILLEGAL_ARGS.getCode(), "券膨胀权益子账户操作流水状态异常");

        CouponExpandAccountRecordDO update = new CouponExpandAccountRecordDO();
        update.setAccountId(Objects.nonNull(accountId) ? accountId : null);
        update.setExpandUserTicketId(Objects.nonNull(equityRpcDto) ? equityRpcDto.getValue() : null);
        update.setStatus(status);
        update.setAuxKey(Objects.equals(StatusEnum.FAIL.getCode(), status) ? String.valueOf(accountRecordId) : null);
        update.setRuleLimitInfo(MapUtils.isEmpty(ruleLimitInfoMap) ? null : JSON.toJSONString(ruleLimitInfoMap));
        update.setRpcCode(Objects.nonNull(equityRpcDto) ? equityRpcDto.getCode() : null);
        update.setRpcMsg(Objects.nonNull(equityRpcDto) ? equityRpcDto.getMessage() : null);
        return this.update(update, Wrappers.<CouponExpandAccountRecordDO>lambdaUpdate()
                .eq(CouponExpandAccountRecordDO::getUserId, userId)
                .eq(CouponExpandAccountRecordDO::getId, accountRecordId));
    }

    @Override
    public CouponExpandAccountRecordDO queryAccountRecordByUniqueKeyAndStatuses(String userId, Integer operateType,
                                                                                String reqNo, List<Integer> statuses) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "券膨胀权益子账户操作流水 userId 不能为空");
        Assert.mustTrue(OperateTypeEnum.contains(operateType), ExceptionEnum.ILLEGAL_ARGS.getCode(), "券膨胀权益子账户操作流水操作类型异常");
        Assert.mustTrue(StringUtils.isNotBlank(reqNo), ExceptionEnum.ILLEGAL_ARGS.getCode(), "券膨胀权益子账户操作流水 reqNo 不能为空");

        Wrapper<CouponExpandAccountRecordDO> wrapper = Wrappers.<CouponExpandAccountRecordDO>lambdaQuery()
                .eq(CouponExpandAccountRecordDO::getUserId, userId)
                .eq(CouponExpandAccountRecordDO::getOperateType, operateType)
                .eq(CouponExpandAccountRecordDO::getReqNo, reqNo)
                .in(CollectionUtils.isNotEmpty(statuses), CouponExpandAccountRecordDO::getStatus, statuses);
        return this.getOne(wrapper);
    }

}
