package com.ddmc.equity.domain.service.benefit.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ddmc.equity.domain.entity.benefit.BenefitConvertEntity;
import com.ddmc.equity.domain.service.benefit.EquityBenefitDomainService;
import com.ddmc.equity.dto.customer.SaveAndGetBenefitDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.mapper.EquityBenefitMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class EquityBenefitDomainServiceImpl implements EquityBenefitDomainService {

    @Resource
    private EquityBenefitMapper equityBenefitMapper;

    @Override
    public void insertOrUpdate(EquityBenefitDO needSaveDO) {
        equityBenefitMapper.insertOrUpdate(needSaveDO);
    }

    @Override
    public void batchInsert(List<EquityBenefitDO> needCreateBenefitList) {
        if (CollectionUtils.isEmpty(needCreateBenefitList)) {
            return;
        }
        // 因为要获取到权益 id，所以循环插入（批量插入获取不到权益 id）
        for (EquityBenefitDO equityBenefitDO : needCreateBenefitList) {
            equityBenefitMapper.insert(equityBenefitDO);
        }
    }

    @Override
    public EquityBenefitDO queryById(Long id) {
        return equityBenefitMapper.selectById(id);
    }

    @Override
    public List<EquityBenefitDO> getBenefitsByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        QueryWrapper<EquityBenefitDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids);
        return equityBenefitMapper.selectList(queryWrapper);
    }

    @Override
    public EquityBenefitDO getBenefitByUni(Integer benefitType, String benefitValue, String externalId) {
        if (Objects.isNull(benefitType) || StringUtils.isBlank(benefitValue)) {
            log.warn("getBenefitByUni params isError benefitType={}, benefitValue={}", benefitType, benefitValue);
            return null;
        }
        // 不指定 externalId 时，默认查询 externalId = '' 的权益
        String paramExternalId = StringUtils.defaultIfBlank(externalId, StringUtils.EMPTY);
        Wrapper<EquityBenefitDO> wrapper = Wrappers.<EquityBenefitDO>lambdaQuery()
                .eq(EquityBenefitDO::getBenefitType, benefitType)
                .eq(EquityBenefitDO::getBenefitValue, benefitValue)
                .eq(EquityBenefitDO::getExternalId, paramExternalId);
        return equityBenefitMapper.selectOne(wrapper);
    }

    @Override
    public EquityBenefitDO getBenefitByUniAndMaster(Integer benefitType, String benefitValue, String externalId) {
        if (Objects.isNull(benefitType) || StringUtils.isBlank(benefitValue)) {
            log.warn("getBenefitByUniAndMaster params isError benefitType={}, benefitValue={}", benefitType, benefitValue);
            return null;
        }
        // 不指定 externalId 时，默认查询 externalId = '' 的权益
        String paramExternalId = StringUtils.defaultIfBlank(externalId, StringUtils.EMPTY);
        Wrapper<EquityBenefitDO> wrapper = Wrappers.<EquityBenefitDO>lambdaQuery()
                .and(w -> w.apply("'bind_master' = 'bind_master'"))
                .eq(EquityBenefitDO::getBenefitType, benefitType)
                .eq(EquityBenefitDO::getBenefitValue, benefitValue)
                .eq(EquityBenefitDO::getExternalId, paramExternalId);
        return equityBenefitMapper.selectOne(wrapper);
    }

    @Override
    public List<EquityBenefitDO> queryEquityBenefitsByType(Integer benefitType) {
        if (!BenefitTypeEnum.contains(benefitType)) {
            log.warn("queryEquityBenefitsByType benefitType is error. benefitType={}", benefitType);
            return null;
        }
        Wrapper<EquityBenefitDO> wrapper = Wrappers.<EquityBenefitDO>lambdaQuery()
                .eq(EquityBenefitDO::getBenefitType, benefitType);
        return equityBenefitMapper.selectList(wrapper);
    }

    @Override
    public EquityBenefitDO saveAndGetBenefitDO(SaveAndGetBenefitDTO saveAndGetBenefitDTO) {
        if (Objects.isNull(saveAndGetBenefitDTO.getBenefitType()) || StringUtils.isBlank(saveAndGetBenefitDTO.getBenefitValue())) {
            log.warn("saveAndGetBenefitDO params isError saveAndGetBenefitDTO={}", JSON.toJSONString(saveAndGetBenefitDTO));
            return null;
        }

        EquityBenefitDO benefitDO = BenefitConvertEntity.convertToEquityBenefitDO(saveAndGetBenefitDTO);
        this.insertOrUpdate(benefitDO);
        return benefitDO;
    }
}