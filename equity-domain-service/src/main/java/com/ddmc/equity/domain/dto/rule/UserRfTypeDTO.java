package com.ddmc.equity.domain.dto.rule;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/2/2 17:08
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class UserRfTypeDTO {

    @ApiModelProperty(value = "RF 人群一级分类名称")
    private String rfFirstTypeName;

    @ApiModelProperty(value = "RF 人群细分类型名称")
    private String rfTypeName;
}
