package com.ddmc.equity.domain.entity.rule;

import com.alibaba.fastjson.JSON;
import com.ddmc.core.view.compat.ResponseBaseVo;
import com.ddmc.equity.common.constant.ActivityConfigConstants;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.AppVersionJoinTypeEnum;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.FrequencyDateTypeEnum;
import com.ddmc.equity.common.enums.PlanDateTypeEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.common.enums.StrategyBenefitReceiveLimitTypeEnum;
import com.ddmc.equity.common.enums.UserTagEnum;
import com.ddmc.equity.common.enums.VipIdentityEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.common.util.IntegerUtil;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.converter.rule.ABTestRuleConverter;
import com.ddmc.equity.domain.converter.rule.AppVersionRuleConverter;
import com.ddmc.equity.domain.converter.rule.CityRuleConverter;
import com.ddmc.equity.domain.converter.rule.CommunityUserRuleConverter;
import com.ddmc.equity.domain.converter.rule.OldSysBOCUserRuleConverter;
import com.ddmc.equity.domain.converter.rule.OldSysMaiCaiNewOrOldUserLimitRuleConverter;
import com.ddmc.equity.domain.converter.rule.ReceiveLimitRuleConverter;
import com.ddmc.equity.domain.converter.rule.StockLimitRuleConverter;
import com.ddmc.equity.domain.converter.rule.UserTagRuleConverter;
import com.ddmc.equity.domain.converter.rule.VipIdentityRuleConverter;
import com.ddmc.equity.domain.converter.rule.WeComUserRuleConverter;
import com.ddmc.equity.domain.converter.rule.WhiteUserListRuleConverter;
import com.ddmc.equity.domain.dto.rule.condition.ABTestRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.AppVersionRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.CityRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.CommunityUserRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.OldSysBOCUserRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.OldSysMaiCaiNewOrOldUserLimitRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.RfTypeRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.UserTagRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.VipIdentityRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.WeComUserRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.WhiteUserListRuleDTO;
import com.ddmc.equity.domain.valueobject.rule.DeleteRuleReqVO;
import com.ddmc.equity.domain.valueobject.rule.RuleReqVO;
import com.ddmc.equity.domain.valueobject.rule.condition.AbTestRuleVO;
import com.ddmc.equity.domain.valueobject.rule.condition.AppVersionRuleVO;
import com.ddmc.equity.domain.valueobject.rule.condition.AppVersionRuleValueVO;
import com.ddmc.equity.domain.valueobject.rule.condition.CityRuleVO;
import com.ddmc.equity.domain.valueobject.rule.condition.UserTagRuleVO;
import com.ddmc.equity.domain.valueobject.rule.condition.VipIdentityRuleVO;
import com.ddmc.equity.domain.valueobject.rule.condition.WhiteUserListRuleVO;
import com.ddmc.equity.dto.business.UniversalRuleDTO;
import com.ddmc.equity.dto.business.provide.ProvideBenefitDTO;
import com.ddmc.equity.dto.business.rule.ABTestDTO;
import com.ddmc.equity.dto.business.rule.AppVersionDTO;
import com.ddmc.equity.dto.business.rule.CitiesDTO;
import com.ddmc.equity.dto.business.rule.CommunityUserDTO;
import com.ddmc.equity.dto.business.rule.OldSysBOCUserDTO;
import com.ddmc.equity.dto.business.rule.OldSysMaiCaiNewOrOldUserLimitDTO;
import com.ddmc.equity.dto.business.rule.ReceiveLimitDTO;
import com.ddmc.equity.dto.business.rule.RfTypeDTO;
import com.ddmc.equity.dto.business.rule.RuleCommonDTO;
import com.ddmc.equity.dto.business.rule.RuleDTO;
import com.ddmc.equity.dto.business.rule.StockLimitDTO;
import com.ddmc.equity.dto.business.rule.UserTagDTO;
import com.ddmc.equity.dto.business.rule.VipIdentityDTO;
import com.ddmc.equity.dto.business.rule.WeComUserDTO;
import com.ddmc.equity.dto.business.rule.WhiteUserListDTO;
import com.ddmc.equity.enums.RuleScopeEnum;
import com.ddmc.equity.infra.repository.dao.EquityRuleDO;
import com.ddmc.equity.model.dto.StrategyRuleCacheDTO;
import com.ddmc.promocore.admin.vo.CountControlConfigVO;
import com.ddmc.promocore.admin.vo.PrizeVO;
import com.ddmc.promoequity.client.EquityAdminClient;
import com.ddmc.promoequity.dto.EquityDTO;
import com.ddmc.station.client.ServiceStationClient;
import com.ddmc.station.response.StationCityV2;
import com.ddmc.vouchercore.admin.vo.sub.AddressVo;
import com.ddmc.voucherprod.client.TicketAdminClient;
import com.ddmc.voucherprod.dto.ticket.request.GetTicketReqDTO;
import com.ddmc.voucherprod.dto.ticket.response.TicketRespDTO;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@Builder
@Slf4j
public class EquityRuleEntity {

    /**
     * 添加规则参数校验
     *
     * @param ruleReqVO 请求
     */
    public void checkAddParam(RuleReqVO ruleReqVO) {
        //不可为空
        Assert.notNull(ruleReqVO, ExceptionEnum.PARAMS_ERROR);
        Assert.notNull(ruleReqVO.getStrategyId(), ExceptionEnum.PARAMS_ERROR);
        //ab
        AbTestRuleVO abTestRuleVO = ruleReqVO.getAbTestRuleVO();
        if (Objects.nonNull(abTestRuleVO) && StringUtils.isNotBlank(abTestRuleVO.getLayerId())) {
            Assert.notEmpty(abTestRuleVO.getDefaultGroup(), ExceptionEnum.PARAMS_ERROR);
            Assert.notEmpty(abTestRuleVO.getChoosePutGroup(), ExceptionEnum.PARAMS_ERROR);
        }
        //app版本
        AppVersionRuleVO appVersionRuleVO = ruleReqVO.getAppVersionRuleVO();
        if (Objects.nonNull(appVersionRuleVO) && Objects.nonNull(appVersionRuleVO.getJoinType())) {
            Assert.mustTrue(AppVersionJoinTypeEnum.isContains(appVersionRuleVO.getJoinType()), ExceptionEnum.PARAMS_ERROR);
            List<AppVersionRuleValueVO> appVersionRuleValueVOList = appVersionRuleVO.getAppVersionList();
            Assert.notEmpty(appVersionRuleValueVOList, ExceptionEnum.PARAMS_ERROR);
        }
    }

    public List<EquityRuleDO> buildEquityRuleDoListFromVO(RuleReqVO ruleReqVO) {
        if (Objects.isNull(ruleReqVO)) {
            return null;
        }
        List<EquityRuleDO> equityRuleDOS = Lists.newArrayList();
        //ab转换
        EquityRuleDO abEquityRuleDO = convertAbTestRuleFromVO(ruleReqVO.getStrategyId(), ruleReqVO.getAbTestRuleVO());
        if (!Objects.isNull(abEquityRuleDO)) {
            fillOpAdminInfo(abEquityRuleDO, ruleReqVO.getOpAdminId(), ruleReqVO.getOpAdminName());
            equityRuleDOS.add(abEquityRuleDO);
        }
        //城市规则转换
        EquityRuleDO cityEquityRuleDO = convertCityRuleFromVO(ruleReqVO.getStrategyId(), ruleReqVO.getCityRuleVO());
        if (!Objects.isNull(cityEquityRuleDO)) {
            fillOpAdminInfo(cityEquityRuleDO, ruleReqVO.getOpAdminId(), ruleReqVO.getOpAdminName());
            equityRuleDOS.add(cityEquityRuleDO);
        }

        //app版本规则转换
        EquityRuleDO appVersionEquityRuleDO = convertAppVersionRuleFromVO(ruleReqVO.getStrategyId(), ruleReqVO.getAppVersionRuleVO());
        if (!Objects.isNull(appVersionEquityRuleDO)) {
            fillOpAdminInfo(appVersionEquityRuleDO, ruleReqVO.getOpAdminId(), ruleReqVO.getOpAdminName());
            equityRuleDOS.add(appVersionEquityRuleDO);
        }

        //人群规则转换
        EquityRuleDO userTagEquityRuleDO = convertUserTagRuleFromVO(ruleReqVO.getStrategyId(), ruleReqVO.getUserTagRuleVO());
        if (!Objects.isNull(userTagEquityRuleDO)) {
            fillOpAdminInfo(userTagEquityRuleDO, ruleReqVO.getOpAdminId(), ruleReqVO.getOpAdminName());
            equityRuleDOS.add(userTagEquityRuleDO);
        }
        //用户白名单规则转换
        EquityRuleDO whiteUserEquityRuleDO = convertWhiteUserRuleFromVO(ruleReqVO.getStrategyId(), ruleReqVO.getWhiteUserListRuleVO());
        if (!Objects.isNull(whiteUserEquityRuleDO)) {
            fillOpAdminInfo(whiteUserEquityRuleDO, ruleReqVO.getOpAdminId(), ruleReqVO.getOpAdminName());
            equityRuleDOS.add(whiteUserEquityRuleDO);
        }
        //会员身份规则转换
        EquityRuleDO vipIdentityEquityRuleDO = convertVipIdentityRuleFromVO(ruleReqVO.getStrategyId(), ruleReqVO.getVipIdentityRuleVO());
        if (!Objects.isNull(vipIdentityEquityRuleDO)) {
            fillOpAdminInfo(vipIdentityEquityRuleDO, ruleReqVO.getOpAdminId(), ruleReqVO.getOpAdminName());
            equityRuleDOS.add(vipIdentityEquityRuleDO);
        }
        return equityRuleDOS;
    }

    private static void fillOpAdminInfo(EquityRuleDO abEquityRuleDO, String opAdminId, String opAdminName) {
        if (!Objects.isNull(abEquityRuleDO)) {
            String addOpAdminId = StringUtils.isBlank(opAdminId) ? Constants.DEFAULT_ADMIN_ID : opAdminId;
            String addOpAdminName = StringUtils.isBlank(opAdminName) ? Constants.DEFAULT_ADMIN_ID : opAdminName;
            abEquityRuleDO.setOpAdminId(addOpAdminId);
            abEquityRuleDO.setOpAdminName(addOpAdminName);
            abEquityRuleDO.setEditAdminId(addOpAdminId);
            abEquityRuleDO.setEditAdminName(addOpAdminName);
            abEquityRuleDO.setIsDelete(Constants.STATUS_NO);
            abEquityRuleDO.setRuleScope(ObjectUtils.defaultIfNull(abEquityRuleDO.getRuleScope(), RuleScopeEnum.CONSULT_AND_RECEIVE.getCode()));
        }
    }

    /**
     * 会员身份规则转换
     *
     * @param strategyId        策略id
     * @param vipIdentityRuleVO 会员身份规则
     * @return 规则
     */
    private EquityRuleDO convertVipIdentityRuleFromVO(Long strategyId, VipIdentityRuleVO vipIdentityRuleVO) {
        if (Objects.isNull(strategyId) || Objects.isNull(vipIdentityRuleVO)
                || Objects.isNull(vipIdentityRuleVO.getUserStatus())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.VIP_IDENTITY_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(vipIdentityRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }


    /**
     * 白名单规则转换
     *
     * @param strategyId          策略id
     * @param whiteUserListRuleVO 白名单规则
     * @return 规则
     */
    private EquityRuleDO convertWhiteUserRuleFromVO(Long strategyId, WhiteUserListRuleVO whiteUserListRuleVO) {
        if (Objects.isNull(strategyId) || Objects.isNull(whiteUserListRuleVO)
                || CollectionUtils.isEmpty(whiteUserListRuleVO.getWhiteUserList())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.WHITE_USER_LIST_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(whiteUserListRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }


    /**
     * 用户标签规则转换
     *
     * @param strategyId    策略ID
     * @param userTagRuleVO 标签规则
     * @return 规则
     */
    private EquityRuleDO convertUserTagRuleFromVO(Long strategyId, UserTagRuleVO userTagRuleVO) {
        if (Objects.isNull(strategyId) || Objects.isNull(userTagRuleVO)
                || CollectionUtils.isEmpty(userTagRuleVO.getRuleIds())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.USER_TAG_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(userTagRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }


    /**
     * app版本规则转换
     *
     * @param strategyId       策略id
     * @param appVersionRuleVO app
     * @return 规则
     */
    private EquityRuleDO convertAppVersionRuleFromVO(Long strategyId, AppVersionRuleVO appVersionRuleVO) {
        if (Objects.isNull(strategyId) || Objects.isNull(appVersionRuleVO)) {
            return null;
        }
        if (CollectionUtils.isEmpty(appVersionRuleVO.getAppVersionList())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.APP_VERSION_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(appVersionRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }


    /**
     * 城市规则转换
     *
     * @param strategyId 策略id
     * @param cityRuleVO 城市规则
     * @return 规则
     */
    private EquityRuleDO convertCityRuleFromVO(Long strategyId, CityRuleVO cityRuleVO) {
        if (Objects.isNull(strategyId) || Objects.isNull(cityRuleVO)
                || CollectionUtils.isEmpty(cityRuleVO.getCityCodeList())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.CITY_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(cityRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }


    /**
     * ab规则转换
     *
     * @param strategyId   策略id
     * @param abTestRuleVO 规则信息
     * @return 规则
     */
    private EquityRuleDO convertAbTestRuleFromVO(Long strategyId, AbTestRuleVO abTestRuleVO) {
        if (Objects.isNull(strategyId) || Objects.isNull(abTestRuleVO)) {
            return null;
        }
        if (StringUtils.isBlank(abTestRuleVO.getLayerId()) || CollectionUtils.isEmpty(abTestRuleVO.getDefaultGroup())
                || CollectionUtils.isEmpty(abTestRuleVO.getChoosePutGroup())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.AB_TEST_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(abTestRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    /**
     * 删除规则校验
     *
     * @param deleteRuleReqVO 删除请求
     */
    public void checkDeleteParam(DeleteRuleReqVO deleteRuleReqVO) {
        Assert.notNull(deleteRuleReqVO, ExceptionEnum.PARAMS_ERROR);
        Assert.notNull(deleteRuleReqVO.getRuleId(), ExceptionEnum.PARAMS_ERROR);
        if (StringUtils.isBlank(deleteRuleReqVO.getOpAdminId())) {
            deleteRuleReqVO.setOpAdminId(Constants.DEFAULT_ADMIN_ID);
        }
        if (StringUtils.isBlank(deleteRuleReqVO.getOpAdminName())) {
            deleteRuleReqVO.setOpAdminName(Constants.DEFAULT_ADMIN_ID);
        }
    }

    public List<EquityRuleDO> convertRuleReqToRuleDOList(Long activityId, Long strategyId, RuleDTO ruleDTO, String adminId, String adminName) {
        if (Objects.isNull(ruleDTO)) {
            return null;
        }
        List<EquityRuleDO> equityRuleDOS = Lists.newArrayList();
        String operateId = StringUtils.isBlank(adminId) ? Constants.DEFAULT_ADMIN_ID : adminId;
        String operateName = StringUtils.isBlank(adminName) ? Constants.DEFAULT_ADMIN_ID : adminName;
        //ab
        ABTestDTO abTestDTO = ruleDTO.getAbTestDTO();
        if (!Objects.isNull(abTestDTO) && StringUtils.isNotBlank(abTestDTO.getLayerId())) {
            ABTestRuleDTO abTestRuleDTO = ABTestRuleConverter.INSTANCE.convertDTOToRuleDTO(abTestDTO);
            EquityRuleDO equityRuleDO = convertAbTestRuleDO(strategyId, abTestRuleDTO);
            if (!Objects.isNull(equityRuleDO)) {
                fillOpAdminInfo(equityRuleDO, operateId, operateName);
                equityRuleDO.setRuleScope(getRuleScopeWithDefault(abTestDTO));
                equityRuleDOS.add(equityRuleDO);
            }
        }

        //城市
        CitiesDTO citiesDTO = ruleDTO.getCitiesDTO();
        if (!Objects.isNull(citiesDTO) && !CollectionUtils.isEmpty(citiesDTO.getCityCodeList())) {
            CityRuleDTO cityRuleDTO = CityRuleConverter.INSTANCE.convertDTOToCityRuleDTO(citiesDTO);
            EquityRuleDO equityRuleDO = convertCityEquityRuleDO(strategyId, cityRuleDTO);
            if (!Objects.isNull(equityRuleDO)) {
                fillOpAdminInfo(equityRuleDO, operateId, operateName);
                equityRuleDO.setRuleScope(getRuleScopeWithDefault(citiesDTO));
                equityRuleDOS.add(equityRuleDO);
            }

        }

        //app版本
        AppVersionDTO appVersionDTO = ruleDTO.getAppVersionDTO();
        if (!Objects.isNull(appVersionDTO) && !Objects.isNull(appVersionDTO.getJoinType())) {
            AppVersionRuleDTO appVersionRuleDTO = AppVersionRuleConverter.INSTANCE.convertToAppVersionRuleDTO(appVersionDTO);
            EquityRuleDO equityRuleDO = convertAppVersionRuleDO(strategyId, appVersionRuleDTO);
            if (!Objects.isNull(equityRuleDO)) {
                fillOpAdminInfo(equityRuleDO, operateId, operateName);
                equityRuleDO.setRuleScope(getRuleScopeWithDefault(appVersionDTO));
                equityRuleDOS.add(equityRuleDO);
            }
        }

        //人群标签
        UserTagDTO userTagDTO = ruleDTO.getUserTagDTO();
        if (!Objects.isNull(userTagDTO) && !CollectionUtils.isEmpty(userTagDTO.getRuleIds())) {
            UserTagRuleDTO userTagRuleDTO = UserTagRuleConverter.INSTANCE.convertToUserTagRuleDTO(userTagDTO);
            EquityRuleDO equityRuleDO = convertUserTagRuleDO(strategyId, userTagRuleDTO);
            if (!Objects.isNull(equityRuleDO)) {
                fillOpAdminInfo(equityRuleDO, operateId, operateName);
                equityRuleDO.setRuleScope(getRuleScopeWithDefault(userTagDTO));
                equityRuleDOS.add(equityRuleDO);
            }
        }

        //会员身份
        VipIdentityDTO vipIdentityDTO = ruleDTO.getVipIdentityDTO();
        if (!Objects.isNull(vipIdentityDTO) && !Objects.isNull(vipIdentityDTO.getUserStatus())) {
            VipIdentityRuleDTO vipIdentityRuleDTO = VipIdentityRuleConverter.INSTANCE.convertToVipIdentityRuleDTO(vipIdentityDTO);
            EquityRuleDO equityRuleDO = convertVipIdentityRuleDO(strategyId, vipIdentityRuleDTO);
            if (!Objects.isNull(equityRuleDO)) {
                fillOpAdminInfo(equityRuleDO, operateId, operateName);
                equityRuleDO.setRuleScope(getRuleScopeWithDefault(vipIdentityDTO));
                equityRuleDOS.add(equityRuleDO);
            }
        }

        //白名单
        WhiteUserListDTO whiteUserListDTO = ruleDTO.getWhiteUserListDTO();
        if (!Objects.isNull(whiteUserListDTO) && !CollectionUtils.isEmpty(whiteUserListDTO.getWhiteUserList())) {
            WhiteUserListRuleDTO whiteUserListRuleVO = WhiteUserListRuleConverter.INSTANCE.convertToWhiteUserListRuleDTO(whiteUserListDTO);
            EquityRuleDO equityRuleDO = convertWhiteUserRuleDO(strategyId, whiteUserListRuleVO);
            if (!Objects.isNull(equityRuleDO)) {
                fillOpAdminInfo(equityRuleDO, operateId, operateName);
                equityRuleDO.setRuleScope(getRuleScopeWithDefault(whiteUserListDTO));
                equityRuleDOS.add(equityRuleDO);
            }
        }

        //频次
        ReceiveLimitDTO receiveLimitDTO = ruleDTO.getReceiveLimitDTO();
        if (Objects.nonNull(receiveLimitDTO) && Objects.nonNull(receiveLimitDTO.getLimitType())) {
            ReceiveLimitRuleDTO receiveLimitRuleDTO = ReceiveLimitRuleConverter.INSTANCE.convertToReceiveLimitRuleDTO(receiveLimitDTO);
            EquityRuleDO equityRuleDO = convertReceiveLimitRuleDO(activityId, strategyId, receiveLimitRuleDTO);
            if (!Objects.isNull(equityRuleDO)) {
                fillOpAdminInfo(equityRuleDO, operateId, operateName);
                equityRuleDO.setRuleScope(getRuleScopeWithDefault(receiveLimitDTO));
                equityRuleDOS.add(equityRuleDO);
            }
        }
        //库存
        StockLimitDTO stockLimitDTO = ruleDTO.getStockLimitDTO();
        if (Objects.nonNull(stockLimitDTO)) {
            if (Objects.nonNull(stockLimitDTO.getTotalStock()) ||
                    Objects.nonNull(stockLimitDTO.getDayStock()) || Objects.nonNull(stockLimitDTO.getWeekStock())
                    || Objects.nonNull(stockLimitDTO.getYearStock()) || Objects.nonNull(stockLimitDTO.getMonthStock())) {
                StockLimitRuleDTO stockLimitRuleDTO = StockLimitRuleConverter.INSTANCE.convertToStockLimitRuleDTO(stockLimitDTO);
                EquityRuleDO equityRuleDO = convertStockLimitRuleDO(activityId, strategyId, stockLimitRuleDTO);
                if (!Objects.isNull(equityRuleDO)) {
                    fillOpAdminInfo(equityRuleDO, operateId, operateName);
                    equityRuleDO.setRuleScope(getRuleScopeWithDefault(stockLimitDTO));
                    equityRuleDOS.add(equityRuleDO);
                }
            }
        }
        // 老玩法系统的新老用户限制
        OldSysMaiCaiNewOrOldUserLimitDTO oldSysMaiCaiNewOrOldUserLimitDTO = ruleDTO.getOldSysMaiCaiNewOrOldUserLimitDTO();
        if (Objects.nonNull(oldSysMaiCaiNewOrOldUserLimitDTO)) {
            if (Objects.nonNull(oldSysMaiCaiNewOrOldUserLimitDTO.getOldSysUserNewOrOldIdentity())) {
                OldSysMaiCaiNewOrOldUserLimitRuleDTO oldSysMaiCaiNewOrOldUserLimitRuleDTO =
                        OldSysMaiCaiNewOrOldUserLimitRuleConverter.INSTANCE
                                .convertToOldSysMaiCaiNewOrOldUserLimitRuleDTO(oldSysMaiCaiNewOrOldUserLimitDTO);
                EquityRuleDO equityRuleDO = convertOldSysMaiCaiNewOrOldUserLimitRuleDO(activityId, strategyId, oldSysMaiCaiNewOrOldUserLimitRuleDTO);
                if (!Objects.isNull(equityRuleDO)) {
                    fillOpAdminInfo(equityRuleDO, operateId, operateName);
                    equityRuleDO.setRuleScope(getRuleScopeWithDefault(oldSysMaiCaiNewOrOldUserLimitDTO));
                    equityRuleDOS.add(equityRuleDO);
                }
            }
        }
        // 老玩法系统的中国银行限制
        OldSysBOCUserDTO oldSysBOCUserDTO = ruleDTO.getOldSysBOCUserDTO();
        if (Objects.nonNull(oldSysBOCUserDTO)) {
            OldSysBOCUserRuleDTO oldSysBOCUserRuleDTO =
                    OldSysBOCUserRuleConverter.INSTANCE
                            .convertToOldSysBOCUserRuleDTO(oldSysBOCUserDTO);
            EquityRuleDO equityRuleDO = convertOldSysBOCUserRuleDO(activityId, strategyId, oldSysBOCUserRuleDTO);
            if (!Objects.isNull(equityRuleDO)) {
                fillOpAdminInfo(equityRuleDO, operateId, operateName);
                equityRuleDO.setRuleScope(getRuleScopeWithDefault(oldSysBOCUserDTO));
                equityRuleDOS.add(equityRuleDO);
            }
        }

        // 老玩法系统的企微新用户限制
        WeComUserDTO weComUserDTO = ruleDTO.getWeComUserDTO();
        if (Objects.nonNull(weComUserDTO)) {
            WeComUserRuleDTO weComUserRuleDTO =
                    WeComUserRuleConverter.INSTANCE
                            .convertToWeComUserRuleDTO(weComUserDTO);
            EquityRuleDO equityRuleDO = convertWeComUserRuleDO(activityId, strategyId, weComUserRuleDTO);
            if (!Objects.isNull(equityRuleDO)) {
                fillOpAdminInfo(equityRuleDO, operateId, operateName);
                equityRuleDO.setRuleScope(getRuleScopeWithDefault(weComUserDTO));
                equityRuleDOS.add(equityRuleDO);
            }
        }

        CommunityUserDTO communityUserDTO = ruleDTO.getCommunityUserDTO();
        if (communityUserDTO != null && communityUserDTO.getCommunityUserStatus() != null) {
            CommunityUserRuleDTO communityUserRuleDTO = CommunityUserRuleConverter.INSTANCE
                    .convertToCommunityUserRuleDTO(communityUserDTO);
            equityRuleDOS.add(buildEquityRuleDO(strategyId, RuleTypeEnum.COMMUNITY_USER.getCode(),
                    JsonUtil.toString(communityUserRuleDTO), communityUserDTO, operateId, operateName));
        }

        return equityRuleDOS;
    }

    private EquityRuleDO convertWeComUserRuleDO(Long activityId, Long strategyId, WeComUserRuleDTO weComUserRuleDTO) {
        if (Objects.isNull(strategyId) || Objects.isNull(activityId)) {
            return null;
        }
        if (Objects.isNull(weComUserRuleDTO)) {
            return null;
        }
        weComUserRuleDTO.setActivityId(activityId);
        weComUserRuleDTO.setStrategyId(strategyId);
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.WE_COM_USER_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(weComUserRuleDTO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private EquityRuleDO convertOldSysBOCUserRuleDO(Long activityId, Long strategyId, OldSysBOCUserRuleDTO oldSysBOCUserRuleDTO) {
        if (Objects.isNull(strategyId) || Objects.isNull(activityId)) {
            return null;
        }
        if (Objects.isNull(oldSysBOCUserRuleDTO)) {
            return null;
        }
        oldSysBOCUserRuleDTO.setActivityId(activityId);
        oldSysBOCUserRuleDTO.setStrategyId(strategyId);
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.OLD_SYS_BANK_BOC_USER_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(oldSysBOCUserRuleDTO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private EquityRuleDO convertOldSysMaiCaiNewOrOldUserLimitRuleDO(
            Long activityId, Long strategyId,
            OldSysMaiCaiNewOrOldUserLimitRuleDTO oldSysMaiCaiNewOrOldUserLimitRuleDTO) {
        if (Objects.isNull(strategyId) || Objects.isNull(activityId)) {
            return null;
        }
        if (Objects.isNull(oldSysMaiCaiNewOrOldUserLimitRuleDTO)) {
            return null;
        }
        oldSysMaiCaiNewOrOldUserLimitRuleDTO.setActivityId(activityId);
        oldSysMaiCaiNewOrOldUserLimitRuleDTO.setStrategyId(strategyId);
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.OLD_SYS_MAI_CAI_NEW_OR_OLD_USER_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(oldSysMaiCaiNewOrOldUserLimitRuleDTO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    /**
     * 库存校验
     *
     * @param activityId        活动id
     * @param strategyId        策略id
     * @param stockLimitRuleDTO 库存信息
     * @return 实体
     */
    private EquityRuleDO convertStockLimitRuleDO(Long activityId, Long strategyId, StockLimitRuleDTO stockLimitRuleDTO) {
        if (Objects.isNull(strategyId) || Objects.isNull(activityId)) {
            return null;
        }
        if (Objects.isNull(stockLimitRuleDTO)) {
            return null;
        }
        stockLimitRuleDTO.setStrategyId(strategyId);
        stockLimitRuleDTO.setActivityId(activityId);
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.STOCK_LIMIT_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(stockLimitRuleDTO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityRuleDO convertStockLimitRuleDO(StockLimitDTO stockLimitDTO) {

        if (Objects.isNull(stockLimitDTO) || IntegerUtil.isFalse(stockLimitDTO.getTotalStock())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.STOCK_LIMIT_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(stockLimitDTO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityRuleDO convertRfTypeRuleDO(RfTypeDTO rfTypeDTO) {
        if (Objects.isNull(rfTypeDTO)) {
            return null;
        }
        if (CollectionUtils.isEmpty(rfTypeDTO.getRfFirstTypeNames()) && CollectionUtils.isEmpty(rfTypeDTO.getRfTypeNames())) {
            return null;
        }
        RfTypeRuleDTO rfTypeRuleDTO = RfTypeRuleDTO.builder()
                .rfFirstTypeNames(rfTypeDTO.getRfFirstTypeNames())
                .rfTypeNames(rfTypeDTO.getRfTypeNames())
                .build();
        EquityRuleDO ruleDO = new EquityRuleDO();
        ruleDO.setRuleType(RuleTypeEnum.RF_TYPE_RULE.getCode());
        ruleDO.setRuleValue(JSON.toJSONString(rfTypeRuleDTO));
        ruleDO.setRuleScope(ObjectUtils.defaultIfNull(rfTypeDTO.getRuleScope(), RuleScopeEnum.CONSULT_AND_RECEIVE.getCode()));
        return ruleDO;
    }

    /**
     * 领取频次转换
     *
     * @param activityId          活动id
     * @param strategyId          策略id
     * @param receiveLimitRuleDTO do
     * @return 结果
     */
    private EquityRuleDO convertReceiveLimitRuleDO(Long activityId, Long strategyId, ReceiveLimitRuleDTO receiveLimitRuleDTO) {
        if (Objects.isNull(strategyId) || Objects.isNull(activityId)) {
            return null;
        }
        if (Objects.isNull(receiveLimitRuleDTO)) {
            return null;
        }
        receiveLimitRuleDTO.setActivityId(activityId);
        receiveLimitRuleDTO.setStrategyId(strategyId);
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(receiveLimitRuleDTO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityRuleDO convertReceiveLimitRuleDO(ReceiveLimitDTO receiveLimitDTO) {

        if (Objects.isNull(receiveLimitDTO) || IntegerUtil.isFalse(receiveLimitDTO.getLimitType())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(receiveLimitDTO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    /**
     * ab规则转换
     *
     * @param strategyId    策略id
     * @param abTestRuleDTO 规则信息
     * @return 规则
     */
    private EquityRuleDO convertAbTestRuleDO(Long strategyId, ABTestRuleDTO abTestRuleDTO) {
        if (Objects.isNull(strategyId) || Objects.isNull(abTestRuleDTO)) {
            return null;
        }
        if (StringUtils.isBlank(abTestRuleDTO.getLayerId()) || CollectionUtils.isEmpty(abTestRuleDTO.getDefaultGroup())
                || CollectionUtils.isEmpty(abTestRuleDTO.getChoosePutGroup())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.AB_TEST_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(abTestRuleDTO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    /**
     * 城市规则转换
     *
     * @param strategyId 策略id
     * @param cityRuleVO 城市规则
     * @return 规则
     */
    private EquityRuleDO convertCityEquityRuleDO(Long strategyId, CityRuleDTO cityRuleVO) {
        if (Objects.isNull(strategyId) || Objects.isNull(cityRuleVO)
                || CollectionUtils.isEmpty(cityRuleVO.getCityCodeList())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.CITY_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(cityRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    /**
     * app版本规则转换
     *
     * @param strategyId       策略id
     * @param appVersionRuleVO app
     * @return 规则
     */
    private EquityRuleDO convertAppVersionRuleDO(Long strategyId, AppVersionRuleDTO appVersionRuleVO) {
        if (Objects.isNull(strategyId) || Objects.isNull(appVersionRuleVO)) {
            return null;
        }
        if (CollectionUtils.isEmpty(appVersionRuleVO.getAppVersionList())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.APP_VERSION_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(appVersionRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    /**
     * 用户标签规则转换
     *
     * @param strategyId    策略ID
     * @param userTagRuleVO 标签规则
     * @return 规则
     */
    private EquityRuleDO convertUserTagRuleDO(Long strategyId, UserTagRuleDTO userTagRuleVO) {
        if (Objects.isNull(strategyId) || Objects.isNull(userTagRuleVO)
                || CollectionUtils.isEmpty(userTagRuleVO.getRuleIds())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.USER_TAG_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(userTagRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    /**
     * 会员身份规则转换
     *
     * @param strategyId        策略id
     * @param vipIdentityRuleVO 会员身份规则
     * @return 规则
     */
    private EquityRuleDO convertVipIdentityRuleDO(Long strategyId, VipIdentityRuleDTO vipIdentityRuleVO) {
        if (Objects.isNull(strategyId) || Objects.isNull(vipIdentityRuleVO)
                || Objects.isNull(vipIdentityRuleVO.getUserStatus())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.VIP_IDENTITY_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(vipIdentityRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    /**
     * 白名单规则转换
     *
     * @param strategyId          策略id
     * @param whiteUserListRuleVO 白名单规则
     * @return 规则
     */
    private EquityRuleDO convertWhiteUserRuleDO(Long strategyId, WhiteUserListRuleDTO whiteUserListRuleVO) {
        if (Objects.isNull(strategyId) || Objects.isNull(whiteUserListRuleVO)
                || CollectionUtils.isEmpty(whiteUserListRuleVO.getWhiteUserList())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(RuleTypeEnum.WHITE_USER_LIST_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(whiteUserListRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    /**
     * 数据转换
     *
     * @param strategyRuleCacheDTOList 缓存集合
     * @return 领取频次
     */
    public List<ReceiveLimitRuleDTO> convertRuleCacheDtoListToReceiveLimitRuleDTOList(List<StrategyRuleCacheDTO> strategyRuleCacheDTOList) {
        if (CollectionUtils.isEmpty(strategyRuleCacheDTOList)) {
            return Lists.newArrayList();
        }
        List<ReceiveLimitRuleDTO> receiveLimitRuleDTOS = Lists.newArrayList();
        for (StrategyRuleCacheDTO strategyRuleCacheDTO : strategyRuleCacheDTOList) {
            if (StringUtils.isBlank(strategyRuleCacheDTO.getRuleValue())) {
                continue;
            }
            try {
                ReceiveLimitRuleDTO receiveLimitRuleDTO = JsonUtil.toJavaObject(strategyRuleCacheDTO.getRuleValue(), ReceiveLimitRuleDTO.class);
                if (Objects.isNull(receiveLimitRuleDTO)) {
                    continue;
                }
                receiveLimitRuleDTOS.add(receiveLimitRuleDTO);
            } catch (Exception e) {
                log.error("EquityRuleEntity.convertRuleCacheDtoListToReceiveLimitRuleDTOList.e;req={}", JsonUtil.toJsonString(strategyRuleCacheDTOList), e);
            }
        }
        return receiveLimitRuleDTOS;
    }

    /**
     * 数据转换
     *
     * @param strategyRuleCacheDTOList 缓存信息
     * @return 库存限制
     */
    public List<StockLimitRuleDTO> convertRuleCacheDtoListToStockLimitRuleDTO(List<StrategyRuleCacheDTO> strategyRuleCacheDTOList) {
        if (CollectionUtils.isEmpty(strategyRuleCacheDTOList)) {
            return Lists.newArrayList();
        }
        List<StockLimitRuleDTO> stockLimitRuleDTOS = Lists.newArrayList();
        for (StrategyRuleCacheDTO strategyRuleCacheDTO : strategyRuleCacheDTOList) {
            if (StringUtils.isBlank(strategyRuleCacheDTO.getRuleValue())) {
                continue;
            }
            try {
                StockLimitRuleDTO stockLimitRuleDTO = JsonUtil.toJavaObject(strategyRuleCacheDTO.getRuleValue(), StockLimitRuleDTO.class);
                if (Objects.isNull(stockLimitRuleDTO)) {
                    continue;
                }
                stockLimitRuleDTOS.add(stockLimitRuleDTO);
            } catch (Exception e) {
                log.error("EquityRuleEntity.convertRuleCacheDtoListToStockLimitRuleDTO.e;req={}", JsonUtil.toJsonString(strategyRuleCacheDTOList), e);
            }
        }
        return stockLimitRuleDTOS;
    }

    public ReceiveLimitRuleDTO convertToReceiveLimitRuleDTO(StrategyRuleCacheDTO strategyRuleCacheDTO) {
        if (Objects.isNull(strategyRuleCacheDTO) || StringUtils.isBlank(strategyRuleCacheDTO.getRuleValue())) {
            return null;
        }
        try {
            return JsonUtil.toJavaObject(strategyRuleCacheDTO.getRuleValue(), ReceiveLimitRuleDTO.class);
        } catch (Exception e) {
            log.error("EquityRuleEntity.convertRuleCacheDtoListToStockLimitRuleDTO.e;req={}", JsonUtil.toJsonString(strategyRuleCacheDTO), e);
        }
        return null;
    }

    public StockLimitRuleDTO convertToStockLimitRuleDTO(StrategyRuleCacheDTO strategyRuleCacheDTO) {
        if (Objects.isNull(strategyRuleCacheDTO) || StringUtils.isBlank(strategyRuleCacheDTO.getRuleValue())) {
            return null;
        }
        try {
            return JsonUtil.toJavaObject(strategyRuleCacheDTO.getRuleValue(), StockLimitRuleDTO.class);
        } catch (Exception e) {
            log.error("EquityRuleEntity.convertToStockLimitRuleDTO.e;req={}", JsonUtil.toJsonString(strategyRuleCacheDTO), e);
        }
        return null;
    }

    public EquityRuleDO convertToEquityRuleDO(Long strategyId, StrategyRuleCacheDTO strategyRuleCacheDTO) {
        if (Objects.isNull(strategyRuleCacheDTO) || StringUtils.isBlank(strategyRuleCacheDTO.getRuleValue())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setRuleType(strategyRuleCacheDTO.getRuleType());
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleValue(strategyRuleCacheDTO.getRuleValue());
        return equityRuleDO;
    }

    public ReceiveLimitRuleDTO convertDoToDTO(EquityRuleDO equityRuleDO) {
        if (Objects.isNull(equityRuleDO) || StringUtils.isBlank(equityRuleDO.getRuleValue())) {
            return null;
        }
        try {
            return JsonUtil.toJavaObject(equityRuleDO.getRuleValue(), ReceiveLimitRuleDTO.class);
        } catch (Exception e) {
            log.error("EquityRuleEntity.convertDoToDTO.e;req={}", JsonUtil.toJsonString(equityRuleDO), e);
        }
        return null;

    }

    public StockLimitRuleDTO convertDoToStockLimitRuleDTO(EquityRuleDO equityRuleDO) {
        if (Objects.isNull(equityRuleDO) || StringUtils.isBlank(equityRuleDO.getRuleValue())) {
            return null;
        }
        try {
            return JsonUtil.toJavaObject(equityRuleDO.getRuleValue(), StockLimitRuleDTO.class);
        } catch (Exception e) {
            log.error("EquityRuleEntity.convertDoToStockLimitRuleDTO.e;req={}", JsonUtil.toJsonString(equityRuleDO), e);
        }
        return null;
    }

    public OldSysBOCUserRuleDTO convertRuleCacheDtoToOldSysBOCUserRuleDTO(StrategyRuleCacheDTO strategyRuleCacheDTO) {
        if (Objects.isNull(strategyRuleCacheDTO) || StringUtils.isBlank(strategyRuleCacheDTO.getRuleValue())) {
            return null;
        }
        try {
            return JsonUtil.toJavaObject(strategyRuleCacheDTO.getRuleValue(), OldSysBOCUserRuleDTO.class);
        } catch (Exception e) {
            log.error("EquityRuleEntity.convertDoToStockLimitRuleDTO.e;req={}", JSON.toJSONString(strategyRuleCacheDTO), e);
        }
        return null;
    }

    /****
     * 创建活动对于的规则
     * @param prizeDTO PrizeVO
     * @param strategyId Long
     * @param countControlConfig CountControlConfigVO
     * @param benefitDTOList List
     * @param equityAdminClient EquityAdminClient
     * @param ticketAdminClient TicketAdminClient
     * @param serviceStationClient ServiceStationClient
     * @return RuleDTO
     */
    public RuleDTO createRuleDTO(PrizeVO prizeDTO, Long strategyId, CountControlConfigVO countControlConfig,
                                 List<ProvideBenefitDTO> benefitDTOList, EquityAdminClient equityAdminClient,
                                 TicketAdminClient ticketAdminClient, ServiceStationClient serviceStationClient,
                                 Integer crowdRuleScope) {
        RuleDTO ruleDTO = new RuleDTO();
        ruleDTO.setStrategyId(strategyId);
        //领券频次规则
        ReceiveLimitDTO receiveLimitDTO = createReceiveLimitDTO(countControlConfig);
        ruleDTO.setReceiveLimitDTO(receiveLimitDTO);
        Long equityId = Long.parseLong(prizeDTO.getReferParam());
        //获取券平台的券模版id
        String ticketId = getTicketTemplateId(equityId, equityAdminClient);
        //根据券模版id获取券对应的城市信息
        List<String> cityList = createTicketCityList(ticketId, benefitDTOList, serviceStationClient, ticketAdminClient);
        CitiesDTO citiesDTO = new CitiesDTO();
        citiesDTO.setCityCodeList(cityList);
        ruleDTO.setCitiesDTO(citiesDTO);
        StockLimitDTO stockLimitDTO = new StockLimitDTO();
        Integer dayStock = Integer.parseInt(prizeDTO.getExtendInfo().get(ActivityConfigConstants.DAY_LIMIT));
        stockLimitDTO.setDayStock(dayStock);
        stockLimitDTO.setPlanDateType(PlanDateTypeEnum.DAY.getType());
        stockLimitDTO.setTotalStock(prizeDTO.getNum());
        ruleDTO.setStockLimitDTO(stockLimitDTO);
        //设置人群规则
        setTag(prizeDTO, ruleDTO, crowdRuleScope);
        return ruleDTO;
    }

    /****
     * 创建活动对应的规则
     * @param prizeDTO RuleDTO
     * @param strategyId Long
     * @param countControlConfig CountControlConfigVO
     * @return RuleDTO
     */
    public RuleDTO createRuleDTOWithLimitCityList(PrizeVO prizeDTO, Long strategyId, CountControlConfigVO countControlConfig,
                                                  List<String> limitCityList, Integer crowdRuleScope) {
        RuleDTO ruleDTO = new RuleDTO();
        ruleDTO.setStrategyId(strategyId);
        //领券频次规则
        ReceiveLimitDTO receiveLimitDTO = createReceiveLimitDTO(countControlConfig);
        ruleDTO.setReceiveLimitDTO(receiveLimitDTO);
        //根据券模版id获取券对应的城市信息
        if (CollectionUtils.isNotEmpty(limitCityList)) {
            CitiesDTO citiesDTO = new CitiesDTO();
            citiesDTO.setCityCodeList(limitCityList);
            ruleDTO.setCitiesDTO(citiesDTO);
        }
        StockLimitDTO stockLimitDTO = new StockLimitDTO();
        Integer dayStock = Integer.parseInt(prizeDTO.getExtendInfo().get(ActivityConfigConstants.DAY_LIMIT));
        stockLimitDTO.setDayStock(dayStock);
        stockLimitDTO.setPlanDateType(PlanDateTypeEnum.DAY.getType());
        stockLimitDTO.setTotalStock(prizeDTO.getNum());
        ruleDTO.setStockLimitDTO(stockLimitDTO);
        //设置人群规则
        setTag(prizeDTO, ruleDTO, crowdRuleScope);
        return ruleDTO;
    }


    private void setTag(PrizeVO prizeDTO, RuleDTO ruleDTO, Integer crowdRuleScope) {
        if (UserTagEnum.MC_NEW.getName().equals(prizeDTO.getTags())) {
            OldSysMaiCaiNewOrOldUserLimitDTO oldSysMaiCaiNewOrOldUserLimitDTO = new OldSysMaiCaiNewOrOldUserLimitDTO();
            oldSysMaiCaiNewOrOldUserLimitDTO.setRuleScope(crowdRuleScope);
            oldSysMaiCaiNewOrOldUserLimitDTO.setOldSysUserNewOrOldIdentity(1);
            ruleDTO.setOldSysMaiCaiNewOrOldUserLimitDTO(oldSysMaiCaiNewOrOldUserLimitDTO);
        } else if (UserTagEnum.MC_OLD.getName().equals(prizeDTO.getTags())) {
            OldSysMaiCaiNewOrOldUserLimitDTO oldSysMaiCaiNewOrOldUserLimitDTO = new OldSysMaiCaiNewOrOldUserLimitDTO();
            oldSysMaiCaiNewOrOldUserLimitDTO.setRuleScope(crowdRuleScope);
            oldSysMaiCaiNewOrOldUserLimitDTO.setOldSysUserNewOrOldIdentity(2);
            ruleDTO.setOldSysMaiCaiNewOrOldUserLimitDTO(oldSysMaiCaiNewOrOldUserLimitDTO);
        } else if (UserTagEnum.VIP.getName().equals(prizeDTO.getTags())) {
            VipIdentityDTO vipIdentityDTO = new VipIdentityDTO();
            vipIdentityDTO.setRuleScope(crowdRuleScope);
            vipIdentityDTO.setUserStatus(1);
            ruleDTO.setVipIdentityDTO(vipIdentityDTO);
        } else if (UserTagEnum.TAG.getName().equals(prizeDTO.getTags())) {
            List<String> tagList = Lists.newArrayList(prizeDTO.getExtendInfo().get("tagId"));
            UserTagDTO userTagDTO = new UserTagDTO();
            userTagDTO.setRuleScope(crowdRuleScope);
            userTagDTO.setRuleIds(tagList);
            ruleDTO.setUserTagDTO(userTagDTO);
        } else if (UserTagEnum.NON_VIP.getName().equals(prizeDTO.getTags())) {
            VipIdentityDTO vipIdentityDTO = new VipIdentityDTO();
            vipIdentityDTO.setRuleScope(crowdRuleScope);
            vipIdentityDTO.setUserStatus(VipIdentityEnum.NO_VIP.getCode());
            ruleDTO.setVipIdentityDTO(vipIdentityDTO);
        } else if (UserTagEnum.BOC.getName().equals(prizeDTO.getTags())) {
            OldSysBOCUserDTO oldSysBOCUserDTO = new OldSysBOCUserDTO();
            oldSysBOCUserDTO.setRuleScope(crowdRuleScope);
            oldSysBOCUserDTO.setBankCode(prizeDTO.getExtendInfo().get("bankNumber"));
            ruleDTO.setOldSysBOCUserDTO(oldSysBOCUserDTO);
        } else if (UserTagEnum.WE_COM_NEW.getName().equals(prizeDTO.getTags())) {
            WeComUserDTO weComUserDTO = new WeComUserDTO();
            weComUserDTO.setRuleScope(crowdRuleScope);
            weComUserDTO.setWeComUserStatus(1);
            ruleDTO.setWeComUserDTO(weComUserDTO);
        } else if (UserTagEnum.COMMUNITY.getName().equals(prizeDTO.getTags())) {
            CommunityUserDTO communityUserDTO = CommunityUserDTO.builder()
                    .ruleScope(crowdRuleScope)
                    .communityUserStatus(1)
                    .build();
            ruleDTO.setCommunityUserDTO(communityUserDTO);
        }
    }

    private String getTicketTemplateId(Long equityId, EquityAdminClient equityAdminClient) {
        //需要提取一个proxy
        ResponseBaseVo<EquityDTO> responseBaseVo = equityAdminClient.detail(equityId);
        if (!responseBaseVo.isSuccess() || Objects.isNull(responseBaseVo.getData()) ||
                Objects.isNull(responseBaseVo.getData().getPropsInfo()) || Objects.isNull(responseBaseVo.getData().getPropsInfo().getCouponInfo())
                || Strings.isBlank(responseBaseVo.getData().getPropsInfo().getCouponInfo().getTicketId())) {
            //TODO 异常
//            log.error("老权益id{}查询老权益获取券模版id:,activityId:{}",activityDTO.getActivityId());
            throw AdminExceptionBuilder.build(ExceptionEnum.ACTIVITY_DATA_ERROR);
        }
        return responseBaseVo.getData().getPropsInfo().getCouponInfo().getTicketId();
    }

    /****
     * 生成 ReceiveLimitDTO
     * @param countControlConfigDTO CountControlConfigVO
     * @return ReceiveLimitDTO
     */
    private ReceiveLimitDTO createReceiveLimitDTO(CountControlConfigVO countControlConfigDTO) {
        ReceiveLimitDTO receiveLimitDTO = new ReceiveLimitDTO();
        Integer lifetime = countControlConfigDTO.getLifetime();
        //设置活动参与次数
        if (IntegerUtil.isTrue(lifetime)) {
            receiveLimitDTO.setLimitType(StrategyBenefitReceiveLimitTypeEnum.FREQUENCY.getType());
            receiveLimitDTO.setFrequencyDateType(FrequencyDateTypeEnum.LIFE.getType());
            receiveLimitDTO.setFrequencyCountLimit(1);
            receiveLimitDTO.setFrequencyDateTypeValue(lifetime);
        } else if (IntegerUtil.isTrue(countControlConfigDTO.getDay())) {
            receiveLimitDTO.setLimitType(StrategyBenefitReceiveLimitTypeEnum.FREQUENCY.getType());
            receiveLimitDTO.setFrequencyDateType(FrequencyDateTypeEnum.DAY.getType());
            receiveLimitDTO.setFrequencyCountLimit(1);
            receiveLimitDTO.setFrequencyDateTypeValue(countControlConfigDTO.getDay());
        } else if (IntegerUtil.isTrue(countControlConfigDTO.getMonth())) {
            receiveLimitDTO.setLimitType(StrategyBenefitReceiveLimitTypeEnum.FREQUENCY.getType());
            receiveLimitDTO.setFrequencyDateType(FrequencyDateTypeEnum.MONTH.getType());
            receiveLimitDTO.setFrequencyCountLimit(1);
            receiveLimitDTO.setFrequencyDateTypeValue(countControlConfigDTO.getMonth());
        } else if (IntegerUtil.isTrue(countControlConfigDTO.getYear())) {
            receiveLimitDTO.setLimitType(StrategyBenefitReceiveLimitTypeEnum.FREQUENCY.getType());
            receiveLimitDTO.setFrequencyDateType(FrequencyDateTypeEnum.YEAR.getType());
            receiveLimitDTO.setFrequencyCountLimit(1);
            receiveLimitDTO.setFrequencyDateTypeValue(countControlConfigDTO.getYear());
        } else if (IntegerUtil.isTrue(countControlConfigDTO.getWeek())) {
            receiveLimitDTO.setLimitType(StrategyBenefitReceiveLimitTypeEnum.FREQUENCY.getType());
            receiveLimitDTO.setFrequencyDateType(FrequencyDateTypeEnum.WEEK.getType());
            receiveLimitDTO.setFrequencyCountLimit(1);
            receiveLimitDTO.setFrequencyDateTypeValue(countControlConfigDTO.getWeek());
        }
        return receiveLimitDTO;
    }

    private List<String> createTicketCityList(String ticketId, List<ProvideBenefitDTO> benefitDTOList, ServiceStationClient serviceStationClient, TicketAdminClient ticketAdminClient) {
        List<String> cityList = new ArrayList<>();
        GetTicketReqDTO getTicketReqDTO = new GetTicketReqDTO();
        getTicketReqDTO.setMongoId(ticketId);
        //获取券的基本信息
        ResponseBaseVo<TicketRespDTO> respDTOResponseBaseVo = ticketAdminClient.getTicketById(getTicketReqDTO);
        if (!respDTOResponseBaseVo.isSuccess() || Objects.isNull(respDTOResponseBaseVo.getData())) {
            log.error("根据券模版id:{}获取券模版详细信息错误!,请求参数:{},响应结果:{}", ticketId, JSON.toJSON(getTicketReqDTO), JSON.toJSON(respDTOResponseBaseVo));
            throw new RuntimeException("根据券模版id:" + ticketId + "获取券的详细信息异常!");
        }
        //券模版id设置到权益id里面
        Map<String, AddressVo> addressVoMap = respDTOResponseBaseVo.getData().getLimitAddressMultiWithName();
        //把老权益id转换成券模版id
        benefitDTOList.forEach(x -> {
            x.setBenefitName(respDTOResponseBaseVo.getData().getName());
            x.setBenefitValue(ticketId);
        });
        if (addressVoMap.size() > 0) {
            for (AddressVo addressVo : addressVoMap.values()) {
                //券模版如果没有配置测试，可能配置的是省份
                if (Objects.nonNull(addressVo.getCity()) && addressVo.getCity().size() > 0) {
                    cityList.addAll(addressVo.getCity().keySet());
                } else {
                    //如果省份也不存在，则没有配置城市信息
                    if (Objects.nonNull(addressVo.getProvince()) && addressVo.getProvince().size() > 0) {
                        for (String provinceCode : addressVo.getProvince().keySet()) {
                            //调用站点服务获取测试
                            ResponseBaseVo<List<StationCityV2>> listResponseBaseVo = serviceStationClient.getCityByProvinceCode(provinceCode);
                            if (listResponseBaseVo.isSuccess() != false || CollectionUtils.isEmpty(listResponseBaseVo.getData())) {
                                //TODO throw Exception

                            }
                            List<String> cityIdList = listResponseBaseVo.getData().stream().map(x -> x.getCityId()).collect(Collectors.toList());
                            cityList.addAll(cityIdList);
                        }
                    }
                }
            }
        }
        return cityList;
    }

    private Integer getRuleScopeWithDefault(RuleCommonDTO ruleCommonDTO) {
        return ObjectUtils.defaultIfNull(ruleCommonDTO.getRuleScope(), RuleScopeEnum.CONSULT_AND_RECEIVE.getCode());
    }

    private EquityRuleDO buildEquityRuleDO(@NotNull(message = "策略 ID 不能为空") Long strategyId,
                                           @NotNull(message = "规则类型不能为空") Integer ruleType,
                                           @NotNull(message = "规则值不能为空") String ruleValue,
                                           @NotNull(message = "规则基础信息不能为空") RuleCommonDTO ruleCommonDTO,
                                           String operateId, String operateName) {
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setStrategyId(strategyId);
        equityRuleDO.setRuleType(ruleType);
        equityRuleDO.setRuleValue(ruleValue);
        equityRuleDO.setRuleScope(getRuleScopeWithDefault(ruleCommonDTO));
        equityRuleDO.setIsDelete(CommonEnum.INTEGER_BOOL.NO.getCode());
        equityRuleDO.setOpAdminId(StringUtils.defaultIfBlank(operateId, "unknown"));
        equityRuleDO.setOpAdminName(StringUtils.defaultIfBlank(operateName, "unknown"));
        equityRuleDO.setEditAdminId(StringUtils.defaultIfBlank(operateId, "unknown"));
        equityRuleDO.setEditAdminName(StringUtils.defaultIfBlank(operateName, "unknown"));
        return equityRuleDO;
    }

    public static List<EquityRuleDO> buildStrategyRuleDoListFromDTO(UniversalRuleDTO ruleDTO, String opAdminId, String opAdminName) {
        List<EquityRuleDO> equityRuleDOS = Lists.newArrayList();
        if (Objects.isNull(ruleDTO)) {
            return equityRuleDOS;
        }

        //ab转换
        EquityRuleDO abEquityRuleDO = convertAbTestRuleFromDTO(ruleDTO.getAbTestDTO());
        if (!Objects.isNull(abEquityRuleDO)) {
            abEquityRuleDO.setRuleScope(ruleDTO.getAbTestDTO().getRuleScope());
            fillOpAdminInfo(abEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(abEquityRuleDO);
        }
        //城市规则转换
        EquityRuleDO cityEquityRuleDO = convertCityRuleFromDTO(ruleDTO.getCitiesDTO());
        if (!Objects.isNull(cityEquityRuleDO)) {
            cityEquityRuleDO.setRuleScope(ruleDTO.getCitiesDTO().getRuleScope());
            fillOpAdminInfo(cityEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(cityEquityRuleDO);
        }

        //app版本规则转换
        EquityRuleDO appVersionEquityRuleDO = convertAppVersionRuleFromDTO(ruleDTO.getAppVersionDTO());
        if (!Objects.isNull(appVersionEquityRuleDO)) {
            appVersionEquityRuleDO.setRuleScope(ruleDTO.getAppVersionDTO().getRuleScope());
            fillOpAdminInfo(appVersionEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(appVersionEquityRuleDO);
        }

        //人群规则转换
        EquityRuleDO userTagEquityRuleDO = convertUserTagRuleFromDTO(ruleDTO.getUserTagDTO());
        if (!Objects.isNull(userTagEquityRuleDO)) {
            userTagEquityRuleDO.setRuleScope(ruleDTO.getUserTagDTO().getRuleScope());
            fillOpAdminInfo(userTagEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(userTagEquityRuleDO);
        }
        //用户白名单规则转换
        EquityRuleDO whiteUserEquityRuleDO = convertWhiteUserRuleFromDTO(ruleDTO.getWhiteUserListDTO());
        if (!Objects.isNull(whiteUserEquityRuleDO)) {
            whiteUserEquityRuleDO.setRuleScope(ruleDTO.getWhiteUserListDTO().getRuleScope());
            fillOpAdminInfo(whiteUserEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(whiteUserEquityRuleDO);
        }
        //会员身份规则转换
        EquityRuleDO vipIdentityEquityRuleDO = convertVipIdentityRuleFromDTO(ruleDTO.getVipIdentityDTO());
        if (!Objects.isNull(vipIdentityEquityRuleDO)) {
            vipIdentityEquityRuleDO.setRuleScope(ruleDTO.getVipIdentityDTO().getRuleScope());
            fillOpAdminInfo(vipIdentityEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(vipIdentityEquityRuleDO);
        }

        //频次
        EquityRuleDO receiveLimitEquityRuleDO = convertReceiveLimitRuleDO(ruleDTO.getReceiveLimitDTO());
        if (!Objects.isNull(receiveLimitEquityRuleDO)) {
            receiveLimitEquityRuleDO.setRuleScope(ruleDTO.getReceiveLimitDTO().getRuleScope());
            fillOpAdminInfo(receiveLimitEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(receiveLimitEquityRuleDO);
        }
        //库存
        EquityRuleDO stockLimitEquityRuleDO = convertStockLimitRuleDO(ruleDTO.getStockLimitDTO());
        if (!Objects.isNull(stockLimitEquityRuleDO)) {
            stockLimitEquityRuleDO.setRuleScope(ruleDTO.getStockLimitDTO().getRuleScope());
            fillOpAdminInfo(stockLimitEquityRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(stockLimitEquityRuleDO);
        }
        // RF 人群身份
        EquityRuleDO rfTypeRuleDO = convertRfTypeRuleDO(ruleDTO.getRfTypeDTO());
        if (Objects.nonNull(rfTypeRuleDO)) {
            fillOpAdminInfo(rfTypeRuleDO, opAdminId, opAdminName);
            equityRuleDOS.add(rfTypeRuleDO);
        }
        return equityRuleDOS;
    }

    private static EquityRuleDO convertAbTestRuleFromDTO(ABTestDTO abTestRuleVO) {
        if (Objects.isNull(abTestRuleVO)) {
            return null;
        }
        if (StringUtils.isBlank(abTestRuleVO.getLayerId()) || CollectionUtils.isEmpty(abTestRuleVO.getDefaultGroup())
                || CollectionUtils.isEmpty(abTestRuleVO.getChoosePutGroup())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.AB_TEST_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(abTestRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityRuleDO convertCityRuleFromDTO(CitiesDTO cityRuleVO) {
        if (Objects.isNull(cityRuleVO)
                || CollectionUtils.isEmpty(cityRuleVO.getCityCodeList())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.CITY_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(cityRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityRuleDO convertAppVersionRuleFromDTO(AppVersionDTO appVersionRuleVO) {
        if (Objects.isNull(appVersionRuleVO)) {
            return null;
        }
        if (CollectionUtils.isEmpty(appVersionRuleVO.getAppVersionList())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.APP_VERSION_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(appVersionRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityRuleDO convertUserTagRuleFromDTO(UserTagDTO userTagRuleVO) {
        if (Objects.isNull(userTagRuleVO)
                || CollectionUtils.isEmpty(userTagRuleVO.getRuleIds())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.USER_TAG_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(userTagRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityRuleDO convertWhiteUserRuleFromDTO(WhiteUserListDTO whiteUserListRuleVO) {
        if (Objects.isNull(whiteUserListRuleVO)
                || CollectionUtils.isEmpty(whiteUserListRuleVO.getWhiteUserList())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.WHITE_USER_LIST_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(whiteUserListRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }

    private static EquityRuleDO convertVipIdentityRuleFromDTO(VipIdentityDTO vipIdentityRuleVO) {
        if (Objects.isNull(vipIdentityRuleVO)
                || Objects.isNull(vipIdentityRuleVO.getUserStatus())) {
            return null;
        }
        EquityRuleDO equityRuleDO = new EquityRuleDO();
        equityRuleDO.setRuleType(RuleTypeEnum.VIP_IDENTITY_RULE.getCode());
        String ruleValue = JsonUtil.toJsonString(vipIdentityRuleVO);
        equityRuleDO.setRuleValue(ruleValue);
        return equityRuleDO;
    }
}


