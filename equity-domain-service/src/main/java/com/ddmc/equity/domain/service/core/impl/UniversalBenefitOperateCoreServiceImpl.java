package com.ddmc.equity.domain.service.core.impl;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.account.AccountStrategyContext;
import com.ddmc.equity.account.DirectAccountContext;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.interceptor.annotation.MonitorSpan;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.ThreadsUtils;
import com.ddmc.equity.common.util.business.BenefitUtil;
import com.ddmc.equity.common.util.risk.RiskCheckUtils;
import com.ddmc.equity.domain.dto.DrawAndReceiveReqDTO;
import com.ddmc.equity.domain.dto.DrawAndReceiveRespDTO;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.domain.dto.InitActivityCountReqDTO;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.dto.SceneReceiveReqDTO;
import com.ddmc.equity.domain.dto.SceneReceiveRespDTO;
import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.entity.benefit.BenefitConvertEntity;
import com.ddmc.equity.domain.entity.common.BenefitOperateConvertEntity;
import com.ddmc.equity.domain.service.core.BenefitCoreService;
import com.ddmc.equity.domain.service.core.EquityAccountCoreService;
import com.ddmc.equity.domain.service.core.SceneActivityBenefitCoreService;
import com.ddmc.equity.domain.service.core.UniversalBenefitOperateCoreService;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.equity.processor.risk.RiskControlContextDTO;
import com.ddmc.equity.processor.risk.RiskControlFactory;
import com.ddmc.equity.dto.customer.RiskControlInterceptRuleDTO;
import com.ddmc.equity.processor.risk.RiskControlResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/29 16:21
 * @description
 */
@Slf4j
@Service
public class UniversalBenefitOperateCoreServiceImpl implements UniversalBenefitOperateCoreService {

    /**
     * 指定的场景调用发放优惠券需要关联外部 id
     */
    @Value("${equity.send.ticket.need.external.scenes:}")
    private Set<String> sendTicketNeedExternalScenes;

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private LocalCacheManager localCacheManager;
    @Autowired
    private SceneActivityBenefitCoreService sceneActivityBenefitCoreService;
    @Autowired
    private EquityAccountCoreService equityAccountCoreService;
    @Autowired
    private BenefitCoreService benefitCoreService;
    @Autowired
    private RiskControlFactory riskControlFactory;

    @Override
    public Boolean initActivityCount(InitActivityCountReqDTO req) {
        // 如果已经初始化过，则无须重复初始化
        String userId = req.getBaseRequestDTO().getUserId();
        Long useActivityId = req.getUseActivityId();
        UniversalBenefitOperateCoreServiceImpl self = (UniversalBenefitOperateCoreServiceImpl) AopContext.currentProxy();
        if (self.hadInitActivityCountByRedis(userId, useActivityId)) {
            return true;
        }

        // 发放初始化活动次数
        if (!self.provideInitActivityCount(req)) {
            return false;
        }

        // 设置已经初始化过标识到 redis
        self.setHadInitActivityCountToRedis(userId, useActivityId);
        return true;
    }

    @Override
    public DrawAndReceiveRespDTO drawAndReceive(DrawAndReceiveReqDTO req) {
        // 1 抽奖
        UniversalBenefitOperateCoreServiceImpl self = (UniversalBenefitOperateCoreServiceImpl) AopContext.currentProxy();
        DrawAndReceiveRespDTO drawResp = self.draw(req);

        // 1.1 如果抽奖返回领取的权益信息为空，则直接返回结果
        FullBenefitInfoDTO receiveBenefit = drawResp.getReceiveBenefit();
        if (Objects.isNull(receiveBenefit)) {
            log.info("drawAndReceive draw receiveBenefit is null activityId={}, userId={}, drawResp={}",
                    req.getActivityId(), req.getBaseRequestDTO().getUserId(), JSON.toJSONString(drawResp));
            String unableReceiveReasonCode = drawResp.getUnableReceiveReasonCode();
            CsossUtils.logEvent(MonitorConstants.DRAW_AND_RECEIVE_CONSULT_NULL,
                    StringUtils.defaultIfBlank(unableReceiveReasonCode, "null"));
            return drawResp;
        }

        // 2 领取
        return self.receive(req, receiveBenefit);
    }

    @MonitorSpan(name = "drawAndReceive.draw")
    @Override
    public DrawAndReceiveRespDTO draw(DrawAndReceiveReqDTO req) {
        // 1 咨询返回本次领取的权益信息
        String sceneCode = req.getSceneCode();
        Long activityId = req.getActivityId();
        String userId = req.getBaseRequestDTO().getUserId();
        EngineContextDTO drawEngineContextDTO = BenefitOperateConvertEntity.convertToDrawEngineContextDTO(req);
        List<SceneActivityCacheDto> activities = sceneActivityBenefitCoreService.consultSceneBenefit(sceneCode,
                drawEngineContextDTO);
        List<FullBenefitInfoDTO> consultBenefits = BenefitConvertEntity.convertToFullBenefitInfoDTOList(activities);
        if (CollectionUtils.isEmpty(consultBenefits)) {
            log.warn("drawAndReceive draw consultBenefits is null userId={}, activityId={}", userId, activityId);
            CsossUtils.logEvent(MonitorConstants.DRAW_AND_RECEIVE_CONSULT_NULL, "consult_failure");
            throw AdminExceptionBuilder.build(ExceptionEnum.DRAW_AND_RECEIVE_BENEFIT_IS_NULL, "活动咨询奖品信息为空");
        }

        // 2 调用风控，不通过需要替换掉本次领取的权益信息
        FullBenefitInfoDTO consultBenefit = consultBenefits.get(0);
        UniversalBenefitOperateCoreServiceImpl self = (UniversalBenefitOperateCoreServiceImpl) AopContext.currentProxy();
        RiskControlResponseDTO riskControlResponseDTO = self.riskControlBefore(req, consultBenefit);
        String riskControlResult = riskControlResponseDTO == null ? null : riskControlResponseDTO.getRiskControlResult();
        List<RiskControlInterceptRuleDTO> riskControlInterceptRuleDTOList = riskControlResponseDTO == null ?
                null : riskControlResponseDTO.getInterceptRuleDTOList();
        // 风控校验通过，抽中的奖品不变
        if (RiskCheckUtils.riskPass(riskControlResult)) {
            return DrawAndReceiveRespDTO.builder()
                    .receiveBenefit(consultBenefit)
                    .build();
        }
        // 风控不通过。不发奖励
        if (RiskCheckUtils.riskNoPrize(riskControlResult)) {
            return DrawAndReceiveRespDTO.builder()
                    .receiveResultStatus(StatusEnum.FAIL.getCode())
                    .unableReceiveReasonCode(BenefitUnableReceiveReasonType.HIT_RISK.getCode())
                    .riskControlInterceptRuleDTOList(riskControlInterceptRuleDTOList)
                    .build();
        }
        // 风控不通过。命中 [黑灰产] 用户，替换成黑灰产策略奖奖励；命中 [普通风控] 用户，替换成兜底策略奖励（如果本来抽中的是兜底策略奖励，则可以不替换）
        return DrawAndReceiveRespDTO.builder()
                .receiveBenefit(getRiskReplaceBenefit(sceneCode, activityId, riskControlResult))
                .build();
    }

    @Override
    public SceneReceiveRespDTO sceneReceive(SceneReceiveReqDTO req) {
        // 1 咨询
        UniversalBenefitOperateCoreServiceImpl self = (UniversalBenefitOperateCoreServiceImpl) AopContext.currentProxy();
        SceneReceiveRespDTO consultResp = self.consult(req);
        // 1.1 如果咨询返回领取的权益信息为空，则直接返回结果
        FullBenefitInfoDTO receiveBenefit = consultResp.getReceiveBenefit();
        if (Objects.isNull(receiveBenefit)) {
            log.info("sceneReceive consult receiveBenefit isNull req={}, consultResp={}", JSON.toJSONString(req), JSON.toJSONString(consultResp));
            String unableReceiveReasonCode = consultResp.getUnableReceiveReasonCode();
            CsossUtils.logEvent(MonitorConstants.SCENE_RECEIVE_CONSULT_NULL, StringUtils.defaultIfBlank(unableReceiveReasonCode, "null"));
            return consultResp;
        }

        // 2 领取
        return self.receive(req, receiveBenefit);
    }

    @MonitorSpan(name = "drawAndReceive.receive")
    public DrawAndReceiveRespDTO receive(DrawAndReceiveReqDTO req, FullBenefitInfoDTO receiveBenefit) {
        // 设置具体发放的 sendAmount + balanceMoney
        BenefitOperateConvertEntity.fillReceiveBenefitSendAmount(receiveBenefit, req.getSendAmount());
        Integer benefitType = receiveBenefit.getBenefitType();
        AccountStrategyContext accountStrategyContext = AccountStrategyContext.builderByBenefitType(benefitType);
        EquityAccountContext equityAccountContext = BenefitOperateConvertEntity.convertToReceiveEquityAccountContext(req,
                receiveBenefit, sendTicketNeedExternalScenes);
        ReceiveBenefitResDTO sceneReceiveRes = accountStrategyContext.sceneReceiveNew(equityAccountContext);

        // 如果领取时幂等，直接返回原来领取的结果，对应权益有可能发生变更所以需要替换掉 receiveBenefit
        FullBenefitInfoDTO realReceiveBenefit = getRealReceiveBenefit(receiveBenefit, equityAccountContext);
        DrawAndReceiveRespDTO drawAndReceiveRespDTO = BenefitOperateConvertEntity.convertToDrawAndReceiveRespDTO(realReceiveBenefit,
                sceneReceiveRes);

        Integer receiveResultStatus = drawAndReceiveRespDTO.getReceiveResultStatus();
        // 领取中
        if (StatusEnum.unfinishedStatus(receiveResultStatus)) {
            log.warn("drawAndReceive receive sceneReceiveNew unfinished equityAccountContext={}, sceneReceiveRes={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(sceneReceiveRes));
            CsossUtils.logEvent(MonitorConstants.DRAW_AND_RECEIVE_RESULT, "unfinished");
            return drawAndReceiveRespDTO;
        }
        // 领取成功
        if (Objects.equals(receiveResultStatus, StatusEnum.SUCCESS.getCode())) {
            // 领取成功后上报风控
            riskControlAfter(req, realReceiveBenefit);
            // 领取成功打点
            CsossUtils.logEvent(MonitorConstants.DRAW_AND_RECEIVE_RESULT,
                    BenefitOperateConvertEntity.convertToBenefitEventName(realReceiveBenefit));
            return drawAndReceiveRespDTO;
        }
        // 领取失败
        log.warn("drawAndReceive receive sceneReceiveNew failure equityAccountContext={}, sceneReceiveRes={}",
                JSON.toJSONString(equityAccountContext), JSON.toJSONString(sceneReceiveRes));
        CsossUtils.logEvent(MonitorConstants.DRAW_AND_RECEIVE_RESULT, "failure");
        return drawAndReceiveRespDTO;
    }

    @MonitorSpan(name = "provideInitActivityCount")
    public boolean provideInitActivityCount(InitActivityCountReqDTO req) {
        String userId = req.getBaseRequestDTO().getUserId();
        Long useActivityId = req.getUseActivityId();
        Long benefitId = req.getBenefitId();
        EquityBenefitDO benefitDO = localCacheManager.getBenefitInfoById(benefitId);
        if (Objects.isNull(benefitDO)) {
            log.warn("provideInitActivityCount benefitDO is null userId={}, useActivityId={}, benefitId={}", userId,
                    useActivityId, benefitId);
            CsossUtils.logEvent(MonitorConstants.INIT_ACTIVITY_COUNT, "benefit_is_null");
            return false;
        }
        DirectAccountContext accountContext = BenefitOperateConvertEntity.convertToInitActivityCountAccountContext(req,
                benefitDO);
        ReceiveBenefitResDTO receiveResp = equityAccountCoreService.receiveDirectBenefit(accountContext);
        if (Objects.isNull(receiveResp) || !Objects.equals(receiveResp.getStatusEnum(), StatusEnum.SUCCESS)) {
            log.warn("provideInitActivityCount provide failure accountContext={}, receiveResp={}",
                    JSON.toJSONString(accountContext), JSON.toJSONString(receiveResp));
            CsossUtils.logEvent(MonitorConstants.INIT_ACTIVITY_COUNT, "provide_failure");
            return false;
        }
        return true;
    }

    @MonitorSpan(name = "setHadInitActivityCountToRedis")
    public void setHadInitActivityCountToRedis(String userId, Long useActivityId) {
        String redisKey = String.format(CacheKeyConstants.HAD_INIT_ACTIVITY_COUNT_KEY, userId);
        redisCache.hSetValue(redisKey, String.valueOf(useActivityId), Constants.ONE_TEXT, 30, TimeUnit.DAYS);
    }

    @MonitorSpan(name = "hadInitActivityCountByRedis")
    public boolean hadInitActivityCountByRedis(String userId, Long useActivityId) {
        String redisKey = String.format(CacheKeyConstants.HAD_INIT_ACTIVITY_COUNT_KEY, userId);
        String value = redisCache.hGetValue(redisKey, String.valueOf(useActivityId));
        return StringUtils.isNotBlank(value);
    }

    @MonitorSpan(name = "drawAndReceive.riskControlBefore")
    public RiskControlResponseDTO riskControlBefore(DrawAndReceiveReqDTO req, FullBenefitInfoDTO benefitInfoDTO) {
        RiskControlContextDTO contextDTO = BenefitOperateConvertEntity.convertToRiskControlContextDTOBefore(req, benefitInfoDTO);
        return riskControlFactory.process(contextDTO);
    }

    private void riskControlAfter(DrawAndReceiveReqDTO req, FullBenefitInfoDTO benefitInfoDTO) {
        ThreadsUtils.runAsync(() -> {
            RiskControlContextDTO contextDTO = BenefitOperateConvertEntity.convertToRiskControlContextDTOAfter(req, benefitInfoDTO);
            riskControlFactory.process(contextDTO);
        }, ThreadsUtils.getOrdinaryDiscardThreadPoll(), Constants.DRAW_AND_RECEIVE_RISK_AFTER);
    }

    @MonitorSpan(name = "sceneReceive.riskControlBefore")
    public RiskControlResponseDTO riskControlBefore(SceneReceiveReqDTO req, FullBenefitInfoDTO benefitInfoDTO) {
        RiskControlContextDTO contextDTO = BenefitOperateConvertEntity.convertToRiskControlContextBefore(req, benefitInfoDTO);
        return riskControlFactory.process(contextDTO);
    }

    private void riskControlAfter(SceneReceiveReqDTO req, FullBenefitInfoDTO benefitInfoDTO) {
        ThreadsUtils.runAsync(() -> {
            RiskControlContextDTO contextDTO = BenefitOperateConvertEntity.convertToRiskControlContextAfter(req, benefitInfoDTO);
            riskControlFactory.process(contextDTO);
        }, ThreadsUtils.getOrdinaryDiscardThreadPoll(), Constants.SCENE_RECEIVE_RISK_AFTER);
    }

    @MonitorSpan(name = "sceneReceive.consult")
    public SceneReceiveRespDTO consult(SceneReceiveReqDTO req) {
        // 1 咨询返回本次领取的权益信息
        String sceneCode = req.getSceneCode();
        Long activityId = req.getActivityId();
        String externalStrategyId = req.getExternalStrategyId();
        Long benefitId = req.getBenefitId();
        EngineContextDTO consultEngineContextDTO = BenefitOperateConvertEntity.convertToConsultEngineContextDTO(req);
        List<SceneActivityCacheDto> activities = sceneActivityBenefitCoreService.consultSceneBenefit(sceneCode, consultEngineContextDTO);
        // 1.1 如果不能领取权益不为空，返回领取失败
        UnableReceiveBenefitDTO unableReceiveBenefitDTO = BenefitOperateConvertEntity.filterUnableReceiveBenefit(
                consultEngineContextDTO.getResultContextDTO(), activityId, externalStrategyId, benefitId);
        if (Objects.nonNull(unableReceiveBenefitDTO)) {
            log.info("sceneReceive consult unableReceiveBenefitDTO notNull req={}, unableReceiveBenefitDTO={}",
                    JSON.toJSONString(req), JSON.toJSONString(unableReceiveBenefitDTO));
            return SceneReceiveRespDTO.builder()
                    .receiveResultStatus(StatusEnum.FAIL.getCode())
                    .unableReceiveReasonCode(unableReceiveBenefitDTO.getUnableReceiveReasonCode())
                    .build();
        }
        // 1.2 如果可以领取权益为空，throw exception
        FullBenefitInfoDTO canReceiveBenefitDTO = BenefitOperateConvertEntity.filterCanReceiveBenefit(activities,
                activityId, externalStrategyId, benefitId);
        if (Objects.isNull(canReceiveBenefitDTO)) {
            log.warn("sceneReceive consult canReceiveBenefitDTO isNull req={}", JSON.toJSONString(req));
            CsossUtils.logEvent(MonitorConstants.SCENE_RECEIVE_CONSULT_NULL, "consult_failure");
            throw AdminExceptionBuilder.build(ExceptionEnum.SCENE_RECEIVE_BENEFIT_IS_NULL, "可以领取权益为空");
        }

        // 2 调用风控，不通过需要替换掉本次领取的奖励
        UniversalBenefitOperateCoreServiceImpl self = (UniversalBenefitOperateCoreServiceImpl) AopContext.currentProxy();
        RiskControlResponseDTO riskControlResponseDTO = self.riskControlBefore(req, canReceiveBenefitDTO);
        String riskControlResult = riskControlResponseDTO == null ? null : riskControlResponseDTO.getRiskControlResult();
        List<RiskControlInterceptRuleDTO> riskControlInterceptRuleDTOList = riskControlResponseDTO == null ?
                null : riskControlResponseDTO.getInterceptRuleDTOList();
        // 2.1 风控通过，奖励不变
        if (RiskCheckUtils.riskPass(riskControlResult)) {
            return SceneReceiveRespDTO.builder()
                    .receiveBenefit(canReceiveBenefitDTO)
                    .build();
        }
        // 2.2 风控不通过，不发奖励
        if (RiskCheckUtils.riskNoPrize(riskControlResult)) {
            return SceneReceiveRespDTO.builder()
                    .receiveResultStatus(StatusEnum.FAIL.getCode())
                    .unableReceiveReasonCode(BenefitUnableReceiveReasonType.HIT_RISK.getCode())
                    .riskControlInterceptRuleDTOList(riskControlInterceptRuleDTOList)
                    .build();
        }
        // 2.3 风控不通过。命中 [黑灰产] 用户，替换成黑灰产策略奖奖励；命中 [普通风控] 用户，替换成兜底策略奖励（如果本来抽中的是兜底策略奖励，则可以不替换）
        return SceneReceiveRespDTO.builder()
                .receiveBenefit(getRiskReplaceBenefit(sceneCode, activityId, riskControlResult))
                .build();
    }

    @MonitorSpan(name = "sceneReceive.receive")
    public SceneReceiveRespDTO receive(SceneReceiveReqDTO req, FullBenefitInfoDTO receiveBenefit) {
        // 设置具体发放的 sendAmount + balanceMoney
        BenefitOperateConvertEntity.fillReceiveBenefitSendAmount(receiveBenefit, req.getSendAmount());
        EquityAccountContext equityAccountContext = BenefitOperateConvertEntity.convertToReceiveEquityAccountContext(req,
                receiveBenefit, sendTicketNeedExternalScenes);
        ReceiveBenefitResDTO sceneReceiveRes = AccountStrategyContext.builderByBenefitType(equityAccountContext.getBenefitType())
                .sceneReceiveNew(equityAccountContext);
        // 如果领取时幂等，直接返回原来领取的结果，对应权益有可能发生变更所以需要替换掉 receiveBenefit
        FullBenefitInfoDTO realReceiveBenefit = getRealReceiveBenefit(receiveBenefit, equityAccountContext);
        SceneReceiveRespDTO sceneReceiveRespDTO = BenefitOperateConvertEntity.convertToSceneReceiveRespDTO(realReceiveBenefit,
                sceneReceiveRes);

        // 领取成功
        Integer receiveResultStatus = sceneReceiveRespDTO.getReceiveResultStatus();
        if (Objects.equals(receiveResultStatus, StatusEnum.SUCCESS.getCode())) {
            // 领取成功后上报风控
            riskControlAfter(req, realReceiveBenefit);
            // 领取成功打点
            CsossUtils.logEvent(MonitorConstants.SCENE_RECEIVE_RESULT, BenefitOperateConvertEntity.convertToBenefitEventName(realReceiveBenefit));
            return sceneReceiveRespDTO;
        }
        // 领取中 or 领取失败打点
        log.warn("sceneReceive receive sceneReceiveNew unsuccessful equityAccountContext={}, sceneReceiveRes={}",
                JSON.toJSONString(equityAccountContext), JSON.toJSONString(sceneReceiveRes));
        CsossUtils.logEvent(MonitorConstants.SCENE_RECEIVE_RESULT, StatusEnum.unfinishedStatus(receiveResultStatus) ? "unfinished" : "failure");
        return sceneReceiveRespDTO;
    }

    private FullBenefitInfoDTO getRealReceiveBenefit(FullBenefitInfoDTO receiveBenefit,
                                                     EquityAccountContext equityAccountContext) {
        Long resultActivityId = equityAccountContext.getActivityId();
        Long resultStrategyId = equityAccountContext.getStrategyId();
        Long resultBenefitGroupId = equityAccountContext.getBenefitGroupId();
        Long resultBenefitId = equityAccountContext.getBenefitId();
        if (Objects.isNull(resultActivityId) || Objects.isNull(resultStrategyId)
                || Objects.isNull(resultBenefitGroupId) || Objects.isNull(resultBenefitId)) {
            return receiveBenefit;
        }
        String receiveBenefitUni = BenefitUtil.getBenefitUni(receiveBenefit);
        String resultBenefitUni = BenefitUtil.getBenefitUni(resultStrategyId, resultBenefitGroupId, resultBenefitId);
        if (StringUtils.equals(receiveBenefitUni, resultBenefitUni)) {
            // 如果领取时幂等，直接返回原来领取的结果，对应权益有可能发生变更所以需要替换掉 balanceMoney
            BenefitOperateConvertEntity.changeReceiveBenefitSendAmount(receiveBenefit, equityAccountContext);
            return receiveBenefit;
        }

        FullBenefitInfoDTO resultBenefit = benefitCoreService.getOneFullBenefitInfoDTO(resultActivityId,
                resultStrategyId, resultBenefitGroupId, resultBenefitId);
        if (Objects.isNull(resultBenefit)) {
            log.warn("getRealReceiveBenefit resultBenefit isNull receiveBenefit={}, resultBenefitUni={}",
                    JSON.toJSONString(receiveBenefit), resultBenefitUni);
            throw AdminExceptionBuilder.build(ExceptionEnum.SCENE_RECEIVE_BENEFIT_IS_NULL, "领取奖励信息为空");
        }
        // 如果领取时幂等，直接返回原来领取的结果，对应权益有可能发生变更所以需要替换掉 balanceMoney
        BenefitOperateConvertEntity.changeReceiveBenefitSendAmount(resultBenefit, equityAccountContext);
        log.info("getRealReceiveBenefit realReceiveBenefit changed receiveBenefit={}, resultBenefit={}",
                JSON.toJSONString(receiveBenefit), JSON.toJSONString(resultBenefit));
        return resultBenefit;
    }

    private FullBenefitInfoDTO getRiskReplaceBenefit(String sceneCode, Long activityId, String riskControlResult) {
        Integer riskStrategyType = RiskCheckUtils.getRiskNoPassUserReceiveStrategyType(riskControlResult);
        List<FullBenefitInfoDTO> riskBenefits = benefitCoreService.getFullBenefitInfosByStrategyType(sceneCode, activityId, riskStrategyType);
        if (CollectionUtils.isEmpty(riskBenefits)) {
            log.warn("getRiskReplaceBenefit riskBenefits isNull sceneCode={}, activityId={}, riskControlResult={}, riskStrategyType={}",
                    sceneCode, activityId, riskControlResult, riskStrategyType);
            throw AdminExceptionBuilder.build(ExceptionEnum.SCENE_RECEIVE_BENEFIT_IS_NULL, "兜底/黑灰产策略奖励为空");
        }
        return riskBenefits.get(0);
    }
}
