package com.ddmc.equity.domain.service.core.impl;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.account.BenefitReceiveRuleContext;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.interceptor.annotation.MonitorSpan;
import com.ddmc.equity.common.util.CatMonitorUtil;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.domain.service.benefit_limit.EquityBenefitLimitService;
import com.ddmc.equity.domain.service.benefit_stock.EquityBenefitStockDomainService;
import com.ddmc.equity.domain.service.core.ReceiveAndStockLimitCoreService;
import com.ddmc.equity.domain.service.receive_limit.UserActivityReceiveLimitDomainService;
import com.ddmc.equity.domain.valueobject.benefit_limit.DeductBenefitLimitResp;
import com.ddmc.equity.domain.valueobject.benefit_limit.FreezeBenefitLimitResp;
import com.ddmc.equity.domain.valueobject.benefit_limit.ReleaseBenefitLimitResp;
import com.ddmc.equity.domain.valueobject.receive_limit.OperateActivityReceiveLimitResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/28 17:34
 * @description
 */
@Slf4j
@Service
public class ReceiveAndStockLimitCoreServiceImpl implements ReceiveAndStockLimitCoreService {

    @Autowired
    private CatMonitorUtil catMonitorUtil;
    @Autowired
    private UserActivityReceiveLimitDomainService userActivityReceiveLimitDomainService;
    @Autowired
    private EquityBenefitLimitService equityBenefitLimitService;
    @Autowired
    private EquityBenefitStockDomainService stockDomainService;

    @Override
    @MonitorSpan(name = "deductActivityReceiveLimit")
    public boolean deductActivityReceiveLimit(EquityAccountContext equityAccountContext,
                                              BenefitReceiveRuleContext benefitReceiveRuleContext) {
        try {
            if (noActivityReceiveLimit(benefitReceiveRuleContext)) {
                return true;
            }
            ReceiveLimitRuleDTO receiveLimitRuleDTO = benefitReceiveRuleContext.getActivityReceiveLimitRuleDTO();
            if (Objects.isNull(receiveLimitRuleDTO)) {
                log.warn("deductActivityReceiveLimit receiveLimitRuleDTO is null. context={}, benefitReceiveRuleContext={}",
                        JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext));
                catMonitorUtil.logEvent(MonitorConstants.ACTIVITY_RECEIVE_LIMIT_RULE_QUERY, MonitorConstants.FAIL);
                return false;
            }

            OperateActivityReceiveLimitResp resp = userActivityReceiveLimitDomainService.deductReceiveLimit(equityAccountContext.getUid(),
                    equityAccountContext.getActivityId(), receiveLimitRuleDTO);
            if (Objects.nonNull(resp) && Boolean.TRUE.equals(resp.getOperateResult())) {
                equityAccountContext.setActivityReceiveLimitId(resp.getReceiveLimitId());
                return true;
            }
            log.warn("deductActivityReceiveLimit deductReceiveLimit failure. context={}, benefitReceiveRuleContext={}, resp={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext), JSON.toJSONString(resp));
            catMonitorUtil.logEventWithSpan(MonitorConstants.DEDUCT_ACTIVITY_RECEIVE_LIMIT, MonitorConstants.FAIL,
                    Constants.STATUS_NO_STR, JSON.toJSONString(equityAccountContext));
            return false;
        } catch (Exception e) {
            log.error("deductActivityReceiveLimit exception. context={}, benefitReceiveRuleContext={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext), e);
            catMonitorUtil.logEventWithSpan(MonitorConstants.DEDUCT_ACTIVITY_RECEIVE_LIMIT, MonitorConstants.EXCEPTION,
                    Constants.STATUS_NO_STR, JSON.toJSONString(equityAccountContext));
            return false;
        }
    }

    @Override
    public void releaseActivityReceiveLimit(EquityAccountContext equityAccountContext,
                                            BenefitReceiveRuleContext benefitReceiveRuleContext) {
        try {
            if (noActivityReceiveLimit(benefitReceiveRuleContext)) {
                return;
            }
            ReceiveLimitRuleDTO receiveLimitRuleDTO = benefitReceiveRuleContext.getActivityReceiveLimitRuleDTO();
            if (Objects.isNull(receiveLimitRuleDTO)) {
                log.warn("releaseActivityReceiveLimit receiveLimitRuleDTO is null. context={}, benefitReceiveRuleContext={}",
                        JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext));
                catMonitorUtil.logEvent(MonitorConstants.ACTIVITY_RECEIVE_LIMIT_RULE_QUERY, MonitorConstants.FAIL);
                return;
            }

            OperateActivityReceiveLimitResp resp = userActivityReceiveLimitDomainService.releaseReceiveLimit(equityAccountContext.getUid(),
                    equityAccountContext.getActivityId(), equityAccountContext.getActivityReceiveLimitId());
            if (Objects.nonNull(resp) && Boolean.TRUE.equals(resp.getOperateResult())) {
                return;
            }
            log.warn("releaseActivityReceiveLimit releaseReceiveLimit failure. context={}, benefitReceiveRuleContext={}, resp={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext), JSON.toJSONString(resp));
            catMonitorUtil.logEventWithSpan(MonitorConstants.RELEASE_ACTIVITY_RECEIVE_LIMIT, MonitorConstants.FAIL,
                    Constants.STATUS_NO_STR, JSON.toJSONString(equityAccountContext));
        } catch (Exception e) {
            log.error("releaseActivityReceiveLimit exception. context={}, benefitReceiveRuleContext={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext), e);
            catMonitorUtil.logEventWithSpan(MonitorConstants.RELEASE_ACTIVITY_RECEIVE_LIMIT, MonitorConstants.EXCEPTION,
                    Constants.STATUS_NO_STR, JSON.toJSONString(equityAccountContext));
        }
    }

    @Override
    @MonitorSpan(name = "freezeBenefitReceiveLimit")
    public boolean freezeBenefitReceiveLimit(EquityAccountContext equityAccountContext,
                                             BenefitReceiveRuleContext benefitReceiveRuleContext) {
        try {
            // 默认不存在处理中的记录
            equityAccountContext.setHasFreezeReceiveLimitProcessingRecord(false);

            String userId = equityAccountContext.getUid();
            Long activityId = equityAccountContext.getActivityId();
            Long strategyId = equityAccountContext.getStrategyId();
            Long benefitId = equityAccountContext.getBenefitId();
            if (noBenefitReceiveLimit(benefitReceiveRuleContext)) {
                // 成功需要返回调用三方唯一请求流水号
                String doRpcUniqueSerialNumber = userId + UUID.randomUUID().toString().trim().replace("-", "") + strategyId + benefitId;
                equityAccountContext.setDoRpcUniqueSerialNumber(doRpcUniqueSerialNumber);
                return true;
            }
            ReceiveLimitRuleDTO receiveLimitRuleDTO = benefitReceiveRuleContext.getBenefitReceiveLimitRuleDTO();
            if (Objects.isNull(receiveLimitRuleDTO)) {
                log.warn("freezeBenefitReceiveLimit receiveLimitRuleDTO is null. context={}, benefitReceiveRuleContext={}",
                        JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext));
                catMonitorUtil.logEvent(MonitorConstants.RECEIVE_LIMIT_RULE_QUERY, MonitorConstants.FAIL);
                return false;
            }

            // 冻结频次
            FreezeBenefitLimitResp resp = equityBenefitLimitService.freezeBenefitLimit(userId, activityId,
                    strategyId, benefitId, receiveLimitRuleDTO);
            log.info("freezeBenefitReceiveLimit freezeBenefitLimit end. context={}, benefitReceiveRuleContext={}, resp={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext), JSON.toJSONString(resp));
            if (Objects.isNull(resp)) {
                return false;
            }
            equityAccountContext.setFreezeReceiveLimitResultId(resp.getFreezeId());
            if (!resp.getFreezeResult()) {
                // 如果之前存在冻结数量，则存在处理中的记录
                Long freezeCount = resp.getFreezeCountBeforeFreeze();
                equityAccountContext.setHasFreezeReceiveLimitProcessingRecord(Objects.nonNull(freezeCount) && freezeCount > 0);
                return false;
            }
            String doRpcUniqueSerialNumber = userId + strategyId + benefitId + resp.getSerialNumber() + resp.getFreezeId();
            equityAccountContext.setDoRpcUniqueSerialNumber(doRpcUniqueSerialNumber);
            return true;
        } catch (Exception e) {
            log.error("freezeBenefitReceiveLimit exception. context={}, benefitReceiveRuleContext={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext), e);
            return false;
        }
    }

    @Override
    public void releaseBenefitReceiveLimit(EquityAccountContext equityAccountContext,
                                           BenefitReceiveRuleContext benefitReceiveRuleContext) {
        try {
            if (noBenefitReceiveLimit(benefitReceiveRuleContext)) {
                return;
            }
            ReceiveLimitRuleDTO receiveLimitRuleDTO = benefitReceiveRuleContext.getBenefitReceiveLimitRuleDTO();
            if (Objects.isNull(receiveLimitRuleDTO)) {
                log.warn("releaseBenefitReceiveLimit receiveLimitRuleDTO is null. context={}, benefitReceiveRuleContext={}",
                        JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext));
                catMonitorUtil.logEvent(MonitorConstants.RECEIVE_LIMIT_RULE_QUERY, MonitorConstants.FAIL);
                return;
            }

            ReleaseBenefitLimitResp resp = equityBenefitLimitService.releaseBenefitLimit(equityAccountContext.getUid(),
                    equityAccountContext.getActivityId(), equityAccountContext.getStrategyId(), equityAccountContext.getBenefitId(),
                    receiveLimitRuleDTO, equityAccountContext.getFreezeReceiveLimitResultId());
            log.info("releaseBenefitReceiveLimit releaseBenefitLimit end. context={}, benefitReceiveRuleContext={}, resp={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext), JSON.toJSONString(resp));
            if (Objects.isNull(resp) || !Boolean.TRUE.equals(resp.getReleaseResult())) {
                catMonitorUtil.logEventWithSpan(MonitorConstants.RELEASE_USER_RECEIVE_BENEFIT_LIMIT, MonitorConstants.FAIL,
                        Constants.STATUS_NO_STR, JSON.toJSONString(equityAccountContext));
            }
        } catch (Exception e) {
            log.error("releaseBenefitReceiveLimit exception. context={}, benefitReceiveRuleContext={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext), e);
            catMonitorUtil.logEventWithSpan(MonitorConstants.RELEASE_USER_RECEIVE_BENEFIT_LIMIT, MonitorConstants.EXCEPTION,
                    Constants.STATUS_NO_STR, JsonUtil.toJsonString(equityAccountContext));
        }
    }

    @Override
    @MonitorSpan(name = "deductBenefitReceiveLimit")
    public String deductBenefitReceiveLimit(EquityAccountContext equityAccountContext, BenefitReceiveRuleContext benefitReceiveRuleContext) {
        try {

            if (Objects.isNull(benefitReceiveRuleContext) || noBenefitReceiveLimit(benefitReceiveRuleContext)) {
                return null;
            }
            ReceiveLimitRuleDTO receiveLimitRuleDTO = benefitReceiveRuleContext.getBenefitReceiveLimitRuleDTO();
            if (Objects.isNull(receiveLimitRuleDTO)) {
                log.warn("deductBenefitReceiveLimit receiveLimitRuleDTO is null. context={}, benefitReceiveRuleContext={}",
                        JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext));
                catMonitorUtil.logEvent(MonitorConstants.RECEIVE_LIMIT_RULE_QUERY, MonitorConstants.FAIL);
                return null;
            }

            DeductBenefitLimitResp resp = equityBenefitLimitService.deductBenefitLimit(equityAccountContext.getUid(),
                    equityAccountContext.getActivityId(), equityAccountContext.getStrategyId(), equityAccountContext.getBenefitId(),
                    receiveLimitRuleDTO, equityAccountContext.getFreezeReceiveLimitResultId());
            log.info("deductBenefitReceiveLimit deductBenefitLimit end. context={}, benefitReceiveRuleContext={}, resp={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext), JSON.toJSONString(resp));
            if (Objects.isNull(resp) || !resp.getDeductResult()) {
                catMonitorUtil.logEvent(MonitorConstants.DEDUCT_RECEIVE_LIMIT, MonitorConstants.FAIL);
                return null;
            }
            return resp.getSerialNumber();
        } catch (Exception e) {
            log.error("deductBenefitReceiveLimit exception. context={}, benefitReceiveRuleContext={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext), e);
            catMonitorUtil.logEvent(MonitorConstants.DEDUCT_RECEIVE_LIMIT, MonitorConstants.EXCEPTION);
            return null;
        }
    }

    @Override
    @MonitorSpan(name = "deductBenefitStock")
    public boolean deductBenefitStock(EquityAccountContext equityAccountContext,
                                      BenefitReceiveRuleContext benefitReceiveRuleContext) {
        try {
            if (noBenefitStockLimit(benefitReceiveRuleContext)) {
                return true;
            }
            StockLimitRuleDTO stockLimitRuleDTO = benefitReceiveRuleContext.getBenefitStockLimitRuleDTO();
            if (Objects.isNull(stockLimitRuleDTO)) {
                log.warn("deductBenefitStock stockLimitRuleDTO is null. context={}, benefitReceiveRuleContext={}",
                        JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext));
                catMonitorUtil.logEvent(MonitorConstants.STOCK_LIMIT_RULE_QUERY, MonitorConstants.FAIL);
                return false;
            }

            String doRpcUniqueSerialNumber = equityAccountContext.getDoRpcUniqueSerialNumber();
            Boolean result = stockDomainService.deductStock(equityAccountContext.getActivityId(),
                    equityAccountContext.getStrategyId(), equityAccountContext.getBenefitId(),
                    Constants.ONE_LONG, doRpcUniqueSerialNumber, stockLimitRuleDTO);
            if (Boolean.TRUE.equals(result)) {
                return true;
            }
            log.warn("deductBenefitStock deductStock failure. context={}, benefitReceiveRuleContext={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext));
            catMonitorUtil.logEventWithSpan(MonitorConstants.DEDUCT_BENEFIT_STOCK, MonitorConstants.FAIL,
                    Constants.STATUS_NO_STR, JSON.toJSONString(equityAccountContext));
            return false;
        } catch (Exception e) {
            log.error("deductBenefitStock exception. context={}, benefitReceiveRuleContext={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext), e);
            catMonitorUtil.logEventWithSpan(MonitorConstants.DEDUCT_BENEFIT_STOCK, MonitorConstants.EXCEPTION,
                    Constants.STATUS_NO_STR, JSON.toJSONString(equityAccountContext));
            return false;
        }
    }

    @Override
    public void releaseStockLimit(EquityAccountContext equityAccountContext,
                                  BenefitReceiveRuleContext benefitReceiveRuleContext) {
        try {
            if (noBenefitStockLimit(benefitReceiveRuleContext)) {
                return;
            }
            StockLimitRuleDTO stockLimitRuleDTO = benefitReceiveRuleContext.getBenefitStockLimitRuleDTO();
            if (Objects.isNull(stockLimitRuleDTO)) {
                log.warn("releaseStockLimit stockLimitRuleDTO is null. context={}, benefitReceiveRuleContext={}",
                        JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext));
                catMonitorUtil.logEvent(MonitorConstants.STOCK_LIMIT_RULE_QUERY, MonitorConstants.FAIL);
                return;
            }

            String doRpcUniqueSerialNumber = equityAccountContext.getDoRpcUniqueSerialNumber();
            Boolean result = stockDomainService.releaseStock(equityAccountContext.getActivityId(),
                    equityAccountContext.getStrategyId(), equityAccountContext.getBenefitId(), Constants.ONE_LONG,
                    doRpcUniqueSerialNumber, stockLimitRuleDTO);
            if (!Boolean.TRUE.equals(result)) {
                log.warn("releaseStockLimit releaseStock failure. context={}, benefitReceiveRuleContext={}",
                        JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext));
                catMonitorUtil.logEventWithSpan(MonitorConstants.RELEASE_STOCK_LIMIT, MonitorConstants.FAIL,
                        Constants.STATUS_NO_STR, JSON.toJSONString(equityAccountContext));
            }
        } catch (Exception e) {
            log.error("releaseStockLimit exception. context={}, benefitReceiveRuleContext={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(benefitReceiveRuleContext), e);
            catMonitorUtil.logEvent(MonitorConstants.RELEASE_STOCK_LIMIT, MonitorConstants.EXCEPTION);
        }
    }

    private boolean noActivityReceiveLimit(BenefitReceiveRuleContext benefitReceiveRuleContext) {
        return Objects.isNull(benefitReceiveRuleContext) || !benefitReceiveRuleContext.getHasActivityReceiveLimit();
    }

    private boolean noBenefitReceiveLimit(BenefitReceiveRuleContext benefitReceiveRuleContext) {
        return Objects.isNull(benefitReceiveRuleContext) || !benefitReceiveRuleContext.getHasBenefitReceiveLimit();
    }

    private boolean noBenefitStockLimit(BenefitReceiveRuleContext benefitReceiveRuleContext) {
        return Objects.isNull(benefitReceiveRuleContext) || !benefitReceiveRuleContext.getHasBenefitStockLimit();
    }
}
