package com.ddmc.equity.domain.converter.rule;

import com.ddmc.equity.domain.dto.rule.condition.AppVersionRuleDTO;
import com.ddmc.equity.domain.valueobject.rule.condition.AppVersionRuleVO;
import com.ddmc.equity.dto.business.rule.AppVersionDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface AppVersionRuleConverter {
    AppVersionRuleConverter INSTANCE = Mappers.getMapper(AppVersionRuleConverter.class);

    @Mapping(target = "appVersionList", source = "appVersionList")
    AppVersionRuleVO convertDTOToVO(AppVersionDTO appVersionDTO);

    @Mapping(target = "appVersionList", source = "appVersionList")
    AppVersionRuleDTO convertToAppVersionRuleDTO(AppVersionDTO appVersionDTO);
}
