package com.ddmc.equity.domain.dto.benefit_group;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class BaseInfoDTO implements Serializable {

    private static final long serialVersionUID = -1327865300959632579L;

    @ApiModelProperty("权益 id")
    private Long id;
    @ApiModelProperty("权益概率或优先级值")
    private String weight;
    /**
     * 状态。0-禁用；1-启用；
     */
    private Integer status;
    /**
     * 过期时间，默认为 null。为空则不判断
     * <p>
     * 目前券膨胀 B 端保存的权益有效期时间 = 膨胀券模板的有效期时间 - 7day
     */
    private Date expiryTime;
}
