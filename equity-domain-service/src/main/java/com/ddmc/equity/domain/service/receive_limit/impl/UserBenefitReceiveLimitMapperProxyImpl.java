package com.ddmc.equity.domain.service.receive_limit.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ddmc.equity.common.util.LongUtils;
import com.ddmc.equity.domain.service.receive_limit.UserBenefitReceiveLimitMapperProxy;
import com.ddmc.equity.domain.service.receive_limit.config.UserReceiveLimitTableSwitchConfig;
import com.ddmc.equity.infra.repository.dao.NewUserBenefitReceiveLimitDO;
import com.ddmc.equity.infra.repository.dao.UserReceiveBenefitLimitDO;
import com.ddmc.equity.infra.repository.dao.mapper.NewUserBenefitReceiveLimitMapper;
import com.ddmc.equity.infra.repository.dao.mapper.UserReceiveBenefitLimitMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 用户权益领取限制表 Mapper 代理实现类
 * 用于新老表切换过程中的数据操作代理
 */
@Slf4j
@Component
public class UserBenefitReceiveLimitMapperProxyImpl implements UserBenefitReceiveLimitMapperProxy {

    /**
     * 限制维度：权益
     */
    private static final String LIMIT_DIMENSION = "benefit";

    @Resource
    private UserReceiveLimitTableSwitchConfig tableSwitchConfig;
    @Resource
    private UserReceiveBenefitLimitMapper userReceiveBenefitLimitMapper;
    @Resource
    private NewUserBenefitReceiveLimitMapper newUserBenefitReceiveLimitMapper;

    @Override
    public UserReceiveBenefitLimitDO queryByDate(String userId, Long activityId, Long strategyId, Long benefitId,
                                                 Date date, Long id) {
        if (tableSwitchConfig.shouldUseOldTable(activityId, LIMIT_DIMENSION, "queryByDate")) {
            LambdaQueryWrapper<UserReceiveBenefitLimitDO> wrapper = new LambdaQueryWrapper<UserReceiveBenefitLimitDO>()
                    .eq(UserReceiveBenefitLimitDO::getUserId, userId)
                    .eq(UserReceiveBenefitLimitDO::getActivityId, activityId)
                    .eq(UserReceiveBenefitLimitDO::getStrategyId, strategyId)
                    .eq(UserReceiveBenefitLimitDO::getBenefitId, benefitId)
                    .le(UserReceiveBenefitLimitDO::getStartTime, date)
                    .ge(UserReceiveBenefitLimitDO::getEndTime, date);
            if (LongUtils.isTrue(id)) {
                wrapper = wrapper.eq(UserReceiveBenefitLimitDO::getId, id);
            }
            wrapper = wrapper.last("limit 1");
            return userReceiveBenefitLimitMapper.selectOne(wrapper);
        }

        LambdaQueryWrapper<NewUserBenefitReceiveLimitDO> newWrapper = new LambdaQueryWrapper<NewUserBenefitReceiveLimitDO>()
                .eq(NewUserBenefitReceiveLimitDO::getUserId, userId)
                .eq(NewUserBenefitReceiveLimitDO::getActivityId, activityId)
                .eq(NewUserBenefitReceiveLimitDO::getStrategyId, strategyId)
                .eq(NewUserBenefitReceiveLimitDO::getBenefitId, benefitId)
                .le(NewUserBenefitReceiveLimitDO::getStartTime, date)
                .ge(NewUserBenefitReceiveLimitDO::getEndTime, date);
        if (LongUtils.isTrue(id)) {
            newWrapper = newWrapper.eq(NewUserBenefitReceiveLimitDO::getId, id);
        }
        newWrapper = newWrapper.last("limit 1");
        NewUserBenefitReceiveLimitDO newLimit = newUserBenefitReceiveLimitMapper.selectOne(newWrapper);
        return convertToOldModel(newLimit);
    }

    @Override
    public int insert(UserReceiveBenefitLimitDO receiveLimitDO) {
        if (tableSwitchConfig.shouldUseOldTable(receiveLimitDO.getActivityId(), LIMIT_DIMENSION, "insert")) {
            return userReceiveBenefitLimitMapper.insert(receiveLimitDO);
        }

        NewUserBenefitReceiveLimitDO newReceiveLimitDO = convertToNewModel(receiveLimitDO);
        int insertResult = newUserBenefitReceiveLimitMapper.insert(newReceiveLimitDO);
        // 插入完成后，需要回写 receiveLimitDO 的 ID
        receiveLimitDO.setId(newReceiveLimitDO.getId());
        return insertResult;
    }

    @Override
    public Integer deductBenefitLimit(Long id, String userId, Long activityId, Long strategyId, Long benefitId,
                                      Long preVersion) {
        if (tableSwitchConfig.shouldUseOldTable(activityId, LIMIT_DIMENSION, "deductBenefitLimit")) {
            return userReceiveBenefitLimitMapper.deductBenefitLimit(id, strategyId, activityId, userId, preVersion, benefitId);
        }

        return newUserBenefitReceiveLimitMapper.deductBenefitLimit(id, userId, activityId, strategyId, benefitId, preVersion);
    }

    @Override
    public Integer freezeBenefitLimit(Long id, String userId, Long activityId, Long strategyId, Long benefitId,
                                      Long preVersion) {
        if (tableSwitchConfig.shouldUseOldTable(activityId, LIMIT_DIMENSION, "freezeBenefitLimit")) {
            return userReceiveBenefitLimitMapper.freezeBenefitLimit(id, strategyId, activityId, userId, preVersion, benefitId);
        }

        return newUserBenefitReceiveLimitMapper.freezeBenefitLimit(id, userId, activityId, strategyId, benefitId, preVersion);
    }

    @Override
    public Integer releaseBenefitLimit(Long id, String userId, Long activityId, Long strategyId, Long benefitId,
                                       Long preVersion) {
        if (tableSwitchConfig.shouldUseOldTable(activityId, LIMIT_DIMENSION, "releaseBenefitLimit")) {
            return userReceiveBenefitLimitMapper.releaseBenefitLimit(id, strategyId, activityId, userId, preVersion, benefitId);
        }

        return newUserBenefitReceiveLimitMapper.releaseBenefitLimit(id, userId, activityId, strategyId, benefitId, preVersion);
    }

    /**
     * 将旧表数据模型转换为新表数据模型
     *
     * @param oldModel 旧表数据模型
     * @return 新表数据模型
     */
    private NewUserBenefitReceiveLimitDO convertToNewModel(UserReceiveBenefitLimitDO oldModel) {
        return oldModel == null ? null : BeanUtil.copyProperties(oldModel, NewUserBenefitReceiveLimitDO.class);
    }

    /**
     * 将新表数据模型转换为旧表数据模型
     *
     * @param newModel 新表数据模型
     * @return 旧表数据模型
     */
    private UserReceiveBenefitLimitDO convertToOldModel(NewUserBenefitReceiveLimitDO newModel) {
        return newModel == null ? null : BeanUtil.copyProperties(newModel, UserReceiveBenefitLimitDO.class);
    }
}