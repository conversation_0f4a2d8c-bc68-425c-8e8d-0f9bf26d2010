package com.ddmc.equity.domain.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class SearchTicketInDTO {
    @ApiModelProperty("查询城市")
    private List<String> cities;
    @ApiModelProperty("券查询关键字")
    private String searchKey;
    @NotNull(message = "标签必传")
    @ApiModelProperty("标签")
    private String tag;
    @NotNull(message = "优惠券类型必传，目前只能转1")
    @ApiModelProperty("优惠券类型  券类型1 满减券 2 赠品券 3 折扣券")
    private Integer ticketType;
}
