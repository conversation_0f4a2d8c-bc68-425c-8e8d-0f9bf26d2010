package com.ddmc.equity.domain.converter.benefit_group;

import com.ddmc.equity.domain.valueobject.benefit_group.BenefitGroupListFilterReqVO;
import com.ddmc.equity.dto.business.BenefitGroupBusinessDTO;
import com.ddmc.equity.dto.business.BenefitGroupListReqDTO;
import com.ddmc.equity.dto.business.BenefitGroupSaveReqDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitGroupDO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface BenefitGroupConverter {
    BenefitGroupConverter INSTANCE = Mappers.getMapper(BenefitGroupConverter.class);

    BenefitGroupListFilterReqVO d2v(BenefitGroupListReqDTO req);

    BenefitGroupBusinessDTO v2d(EquityBenefitGroupDO input);

    List<BenefitGroupBusinessDTO> v2ds(List<EquityBenefitGroupDO> list);

    @Mapping(target = "opAdminName", expression = "java(com.ddmc.equity.common.util.LongUtils.isTrue(req.getId()) ? null : req.getAdminName())")
    @Mapping(target = "opAdminId", expression = "java(com.ddmc.equity.common.util.LongUtils.isTrue(req.getId()) ? null : req.getAdminId())")
    @Mapping(target = "editAdminId", source = "adminId")
    @Mapping(target = "editAdminName", source = "adminName")
    @Mapping(target = "isDelete", constant = "0")
    @Mapping(target = "createTime", expression = "java(new java.util.Date())")
    @Mapping(target = "updateTime", expression = "java(new java.util.Date())")
    EquityBenefitGroupDO srd2e(BenefitGroupSaveReqDTO req);
}
