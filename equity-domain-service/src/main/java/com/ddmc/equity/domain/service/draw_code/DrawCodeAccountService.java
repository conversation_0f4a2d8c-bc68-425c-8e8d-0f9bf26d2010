package com.ddmc.equity.domain.service.draw_code;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ddmc.equity.infra.repository.dao.DrawCodeAccountDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/5/30 16:11
 * @description
 */
public interface DrawCodeAccountService extends IService<DrawCodeAccountDO> {

    /**
     * 创建抽签码权益子账户
     *
     * @param accountDOList DB_DOList
     */
    void batchInsertAccountDO(List<DrawCodeAccountDO> accountDOList);

    /**
     * 通过 userId、accountIds 更新抽签码权益子账户状态、最后一次操作的业务流水号
     *
     * @param userId       用户 id
     * @param accountIds   子账户 ids
     * @param sourceStatus 原始状态
     * @param targetStatus 目标状态
     * @param reqNo        业务流水号
     * @return 更新结果
     */
    boolean updateAccountsStatus(String userId, List<Long> accountIds, Integer sourceStatus, Integer targetStatus, String reqNo);

    /**
     * 通过 userId、子账户 id 查询权益子账户
     *
     * @param userId    用户 id
     * @param accountId 子账户 id
     * @return 权益子账户信息
     */
    DrawCodeAccountDO queryAccountByUserIdAndId(String userId, Long accountId);

    /**
     * 通过 userId 和抽签码 drawCodes 查询权益子账户列表
     *
     * @param userId    用户 id
     * @param drawCodes 抽签码。如果未指定抽签码，则查询所有
     * @return 权益子账户列表
     */
    List<DrawCodeAccountDO> queryAccountsByUserIdAndDrawCodes(String userId, List<String> drawCodes);

    /**
     * 通过 userId 和业务流水号查询权益子账户列表
     *
     * @param userId 用户 id
     * @param reqNo  业务流水号
     * @return 权益子账户列表
     */
    List<DrawCodeAccountDO> queryAccountsByUserIdAndReqNo(String userId, String reqNo);

    /**
     * 通过 userId 查询权益子账户列表
     *
     * @param userId    用户 id
     * @param benefitId 权益 id。如果未指定权益 id，则查询所有
     * @param sources   抽签码来源。如果未指定抽签码来源，则查询所有
     * @param statuses  权益子账户状态。如果未指定状态，则查询所有
     * @param startDate 开始时间。可以指定开始时间（创建时间）查询，不指定则查询所有
     * @param endDate   结束时间。可以指定结束时间（创建时间）查询，不指定则查询所有
     * @return 权益子账户列表
     */
    List<DrawCodeAccountDO> queryAccountsByUserId(String userId, Long benefitId, List<Integer> sources,
                                                  List<Integer> statuses, Date startDate, Date endDate);
}
