package com.ddmc.equity.domain.dto.account;

import com.ddmc.equity.model.vo.UniversalAccountDetailVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/23 15:13
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@ApiModel("通用权益账户操作流水-内部拓展信息")
public class UniversalAccountRecordInnerExtDTO {

    @ApiModelProperty("城市 code")
    private String cityCode;

    @ApiModelProperty("站点 id")
    private String stationId;

    /**
     * @see com.ddmc.equity.enums.ExternalTypeEnum
     */
    @ApiModelProperty(value = "外部活动类型")
    private Integer externalType;

    @ApiModelProperty(value = "外部活动 id")
    private String externalId;

    @ApiModelProperty(value = "策略外部关联 id。如果是来源玩法的活动，则为活动 prizeId；如果是膨胀券活动，则为母券券模板 id；")
    private String strategyExternalId;

    @ApiModelProperty("消耗权益，冻结记录 ID")
    private String consumeBenefitFreezeLogId;

    @ApiModelProperty("操作的权益账户明细 list")
    private List<OperateAccountDetailDTO> operateDetailDTOList;

    @ApiModelProperty("对应操作的权益账户明细 list")
    @Deprecated
    private List<UniversalAccountDetailVO> accountDetailVOList;
}
