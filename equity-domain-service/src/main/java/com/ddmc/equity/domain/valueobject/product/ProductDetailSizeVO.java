package com.ddmc.equity.domain.valueobject.product;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created on 2022/10/13.
 *
 * <AUTHOR>
 */
@Data
public class ProductDetailSizeVO implements Serializable {
    private static final long serialVersionUID = -8011099778256428456L;

    @ApiModelProperty("处理方式标题")
    @JsonProperty("title")
    private String title;

    @ApiModelProperty("属性名称")
    @JsonProperty("title_name")
    private String titleName;

    @ApiModelProperty("推荐理由")
    @JsonProperty("recommended_reason")
    private String recommendedReason;

    @ApiModelProperty("id")
    @JsonProperty("id")
    private Integer id;

    @ApiModelProperty("_id")
    @JsonProperty("_id")
    private String mongoId;

    /**
     * 处理方式商品增加缺重提示
     */
    @ApiModelProperty("缺重提示")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String prompt;


    private List<ProductDetailSizeValueVO> values;
}
