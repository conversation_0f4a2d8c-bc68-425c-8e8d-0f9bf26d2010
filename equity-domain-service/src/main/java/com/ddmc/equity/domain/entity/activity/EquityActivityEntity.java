package com.ddmc.equity.domain.entity.activity;

import com.ddmc.abtest.dto.LayerDTO;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.PromoActivityStatusEnum;
import com.ddmc.equity.common.util.ruleType.RuleTypeContext;
import com.ddmc.equity.domain.converter.activity.ActivityConverter;
import com.ddmc.equity.domain.entity.activityRule.EquityActivityRuleEntity;
import com.ddmc.equity.domain.entity.strategy.EquityStrategyEntity;
import com.ddmc.equity.domain.valueobject.activity.ActivityListFilterReqVO;
import com.ddmc.equity.dto.business.*;
import com.ddmc.equity.dto.business.provide.ProvideActivityDTO;
import com.ddmc.equity.enums.ActivitySendTypeEnum;
import com.ddmc.equity.infra.repository.dao.EquityActivityDO;
import com.ddmc.equity.infra.repository.dao.EquityActivityRuleDO;
import com.ddmc.promocore.admin.vo.ActivityVO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Getter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class EquityActivityEntity extends EquityActivityDO implements Serializable {
    private static final long serialVersionUID = -5731740362250073712L;

    @NotNull
    public static ActivityListFilterReqVO getActivityFilterByListReq(@NotNull ActivityListReqDTO req) {
        return ActivityConverter.INSTANCE.d2v(req);
    }

    @NotNull
    public static List<ActivityBusinessDTO> listActivityBusinessDTO(@NotNull List<EquityActivityDO> list) {
        return ActivityConverter.INSTANCE.v2ds(list);
    }

    public static @NotNull EquityActivityDO getActivityDOBySaveReq(@NotNull ActivitySaveReqDTO req) {
        return ActivityConverter.INSTANCE.srd2e(req);
    }

    public static ActivitySaveReqDTO getActivitySaveReqByActivityDO(EquityActivityDO activityDO) {
        return ActivityConverter.INSTANCE.d2d(activityDO);
    }

    public static EquityActivityDO universalActivityDTO2DO(UniversalActivityDTO dto) {
        return ActivityConverter.INSTANCE.dto2do(dto);
    }

    public EquityActivityDO convertToDO(ProvideActivityDTO provideActivityDTO, String sceneCode, String adminId, String adminName) {
        //convert 类
        EquityActivityDO equityActivityDO = new EquityActivityDO();
        equityActivityDO.setActivityName(provideActivityDTO.getActivityName());
        equityActivityDO.setStatus(provideActivityDTO.getStatus());
        equityActivityDO.setSendType(provideActivityDTO.getSendType());
        equityActivityDO.setExternalType(provideActivityDTO.getExternalType());
        equityActivityDO.setStartTime(provideActivityDTO.getStartTime());
        equityActivityDO.setEndTime(provideActivityDTO.getEndTime());
        String operateId = StringUtils.isBlank(adminId) ? Constants.DEFAULT_ADMIN_ID : adminId;
        String operateName = StringUtils.isBlank(adminName) ? Constants.DEFAULT_ADMIN_ID : adminName;
        equityActivityDO.setOpAdminId(operateId);
        equityActivityDO.setOpAdminName(operateName);
        equityActivityDO.setEditAdminId(operateId);
        equityActivityDO.setEditAdminName(operateName);
        equityActivityDO.setSceneCode(sceneCode);
        equityActivityDO.setId(provideActivityDTO.getActivityId());
        if (Objects.isNull(provideActivityDTO.getExternalType())) {
            equityActivityDO.setExternalType(Constants.STATUS_NO);
            equityActivityDO.setExternalId(UUID.randomUUID().toString().trim().replace("-", ""));
        } else {
            equityActivityDO.setExternalType(provideActivityDTO.getExternalType());
            equityActivityDO.setExternalId(provideActivityDTO.getExternalId());
        }
        return equityActivityDO;
    }


    /****
     * 创建活动DTO
     * @param externalType
     * @param activityDTO
     * @param equityActivityDO
     * @return
     */
    public ProvideActivityDTO createEquityActivityDO(Integer externalType, ActivityVO activityDTO, EquityActivityDO equityActivityDO) {
        ProvideActivityDTO provideActivityDTO = new ProvideActivityDTO();
        if (Objects.nonNull(equityActivityDO)) {
            provideActivityDTO.setActivityId(equityActivityDO.getId());
        }
        provideActivityDTO.setActivityName(activityDTO.getName());
        provideActivityDTO.setStartTime(activityDTO.getBeginTime());
        provideActivityDTO.setEndTime(activityDTO.getEndTime());
        provideActivityDTO.setExternalId(activityDTO.getActivityId());
        provideActivityDTO.setExternalType(externalType);
        Integer status = PromoActivityStatusEnum.GOING.getCode().equals(activityDTO.getState()) ? 1 : 2;
        provideActivityDTO.setStatus(status);
        provideActivityDTO.setSendType(ActivitySendTypeEnum.WHOLE.getCode());
        return provideActivityDTO;
    }

    public ActivitySaveRspDTO createActivitySaveRspDTO(EquityActivityDO equityActivityDO, List<EquityActivityRuleDO> equityActivityRuleDOList, LayerDTO layerDTO) {

        ActivitySaveRspDTO activitySaveRspDTO = ActivitySaveRspDTO.builder().build();
        activitySaveRspDTO.setId(equityActivityDO.getId());
        activitySaveRspDTO.setSceneCode(equityActivityDO.getSceneCode());
        activitySaveRspDTO.setActivityName(equityActivityDO.getActivityName());
        activitySaveRspDTO.setDescription(equityActivityDO.getDescription());
        activitySaveRspDTO.setStartTime(equityActivityDO.getStartTime());
        activitySaveRspDTO.setEndTime(equityActivityDO.getEndTime());
        activitySaveRspDTO.setSendType(equityActivityDO.getSendType());
        activitySaveRspDTO.setExternalType(equityActivityDO.getExternalType());
        activitySaveRspDTO.setExternalId(equityActivityDO.getExternalId());
        activitySaveRspDTO.setStatus(equityActivityDO.getStatus());
        activitySaveRspDTO.setAdminId(equityActivityDO.getOpAdminId());
        activitySaveRspDTO.setAdminName(equityActivityDO.getOpAdminName());
        RuleTypeContext ruleTypeContext = new RuleTypeContext();
        equityActivityRuleDOList.forEach(x -> ruleTypeContext.setRuleValue(x.getRuleType(), x.getRuleValue(), activitySaveRspDTO));
        if (Objects.nonNull(layerDTO) && CollectionUtils.isNotEmpty(layerDTO.getJoinGroups())) {
            com.ddmc.equity.dto.business.LayerDTO layerDTO1 = new com.ddmc.equity.dto.business.LayerDTO();
            List<TestGroupRspDTO> testGroupRspDTOList = layerDTO.getJoinGroups().stream().map(x -> {
                TestGroupRspDTO testGroupRspDTO = new TestGroupRspDTO();
                BeanUtils.copyProperties(x, testGroupRspDTO);
                return testGroupRspDTO;
            }).collect(Collectors.toList());
            layerDTO1.setTestGroupRspDTOList(testGroupRspDTOList);
            layerDTO1.setId(layerDTO.getAbId() + "");
            layerDTO1.setName(layerDTO.getName());
            activitySaveRspDTO.setLayerDTO(layerDTO1);
        }
        return activitySaveRspDTO;
    }

    public EquityStrategyEntity buildStrategyEntity() {
        return new EquityStrategyEntity();
    }

    public static EquityActivityRuleEntity buildActivityRuleEntity() {
        return new EquityActivityRuleEntity();
    }

    public boolean checkIfStartedAndNotEnd() {
        Date now = new Date();
        return CommonEnum.INTEGER_BOOL.NO.getCode().equals(this.getIsDelete())
                && this.getEndTime().after(now) && now.after(this.getStartTime());
    }
}
