package com.ddmc.equity.domain.dto.benefit_stock;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/30 14:41
 * @description
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class BenefitStockLimitInfo {

    /**
     * 策略 id
     */
    private Long strategyId;
    /**
     * 权益组 id
     */
    private Long benefitGroupId;
    /**
     * 权益 id
     */
    private Long benefitId;
}
