package com.ddmc.equity.domain.service.core;

import com.ddmc.equity.account.BenefitReceiveRuleContext;
import com.ddmc.equity.account.EquityAccountContext;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/28 17:33
 * @description
 */
public interface ReceiveAndStockLimitCoreService {

    /**
     * 扣减频次，活动维度
     *
     * @param equityAccountContext      权益账户上下文
     * @param benefitReceiveRuleContext 权益领取需要的频次限制、库存限制规则上下文
     * @return 频次流水号
     */
    boolean deductActivityReceiveLimit(EquityAccountContext equityAccountContext,
                                       BenefitReceiveRuleContext benefitReceiveRuleContext);

    /**
     * 释放频次，活动维度
     *
     * @param equityAccountContext      权益账户上下文
     * @param benefitReceiveRuleContext 权益领取需要的频次限制、库存限制规则上下文
     */
    void releaseActivityReceiveLimit(EquityAccountContext equityAccountContext,
                                     BenefitReceiveRuleContext benefitReceiveRuleContext);

    /**
     * 冻结频次，权益维度
     *
     * @param equityAccountContext      权益账户上下文
     * @param benefitReceiveRuleContext 权益领取需要的频次限制、库存限制规则上下文
     * @return 是否成功
     */
    boolean freezeBenefitReceiveLimit(EquityAccountContext equityAccountContext,
                                      BenefitReceiveRuleContext benefitReceiveRuleContext);

    /**
     * 频次回滚，权益维度
     *
     * @param equityAccountContext      权益账户上下文
     * @param benefitReceiveRuleContext 权益领取需要的频次限制、库存限制规则上下文
     */
    void releaseBenefitReceiveLimit(EquityAccountContext equityAccountContext,
                                    BenefitReceiveRuleContext benefitReceiveRuleContext);

    /**
     * 扣减频次，权益组维度（具体到权益组下的权益维度）
     *
     * @param equityAccountContext 权益账户上下文
     * @return 频次流水号
     */
    String deductBenefitReceiveLimit(EquityAccountContext equityAccountContext, BenefitReceiveRuleContext benefitReceiveRuleContext);

    /**
     * 扣减库存，权益维度
     *
     * @param equityAccountContext      权益账户上下文
     * @param benefitReceiveRuleContext 权益领取需要的频次限制、库存限制规则上下文
     * @return 是否成功
     */
    boolean deductBenefitStock(EquityAccountContext equityAccountContext,
                               BenefitReceiveRuleContext benefitReceiveRuleContext);

    /**
     * 释放库存，权益维度
     *
     * @param equityAccountContext      权益账户上下文
     * @param benefitReceiveRuleContext 权益领取需要的频次限制、库存限制规则上下文
     */
    void releaseStockLimit(EquityAccountContext equityAccountContext,
                           BenefitReceiveRuleContext benefitReceiveRuleContext);
}
