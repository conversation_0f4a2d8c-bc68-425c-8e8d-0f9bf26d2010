package com.ddmc.equity.domain.service.low_stock_alarm.impl;

import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.util.business.BenefitUtil;
import com.ddmc.equity.domain.dto.benefit_stock.LowStockAlarmContextDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/1/25 15:32
 * @description
 */
@Slf4j
@Component("defaultLowStockAlarm")
public class DefaultLowStockAlarm extends AbstractLowStockAlarm {

    @Override
    protected String getLowStockAlarmTimesKey(LowStockAlarmContextDTO contextDTO, String hitLowStockAlarmType) {
        String benefitUni = BenefitUtil.getBenefitUni(contextDTO.getActivityId(), contextDTO.getStrategyId(),
                contextDTO.getBenefitId());
        return String.format(CacheKeyConstants.LOW_STOCK_ALARM_TIMES_KEY, "default", benefitUni, hitLowStockAlarmType);
    }
}
