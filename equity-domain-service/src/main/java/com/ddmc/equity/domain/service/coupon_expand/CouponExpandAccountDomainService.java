package com.ddmc.equity.domain.service.coupon_expand;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ddmc.equity.infra.repository.dao.CouponExpandAccountDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/26 16:45
 * @description
 */
public interface CouponExpandAccountDomainService extends IService<CouponExpandAccountDO> {

    /**
     * 创建券膨胀权益子账户
     *
     * @param accountDO DB_DO
     */
    void insertAccountDO(CouponExpandAccountDO accountDO);

    /**
     * 通过 userId 和业务流水号查询权益子账户列表
     *
     * @param userId 用户 id
     * @param reqNo  业务流水号
     * @return 权益子账户列表
     */
    List<CouponExpandAccountDO> queryAccountsByUserIdAndReqNo(String userId, String reqNo);

    List<CouponExpandAccountDO> queryAccountsByUserId(String userId);

    List<CouponExpandAccountDO> queryAccountRecordByUserIdAndActivityIdsAndBegTimeAndEndTime(String userId,List<Long> activityIds, Date begDate, Date endDate);
}
