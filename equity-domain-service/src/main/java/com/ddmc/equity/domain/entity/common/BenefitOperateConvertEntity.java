package com.ddmc.equity.domain.entity.common;

import cn.hutool.core.bean.BeanUtil;
import com.ddmc.equity.account.DirectAccountContext;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.domain.converter.common.EngineContextConverter;
import com.ddmc.equity.domain.dto.DrawAndReceiveReqDTO;
import com.ddmc.equity.domain.dto.DrawAndReceiveRespDTO;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.domain.dto.InitActivityCountReqDTO;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.dto.SceneReceiveReqDTO;
import com.ddmc.equity.domain.dto.SceneReceiveRespDTO;
import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.engine.EngineResultContextDTO;
import com.ddmc.equity.domain.entity.account.UniversalAccountAmountCalEntity;
import com.ddmc.equity.domain.entity.benefit.BenefitConvertEntity;
import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.ddmc.equity.dto.customer.benefit.BenefitAccountRpcResponseExtDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.enums.StrategyTypeEnum;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.model.convert.BaseRequestConvert;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.equity.processor.risk.RiskControlContextDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/29 16:25
 * @description
 */
public class BenefitOperateConvertEntity {

    public static DirectAccountContext convertToInitActivityCountAccountContext(InitActivityCountReqDTO req,
                                                                                EquityBenefitDO benefitDO) {
        Integer benefitType = benefitDO.getBenefitType();
        String benefitValue = benefitDO.getBenefitValue();
        String realSendAmount = UniversalAccountAmountCalEntity.getRealSendAmount(benefitType, benefitValue,
                benefitDO.getBenefitAmountType(), req.getSendAmount());

        BaseRequestDTO baseRequestDTO = req.getBaseRequestDTO();
        String userId = baseRequestDTO.getUserId();
        Long useActivityId = req.getUseActivityId();
        DirectAccountContext directAccountContext = new DirectAccountContext();
        directAccountContext.setUserId(userId);
        directAccountContext.setUseActivityId(useActivityId);
        directAccountContext.setBenefitId(benefitDO.getId());
        directAccountContext.setBenefitType(benefitType);
        directAccountContext.setBenefitValue(benefitValue);
        directAccountContext.setBenefitName(benefitDO.getName());
        directAccountContext.setReqNo("init_activity_count" + Constants.UNDERLINE + userId + Constants.UNDERLINE + useActivityId);
        directAccountContext.setOperateType(OperateTypeEnum.PROVIDE.getCode());
        directAccountContext.setSendAmount(realSendAmount);
        directAccountContext.setOperateCount(UniversalAccountAmountCalEntity.getReceivedCount(benefitType, realSendAmount));
        directAccountContext.setAppId(req.getAppId());
        directAccountContext.setPageId(req.getPageId());
        directAccountContext.setSource(req.getSource());
        return directAccountContext;
    }

    public static EngineContextDTO convertToDrawEngineContextDTO(DrawAndReceiveReqDTO req) {
        String externalStrategyId = req.getExternalStrategyId();
        BaseRequestDTO baseRequestDTO = req.getBaseRequestDTO();
        EngineContextDTO engineContextDTO = EngineContextConverter.INSTANCE.convertToEngineContextDTO(baseRequestDTO);
        engineContextDTO.setAppId(req.getAppId());
        engineContextDTO.setPageId(req.getPageId());
        engineContextDTO.setSource(req.getSource());
        engineContextDTO.setActivityIdList(Lists.newArrayList(req.getActivityId()));
        engineContextDTO.setStrategyExternalIdList(StringUtils.isBlank(externalStrategyId) ? null : Lists.newArrayList(externalStrategyId));
        // 领取时调用咨询，相当于是领取（需要走领取时的规则校验，因为后续翻牌领取时不再调用咨询接口）
        engineContextDTO.setIsReceive(Boolean.TRUE);
        engineContextDTO.setNeedUnableReceiveReason(Boolean.TRUE);
        return engineContextDTO;
    }

    public static EquityAccountContext convertToReceiveEquityAccountContext(DrawAndReceiveReqDTO req,
                                                                            FullBenefitInfoDTO benefitInfoDTO,
                                                                            Set<String> sendTicketNeedExternalScenes) {
        BaseRequestDTO baseRequestDTO = req.getBaseRequestDTO();
        EquityAccountContext equityAccountContext = new EquityAccountContext();
        equityAccountContext.setAppId(req.getAppId());
        equityAccountContext.setPageId(req.getPageId());
        equityAccountContext.setSource(req.getSource());
        equityAccountContext.setCityCode(baseRequestDTO.getCityNumber());
        equityAccountContext.setStationId(baseRequestDTO.getStationId());
        equityAccountContext.setUid(baseRequestDTO.getUserId());
        equityAccountContext.setSerialNumber(req.getReqNo());
        // 设置操作人为当前用户 id
        equityAccountContext.setOperator(baseRequestDTO.getUserId());

        equityAccountContext.setSceneCode(req.getSceneCode());
        equityAccountContext.setActivityId(benefitInfoDTO.getActivityId());
        equityAccountContext.setExternalType(benefitInfoDTO.getExternalType());
        equityAccountContext.setExternalId(benefitInfoDTO.getExternalId());
        equityAccountContext.setActivityName(benefitInfoDTO.getActivityName());
        equityAccountContext.setStrategyId(benefitInfoDTO.getStrategyId());
        equityAccountContext.setStrategyExternalId(benefitInfoDTO.getStrategyExternalId());
        equityAccountContext.setBenefitGroupId(benefitInfoDTO.getBenefitGroupId());
        equityAccountContext.setBenefitId(benefitInfoDTO.getBenefitId());
        equityAccountContext.setBenefitType(benefitInfoDTO.getBenefitType());
        equityAccountContext.setEquityValue(benefitInfoDTO.getBenefitValue());
        equityAccountContext.setBenefitName(benefitInfoDTO.getBenefitName());
        equityAccountContext.setSendAmount(benefitInfoDTO.getSendAmount());
        equityAccountContext.setBalanceMoney(benefitInfoDTO.getBalanceMoney());
        equityAccountContext.setUseActivityId(req.getUseActivityId());

        equityAccountContext.setConsumeBenefitType(req.getConsumeBenefitType());
        equityAccountContext.setConsumeBenefitAmount(req.getConsumeBenefitAmount());
        equityAccountContext.setRpcReqCustomMap(req.getRpcReqCustomMap());
        equityAccountContext.setSendPointScene(req.getSendPointScene());
        equityAccountContext.setSendPointDesc(req.getSendPointDesc());
        equityAccountContext.setSendPointSource(req.getSendPointSource());
        equityAccountContext.setSendTicketScene(req.getSendTicketScene());
        // 指定的场景调用发放优惠券需要关联外部 id
        if (CollectionUtils.isNotEmpty(sendTicketNeedExternalScenes)
                && sendTicketNeedExternalScenes.contains(req.getSceneCode())) {
            equityAccountContext.setSendTicketActivity(benefitInfoDTO.getExternalId());
            equityAccountContext.setSendTicketPrize(benefitInfoDTO.getStrategyExternalId());
        }
        equityAccountContext.setSendBalanceScene(req.getSendBalanceScene());
        equityAccountContext.setSendBalanceDesc(req.getSendBalanceDesc());
        equityAccountContext.setSendVipDaysScene(req.getSendVipDaysScene());
        return equityAccountContext;
    }

    public static RiskControlContextDTO convertToRiskControlContextDTOBefore(DrawAndReceiveReqDTO req,
                                                                             FullBenefitInfoDTO benefitInfoDTO) {
        BaseRequestDTO baseRequestDTO = BaseRequestConvert.INSTANCE.convertToBaseRequestDTO(req.getBaseRequestDTO());
        return RiskControlContextDTO.builder()
                .baseRequestDTO(baseRequestDTO)
                .sceneCode(req.getRiskSceneCode())
                .activityId(String.valueOf(benefitInfoDTO.getActivityId()))
                .activityName(benefitInfoDTO.getActivityName())
                .benefitInfoDTO(benefitInfoDTO)
                .riskSceneStrategyDataMap(req.getRiskSceneStrategyDataMap())
                .build();
    }

    public static RiskControlContextDTO convertToRiskControlContextDTOAfter(DrawAndReceiveReqDTO req,
                                                                            FullBenefitInfoDTO benefitInfoDTO) {
        // 领取后调用风控无须再传 headers
        BaseRequestDTO baseRequestDTO = BaseRequestConvert.INSTANCE.convertToBaseRequestDTOIgnoreHeaders(req.getBaseRequestDTO());
        return RiskControlContextDTO.builder()
                .baseRequestDTO(baseRequestDTO)
                .sceneCode(req.getRiskSceneCodeAfter())
                .activityId(String.valueOf(benefitInfoDTO.getActivityId()))
                .activityName(benefitInfoDTO.getActivityName())
                .benefitInfoDTO(benefitInfoDTO)
                .riskSceneStrategyDataMap(req.getRiskSceneStrategyDataMap())
                .build();
    }

    public static RiskControlContextDTO convertToRiskControlContextBefore(SceneReceiveReqDTO req,
                                                                          FullBenefitInfoDTO benefitInfoDTO) {
        BaseRequestDTO baseRequestDTO = BaseRequestConvert.INSTANCE.convertToBaseRequestDTO(req.getBaseRequestDTO());
        return RiskControlContextDTO.builder()
                .baseRequestDTO(baseRequestDTO)
                .sceneCode(req.getRiskSceneCode())
                .activityId(String.valueOf(benefitInfoDTO.getActivityId()))
                .activityName(benefitInfoDTO.getActivityName())
                .benefitInfoDTO(benefitInfoDTO)
                .riskSceneStrategyDataMap(req.getRiskSceneStrategyDataMap())
                .receiveExternalInfoDTO(req.getReceiveExternalInfoDTO())
                .build();
    }

    public static RiskControlContextDTO convertToRiskControlContextAfter(SceneReceiveReqDTO req,
                                                                         FullBenefitInfoDTO benefitInfoDTO) {
        // 领取后调用风控无须再传 headers
        BaseRequestDTO baseRequestDTO = BaseRequestConvert.INSTANCE.convertToBaseRequestDTOIgnoreHeaders(req.getBaseRequestDTO());
        return RiskControlContextDTO.builder()
                .baseRequestDTO(baseRequestDTO)
                .sceneCode(req.getRiskSceneCodeAfter())
                .activityId(String.valueOf(benefitInfoDTO.getActivityId()))
                .activityName(benefitInfoDTO.getActivityName())
                .benefitInfoDTO(benefitInfoDTO)
                .riskSceneStrategyDataMap(req.getRiskSceneStrategyDataMap())
                .receiveExternalInfoDTO(req.getReceiveExternalInfoDTO())
                .build();
    }

    public static String convertToBenefitEventName(FullBenefitInfoDTO benefitInfoDTO) {
        String strategyTypeName = Optional.ofNullable(benefitInfoDTO.getStrategyType())
                .map(StrategyTypeEnum::getByCode).map(Enum::name).orElse("null");
        String benefitTypeName = Optional.ofNullable(benefitInfoDTO.getBenefitType())
                .map(BenefitTypeEnum::getById).map(Enum::name).orElse("null");
        return StringUtils.lowerCase(strategyTypeName + Constants.UNDERLINE + benefitTypeName);
    }

    public static DrawAndReceiveRespDTO convertToDrawAndReceiveRespDTO(FullBenefitInfoDTO receiveBenefit,
                                                                       ReceiveBenefitResDTO sceneReceiveRes) {
        Integer receiveResultStatus = Optional.ofNullable(sceneReceiveRes).map(ReceiveBenefitResDTO::getStatusEnum)
                .map(StatusEnum::getCode).orElse(null);
        Long accountDetailId = Optional.ofNullable(sceneReceiveRes).map(ReceiveBenefitResDTO::getAccountDetailId).orElse(null);
        Integer benefitType = receiveBenefit.getBenefitType();

        return DrawAndReceiveRespDTO.builder()
                .receiveBenefit(receiveBenefit)
                .receiveResultStatus(receiveResultStatus)
                .accountDetailId(accountDetailId)
                .userTicketId(getUserTicketId(benefitType, sceneReceiveRes))
                .userTicketPackageId(getUserTicketPackageId(benefitType, sceneReceiveRes))
                .rpcResponseExtDTO(Objects.isNull(sceneReceiveRes) ? null : sceneReceiveRes.getRpcResponseExtDTO())
                .build();
    }

    public static EngineContextDTO convertToConsultEngineContextDTO(SceneReceiveReqDTO req) {
        BaseRequestDTO baseRequestDTO = req.getBaseRequestDTO();
        EngineContextDTO engineContextDTO = EngineContextConverter.INSTANCE.convertToEngineContextDTO(baseRequestDTO);
        engineContextDTO.setAppId(req.getAppId());
        engineContextDTO.setPageId(req.getPageId());
        engineContextDTO.setSource(req.getSource());
        engineContextDTO.setActivityIdList(Lists.newArrayList(req.getActivityId()));
        engineContextDTO.setStrategyExternalIdList(Lists.newArrayList(req.getExternalStrategyId()));
        // 领取时调用咨询，相当于是领取（需要走领取时的规则校验，因为后续翻牌领取时不再调用咨询接口）
        engineContextDTO.setIsReceive(Boolean.TRUE);
        engineContextDTO.setNeedUnableReceiveReason(Boolean.TRUE);
        return engineContextDTO;
    }

    public static EquityAccountContext convertToReceiveEquityAccountContext(SceneReceiveReqDTO req,
                                                                            FullBenefitInfoDTO benefitInfoDTO,
                                                                            Set<String> sendTicketNeedExternalScenes) {
        BaseRequestDTO baseRequestDTO = req.getBaseRequestDTO();
        String userId = baseRequestDTO.getUserId();
        EquityAccountContext equityAccountContext = new EquityAccountContext();
        equityAccountContext.setAppId(req.getAppId());
        equityAccountContext.setPageId(req.getPageId());
        equityAccountContext.setSource(req.getSource());
        equityAccountContext.setSerialNumber(req.getReqNo());
        equityAccountContext.setCityCode(baseRequestDTO.getCityNumber());
        equityAccountContext.setStationId(baseRequestDTO.getStationId());
        equityAccountContext.setLongitude(baseRequestDTO.getLongitude());
        equityAccountContext.setLatitude(baseRequestDTO.getLatitude());
        equityAccountContext.setUid(userId);

        equityAccountContext.setSceneCode(req.getSceneCode());
        equityAccountContext.setUseActivityId(req.getUseActivityId());
        equityAccountContext.setActivityId(benefitInfoDTO.getActivityId());
        equityAccountContext.setExternalType(benefitInfoDTO.getExternalType());
        equityAccountContext.setExternalId(benefitInfoDTO.getExternalId());
        equityAccountContext.setActivityName(benefitInfoDTO.getActivityName());
        equityAccountContext.setStrategyId(benefitInfoDTO.getStrategyId());
        equityAccountContext.setStrategyExternalId(benefitInfoDTO.getStrategyExternalId());
        equityAccountContext.setBenefitGroupId(benefitInfoDTO.getBenefitGroupId());
        equityAccountContext.setBenefitId(benefitInfoDTO.getBenefitId());
        equityAccountContext.setBenefitName(benefitInfoDTO.getBenefitName());
        equityAccountContext.setBenefitType(benefitInfoDTO.getBenefitType());
        equityAccountContext.setEquityValue(benefitInfoDTO.getBenefitValue());
        equityAccountContext.setSendAmount(benefitInfoDTO.getSendAmount());
        equityAccountContext.setBalanceMoney(benefitInfoDTO.getBalanceMoney());
        // 设置操作人为当前用户 id
        equityAccountContext.setOperator(userId);

        equityAccountContext.setReceiveExternalInfoDTO(req.getReceiveExternalInfoDTO());
        equityAccountContext.setRpcReqCustomMap(req.getRpcReqCustomMap());
        equityAccountContext.setSendPointScene(req.getSendPointScene());
        equityAccountContext.setSendPointDesc(req.getSendPointDesc());
        equityAccountContext.setSendPointSource(req.getSendPointSource());
        equityAccountContext.setSendBalanceScene(req.getSendBalanceScene());
        equityAccountContext.setSendBalanceDesc(req.getSendBalanceDesc());
        equityAccountContext.setSendTicketScene(req.getSendTicketScene());
        // 指定的场景调用发放优惠券需要关联外部 id
        if (CollectionUtils.isNotEmpty(sendTicketNeedExternalScenes) && sendTicketNeedExternalScenes.contains(req.getSceneCode())) {
            equityAccountContext.setSendTicketActivity(benefitInfoDTO.getExternalId());
            equityAccountContext.setSendTicketPrize(benefitInfoDTO.getStrategyExternalId());
        }
        equityAccountContext.setSendVipDaysScene(req.getSendVipDaysScene());
        return equityAccountContext;
    }

    public static UnableReceiveBenefitDTO filterUnableReceiveBenefit(EngineResultContextDTO resultContextDTO,
                                                                     Long activityId, String externalStrategyId,
                                                                     Long benefitId) {
        List<UnableReceiveBenefitDTO> unableReceiveBenefitDTOList = resultContextDTO.getUnableReceiveBenefitDTOList();
        if (CollectionUtils.isEmpty(unableReceiveBenefitDTOList)) {
            return null;
        }
        return unableReceiveBenefitDTOList.stream()
                .filter(e -> Objects.equals(e.getActivityId(), activityId))
                .filter(e -> StringUtils.equals(e.getStrategyExternalId(), externalStrategyId))
                .filter(e -> Objects.equals(e.getBenefitId(), benefitId))
                .findFirst().orElse(null);
    }

    public static FullBenefitInfoDTO filterCanReceiveBenefit(List<SceneActivityCacheDto> activities,
                                                             Long activityId, String externalStrategyId,
                                                             Long benefitId) {
        List<FullBenefitInfoDTO> fullBenefitInfoDTOList = BenefitConvertEntity.convertToFullBenefitInfoDTOList(activities);
        if (CollectionUtils.isEmpty(fullBenefitInfoDTOList)) {
            return null;
        }
        return fullBenefitInfoDTOList.stream()
                .filter(e -> Objects.equals(e.getActivityId(), activityId))
                .filter(e -> StringUtils.equals(e.getStrategyExternalId(), externalStrategyId))
                .filter(e -> Objects.equals(e.getBenefitId(), benefitId))
                .findFirst().orElse(null);
    }

    public static SceneReceiveRespDTO convertToSceneReceiveRespDTO(FullBenefitInfoDTO receiveBenefit,
                                                                   ReceiveBenefitResDTO sceneReceiveRes) {
        Integer benefitType = receiveBenefit.getBenefitType();
        return SceneReceiveRespDTO.builder()
                .receiveBenefit(receiveBenefit)
                .receiveResultStatus(Optional.ofNullable(sceneReceiveRes)
                        .map(ReceiveBenefitResDTO::getStatusEnum).map(StatusEnum::getCode).orElse(StatusEnum.FAIL.getCode()))
                .accountDetailId(Optional.ofNullable(sceneReceiveRes)
                        .map(ReceiveBenefitResDTO::getAccountDetailId).orElse(null))
                .userTicketId(getUserTicketId(benefitType, sceneReceiveRes))
                .userTicketPackageId(getUserTicketPackageId(benefitType, sceneReceiveRes))
                .rpcResponseExtDTO(Objects.isNull(sceneReceiveRes) ? null : sceneReceiveRes.getRpcResponseExtDTO())
                .build();
    }

    public static void fillReceiveBenefitSendAmount(FullBenefitInfoDTO receiveBenefit, String reqSendAmount) {
        Integer benefitType = receiveBenefit.getBenefitType();
        String realSendAmount = UniversalAccountAmountCalEntity.getRealSendAmount(benefitType,
                receiveBenefit.getBenefitValue(), receiveBenefit.getBenefitAmountType(), receiveBenefit.getMaxAmount(),
                reqSendAmount);
        receiveBenefit.setSendAmount(realSendAmount);
        receiveBenefit.setBalanceMoney(UniversalAccountAmountCalEntity.getReceivedBalanceMoneyJudgedType(benefitType, realSendAmount));
    }

    public static void changeReceiveBenefitSendAmount(FullBenefitInfoDTO receiveBenefit,
                                                      EquityAccountContext equityAccountContext) {
        String resultSendAmount = equityAccountContext.getSendAmount();
        if (Objects.nonNull(resultSendAmount) && !Objects.equals(resultSendAmount, receiveBenefit.getSendAmount())) {
            receiveBenefit.setSendAmount(resultSendAmount);
        }
        BigDecimal resultBalanceMoney = equityAccountContext.getBalanceMoney();
        if (Objects.nonNull(resultBalanceMoney) && !Objects.equals(resultBalanceMoney, receiveBenefit.getBalanceMoney())) {
            receiveBenefit.setBalanceMoney(resultBalanceMoney);
        }
    }

    private static String getUserTicketId(Integer benefitType, ReceiveBenefitResDTO receiveResp) {
        return Objects.equals(benefitType, BenefitTypeEnum.TICKET.getId()) ?
                Optional.ofNullable(receiveResp).map(ReceiveBenefitResDTO::getThirdResNo).orElse(null) : null;
    }

    private static String getUserTicketPackageId(Integer benefitType, ReceiveBenefitResDTO receiveResp) {
        return Objects.equals(benefitType, BenefitTypeEnum.TICKET_PACKAGE.getId()) ?
                Optional.ofNullable(receiveResp).map(ReceiveBenefitResDTO::getThirdResNo).orElse(null) : null;
    }

    public static BenefitAccountRpcResponseExtDTO convertToBenefitAccountRpcResponseExtDTO(ReceiveBenefitResDTO receiveResp) {
        if (Objects.isNull(receiveResp) || Objects.isNull(receiveResp.getRpcResponseExtDTO())) {
            return null;
        }
        return BeanUtil.copyProperties(receiveResp.getRpcResponseExtDTO(), BenefitAccountRpcResponseExtDTO.class);
    }


    public static InitActivityCountReqDTO convertToInitActivityCountReqDTO(EngineContextDTO engineContextDTO, SceneActivityCacheDto activity, String applicationName) {
        BaseRequestDTO baseRequestDTO = new BaseRequestDTO();
        baseRequestDTO.setUserId(engineContextDTO.getUserId());
        Map<String, Object> extInfoMap = JsonUtil.parseToMap(activity.getExtInfo());
        Long benefitId = Long.valueOf(String.valueOf(extInfoMap.get("benefitId")));
        String sendAmount = String.valueOf(extInfoMap.get("initActivityCount"));
        return InitActivityCountReqDTO.builder()
                .baseRequestDTO(baseRequestDTO)
                .appId(applicationName)
                .source("init")
                .useActivityId(activity.getActivityId())
                .benefitId(benefitId)
                .sendAmount(sendAmount)
                .build();
    }
}
