package com.ddmc.equity.domain.converter.common;

import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, imports = {StringUtils.class, Lists.class})
public interface EngineContextConverter {

    EngineContextConverter INSTANCE = Mappers.getMapper(EngineContextConverter.class);

    EngineContextDTO convertToEngineContextDTO(BaseRequestDTO baseRequestDTO);
}
