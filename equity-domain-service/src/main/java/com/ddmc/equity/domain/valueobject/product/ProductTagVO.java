package com.ddmc.equity.domain.valueobject.product;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Map;

/**
 * Created on 2022/10/14.
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class ProductTagVO {


    /**
     * 目前有
     * 储存条件| 1301：冷冻-温层1, 1302：冷鲜-温层2, 1602：冰鲜-温层2, 1803：冰镇-温层3, 1700：热食
     * 口味| 2201：微甜，2202：清甜，2203：蜜甜，2301：微辣，2302：中辣，2303：重辣
     */
    @ApiModelProperty("type")
    private String type;

    @ApiModelProperty("name")
    private String name;

    /**
     * 图片地址
     * 示例：
     *      "large":"http://img.ddimg.mobi/a55b6c0ed56af1622172317422.png",
     * 		"middle":"http://img.ddimg.mobi/349a1ebd1b1991622172329876.png",
     * 		"small":"http://img.ddimg.mobi/4219d7a7b1dbf1622172342356.png"
     */
    @ApiModelProperty("图片地址:大中小")
    @JsonProperty("url_map")
    private Map<String, String> urlMap;

}