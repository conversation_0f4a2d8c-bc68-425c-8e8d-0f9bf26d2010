package com.ddmc.equity.domain.service.activityStockFlow.impl;

import com.ddmc.equity.domain.service.activityStockFlow.ActivityStockFlowService;
import com.ddmc.equity.infra.repository.dao.ActivityStockFlowDO;
import com.ddmc.equity.infra.repository.dao.mapper.ActivityStockFlowMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ActivityStockFlowServiceImpl implements ActivityStockFlowService {

    @Resource
    private ActivityStockFlowMapper activityStockFlowMapper;

    @Override
    public int insertActivityStockFlow(ActivityStockFlowDO activityStockFlowDO) {
        return activityStockFlowMapper.insert(activityStockFlowDO);
    }

    @Override
    public int updateActivityStockFlow(ActivityStockFlowDO activityStockFlowDO) {
        return activityStockFlowMapper.updateById(activityStockFlowDO);
    }

    @Override
    public int updateActivityStockFlowBatch(List<ActivityStockFlowDO> activityStockFlowDOList) {
        return activityStockFlowMapper.insertBatchSomeColumn(activityStockFlowDOList);
    }
}
