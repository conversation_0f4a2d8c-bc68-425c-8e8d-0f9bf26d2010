package com.ddmc.equity.domain.service.cash.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.domain.service.cash.CashCouponAccountDomainService;
import com.ddmc.equity.infra.repository.dao.CashCouponAccountDO;
import com.ddmc.equity.infra.repository.dao.mapper.CashCouponAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
public class CashCouponAccountDomainServiceImpl extends ServiceImpl<CashCouponAccountMapper, CashCouponAccountDO>
        implements CashCouponAccountDomainService {

    @Override
    public void insertAccountDO(CashCouponAccountDO accountDO) {
        this.save(accountDO);
    }

    @Override
    public List<CashCouponAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds,
                                                           List<Integer> statuses, Date startDate, Date endDate) {
        Wrapper<CashCouponAccountDO> wrapper = Wrappers.<CashCouponAccountDO>lambdaQuery()
                .eq(CashCouponAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(CashCouponAccountDO::getUserId, userId)
                .in(CollectionUtils.isNotEmpty(activityIds), CashCouponAccountDO::getActivityId, activityIds)
                .in(CollectionUtils.isNotEmpty(statuses), CashCouponAccountDO::getStatus, statuses)
                .ge(Objects.nonNull(startDate), CashCouponAccountDO::getCreateTime, startDate)
                .le(Objects.nonNull(endDate), CashCouponAccountDO::getCreateTime, endDate)
                .orderByDesc(CashCouponAccountDO::getId);
        return this.list(wrapper);
    }
}
