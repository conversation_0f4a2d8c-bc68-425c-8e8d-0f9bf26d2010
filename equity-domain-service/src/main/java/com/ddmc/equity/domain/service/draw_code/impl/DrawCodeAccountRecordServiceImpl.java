package com.ddmc.equity.domain.service.draw_code.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.service.draw_code.DrawCodeAccountRecordService;
import com.ddmc.equity.infra.repository.dao.DrawCodeAccountRecordDO;
import com.ddmc.equity.infra.repository.dao.mapper.DrawCodeAccountRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/5/30 16:12
 * @description
 */
@Slf4j
@Service
public class DrawCodeAccountRecordServiceImpl extends ServiceImpl<DrawCodeAccountRecordMapper, DrawCodeAccountRecordDO> implements DrawCodeAccountRecordService {

    @Override
    public void insertAccountRecordDO(DrawCodeAccountRecordDO accountRecordDO) {
        Assert.notNull(accountRecordDO, ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户操作流水不能为空");
        Assert.mustTrue(StringUtils.isNotBlank(accountRecordDO.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户操作流水 userId 不能为空");
        this.save(accountRecordDO);
    }

    @Override
    public boolean updateAccountRecordStatusAndRpcResult(String userId, Long accountRecordId, Long accountId,
                                                         Integer status, EquityRpcDto equityRpcDto) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户操作流水 userId 不能为空");
        Assert.notNull(accountRecordId, ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户操作流水 accountRecordId 不能为空");
        Assert.mustTrue(StatusEnum.isContain(status), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户操作流水状态异常");

        DrawCodeAccountRecordDO update = new DrawCodeAccountRecordDO();
        update.setAccountId(Objects.nonNull(accountId) ? accountId : null);
        update.setStatus(status);
        update.setAuxKey(Objects.equals(StatusEnum.FAIL.getCode(), status) ? String.valueOf(accountRecordId) : null);
        update.setRpcCode(Objects.nonNull(equityRpcDto) ? equityRpcDto.getCode() : null);
        update.setRpcMsg(Objects.nonNull(equityRpcDto) ? equityRpcDto.getMessage() : null);
        return this.update(update, Wrappers.<DrawCodeAccountRecordDO>lambdaUpdate()
                .eq(DrawCodeAccountRecordDO::getUserId, userId)
                .eq(DrawCodeAccountRecordDO::getId, accountRecordId));
    }

    @Override
    public DrawCodeAccountRecordDO queryAccountRecordByUniqueKeyAndStatuses(String userId, Integer operateType,
                                                                            String reqNo, List<Integer> statuses) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户操作流水 userId 不能为空");
        Assert.mustTrue(OperateTypeEnum.contains(operateType), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户操作流水操作类型异常");
        Assert.mustTrue(StringUtils.isNotBlank(reqNo), ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码权益子账户操作流水 reqNo 不能为空");

        Wrapper<DrawCodeAccountRecordDO> wrapper = Wrappers.<DrawCodeAccountRecordDO>lambdaQuery()
                .eq(DrawCodeAccountRecordDO::getUserId, userId)
                .eq(DrawCodeAccountRecordDO::getOperateType, operateType)
                .eq(DrawCodeAccountRecordDO::getReqNo, reqNo)
                .eq(DrawCodeAccountRecordDO::getAuxKey, reqNo)
                .in(CollectionUtils.isNotEmpty(statuses), DrawCodeAccountRecordDO::getStatus, statuses);
        return this.getOne(wrapper);
    }
}
