package com.ddmc.equity.domain.service.receive_limit.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.domain.service.receive_limit.UserActivityReceiveLimitMapperProxy;
import com.ddmc.equity.domain.service.receive_limit.config.UserReceiveLimitTableSwitchConfig;
import com.ddmc.equity.infra.repository.dao.NewUserActivityReceiveLimitDO;
import com.ddmc.equity.infra.repository.dao.UserActivityReceiveLimitDO;
import com.ddmc.equity.infra.repository.dao.mapper.NewUserActivityReceiveLimitMapper;
import com.ddmc.equity.infra.repository.dao.mapper.UserActivityReceiveLimitMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 用户活动领取频次限制表 Mapper 代理实现类
 * 用于新老表切换过程中的数据操作代理
 */
@Slf4j
@Component
public class UserActivityReceiveLimitMapperProxyImpl implements UserActivityReceiveLimitMapperProxy {

    /**
     * 限制维度：活动
     */
    private static final String LIMIT_DIMENSION = "activity";

    @Resource
    private UserReceiveLimitTableSwitchConfig tableSwitchConfig;
    @Resource
    private UserActivityReceiveLimitMapper userActivityReceiveLimitMapper;
    @Resource
    private NewUserActivityReceiveLimitMapper newUserActivityReceiveLimitMapper;

    @Override
    public UserActivityReceiveLimitDO queryByDate(String userId, Long activityId, Date date) {
        if (tableSwitchConfig.shouldUseOldTable(activityId, LIMIT_DIMENSION, "queryByDate")) {
            Wrapper<UserActivityReceiveLimitDO> wrapper = Wrappers.<UserActivityReceiveLimitDO>lambdaQuery()
                    .eq(UserActivityReceiveLimitDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                    .eq(UserActivityReceiveLimitDO::getActivityId, activityId)
                    .eq(UserActivityReceiveLimitDO::getUserId, userId)
                    .le(UserActivityReceiveLimitDO::getStartTime, date)
                    .ge(UserActivityReceiveLimitDO::getEndTime, date);
            return userActivityReceiveLimitMapper.selectOne(wrapper);
        }

        Wrapper<NewUserActivityReceiveLimitDO> newWrapper = Wrappers.<NewUserActivityReceiveLimitDO>lambdaQuery()
                .eq(NewUserActivityReceiveLimitDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(NewUserActivityReceiveLimitDO::getUserId, userId)
                .eq(NewUserActivityReceiveLimitDO::getActivityId, activityId)
                .le(NewUserActivityReceiveLimitDO::getStartTime, date)
                .ge(NewUserActivityReceiveLimitDO::getEndTime, date);
        NewUserActivityReceiveLimitDO newReceiveLimitDO = newUserActivityReceiveLimitMapper.selectOne(newWrapper);
        return convertToOldModel(newReceiveLimitDO);
    }

    @Override
    public UserActivityReceiveLimitDO queryByReceiveLimitId(Long id, String userId, Long activityId) {
        if (tableSwitchConfig.shouldUseOldTable(activityId, LIMIT_DIMENSION, "queryByReceiveLimitId")) {
            Wrapper<UserActivityReceiveLimitDO> wrapper = Wrappers.<UserActivityReceiveLimitDO>lambdaQuery()
                    .eq(UserActivityReceiveLimitDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                    .eq(UserActivityReceiveLimitDO::getActivityId, activityId)
                    .eq(UserActivityReceiveLimitDO::getUserId, userId)
                    .eq(UserActivityReceiveLimitDO::getId, id);
            return userActivityReceiveLimitMapper.selectOne(wrapper);
        }

        Wrapper<NewUserActivityReceiveLimitDO> newWrapper = Wrappers.<NewUserActivityReceiveLimitDO>lambdaQuery()
                .eq(NewUserActivityReceiveLimitDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(NewUserActivityReceiveLimitDO::getUserId, userId)
                .eq(NewUserActivityReceiveLimitDO::getActivityId, activityId)
                .eq(NewUserActivityReceiveLimitDO::getId, id);
        NewUserActivityReceiveLimitDO newReceiveLimitDO = newUserActivityReceiveLimitMapper.selectOne(newWrapper);
        return convertToOldModel(newReceiveLimitDO);
    }

    @Override
    public int insert(UserActivityReceiveLimitDO receiveLimitDO) {
        if (tableSwitchConfig.shouldUseOldTable(receiveLimitDO.getActivityId(), LIMIT_DIMENSION, "insert")) {
            return userActivityReceiveLimitMapper.insert(receiveLimitDO);
        }

        NewUserActivityReceiveLimitDO newReceiveLimitDO = convertToNewModel(receiveLimitDO);
        int insertResult = newUserActivityReceiveLimitMapper.insert(newReceiveLimitDO);
        // 插入完成后，需要回写 receiveLimitDO 的 ID
        receiveLimitDO.setId(newReceiveLimitDO.getId());
        return insertResult;
    }

    @Override
    public int deduct(Long id, String userId, Long activityId, Long count, Long version) {
        if (tableSwitchConfig.shouldUseOldTable(activityId, LIMIT_DIMENSION, "deduct")) {
            return userActivityReceiveLimitMapper.deduct(activityId, id, count, version);
        }

        return newUserActivityReceiveLimitMapper.deduct(id, userId, activityId, count, version);
    }

    @Override
    public int release(Long id, String userId, Long activityId, Long count, Long version) {
        if (tableSwitchConfig.shouldUseOldTable(activityId, LIMIT_DIMENSION, "release")) {
            return userActivityReceiveLimitMapper.release(activityId, id, count, version);
        }

        return newUserActivityReceiveLimitMapper.release(id, userId, activityId, count, version);
    }

    /**
     * 将旧表数据模型转换为新表数据模型
     *
     * @param oldModel 旧表数据模型
     * @return 新表数据模型
     */
    private NewUserActivityReceiveLimitDO convertToNewModel(UserActivityReceiveLimitDO oldModel) {
        return oldModel == null ? null : BeanUtil.copyProperties(oldModel, NewUserActivityReceiveLimitDO.class);
    }

    /**
     * 将新表数据模型转换为旧表数据模型
     *
     * @param newModel 新表数据模型
     * @return 旧表数据模型
     */
    private UserActivityReceiveLimitDO convertToOldModel(NewUserActivityReceiveLimitDO newModel) {
        return newModel == null ? null : BeanUtil.copyProperties(newModel, UserActivityReceiveLimitDO.class);
    }
}