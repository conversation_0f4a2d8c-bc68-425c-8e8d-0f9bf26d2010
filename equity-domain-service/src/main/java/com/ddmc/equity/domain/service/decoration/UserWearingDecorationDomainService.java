package com.ddmc.equity.domain.service.decoration;

import com.ddmc.equity.dto.customer.decoration.UnwearDecorationReqDTO;
import com.ddmc.equity.dto.customer.decoration.WearDecorationReqDTO;
import com.ddmc.equity.infra.repository.dao.UserWearingDecorationDO;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/16 11:30
 * @description 用户佩戴装饰领域服务接口
 */
public interface UserWearingDecorationDomainService {

    /**
     * 根据用户 ID 和装饰类型查询用户当前佩戴装饰信息
     *
     * @param userId         用户 ID
     * @param decorationType 装饰类型
     * @param includeExpired 是否包含已过期装饰。为 true 时包含已过期装饰，为 false 或 null 时只返回未过期装饰
     * @return 用户当前佩戴装饰信息
     */
    UserWearingDecorationDO queryCurrentUserWearingDecoration(String userId, Integer decorationType,
                                                              Boolean includeExpired);

    /**
     * 查询用户当前佩戴装饰信息（不包含已过期装饰）
     *
     * @param userId         用户 ID
     * @param decorationType 装饰类型
     * @return 用户当前佩戴装饰（未过期）信息
     */
    default UserWearingDecorationDO queryCurrentUserWearingDecoration(String userId, Integer decorationType) {
        return queryCurrentUserWearingDecoration(userId, decorationType, false);
    }

    /**
     * 查询用户佩戴装饰记录（不考虑佩戴状态）
     *
     * @param userId         用户 ID
     * @param decorationType 装饰类型
     * @return 用户佩戴装饰记录
     */
    UserWearingDecorationDO queryUserWearingDecoration(String userId, Integer decorationType);

    /**
     * 根据用户 ID 和装饰类型查询用户佩戴装饰记录（强制走主库）
     *
     * @param userId         用户 ID
     * @param decorationType 装饰类型
     * @return 用户佩戴装饰记录
     */
    UserWearingDecorationDO queryUserWearingDecorationBindMaster(String userId, Integer decorationType);

    /**
     * 佩戴装饰
     * 用户可以佩戴新的装饰，如果已有佩戴中的装饰，会先取消当前佩戴，再佩戴新的装饰
     *
     * @param req 佩戴装饰请求参数，包含用户 ID、装饰类型、装饰记录 ID 等信息
     * @throws com.ddmc.equity.common.exception.BusinessException 当参数非法、获取锁失败、装饰记录不存在、装饰类型不匹配、装饰已过期
     *                                                            装饰权益信息不存在、佩戴操作失败时抛出
     */
    void wearDecoration(WearDecorationReqDTO req);

    /**
     * 取消佩戴装饰
     * 将用户当前佩戴的装饰状态更新为已取消佩戴，并记录取消佩戴时间
     *
     * @param req 取消佩戴装饰请求参数，包含用户 ID、装饰类型等信息
     * @throws com.ddmc.equity.common.exception.BusinessException 当参数非法、获取锁失败、没有佩戴中的装饰、取消佩戴失败时抛出
     */
    void unwearDecoration(UnwearDecorationReqDTO req);
} 