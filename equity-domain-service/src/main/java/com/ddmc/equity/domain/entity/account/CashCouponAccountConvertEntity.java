package com.ddmc.equity.domain.entity.account;

import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.dto.business.account.CashCouponAccountRecordDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.repository.dao.CashCouponAccountDO;
import com.ddmc.equity.infra.repository.dao.CashCouponAccountRecordDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.CashCouponAccountInfoDTO;
import com.ddmc.equity.model.dto.SubAccountRecordDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


public class CashCouponAccountConvertEntity extends AccountConvertEntity {

    public static CashCouponAccountRecordDO createAccountRecordDO(int operateType, EquityAccountContext equityAccountContext) {
        CashCouponAccountRecordDO accountRecordDO = new CashCouponAccountRecordDO();
        createSubAccountRecordBaseFieldDO(accountRecordDO, operateType, equityAccountContext);
        accountRecordDO.setTicketId(equityAccountContext.getEquityValue());
        accountRecordDO.setOpenId(equityAccountContext.getOpenId());
        return accountRecordDO;
    }

    public static CashCouponAccountDO createAccountDO(EquityAccountContext equityAccountContext) {
        CashCouponAccountDO accountDO = new CashCouponAccountDO();
        createSubAccountBaseFieldDO(accountDO, equityAccountContext);
        accountDO.setTicketId(equityAccountContext.getEquityValue());
        return accountDO;
    }

    public static CashCouponAccountRecordDO createUpdateAccountRecordDO(Long accountRecordId, Long accountId, Integer status,
                                                                        EquityRpcDto equityRpcDto, Map<String, Object> ruleLimitInfoMap) {
        CashCouponAccountRecordDO update = new CashCouponAccountRecordDO();
        fillUpdateBaseAccountRecordDO(update, accountRecordId, accountId, status, equityRpcDto, ruleLimitInfoMap);
        update.setUserTicketId(Objects.nonNull(equityRpcDto) ? equityRpcDto.getValue() : null);
        return update;
    }

    public static SubAccountRecordDTO convertToSubAccountRecordDTO(CashCouponAccountRecordDO recordDO) {
        SubAccountRecordDTO subAccountRecordDTO = new SubAccountRecordDTO();
        fillSubAccountRecordDTO(subAccountRecordDTO, recordDO);
        subAccountRecordDTO.setBenefitValue(recordDO.getTicketId());
        return subAccountRecordDTO;
    }


    public static List<AccountInfoDTO> convertToAccountInfoDTOList(List<CashCouponAccountDO> accountDOList) {
        if (CollectionUtils.isEmpty(accountDOList)) {
            return null;
        }
        return accountDOList.stream().map(CashCouponAccountConvertEntity::convertToAccountInfoDTO).collect(Collectors.toList());
    }

    private static AccountInfoDTO convertToAccountInfoDTO(CashCouponAccountDO accountDO) {
        CashCouponAccountInfoDTO cashCouponAccountInfoDTO = CashCouponAccountInfoDTO.builder()
                .ticketId(accountDO.getTicketId())
                .build();
        AccountInfoDTO accountInfoDTO = convertToBaseAccountInfoDTO(accountDO);
        accountInfoDTO.setBenefitType(BenefitTypeEnum.CASH_COUPON.getId());
        accountInfoDTO.setCashCouponAccountInfoDTO(cashCouponAccountInfoDTO);
        return accountInfoDTO;
    }

    public static List<CashCouponAccountRecordDTO> convertToCashCouponAccountRecordDTOList(List<CashCouponAccountRecordDO> recordDOList) {
        if (CollectionUtils.isEmpty(recordDOList)) {
            return null;
        }
        return recordDOList.stream().map(CashCouponAccountConvertEntity::convertToCashCouponAccountRecordDTO).collect(Collectors.toList());
    }

    private static CashCouponAccountRecordDTO convertToCashCouponAccountRecordDTO(CashCouponAccountRecordDO recordDO) {
        return CashCouponAccountRecordDTO.builder()
                .activityId(recordDO.getActivityId())
                .strategyId(recordDO.getStrategyId())
                .benefitGroupId(recordDO.getBenefitGroupId())
                .benefitId(recordDO.getBenefitId())
                .accountId(recordDO.getAccountId())
                .userId(recordDO.getUserId())
                .operateType(recordDO.getOperateType())
                .status(recordDO.getStatus())
                .appId(recordDO.getAppId())
                .pageId(recordDO.getPageId())
                .source(recordDO.getSource())
                .reqNo(recordDO.getReqNo())
                .createTime(recordDO.getCreateTime())
                .ticketId(recordDO.getTicketId())
                .userTicketId(recordDO.getUserTicketId())
                .openId(recordDO.getOpenId())
                .build();
    }
}
