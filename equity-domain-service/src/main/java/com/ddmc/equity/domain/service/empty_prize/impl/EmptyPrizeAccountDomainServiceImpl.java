package com.ddmc.equity.domain.service.empty_prize.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.service.empty_prize.EmptyPrizeAccountDomainService;
import com.ddmc.equity.infra.repository.dao.EmptyPrizeAccountDO;
import com.ddmc.equity.infra.repository.dao.mapper.EmptyPrizeAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/16 11:32
 * @description
 */
@Slf4j
@Service
public class EmptyPrizeAccountDomainServiceImpl extends ServiceImpl<EmptyPrizeAccountMapper, EmptyPrizeAccountDO>
        implements EmptyPrizeAccountDomainService {

    @Override
    public void insertAccountDO(EmptyPrizeAccountDO accountDO) {
        Assert.mustTrue(StringUtils.isNotBlank(accountDO.getUserId()), ExceptionEnum.ILLEGAL_ARGS.getCode(), "空奖权益子账户 userId 不能为空");
        this.save(accountDO);
    }

    @Override
    public List<EmptyPrizeAccountDO> queryAccountsByUserId(String userId, List<Long> activityIds,
                                                           List<Integer> statuses, Date startDate, Date endDate) {
        Assert.mustTrue(StringUtils.isNotBlank(userId), ExceptionEnum.ILLEGAL_ARGS.getCode(), "查询空奖权益子账户 userId 不能为空");

        Wrapper<EmptyPrizeAccountDO> wrapper = Wrappers.<EmptyPrizeAccountDO>lambdaQuery()
                .eq(EmptyPrizeAccountDO::getIsDelete, CommonEnum.INTEGER_BOOL.NO.getCode())
                .eq(EmptyPrizeAccountDO::getUserId, userId)
                .in(CollectionUtils.isNotEmpty(activityIds), EmptyPrizeAccountDO::getActivityId, activityIds)
                .in(CollectionUtils.isNotEmpty(statuses), EmptyPrizeAccountDO::getStatus, statuses)
                .ge(Objects.nonNull(startDate), EmptyPrizeAccountDO::getCreateTime, startDate)
                .le(Objects.nonNull(endDate), EmptyPrizeAccountDO::getCreateTime, endDate)
                .orderByDesc(EmptyPrizeAccountDO::getId);
        return this.list(wrapper);
    }
}
