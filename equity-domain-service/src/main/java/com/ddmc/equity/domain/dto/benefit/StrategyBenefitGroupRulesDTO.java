package com.ddmc.equity.domain.dto.benefit;

import com.ddmc.equity.domain.dto.rule.condition.CityRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.UserTagRuleDTO;
import lombok.Data;

import java.util.Map;

@Data
public class StrategyBenefitGroupRulesDTO {

    /**
     * 城市限制规则
     */
    private Map<Long/*组ID*/, Map<Long/*权益ID*/, CityRuleDTO>> cityR;
    /**
     * 库存限制规则
     */
    private Map<Long/*组ID*/, Map<Long/*权益ID*/, StockLimitRuleDTO>> stockR;
    /**
     * 用户人群标签规则
     */
    private Map<Long/* benefitGroupId */, Map<Long/* benefitId*/, UserTagRuleDTO>> userTagR;
}
