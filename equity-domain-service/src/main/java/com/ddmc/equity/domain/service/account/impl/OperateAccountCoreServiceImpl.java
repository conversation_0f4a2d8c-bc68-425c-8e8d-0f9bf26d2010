package com.ddmc.equity.domain.service.account.impl;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.ddmc.equity.account.DirectAccountContext;
import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.interceptor.annotation.MonitorSpan;
import com.ddmc.equity.common.interceptor.annotation.MonitorTransaction;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.domain.dto.account.UniversalAccountRecordInnerExtDTO;
import com.ddmc.equity.domain.dto.account_deduct.DeductAccountConfigDTO;
import com.ddmc.equity.domain.dto.account_deduct.DeductOperateAccountDTO;
import com.ddmc.equity.domain.dto.account_deduct.DeductOperateAccountDetailDTO;
import com.ddmc.equity.domain.dto.account_deduct.FetchDeductOperateAccountDetailsReqDTO;
import com.ddmc.equity.domain.dto.account_deduct.FetchDeductOperateAccountDetailsRespDTO;
import com.ddmc.equity.domain.dto.account_expire.ExpireOperateAccountDTO;
import com.ddmc.equity.domain.dto.account_expire.ExpireOperateAccountDetailDTO;
import com.ddmc.equity.domain.entity.account.OperateAccountConvertEntity;
import com.ddmc.equity.domain.service.account.OperateAccountCoreService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountDetailDomainService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountDomainService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountRecordDomainService;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/7/16 15:48
 * @description
 */
@Slf4j
@Service
public class OperateAccountCoreServiceImpl implements OperateAccountCoreService {

    @Value("${spring.application.name}")
    private String applicationName;
    /**
     * 扣减权益账户配置
     */
    @ApolloJsonValue("${equity.deductAccount.config:{\"fetchPageLimit\":1000,\"fetchMaxLoopTimes\":10,\"updatePageLimit\":500}}")
    private DeductAccountConfigDTO deductAccountConfig;
    /**
     * 过期权益通用账户，更新账户明细每页执行数量限制，默认 500
     */
    @Value("${equity.expireAccount.updatePageLimit:500}")
    private Integer expireAccountUpdatePageLimit;

    @Autowired
    private UniversalAccountDomainService accountDomainService;
    @Autowired
    private UniversalAccountDetailDomainService accountDetailDomainService;
    @Autowired
    private UniversalAccountRecordDomainService accountRecordDomainService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private TransactionUtil transactionUtil;

    @MonitorTransaction(type = "operate_account", name = "deduct_account")
    @Override
    public Boolean deductAccount(DirectAccountContext accountContext) {
        String userId = accountContext.getUserId();
        Integer benefitType = accountContext.getBenefitType();
        Integer deductCount = accountContext.getOperateCount();

        String lockKey = getOperateAccountLockKey(userId, benefitType);
        boolean locked = false;
        try {
            // 0 加锁
            locked = redisCache.lock(lockKey, 10, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("deductAccount lock failed accountContext={}", JSON.toJSONString(accountContext));
                CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "lock_failed");
                throw AdminExceptionBuilder.build(ExceptionEnum.TOO_FREQUENTING);
            }

            // 1 查询可以操作的 operateAccounts
            List<Long> useActivityIds = OperateAccountConvertEntity.getQueryUserAccountsUseActivityIds(accountContext.getUseActivityId());
            OperateAccountCoreServiceImpl self = (OperateAccountCoreServiceImpl) AopContext.currentProxy();
            List<DeductOperateAccountDTO> operateAccounts = accountDomainService.fetchDeductOperateAccounts(userId,
                    useActivityIds, benefitType);
            // 1.1 校验 operateAccounts 可用数量是否足够
            if (CollectionUtils.isEmpty(operateAccounts) ||
                    operateAccounts.stream().mapToInt(DeductOperateAccountDTO::getAvailableCount).sum() < deductCount) {
                log.warn("deductAccount operateAccounts notEnough accountContext={}", JSON.toJSONString(accountContext));
                CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_account_not_enough");
                throw AdminExceptionBuilder.build(ExceptionEnum.DEDUCT_ACCOUNT_NOT_ENOUGH);
            }

            // 2 获取扣减需要操作的账户明细列表，包含每一条账户明细的扣减数量
            FetchDeductOperateAccountDetailsReqDTO detailsReqDTO = OperateAccountConvertEntity.
                    convertToFetchDeductOperateAccountDetailsReqDTO(accountContext, deductAccountConfig);
            FetchDeductOperateAccountDetailsRespDTO detailsRespDTO = self.fetchDeductOperateAccountDetails(detailsReqDTO);

            // 3. 如果超过 maxLoopTimes，则不再继续查询，扣减只进行总账户扣减
            if (Objects.isNull(detailsRespDTO) || CollectionUtils.isEmpty(detailsRespDTO.getOperateAccountDetails()) ||
                    Boolean.TRUE.equals(detailsRespDTO.getExceedMaxLoopTimes())) {
                // 3.1 执行扣减，只扣减总账户
                CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_WAY, "deduct_without_details");
                // 获取扣减需要操作的账户列表，包含每一条账户的扣减数量
                List<DeductOperateAccountDTO> realOperateAccounts = self.fetchRealDeductOperateAccounts(accountContext,
                        operateAccounts);
                return self.executeDeductWithoutDetails(accountContext, realOperateAccounts);
            }

            // 3.2 执行扣减，一并扣减明细
            List<DeductOperateAccountDetailDTO> realOperateAccountDetails = detailsRespDTO.getOperateAccountDetails();
            // 获取扣减需要操作的账户列表，包含每一条账户的扣减数量
            List<DeductOperateAccountDTO> realOperateAccounts = self.fetchRealDeductOperateAccounts(accountContext,
                    operateAccounts, realOperateAccountDetails);
            return self.executeDeduct(accountContext, realOperateAccounts, realOperateAccountDetails);
        } catch (InterruptedException e) {
            log.warn("deductAccount lock exception accountContext={}", JSON.toJSONString(accountContext), e);
            CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "lock_exception");
            Thread.currentThread().interrupt();
            return Boolean.FALSE;
        } finally {
            redisCache.unlock(lockKey, locked);
        }
    }

    @MonitorTransaction(type = "operate_account", name = "old_expire_account")
    @Override
    public void expireAccount(UniversalAccountDetailDO detailDO) {
        if (Objects.isNull(detailDO)) {
            log.warn("expireAccount detailDO isNull");
            return;
        }
        String userId = detailDO.getUserId();
        Long detailId = detailDO.getId();
        Date expireTime = detailDO.getExpireTime();
        Integer expireCount = detailDO.getAvailableCount();
        // 1 校验权益账户明细是否需要过期
        if (!checkNeedExpire(detailDO)) {
            log.info("expireAccount notNeedExpire userId={}, detailId={}, expireTime={}, expireCount={}", userId, detailId,
                    expireTime, expireCount);
            return;
        }

        // 2 过期权益
        UniversalAccountRecordDO expireRecordDO = OperateAccountConvertEntity.convertToExpireRecordDO(detailDO, applicationName);
        transactionUtil.transactionalAndCatch(s -> {
            // 1 插入一条过期的权益账户操作记录（在一个事务里面，后续失败可以直接回滚，所以初始化状态为成功）
            expireRecordDO.setStatus(StatusEnum.SUCCESS.getCode());
            accountRecordDomainService.add(expireRecordDO);

            // 2 过期权益账户明细可用数量
            boolean expireDetailResult = accountDetailDomainService.expireDetailCount(userId, detailId, expireCount, null);
            if (!expireDetailResult) {
                // 过期权益账户明细可用数量失败，打点并回滚
                log.info("expireAccount expireDetailFailed userId={}, detailId={}, expireTime={}, expireCount={}",
                        userId, detailId, expireTime, expireCount);
                CsossUtils.logEvent(MonitorConstants.EXPIRE_UNIVERSAL_ACCOUNT_RESULT, "expire_account_detail_failure");
                throw AdminExceptionBuilder.build(ExceptionEnum.EXPIRE_ACCOUNT_DETAIL_FAILURE);
            }

            // 3 过期权益账户可用数量
            Long accountId = detailDO.getAccountId();
            boolean expireAccountResult = accountDomainService.expireAccountCount(userId, accountId, expireCount, null);
            if (!expireAccountResult) {
                // 过期权益总账户可用数量失败，打点并回滚
                log.warn("expireAccount expireAccountFailed userId={}, detailId={}, expireTime={}, expireCount={}",
                        userId, detailId, expireTime, expireCount);
                CsossUtils.logEvent(MonitorConstants.EXPIRE_UNIVERSAL_ACCOUNT_RESULT, "expire_account_failure");
                throw AdminExceptionBuilder.build(ExceptionEnum.EXPIRE_ACCOUNT_FAILURE);
            }
        });
    }

    @MonitorTransaction(type = "operate_account", name = "expire_account")
    @Override
    public Boolean expireAccount(DirectAccountContext accountContext, List<ExpireOperateAccountDetailDTO> operateAccountDetails) {
        // 1 查询对应的权益账户信息
        String userId = accountContext.getUserId();
        OperateAccountCoreServiceImpl self = (OperateAccountCoreServiceImpl) AopContext.currentProxy();
        List<ExpireOperateAccountDTO> operateAccounts = self.fetchExpireOperateAccounts(userId, operateAccountDetails);

        // 2 开启事务执行过期操作
        return transactionUtil.transactionalAndCatch(s -> {
            // 1 新增一条成功的扣减操作记录
            self.saveSuccessExpireRecord(accountContext, operateAccountDetails);

            // 2 过期 accountDetails.expireCount += availableCount、accountDetails.availableCount = 0
            ListUtils.partition(operateAccountDetails, expireAccountUpdatePageLimit)
                    .forEach(e -> expireDetailZero(userId, e));

            // 3 过期 accounts.expireCount += operateCount、accountDetails.availableCount -= operateCount
            operateAccounts.forEach(operateAccount -> expireAccountCount(userId, operateAccount));
        });
    }

    @MonitorSpan(name = "fetchDeductOperateAccountDetails")
    public FetchDeductOperateAccountDetailsRespDTO fetchDeductOperateAccountDetails(FetchDeductOperateAccountDetailsReqDTO req) {
        String userId = req.getUserId();
        List<Long> useActivityIds = req.getUseActivityIds();
        Integer benefitType = req.getBenefitType();
        Integer deductCount = req.getDeductCount();
        Integer pageLimit = req.getPageLimit();
        Integer maxLoopTimes = req.getMaxLoopTimes();

        int loopTimes = 0;
        int remainingDeductCount = deductCount;
        long offset = 0L;
        List<DeductOperateAccountDetailDTO> allOperateAccountDetails = Lists.newArrayList();
        Set<Long> allOperateAccountDetailIds = Sets.newHashSet();
        while (true) {
            // 1 如果超过 maxLoopTimes，则不再继续查询，扣减只进行总账户扣减
            if (Objects.nonNull(maxLoopTimes) && loopTimes >= maxLoopTimes) {
                log.info("fetchDeductOperateAccountDetails exceedMaxLoopTimes req={}, loopTimes={}, offset={}, " +
                        "remainingDeductCount={}", JSON.toJSONString(req), loopTimes, offset, remainingDeductCount);
                return FetchDeductOperateAccountDetailsRespDTO.builder()
                        .exceedMaxLoopTimes(Boolean.TRUE)
                        .build();
            }

            // 2 分页查询扣减需要操作的账户明细列表
            List<DeductOperateAccountDetailDTO> detailsByPage = accountDetailDomainService.fetchDeductOperateAccountDetailsByPage(userId,
                    useActivityIds, benefitType, pageLimit, offset);
            if (CollectionUtils.isEmpty(detailsByPage)) {
                log.warn("fetchDeductOperateAccountDetails detailsByPage notEnough req={}, loopTimes={}, offset={}, " +
                        "remainingDeductCount={}", JSON.toJSONString(req), loopTimes, offset, remainingDeductCount);
                break;
            }

            // 3 计算每一条明细需要扣减的数量，并添加到 allOperateAccountDetails
            remainingDeductCount = addAllOperateAccountDetails(allOperateAccountDetails, allOperateAccountDetailIds,
                    remainingDeductCount, detailsByPage);

            // 4 查询到的 allOperateAccountDetails 已经足够扣减，结束循环
            int allOperateAccountDetailsSize = CollectionUtils.size(allOperateAccountDetails);
            DeductOperateAccountDetailDTO lastOperateAccountDetailDTO = allOperateAccountDetails.get(allOperateAccountDetailsSize - 1);
            // 如果剩余需要扣减的数量 remainingDeductCount == 0，则获取到的 allOperateAccountDetails 已经满足扣减数量
            if (remainingDeductCount == 0) {
                // 检查最后一条记录的 cumulativeCount 是否 >= needCount。如果满足条件，则所取 details 已经满足扣减条件
                log.info("fetchDeductOperateAccountDetails end req={}, loopTimes={}, offset={}, allOperateAccountDetailsSize={}, " +
                                "lastOperateAccountDetailDTO={}", JSON.toJSONString(req), loopTimes, offset,
                        allOperateAccountDetailsSize, JSON.toJSONString(lastOperateAccountDetailDTO));
                break;
            }

            // 5 如果当前页结果的数量 < pageLimit，则当前页是最后一页，结束循环
            if (CollectionUtils.size(detailsByPage) < pageLimit) {
                log.warn("fetchDeductOperateAccountDetails detailsByPageSize lt pageLimit req={}, loopTimes={}, offset={}, " +
                        "remainingDeductCount={}", JSON.toJSONString(req), loopTimes, offset, remainingDeductCount);
                break;
            }

            // 6 继续下一页
            offset += pageLimit;
            loopTimes++;
        }

        // 校验 realOperateAccountDetails 可用数量是否足够
        if (CollectionUtils.isEmpty(allOperateAccountDetails) ||
                allOperateAccountDetails.stream().mapToInt(DeductOperateAccountDetailDTO::getAvailableCount).sum() < deductCount) {
            log.warn("deductAccount operateAccountDetails notEnough req={}", JSON.toJSONString(req));
            CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_account_details_not_enough");
            throw AdminExceptionBuilder.build(ExceptionEnum.DEDUCT_ACCOUNT_DETAILS_NOT_ENOUGH);
        }
        // 校验 realOperateAccountDetails 扣减数量计算是否正确
        if (allOperateAccountDetails.stream().mapToInt(DeductOperateAccountDetailDTO::getDeduceCount).sum() != deductCount) {
            log.warn("deductAccount operateAccountDetails countError req={}", JSON.toJSONString(req));
            CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_account_details_count_error");
            throw AdminExceptionBuilder.build(ExceptionEnum.DEDUCT_ACCOUNT_DETAILS_COUNT_ERROR);
        }
        return FetchDeductOperateAccountDetailsRespDTO.builder()
                .operateAccountDetails(allOperateAccountDetails)
                .exceedMaxLoopTimes(Boolean.FALSE)
                .build();
    }

    @MonitorSpan(name = "executeDeduct")
    public boolean executeDeduct(DirectAccountContext accountContext, List<DeductOperateAccountDTO> operateAccounts,
                                 List<DeductOperateAccountDetailDTO> operateAccountDetails) {
        String userId = accountContext.getUserId();
        OperateAccountCoreServiceImpl self = (OperateAccountCoreServiceImpl) AopContext.currentProxy();
        Integer updatePageLimit = deductAccountConfig.getUpdatePageLimit();

        //  扣减前 size - 1 条 accountDetails.availableCount = 0
        int operateAccountDetailsSize = CollectionUtils.size(operateAccountDetails);
        List<Long> deductZeroAccountDetailIds = operateAccountDetails.subList(0, operateAccountDetailsSize - 1).stream()
                .map(DeductOperateAccountDetailDTO::getAccountDetailId).collect(Collectors.toList());
        // 扣减最后一条 accountDetail.availableCount -= deduceCount
        DeductOperateAccountDetailDTO lastOperateAccountDetail = operateAccountDetails.get(operateAccountDetailsSize - 1);
        if (Objects.equals(lastOperateAccountDetail.getAvailableCount(), lastOperateAccountDetail.getDeduceCount())) {
            // 如果最后一条也是全部扣减，则合并到 deductZeroAccountDetailIds 一起处理
            deductZeroAccountDetailIds.add(lastOperateAccountDetail.getAccountDetailId());
            lastOperateAccountDetail = null;
        }

        try {
            // 1 新增一条初始化的扣减操作记录
            Long accountRecordId = self.saveDeductRecord(accountContext, null);
            accountContext.setAccountDetailId(accountRecordId);
            DeductOperateAccountDetailDTO finalLastOperateAccountDetail = lastOperateAccountDetail;
            List<List<Long>> partition = ListUtils.partition(deductZeroAccountDetailIds, updatePageLimit);
            boolean deductResult = transactionUtil.transactionalAndCatch(s -> {
                // 2 扣减前 size - 1 条 accountDetails.availableCount = 0
                partition.forEach(e -> deductDetailZero(userId, e));

                // 3 扣减最后一条 accountDetail.availableCount -= deduceCount
                deductDetailCount(userId, finalLastOperateAccountDetail);

                // 4 扣减 accounts.availableCount -= deduceCount
                operateAccounts.forEach(operateAccount -> deductAccountCount(userId, operateAccount));

                // 5 更新扣减操作记录状态为成功
                self.updateRecordStatus(userId, accountRecordId, StatusEnum.SUCCESS.getCode());
            });
            if (!deductResult) {
                // 事务回滚，则更新扣减操作记录状态为失败
                log.warn("deductAccount deduct failed accountContext={}", JSON.toJSONString(accountContext));
                CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_failed");
                self.updateRecordStatus(userId, accountRecordId, StatusEnum.FAIL.getCode());
                return false;
            }
            // 6 更新扣减操作记录 innerExt 放事务外，失败只打点不影响业务流程
            self.updateDeductRecordInnerExt(userId, accountRecordId, operateAccountDetails);
            return true;
        } catch (DuplicateKeyException e) {
            log.warn("deductAccount deduct duplicateKeyException accountContext={}", JSON.toJSONString(accountContext), e);
            CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_duplicate_key_exception");
            return self.idempotentConflict(accountContext);
        }
    }

    @MonitorSpan(name = "executeDeductWithoutDetails")
    public boolean executeDeductWithoutDetails(DirectAccountContext accountContext,
                                               List<DeductOperateAccountDTO> operateAccounts) {
        String userId = accountContext.getUserId();
        OperateAccountCoreServiceImpl self = (OperateAccountCoreServiceImpl) AopContext.currentProxy();
        try {
            // 1 新增一条初始化的扣减操作记录
            Long accountRecordId = self.saveDeductRecord(accountContext, null);
            accountContext.setAccountDetailId(accountRecordId);
            boolean deductResult = transactionUtil.transactionalAndCatch(s -> {
                // 2 扣减 accounts.availableCount -= deduceCount
                operateAccounts.forEach(operateAccount -> deductAccountCount(userId, operateAccount));

                // 3 更新扣减操作记录状态为成功
                self.updateRecordStatus(userId, accountRecordId, StatusEnum.SUCCESS.getCode());
            });
            if (!deductResult) {
                // 事务回滚，则更新扣减操作记录状态为失败
                log.warn("deductAccount deductWithoutDetails failed accountContext={}", JSON.toJSONString(accountContext));
                CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_failed");
                self.updateRecordStatus(userId, accountRecordId, StatusEnum.FAIL.getCode());
            }
            return deductResult;
        } catch (DuplicateKeyException e) {
            log.warn("deductAccount deductWithoutDetails duplicateKeyException accountContext={}", JSON.toJSONString(accountContext), e);
            CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_duplicate_key_exception");
            return self.idempotentConflict(accountContext);
        }
    }

    @MonitorSpan(name = "saveDeductRecord")
    public Long saveDeductRecord(DirectAccountContext accountContext,
                                 List<DeductOperateAccountDetailDTO> operateAccountDetails) {
        UniversalAccountRecordDO accountRecordDO = OperateAccountConvertEntity.convertToDeductRecordDO(
                accountContext, operateAccountDetails);
        accountRecordDomainService.add(accountRecordDO);
        return accountRecordDO.getId();
    }

    @MonitorSpan(name = "updateRecordStatus")
    public void updateRecordStatus(String userId, Long accountRecordId, Integer status) {
        if (Objects.isNull(accountRecordId)) {
            log.info("updateRecordStatus accountRecordId isNull noNeedToUpdate userId={}, status={}", userId, status);
            return;
        }
        accountRecordDomainService.updateStatusAndRpcResult(userId, accountRecordId, status, null, null);
    }

    @MonitorSpan(name = "updateDeductRecordInnerExt")
    public void updateDeductRecordInnerExt(String userId, Long accountRecordId,
                                           List<DeductOperateAccountDetailDTO> operateAccountDetails) {
        try {
            UniversalAccountRecordInnerExtDTO innerExtDTO = OperateAccountConvertEntity.convertToDeductRecordInnerExtDTO(
                    operateAccountDetails);
            accountRecordDomainService.updateInnerExt(userId, accountRecordId, innerExtDTO);
        } catch (Exception e) {
            log.warn("deductAccount updateDeductRecordInnerExt exception userId={}, accountRecordId={}", userId, accountRecordId, e);
            CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "update_deduct_record_inner_ext_exception");
        }
    }

    @MonitorSpan(name = "idempotentConflict")
    public boolean idempotentConflict(DirectAccountContext accountContext) {
        String sceneCode = accountContext.getSceneCode();
        String userId = accountContext.getUserId();
        Integer operateType = accountContext.getOperateType();
        String reqNo = accountContext.getReqNo();
        UniversalAccountRecordDO existRecord = accountRecordDomainService.queryOneByUniqueKey(userId, sceneCode,
                operateType, reqNo, reqNo);
        return Objects.nonNull(existRecord) && Objects.equals(existRecord.getStatus(), StatusEnum.SUCCESS.getCode());
    }

    private void deductDetailZero(String userId, List<Long> subAccountDetailIds) {
        boolean deductResult = accountDetailDomainService.deductDetailZero(userId, subAccountDetailIds);
        if (!deductResult) {
            log.warn("deductAccount deductDetailZero failed userId={}, subAccountDetailIds={}", userId,
                    JSON.toJSONString(subAccountDetailIds));
            CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_account_details_zero_failed");
            throw AdminExceptionBuilder.build(ExceptionEnum.DEDUCT_ACCOUNT_DETAILS_ZERO_FAILED);
        }
    }

    private void deductDetailCount(String userId, DeductOperateAccountDetailDTO operateAccountDetail) {
        // 如果最后一条也是全部扣减，则合并到 deductZeroAccountDetailIds 一起处理
        if (Objects.isNull(operateAccountDetail)) {
            return;
        }
        boolean deductResult = accountDetailDomainService.deductDetailCount(userId,
                operateAccountDetail.getAccountDetailId(), operateAccountDetail.getDeduceCount());
        if (!deductResult) {
            log.warn("deductAccount deductDetailCount failed userId={}, operateAccountDetail={}",
                    userId, JSON.toJSONString(operateAccountDetail));
            CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_account_details_count_failed");
            throw AdminExceptionBuilder.build(ExceptionEnum.DEDUCT_ACCOUNT_DETAILS_COUNT_FAILED);
        }
    }

    private void deductAccountCount(String userId, DeductOperateAccountDTO operateAccount) {
        boolean deductResult = accountDomainService.deductAccountCount(userId, operateAccount.getAccountId(),
                operateAccount.getDeduceCount(), operateAccount.getVersion());
        if (!deductResult) {
            log.warn("deductAccount deductAccountCount failed userId={}, operateAccount={}",
                    userId, JSON.toJSONString(operateAccount));
            CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_account_count_failed");
            throw AdminExceptionBuilder.build(ExceptionEnum.DEDUCT_ACCOUNT_COUNT_FAILED);
        }
    }

    private int addAllOperateAccountDetails(List<DeductOperateAccountDetailDTO> allOperateAccountDetails,
                                            Set<Long> allOperateAccountDetailIds, int remainingDeductCount,
                                            List<DeductOperateAccountDetailDTO> detailsByPage) {
        for (DeductOperateAccountDetailDTO operateDetailDTO : detailsByPage) {
            // 如果记录已经在 allOperateAccountDetails 中了，则记录不可重复计算使用
            Long accountDetailId = operateDetailDTO.getAccountDetailId();
            if (allOperateAccountDetailIds.contains(accountDetailId)) {
                continue;
            }
            Integer availableCount = operateDetailDTO.getAvailableCount();
            if (Objects.isNull(availableCount) || availableCount <= 0) {
                continue;
            }
            // 如果记录的 availableCount <= remainingDeductCount，则记录的 availableCount 都扣减掉；
            int deduceCount = Math.min(availableCount, remainingDeductCount);
            operateDetailDTO.setDeduceCount(deduceCount);
            allOperateAccountDetailIds.add(accountDetailId);
            allOperateAccountDetails.add(operateDetailDTO);
            remainingDeductCount -= deduceCount;
            // 如果剩余需要扣减的数量 remainingDeductCount == 0，则获取到的 allOperateAccountDetails 已经满足扣减数量
            if (remainingDeductCount == 0) {
                break;
            }
        }
        return remainingDeductCount;
    }

    @MonitorSpan(name = "fetchRealDeductOperateAccounts")
    public List<DeductOperateAccountDTO> fetchRealDeductOperateAccounts(DirectAccountContext accountContext,
                                                                        List<DeductOperateAccountDTO> operateAccounts,
                                                                        List<DeductOperateAccountDetailDTO> operateAccountDetails) {
        String userId = accountContext.getUserId();
        Integer deductCount = accountContext.getOperateCount();
        Map<Long /* accountId */, DeductOperateAccountDTO> operateAccountMap = operateAccounts.stream()
                .collect(Collectors.toMap(DeductOperateAccountDTO::getAccountId, Function.identity(), (var1, var2) -> var1));

        List<DeductOperateAccountDTO> realOperateAccounts = operateAccountDetails.stream()
                .collect(Collectors.groupingBy(DeductOperateAccountDetailDTO::getAccountId))
                .entrySet().stream().map(entry -> {
                    Long accountId = entry.getKey();
                    DeductOperateAccountDTO operateAccount = operateAccountMap.get(accountId);
                    if (Objects.isNull(operateAccount)) {
                        log.warn("expireAccount deductAccount isNull userId={}, accountId={}", userId, accountId);
                        CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_account_is_null");
                        throw AdminExceptionBuilder.build(ExceptionEnum.DEDUCT_ACCOUNT_IS_NULL);
                    }
                    Integer availableCount = operateAccount.getAvailableCount();
                    int deduceCount = entry.getValue().stream().mapToInt(DeductOperateAccountDetailDTO::getDeduceCount).sum();
                    if (Objects.isNull(availableCount) || availableCount < deduceCount) {
                        log.warn("expireAccount deductAccount notEnough userId={}, accountId={}, availableCount={}, deduceCount={}",
                                userId, accountId, availableCount, deduceCount);
                        CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_account_not_enough");
                        throw AdminExceptionBuilder.build(ExceptionEnum.DEDUCT_ACCOUNT_NOT_ENOUGH);
                    }
                    // 账户需要有足够的可用数量，才能进行操作
                    operateAccount.setDeduceCount(deduceCount);
                    return operateAccount;
                }).collect(Collectors.toList());

        // 校验 realOperateAccounts 扣减数量计算是否正确
        if (realOperateAccounts.stream().mapToInt(DeductOperateAccountDTO::getDeduceCount).sum() != deductCount) {
            log.warn("deductAccount realOperateAccounts countError accountContext={}", JSON.toJSONString(accountContext));
            CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_account_count_error");
            throw AdminExceptionBuilder.build(ExceptionEnum.DEDUCT_ACCOUNT_COUNT_ERROR);
        }
        return realOperateAccounts;
    }

    @MonitorSpan(name = "fetchRealDeductOperateAccounts")
    public List<DeductOperateAccountDTO> fetchRealDeductOperateAccounts(DirectAccountContext accountContext,
                                                                        List<DeductOperateAccountDTO> operateAccounts) {
        Integer deductCount = accountContext.getOperateCount();
        int remainingDeductCount = deductCount;
        List<DeductOperateAccountDTO> realOperateAccounts = Lists.newArrayList();
        for (DeductOperateAccountDTO operateAccountDTO : operateAccounts) {
            Integer availableCount = operateAccountDTO.getAvailableCount();
            if (Objects.isNull(availableCount) || availableCount <= 0) {
                continue;
            }
            // 如果记录的 availableCount <= remainingDeductCount，则记录的 availableCount 都扣减掉；
            int deduceCount = Math.min(availableCount, remainingDeductCount);
            operateAccountDTO.setDeduceCount(deduceCount);
            realOperateAccounts.add(operateAccountDTO);
            remainingDeductCount -= deduceCount;
            // 如果剩余需要扣减的数量 remainingDeductCount == 0，则获取到的 allOperateAccountDetails 已经满足扣减数量
            if (remainingDeductCount == 0) {
                break;
            }
        }

        // 校验 realOperateAccounts 扣减数量计算是否正确
        if (realOperateAccounts.stream().mapToInt(DeductOperateAccountDTO::getDeduceCount).sum() != deductCount) {
            log.warn("deductAccount realOperateAccounts countError accountContext={}", JSON.toJSONString(accountContext));
            CsossUtils.logEvent(MonitorConstants.DEDUCT_UNIVERSAL_ACCOUNT_RESULT, "deduct_account_count_error");
            throw AdminExceptionBuilder.build(ExceptionEnum.DEDUCT_ACCOUNT_COUNT_ERROR);
        }
        return realOperateAccounts;
    }

    private boolean checkNeedExpire(UniversalAccountDetailDO detailDO) {
        // 1 detail.expireTime < now，该账户明细才需要过期
        Date expireTime = detailDO.getExpireTime();
        if (Objects.nonNull(expireTime) && expireTime.before(new Date())) {
            return true;
        }

        // 2 detail.availableCount > 0，该账户明细才需要过期
        Integer availableCount = detailDO.getAvailableCount();
        return Objects.nonNull(availableCount) && availableCount > 0;
    }

    @MonitorSpan(name = "saveSuccessExpireRecord")
    public void saveSuccessExpireRecord(DirectAccountContext accountContext,
                                        List<ExpireOperateAccountDetailDTO> operateAccountDetails) {
        UniversalAccountRecordDO accountRecordDO = OperateAccountConvertEntity.convertToExpireRecordDO(
                accountContext, operateAccountDetails);
        accountRecordDomainService.add(accountRecordDO);
    }

    private void expireDetailZero(String userId, List<ExpireOperateAccountDetailDTO> subOperateAccountDetails) {
        List<Long> subAccountDetailIds = subOperateAccountDetails.stream()
                .map(ExpireOperateAccountDetailDTO::getAccountDetailId)
                .collect(Collectors.toList());
        boolean expireResult = accountDetailDomainService.expireDetailZero(userId, subAccountDetailIds);
        if (!expireResult) {
            log.warn("expireAccount expireDetailZero failed userId={}, subAccountDetailIds={}", userId,
                    JSON.toJSONString(subAccountDetailIds));
            CsossUtils.logEvent(MonitorConstants.EXPIRE_UNIVERSAL_ACCOUNT_RESULT, "expire_account_details_zero_failed");
            throw AdminExceptionBuilder.build(ExceptionEnum.EXPIRE_ACCOUNT_DETAILS_ZERO_FAILED);
        }
    }

    private void expireAccountCount(String userId, ExpireOperateAccountDTO operateAccount) {
        Long accountId = operateAccount.getAccountId();
        Integer operateCount = operateAccount.getExpireCount();
        boolean expireResult = accountDomainService.expireAccountCount(userId, accountId, operateCount,
                operateAccount.getVersion());
        if (!expireResult) {
            log.warn("expireAccount expireAccountCount failed userId={}, accountId={}, operateCount={}", userId, accountId,
                    operateCount);
            CsossUtils.logEvent(MonitorConstants.EXPIRE_UNIVERSAL_ACCOUNT_RESULT, "expire_account_count_failed");
            throw AdminExceptionBuilder.build(ExceptionEnum.EXPIRE_ACCOUNT_COUNT_FAILED);
        }
    }

    @MonitorSpan(name = "fetchExpireOperateAccounts")
    public List<ExpireOperateAccountDTO> fetchExpireOperateAccounts(String userId, List<ExpireOperateAccountDetailDTO> operateAccountDetails) {
        Map<Long /* accountId */, List<ExpireOperateAccountDetailDTO>> operateAccountDetailsMap = operateAccountDetails.stream()
                .collect(Collectors.groupingBy(ExpireOperateAccountDetailDTO::getAccountId));
        Set<Long> accountIds = operateAccountDetailsMap.keySet();
        List<UniversalAccountDO> accountDOList = accountDomainService.queryAccounts(userId, Lists.newArrayList(accountIds));
        Map<Long /* accountId */, UniversalAccountDO> accountDOMap = CollectionUtils.isEmpty(accountDOList) ? Maps.newHashMap() :
                accountDOList.stream().collect(Collectors.toMap(UniversalAccountDO::getId, Function.identity(), (var1, var2) -> var1));

        return operateAccountDetailsMap.entrySet().stream().map(e -> {
            Long accountId = e.getKey();
            UniversalAccountDO accountDO = accountDOMap.get(accountId);
            if (Objects.isNull(accountDO)) {
                log.warn("expireAccount expireAccount isNull userId={}, accountId={}", userId, accountId);
                CsossUtils.logEvent(MonitorConstants.EXPIRE_UNIVERSAL_ACCOUNT_RESULT, "expire_account_is_null");
                throw AdminExceptionBuilder.build(ExceptionEnum.EXPIRE_ACCOUNT_IS_NULL);
            }
            int expireCount = e.getValue().stream().mapToInt(ExpireOperateAccountDetailDTO::getExpireCount).sum();
            if (Objects.isNull(accountDO.getAvailableCount()) || accountDO.getAvailableCount() < expireCount) {
                log.warn("expireAccount expireAccount notEnough userId={}, accountId={}", userId, accountId);
                CsossUtils.logEvent(MonitorConstants.EXPIRE_UNIVERSAL_ACCOUNT_RESULT, "expire_account_not_enough");
                throw AdminExceptionBuilder.build(ExceptionEnum.EXPIRE_ACCOUNT_NOT_ENOUGH);
            }
            return ExpireOperateAccountDTO.builder()
                    .accountId(accountId)
                    .availableCount(accountDO.getAvailableCount())
                    .version(accountDO.getVersion())
                    .expireCount(expireCount)
                    .build();
        }).collect(Collectors.toList());
    }

    private String getOperateAccountLockKey(String userId, Integer benefitType) {
        return String.format(CacheKeyConstants.UNIVERSAL_USE_BENEFIT_LOCK, userId, benefitType);
    }
}
