package com.ddmc.equity.domain.dto.engine;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/7/6 16:27
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class BenefitGroupEngineContextDTO extends StrategyEngineContextDTO {

    /**
     * 权益组 id
     */
    private Long benefitGroupId;
    /**
     * 权益 id
     */
    private Long benefitId;
}
