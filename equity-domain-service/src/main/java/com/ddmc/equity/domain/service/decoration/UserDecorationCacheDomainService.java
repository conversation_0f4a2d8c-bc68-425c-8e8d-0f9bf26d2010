package com.ddmc.equity.domain.service.decoration;

import com.ddmc.equity.domain.dto.decoration.UserDecorationCacheDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;
import com.ddmc.equity.infra.repository.dao.UserWearingDecorationDO;

import java.util.List;

/**
 * 用户装饰缓存域服务
 *
 * <AUTHOR>
 * @date 2025/03/20
 */
public interface UserDecorationCacheDomainService {

    /**
     * 按用户刷新缓存
     *
     * @param userWearingDecorationDO 用户佩戴装饰做
     */
    void refreshCacheByUser(UserWearingDecorationDO userWearingDecorationDO);

    /**
     * 按照JOB刷新缓存
     *
     * @param userWearingDecorationDO 用户佩戴装饰做
     */
    void refreshCacheByJob(UserWearingDecorationDO userWearingDecorationDO);

    /**
     * 查询装修清单
     *
     * @param uidList        uid列表
     * @param decorationType 装饰类型
     * @return {@link List }<{@link UserDecorationCacheDTO }>
     */
    List<UserDecorationCacheDTO> queryCacheList(List<String> uidList, Integer decorationType);

    /**
     * 查询需要处理缓存的数据列表
     *
     * @param shardingIndex 分片指数
     * @param userIds       用户id
     * @param lastId        最后id
     * @param limit         限制
     * @return {@link List }<{@link UserWearingDecorationDO }>
     */
    List<UserWearingDecorationDO> queryNeedCacheList(Integer shardingIndex, List<String> userIds, Long lastId, Integer limit);

}
