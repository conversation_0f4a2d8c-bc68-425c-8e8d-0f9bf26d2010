package com.ddmc.equity.domain.service.benefit_group;

import com.ddmc.equity.domain.valueobject.benefit_group.BenefitGroupListFilterReqVO;
import com.ddmc.equity.domain.valueobject.benefit_group.BenefitGroupListFilterRespVO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitGroupDO;

import java.util.List;

public interface EquityBenefitGroupDomainService {

    List<EquityBenefitGroupDO> getAllEquityBenefitGroupDO();

    List<EquityBenefitGroupDO> getAllEquityBenefitGroupDOByStrategyIds(List<Long> strategyIds);

    List<EquityBenefitGroupDO> getEquityBenefitGroupDOListByStrategyId(Long strategyId);

    EquityBenefitGroupDO getEquityBenefitGroupDOByStrategyId(Long strategyId);

    List<EquityBenefitGroupDO> getEquityBenefitGroupDOListByStrategyIds(List<Long> strategyIds);

    BenefitGroupListFilterRespVO getListedBenefitGroupsRespByFilter(BenefitGroupListFilterReqVO getListFilterReq);

    int save(EquityBenefitGroupDO benefitGroupDO);

    void insert(EquityBenefitGroupDO equityBenefitGroupDO);

    void insertOrUpdate(EquityBenefitGroupDO equityBenefitGroupDO);

    /**
     * 根据ID删除
     *
     * @param benefitGroupId
     * @return
     */
    int deleteById(Long benefitGroupId);


    /**
     * 根据id列表查询
     *
     * @param benefitGroupIds
     * @return
     */
    List<EquityBenefitGroupDO> getBenefitGroupByIds(List<Long> benefitGroupIds);

    int update(EquityBenefitGroupDO equityBenefitGroupDO);
}
