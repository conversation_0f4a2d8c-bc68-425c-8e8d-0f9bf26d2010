package com.ddmc.equity.factor.strategy;

import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.receive_limit.UserActivityReceiveLimitCacheDTO;
import com.ddmc.equity.domain.dto.rule.ActivityReceiveLimitFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.service.receive_limit.UserActivityReceiveLimitDomainService;
import com.ddmc.equity.factor.AbstractRuleFactorHandler;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 领取频次限制规则
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ActivityReceiveRuleLimitFactorHandler extends AbstractRuleFactorHandler {

    @Autowired
    private UserActivityReceiveLimitDomainService userActivityReceiveLimitDomainService;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.ACTIVITY.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode();
    }

    @Override
    public boolean needQueryFactorValueFromBaseSystem() {
        return true;
    }

    @Override
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        String userId = getUserId(engineContextDTO);
        if (StringUtils.isBlank(userId)) {
            return;
        }

        // 获取活动维度的频次结果
        List<Long> needQueryUserLimitActivityIds = getNeedQueryUserLimitActivityIds(rules);
        if (CollectionUtils.isEmpty(needQueryUserLimitActivityIds)) {
            return;
        }
        Map<Long, UserActivityReceiveLimitCacheDTO> activityReceiveLimitCacheMap = userActivityReceiveLimitDomainService
                .getActivityReceiveLimitCacheMul(userId, needQueryUserLimitActivityIds);
        ruleConditionFactorValueDTO.setActivityReceiveLimitFactorValueMap(gainActivityFactorValueMap(activityReceiveLimitCacheMap));
    }

    private Map<Long /* activityId */, ActivityReceiveLimitFactorValueDTO> gainActivityFactorValueMap(
            Map<Long, UserActivityReceiveLimitCacheDTO> activityReceiveLimitCacheMap) {
        Map<Long /* activityId */, ActivityReceiveLimitFactorValueDTO> factorValueMap = Maps.newHashMap();
        if (MapUtils.isEmpty(activityReceiveLimitCacheMap)) {
            return factorValueMap;
        }
        activityReceiveLimitCacheMap.forEach((k, v) -> {
            ActivityReceiveLimitFactorValueDTO receiveLimitFactorValueDTO = ActivityReceiveLimitFactorValueDTO.builder()
                    .limitCount(v.getLimitCount())
                    .receiveCount(v.getReceiveCount())
                    .isCanReceive(isCanReceive(v.getLimitCount(), v.getReceiveCount()))
                    .build();
            factorValueMap.put(k, receiveLimitFactorValueDTO);
        });
        return factorValueMap;
    }

    private List<Long> getNeedQueryUserLimitActivityIds(List<GeneralRuleCacheDTO> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return null;
        }
        return rules.stream().map(GeneralRuleCacheDTO::getActivityId).distinct().collect(Collectors.toList());
    }

    /**
     * 判断是否可领取
     *
     * @param limitCount   可领取数量
     * @param receiveCount 已领取数量
     * @return 是否可领取
     */
    protected boolean isCanReceive(Long limitCount, Long receiveCount) {
        // 没有频次限制
        if (Objects.isNull(limitCount) || Objects.isNull(receiveCount)) {
            return true;
        }
        return limitCount > receiveCount;
    }
}
