package com.ddmc.equity.factor.strategy;

import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.BenefitReceiveLimitFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.domain.service.benefit_limit.EquityBenefitLimitService;
import com.ddmc.equity.domain.valueobject.benefit_limit.BenefitIdInfoDTO;
import com.ddmc.equity.domain.valueobject.benefit_limit.GetBenefitLimitCacheInfoResp;
import com.ddmc.equity.factor.AbstractRuleFactorHandler;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 领取频次限制规则
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BenefitReceiveRuleLimitFactorHandler extends AbstractRuleFactorHandler {

    @Autowired
    private EquityBenefitLimitService equityBenefitLimitService;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.BENEFIT.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode();
    }

    @Override
    public boolean needQueryFactorValueFromBaseSystem() {
        return true;
    }

    @Override
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        String userId = getUserId(engineContextDTO);
        if (StringUtils.isBlank(userId)) {
            return;
        }

        // 获取权益维度频次结果
        List<BenefitIdInfoDTO> needQueryUserLimitBenefits = getBenefitUserLimitByBenefitRules(rules);
        List<GetBenefitLimitCacheInfoResp> benefitLimitCacheInfoRespList = equityBenefitLimitService
                .getBenefitLimitCacheMul(userId, needQueryUserLimitBenefits);
        ruleConditionFactorValueDTO.setBenefitReceiveLimitFactorValueMap(gainBenefitFactorValueMap(benefitLimitCacheInfoRespList, needQueryUserLimitBenefits.get(0)));
    }

    private List<BenefitIdInfoDTO> getBenefitUserLimitByBenefitRules(List<GeneralRuleCacheDTO> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return null;
        }
        return rules.stream().map(ruleCacheDTO -> {
            ReceiveLimitRuleDTO receiveLimitRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleCacheDTO, ReceiveLimitRuleDTO.class);
            if (Objects.isNull(receiveLimitRuleDTO)) {
                return null;
            }
            BenefitIdInfoDTO benefitStockLimitReq = new BenefitIdInfoDTO();
            benefitStockLimitReq.setActivityId(ruleCacheDTO.getActivityId());
            benefitStockLimitReq.setStrategyId(ruleCacheDTO.getStrategyId());
            benefitStockLimitReq.setBenefitGroupId(ruleCacheDTO.getBenefitGroupId());
            benefitStockLimitReq.setBenefitId(ruleCacheDTO.getBenefitId());
            return benefitStockLimitReq;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private Map<String, BenefitReceiveLimitFactorValueDTO> gainBenefitFactorValueMap(List<GetBenefitLimitCacheInfoResp> respList, BenefitIdInfoDTO benefitIdInfoDTO) {
        Map<String, BenefitReceiveLimitFactorValueDTO> factorValueMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(respList)) {
            return factorValueMap;
        }
        respList.forEach(getBenefitLimitCacheInfoResp -> {
            convert(factorValueMap, getBenefitLimitCacheInfoResp);
        });

        return factorValueMap;
    }

    private void convert(Map<String, BenefitReceiveLimitFactorValueDTO> factorValueMap, GetBenefitLimitCacheInfoResp getBenefitLimitCacheInfoResp) {
        Long strategyId = getBenefitLimitCacheInfoResp.getStrategyId();
        Long benefitId = getBenefitLimitCacheInfoResp.getBenefitId();
        String key = strategyId + Constants.COLON + benefitId;
        Long limitCount = getBenefitLimitCacheInfoResp.getLimitCount();
        Long receiveCount = getBenefitLimitCacheInfoResp.getReceiveCount();
        Long freezeCount = getBenefitLimitCacheInfoResp.getFreezeCount();

        BenefitReceiveLimitFactorValueDTO benefitReceiveLimitFactorValueDTO = new BenefitReceiveLimitFactorValueDTO();
        benefitReceiveLimitFactorValueDTO.setActivityId(getBenefitLimitCacheInfoResp.getActivityId());
        benefitReceiveLimitFactorValueDTO.setStrategyId(strategyId);
        benefitReceiveLimitFactorValueDTO.setBenefitGroupId(getBenefitLimitCacheInfoResp.getBenefitGroupId());
        benefitReceiveLimitFactorValueDTO.setBenefitId(benefitId);
        benefitReceiveLimitFactorValueDTO.setUserLimitCount(limitCount);
        benefitReceiveLimitFactorValueDTO.setUserReceiveCount(receiveCount);
        benefitReceiveLimitFactorValueDTO.setUserFreezeCount(freezeCount);
        benefitReceiveLimitFactorValueDTO.setCanReceive(isCanReceive(limitCount, receiveCount, freezeCount));
        factorValueMap.put(key, benefitReceiveLimitFactorValueDTO);
    }

    /**
     * 判断是否可领取
     *
     * @param limitCount   可领取数量
     * @param receiveCount 已领取数量
     * @param freezeCount  冻结数量
     * @return 是否可领取
     */
    protected boolean isCanReceive(Long limitCount, Long receiveCount, Long freezeCount) {
        // 没有频次限制
        if (Objects.isNull(limitCount) || Objects.isNull(receiveCount)) {
            return true;
        }
        // 存在冻结的数据
        if (Objects.nonNull(freezeCount) && freezeCount > 0) {
            return true;
        }
        return limitCount > receiveCount;
    }
}
