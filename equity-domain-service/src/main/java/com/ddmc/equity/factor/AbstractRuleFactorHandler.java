package com.ddmc.equity.factor;

import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.benefit_stock.QueryStockStatusByRedisMulResp;
import com.ddmc.equity.domain.dto.rule.BenefitStockFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public abstract class AbstractRuleFactorHandler {

    /**
     * 获取规则维度
     *
     * @return 规则维度
     */
    public abstract Integer getRuleDimension();

    /**
     * 获取规则类型
     *
     * @return 规则类型
     */
    public abstract Integer getRuleType();

    /**
     * 是否需要从基础系统获取对应规则因子值
     *
     * @return 是否需要
     */
    public boolean needQueryFactorValueFromBaseSystem() {
        return false;
    }

    /**
     * 获取规则因子值
     *
     * @param engineContextDTO            上下文
     * @param sceneActivityDTOList        活动列表
     * @param rules                       规则列表
     * @param ruleConditionFactorValueDTO 规则因子值
     */
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
    }

    /**
     * 获取用户 id
     *
     * @param engineContextDTO 上下文
     * @return 用户 id
     */
    protected String getUserId(EngineContextDTO engineContextDTO) {
        return Optional.ofNullable(engineContextDTO).map(EngineContextDTO::getUserId).orElse(null);
    }

    /**
     * 库存因子值组装
     *
     * @param resp 库存信息
     * @return 库存因子值
     */
    protected Map<String, BenefitStockFactorValueDTO> gainBenefitFactorValueMap(QueryStockStatusByRedisMulResp resp) {
        Map<String, BenefitStockFactorValueDTO> factorValueMap = Maps.newHashMap();
        if (Objects.isNull(resp) || MapUtils.isEmpty(resp.getStockInfo())) {
            return factorValueMap;
        }
        resp.getStockInfo().forEach((k, v) -> {
            Long strategyId = k.getStrategyId();
            Long benefitId = k.getBenefitId();
            String key = strategyId + Constants.COLON + benefitId;

            BenefitStockFactorValueDTO benefitStockFactorValueDTO = new BenefitStockFactorValueDTO();
            benefitStockFactorValueDTO.setStrategyId(strategyId);
            benefitStockFactorValueDTO.setBenefitGroupId(k.getBenefitGroupId());
            benefitStockFactorValueDTO.setBenefitId(benefitId);
            benefitStockFactorValueDTO.setCanReceive(v);
            factorValueMap.put(key, benefitStockFactorValueDTO);
        });
        return factorValueMap;
    }
}
