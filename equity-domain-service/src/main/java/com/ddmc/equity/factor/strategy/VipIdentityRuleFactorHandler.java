package com.ddmc.equity.factor.strategy;

import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.common.enums.VipIdentityEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.condition.VipIdentityRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.factor.AbstractRuleFactorHandler;
import com.ddmc.equity.infra.rpc.vip.EquityUserVipProxy;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 会员身份
 *
 * <AUTHOR>
 */
@Component
public class VipIdentityRuleFactorHandler extends AbstractRuleFactorHandler {

    @Autowired
    private EquityUserVipProxy equityUserVipProxy;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.VIP_IDENTITY_RULE.getCode();
    }

    @Override
    public boolean needQueryFactorValueFromBaseSystem() {
        return true;
    }

    @Override
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        String userId = getUserId(engineContextDTO);
        ruleConditionFactorValueDTO.setUserStatus(getUserVipIdentity(userId, rules));
    }

    private Integer getUserVipIdentity(String userId, List<GeneralRuleCacheDTO> ruleDTOList) {
        // 默认非会员
        if (StringUtils.isBlank(userId)) {
            return VipIdentityEnum.NO_VIP.getCode();
        }

        // 规则为空，则不发起调用，默认为空
        List<VipIdentityRuleDTO> vipIdentityRuleDTOList = RuleConvertEntity.convertToFormatRuleDTOList(ruleDTOList, VipIdentityRuleDTO.class);
        if (CollectionUtils.isEmpty(vipIdentityRuleDTOList)) {
            return null;
        }

        // 判断所有规则是否为全部用户。如果规则为全部用户，则不发起调用，默认为空
        boolean isAllUser = vipIdentityRuleDTOList.stream().allMatch(e -> VipIdentityEnum.ALL.getCode().equals(e.getUserStatus()));
        if (isAllUser) {
            return null;
        }

        // 查询会员状态
        Boolean isVip = equityUserVipProxy.isVip(userId);
        return Boolean.TRUE.equals(isVip) ? VipIdentityEnum.VIP.getCode() : VipIdentityEnum.NO_VIP.getCode();
    }
}
