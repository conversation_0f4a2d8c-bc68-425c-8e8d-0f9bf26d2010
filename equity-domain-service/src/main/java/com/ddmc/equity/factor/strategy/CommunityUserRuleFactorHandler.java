package com.ddmc.equity.factor.strategy;

import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.factor.AbstractRuleFactorHandler;
import com.ddmc.equity.infra.rpc.tag.EquityUserTagsProxy;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 社群用户规则因子处理器
 *
 * <AUTHOR>
 */
@Component
public class CommunityUserRuleFactorHandler extends AbstractRuleFactorHandler {

    @Resource
    private EquityUserTagsProxy equityUserTagsProxy;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.COMMUNITY_USER.getCode();
    }

    @Override
    public boolean needQueryFactorValueFromBaseSystem() {
        return true;
    }

    @Override
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        String userId = getUserId(engineContextDTO);
        if (StringUtils.isBlank(userId)) {
            return;
        }

        Boolean isCommunityUser = equityUserTagsProxy.isCommunityUser(userId);
        ruleConditionFactorValueDTO.setIsCommunityUser(isCommunityUser);
    }
} 