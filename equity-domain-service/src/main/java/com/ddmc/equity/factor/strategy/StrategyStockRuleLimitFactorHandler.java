package com.ddmc.equity.factor.strategy;

import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.benefit_stock.BenefitStockLimitReq;
import com.ddmc.equity.domain.dto.benefit_stock.QueryStockStatusByRedisMulReq;
import com.ddmc.equity.domain.dto.benefit_stock.QueryStockStatusByRedisMulResp;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.domain.service.benefit_stock.EquityBenefitStockDomainService;
import com.ddmc.equity.factor.AbstractRuleFactorHandler;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 领取库存限制规则
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class StrategyStockRuleLimitFactorHandler extends AbstractRuleFactorHandler {

    @Autowired
    private EquityBenefitStockDomainService equityBenefitStockDomainService;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.STRATEGY.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.STOCK_LIMIT_RULE.getCode();
    }

    @Override
    public boolean needQueryFactorValueFromBaseSystem() {
        return true;
    }

    @Override
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        if (Objects.isNull(engineContextDTO)) {
            return;
        }
        // 需要过滤没有对应的领取库存限制规则的策略，不去查询 redis 获取库存信息
        List<BenefitStockLimitReq> byStrategyRules = getBenefitStockLimitReqsByStrategyRules(sceneActivityDTOList, rules);
        if (CollectionUtils.isEmpty(byStrategyRules)) {
            return;
        }
        QueryStockStatusByRedisMulReq queryStockStatusByRedisMulReq = QueryStockStatusByRedisMulReq.builder()
                .reqList(byStrategyRules)
                .build();
        QueryStockStatusByRedisMulResp queryStockStatusByRedisMulResp = equityBenefitStockDomainService.queryStockStatusByRedisMul(queryStockStatusByRedisMulReq);
        // 规则因子值组装
        ruleConditionFactorValueDTO.setBenefitStockLimitFactorValueMap(gainBenefitFactorValueMap(queryStockStatusByRedisMulResp));
    }

    private List<BenefitStockLimitReq> getBenefitStockLimitReqsByStrategyRules(List<SceneActivityCacheDto> activities,
                                                                               List<GeneralRuleCacheDTO> rules) {
        if (CollectionUtils.isEmpty(activities)) {
            return null;
        }
        Map<Long /* strategyId */, GeneralRuleCacheDTO> strategyRuleMap = CollectionUtils.isEmpty(rules) ? Maps.newHashMap() :
                rules.stream().collect(Collectors.toMap(GeneralRuleCacheDTO::getStrategyId, Function.identity(), (v1, v2) -> v1));
        return activities.stream().map(activity ->
                getBenefitStockLimitReqsByStrategyRules(activity.getStrategyCacheDtoList(), strategyRuleMap)
        ).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<BenefitStockLimitReq> getBenefitStockLimitReqsByStrategyRules(List<StrategyCacheDto> strategies,
                                                                               Map<Long, GeneralRuleCacheDTO> strategyRuleMap) {
        if (CollectionUtils.isEmpty(strategies)) {
            return null;
        }
        return strategies.stream().map(strategy -> {
            Long strategyId = strategy.getStrategyId();
            // 如果该策略没有对应的领取库存限制规则，则过滤不用查询 redis 获取库存信息
            GeneralRuleCacheDTO ruleCacheDTO = strategyRuleMap.get(strategyId);
            StockLimitRuleDTO stockLimitRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleCacheDTO, StockLimitRuleDTO.class);
            if (Objects.isNull(stockLimitRuleDTO)) {
                return null;
            }
            Integer planDateType = stockLimitRuleDTO.getPlanDateType();
            return getBenefitStockLimitReqsByStrategyRules(strategyId, strategy.getStrategyBenefitGroup().get(strategyId), planDateType);
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<BenefitStockLimitReq> getBenefitStockLimitReqsByStrategyRules(Long strategyId,
                                                                               List<BenefitIdWithConfDto> benefits,
                                                                               Integer planDateType) {
        if (CollectionUtils.isEmpty(benefits)) {
            return null;
        }
        return benefits.stream().map(benefit -> {
            BenefitStockLimitReq benefitStockLimitReq = new BenefitStockLimitReq();
            benefitStockLimitReq.setStrategyId(strategyId);
            benefitStockLimitReq.setBenefitGroupId(benefit.getBenefitGroupId());
            benefitStockLimitReq.setBenefitId(benefit.getId());
            benefitStockLimitReq.setPlanDateType(planDateType);
            return benefitStockLimitReq;
        }).collect(Collectors.toList());
    }
}
