package com.ddmc.equity.factor;

import com.ddmc.equity.common.apollo.RuleConstants;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.util.MapUtils;
import com.ddmc.equity.common.util.ThreadsUtils;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.entity.activity.ActivityCacheRuleConvertEntity;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.google.common.collect.ListMultimap;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 规则条件中的值处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RuleFactorContext {

    @Autowired
    List<AbstractRuleFactorHandler> abstractRuleHandlers;
    @Autowired
    private RuleConstants ruleConstants;

    private final Map<Pair<Integer /* ruleDimension */, Integer /* ruleType */>, AbstractRuleFactorHandler> HANDLER_MAP = new HashMap<>(16);

    /**
     * 将处理类放到 HANDLER_MAP 中
     */
    @PostConstruct
    public void afterPropertiesSet() {
        HANDLER_MAP.putAll(abstractRuleHandlers.stream().collect(Collectors.toMap(handler ->
                Pair.of(handler.getRuleDimension(), handler.getRuleType()), Function.identity(), (v1, v2) -> v1)));
    }

    public RuleConditionFactorValueDTO getRuleConditionFactorValue(EngineContextDTO engineContextDTO,
                                                                   List<SceneActivityCacheDto> activeActivityList) {
        RuleConditionFactorValueDTO ruleConditionFactorValueDTO = new RuleConditionFactorValueDTO();
        ListMultimap<Pair<Integer /* ruleDimension */, Integer /* ruleType */>, GeneralRuleCacheDTO> generalRulesMap =
                ActivityCacheRuleConvertEntity.getGeneralRulesMap(activeActivityList);
        if (MapUtils.isEmpty(generalRulesMap)) {
            return ruleConditionFactorValueDTO;
        }
        List<CompletableFuture<Void>> asyncTasks = Lists.newArrayList();
        generalRulesMap.keySet().forEach(key -> {
            List<GeneralRuleCacheDTO> rules = generalRulesMap.get(key);
            if (CollectionUtils.isEmpty(rules)) {
                return;
            }
            // 获取处理类
            AbstractRuleFactorHandler handler = HANDLER_MAP.get(key);
            if (Objects.isNull(handler)) {
                return;
            }
            if (!handler.needQueryFactorValueFromBaseSystem()) {
                return;
            }
            // 异步获取因子值
            CompletableFuture<Void> ruleFactorFuture = ThreadsUtils.runAsync(() ->
                            handler.getRuleFactorValue(engineContextDTO, activeActivityList, rules, ruleConditionFactorValueDTO),
                    ThreadsUtils.getCoreThreadPoll(), Constants.RULE_FACTOR_QUERY);
            asyncTasks.add(ruleFactorFuture);
        });

        if (CollectionUtils.isEmpty(asyncTasks)) {
            return ruleConditionFactorValueDTO;
        }
        ThreadsUtils.getCompletableFutureList(asyncTasks, ruleConstants.getRuleQueryFromBaseSystemWaitMsTime(), TimeUnit.MILLISECONDS);
        return ruleConditionFactorValueDTO;
    }
}
