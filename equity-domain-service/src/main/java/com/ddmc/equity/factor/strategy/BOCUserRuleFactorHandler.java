package com.ddmc.equity.factor.strategy;

import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.condition.OldSysBOCUserRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.factor.AbstractRuleFactorHandler;
import com.ddmc.equity.infra.rpc.user.UserProxy;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.usercenter.open.entity.response.ThirdAuthOpenIdDTO;
import com.ddmc.usercenter.open.enums.ThirdAuthTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class BOCUserRuleFactorHandler extends AbstractRuleFactorHandler {

    @Resource
    private UserProxy userProxy;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.OLD_SYS_BANK_BOC_USER_RULE.getCode();
    }

    @Override
    public boolean needQueryFactorValueFromBaseSystem() {
        return true;
    }

    @Override
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        String userId = getUserId(engineContextDTO);
        if (StringUtils.isBlank(userId)) {
            return;
        }
        // 规则为空，则不发起调用，默认为空
        List<OldSysBOCUserRuleDTO> vipIdentityRuleDTOList = RuleConvertEntity.convertToFormatRuleDTOList(rules, OldSysBOCUserRuleDTO.class);
        if (CollectionUtils.isEmpty(vipIdentityRuleDTOList)) {
            return;
        }
        ThirdAuthOpenIdDTO thirdAuthOpenIdDTO = userProxy.getOpenId(userId, ThirdAuthTypeEnum.PLAT_BOC.getAuthType());
        ruleConditionFactorValueDTO.setBankCode(thirdAuthOpenIdDTO == null ? null : thirdAuthOpenIdDTO.getBocIbknum());
    }

}
