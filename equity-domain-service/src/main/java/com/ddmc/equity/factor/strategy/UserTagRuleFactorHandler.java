package com.ddmc.equity.factor.strategy;

import com.ddmc.equity.common.apollo.UserTagRulesConstants;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.common.util.GreyUtils;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.entity.activity.ActivityCacheRuleConvertEntity;
import com.ddmc.equity.factor.AbstractRuleFactorHandler;
import com.ddmc.equity.infra.rpc.tag.EquityUserRulesProxy;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 人群标签
 *
 * <AUTHOR>
 */
@Component
public class UserTagRuleFactorHandler extends AbstractRuleFactorHandler {

    @Autowired
    private EquityUserRulesProxy equityUserRulesProxy;
    @Autowired
    private UserTagRulesConstants userTagRulesConstants;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.USER_TAG_RULE.getCode();
    }

    @Override
    public boolean needQueryFactorValueFromBaseSystem() {
        return true;
    }

    @Override
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        String userId = getUserId(engineContextDTO);
        if (StringUtils.isBlank(userId)) {
            return;
        }
        ruleConditionFactorValueDTO.setUserMatchTagRuleIds(getUserMatchTagRules(userId, sceneActivityDTOList));
    }

    private List<String> getUserMatchTagRules(String userId, List<SceneActivityCacheDto> sceneActivityDTOList) {
        // 调用新接口
        if (useNewInterface(userId)) {
            List<String> tagRuleIds = ActivityCacheRuleConvertEntity.getTagRuleIds(sceneActivityDTOList);
            return equityUserRulesProxy.getMatchUserTagRules(userId, tagRuleIds);
        }
        // 老逻辑
        return equityUserRulesProxy.getUserMatchTagRules(userId);
    }

    private boolean useNewInterface(String userId) {
        if (!Boolean.TRUE.equals(userTagRulesConstants.getNewInterfaceSwitch())) {
            return false;
        }
        return GreyUtils.hitWhiteUser(userId, userTagRulesConstants.getNewInterfaceWhiteUserIds()) ||
                GreyUtils.hitGrayScale(userId, userTagRulesConstants.getNewInterfaceGrayScale());
    }
}
