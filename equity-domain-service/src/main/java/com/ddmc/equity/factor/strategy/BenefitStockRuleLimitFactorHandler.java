package com.ddmc.equity.factor.strategy;

import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.benefit_stock.BenefitStockLimitReq;
import com.ddmc.equity.domain.dto.benefit_stock.QueryStockStatusByRedisMulReq;
import com.ddmc.equity.domain.dto.benefit_stock.QueryStockStatusByRedisMulResp;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.domain.service.benefit_stock.EquityBenefitStockDomainService;
import com.ddmc.equity.factor.AbstractRuleFactorHandler;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 领取库存限制规则
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BenefitStockRuleLimitFactorHandler extends AbstractRuleFactorHandler {

    @Autowired
    private EquityBenefitStockDomainService equityBenefitStockDomainService;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.BENEFIT.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.STOCK_LIMIT_RULE.getCode();
    }

    @Override
    public boolean needQueryFactorValueFromBaseSystem() {
        return true;
    }

    @Override
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        if (Objects.isNull(engineContextDTO)) {
            return;
        }
        // 需要过滤没有对应的领取库存限制规则的策略，不去查询 redis 获取库存信息
        List<BenefitStockLimitReq> byBenefitRules = getBenefitStockLimitReqsByBenefitRules(rules);
        if (CollectionUtils.isEmpty(byBenefitRules)) {
            return;
        }
        QueryStockStatusByRedisMulReq queryStockStatusByRedisMulReq = QueryStockStatusByRedisMulReq.builder()
                .reqList(byBenefitRules)
                .build();
        QueryStockStatusByRedisMulResp queryStockStatusByRedisMulResp = equityBenefitStockDomainService.queryStockStatusByRedisMul(queryStockStatusByRedisMulReq);
        // 规则因子值组装
        ruleConditionFactorValueDTO.setBenefitStockLimitFactorValueMap(gainBenefitFactorValueMap(queryStockStatusByRedisMulResp));
    }

    private List<BenefitStockLimitReq> getBenefitStockLimitReqsByBenefitRules(List<GeneralRuleCacheDTO> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return null;
        }
        return rules.stream().map(ruleCacheDTO -> {
            StockLimitRuleDTO stockLimitRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleCacheDTO, StockLimitRuleDTO.class);
            if (Objects.isNull(stockLimitRuleDTO)) {
                return null;
            }
            BenefitStockLimitReq benefitStockLimitReq = new BenefitStockLimitReq();
            benefitStockLimitReq.setStrategyId(ruleCacheDTO.getStrategyId());
            benefitStockLimitReq.setBenefitGroupId(ruleCacheDTO.getBenefitGroupId());
            benefitStockLimitReq.setBenefitId(ruleCacheDTO.getBenefitId());
            benefitStockLimitReq.setPlanDateType(stockLimitRuleDTO.getPlanDateType());
            return benefitStockLimitReq;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
