package com.ddmc.equity.factor.strategy;

import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.condition.WeComUserRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.factor.AbstractRuleFactorHandler;
import com.ddmc.equity.infra.rpc.wecom.WeComUserProxy;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 人群标签
 *
 * <AUTHOR>
 */
@Component
public class WeComUserRuleFactorHandler extends AbstractRuleFactorHandler {

    @Autowired
    private WeComUserProxy weComUserProxy;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.WE_COM_USER_RULE.getCode();
    }

    @Override
    public boolean needQueryFactorValueFromBaseSystem() {
        return true;
    }

    @Override
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        String userId = getUserId(engineContextDTO);
        if (StringUtils.isBlank(userId)) {
            return;
        }
        // 规则为空，则不发起调用，默认为空
        List<WeComUserRuleDTO> vipIdentityRuleDTOList = RuleConvertEntity.convertToFormatRuleDTOList(rules, WeComUserRuleDTO.class);
        if (CollectionUtils.isEmpty(vipIdentityRuleDTOList)) {
            return;
        }
        ruleConditionFactorValueDTO.setWeComUserStatus(weComUserProxy.checkWeComNewUser(userId) ? 1 : 2);
    }
}
