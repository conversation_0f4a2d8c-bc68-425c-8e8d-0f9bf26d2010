package com.ddmc.equity.factor.strategy;

import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.BenefitReceiveLimitFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.domain.service.benefit_limit.EquityBenefitLimitService;
import com.ddmc.equity.domain.valueobject.benefit_limit.BenefitIdInfoDTO;
import com.ddmc.equity.domain.valueobject.benefit_limit.GetBenefitLimitCacheInfoResp;
import com.ddmc.equity.factor.AbstractRuleFactorHandler;
import com.ddmc.equity.model.dto.BenefitIdWithConfDto;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.equity.model.dto.StrategyCacheDto;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 领取频次限制规则
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class StrategyReceiveRuleLimitFactorHandler extends AbstractRuleFactorHandler {

    @Autowired
    private EquityBenefitLimitService equityBenefitLimitService;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.STRATEGY.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.RECEIVE_LIMIT_RULE.getCode();
    }

    @Override
    public boolean needQueryFactorValueFromBaseSystem() {
        return true;
    }

    @Override
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        String userId = getUserId(engineContextDTO);
        if (StringUtils.isBlank(userId)) {
            return;
        }

        // 获取权益维度频次结果
        List<BenefitIdInfoDTO> needQueryUserLimitBenefits = getNeedQueryUserLimitBenefits(sceneActivityDTOList, rules);
        List<GetBenefitLimitCacheInfoResp> benefitLimitCacheInfoRespList = equityBenefitLimitService
                .getBenefitLimitCacheMul(userId, needQueryUserLimitBenefits);
        ruleConditionFactorValueDTO.setBenefitReceiveLimitFactorValueMap(gainBenefitFactorValueMap(benefitLimitCacheInfoRespList));
    }

    private Map<String, BenefitReceiveLimitFactorValueDTO> gainBenefitFactorValueMap(List<GetBenefitLimitCacheInfoResp> respList) {
        Map<String, BenefitReceiveLimitFactorValueDTO> factorValueMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(respList)) {
            return factorValueMap;
        }
        respList.forEach(getBenefitLimitCacheInfoResp -> {
            Long strategyId = getBenefitLimitCacheInfoResp.getStrategyId();
            Long benefitId = getBenefitLimitCacheInfoResp.getBenefitId();
            String key = strategyId + Constants.COLON + benefitId;
            Long limitCount = getBenefitLimitCacheInfoResp.getLimitCount();
            Long receiveCount = getBenefitLimitCacheInfoResp.getReceiveCount();
            Long freezeCount = getBenefitLimitCacheInfoResp.getFreezeCount();

            BenefitReceiveLimitFactorValueDTO benefitReceiveLimitFactorValueDTO = new BenefitReceiveLimitFactorValueDTO();
            benefitReceiveLimitFactorValueDTO.setActivityId(getBenefitLimitCacheInfoResp.getActivityId());
            benefitReceiveLimitFactorValueDTO.setStrategyId(strategyId);
            benefitReceiveLimitFactorValueDTO.setBenefitGroupId(getBenefitLimitCacheInfoResp.getBenefitGroupId());
            benefitReceiveLimitFactorValueDTO.setBenefitId(benefitId);
            benefitReceiveLimitFactorValueDTO.setUserLimitCount(limitCount);
            benefitReceiveLimitFactorValueDTO.setUserReceiveCount(receiveCount);
            benefitReceiveLimitFactorValueDTO.setUserFreezeCount(freezeCount);
            benefitReceiveLimitFactorValueDTO.setCanReceive(isCanReceive(limitCount, receiveCount, freezeCount));
            factorValueMap.put(key, benefitReceiveLimitFactorValueDTO);
        });
        return factorValueMap;
    }

    private List<BenefitIdInfoDTO> getNeedQueryUserLimitBenefits(List<SceneActivityCacheDto> activities,
                                                                 List<GeneralRuleCacheDTO> rules) {
        if (CollectionUtils.isEmpty(activities)) {
            return null;
        }
        Map<Long /* strategyId */, GeneralRuleCacheDTO> strategyRuleMap = CollectionUtils.isEmpty(rules) ? Maps.newHashMap() :
                rules.stream().collect(Collectors.toMap(GeneralRuleCacheDTO::getStrategyId, Function.identity(), (v1, v2) -> v1));
        return activities.stream().map(activity ->
                getNeedQueryUserLimitBenefits(activity.getActivityId(), activity.getStrategyCacheDtoList(), strategyRuleMap)
        ).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<BenefitIdInfoDTO> getNeedQueryUserLimitBenefits(Long activityId, List<StrategyCacheDto> strategies,
                                                                 Map<Long, GeneralRuleCacheDTO> strategyRuleMap) {
        if (CollectionUtils.isEmpty(strategies)) {
            return null;
        }
        return strategies.stream().map(strategy -> {
            Long strategyId = strategy.getStrategyId();
            // 如果该策略没有对应的领取频次限制规则，则过滤不用查询 redis 获取频次信息
            GeneralRuleCacheDTO ruleCacheDTO = strategyRuleMap.get(strategyId);
            ReceiveLimitRuleDTO receiveLimitRuleDTO = RuleConvertEntity.convertToFormatRuleDTO(ruleCacheDTO, ReceiveLimitRuleDTO.class);
            if (Objects.isNull(receiveLimitRuleDTO)) {
                return null;
            }
            return getNeedQueryUserLimitBenefits(activityId, strategyId, strategy.getStrategyBenefitGroup().get(strategyId));
        }).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<BenefitIdInfoDTO> getNeedQueryUserLimitBenefits(Long activityId, Long strategyId,
                                                                 List<BenefitIdWithConfDto> benefits) {
        if (CollectionUtils.isEmpty(benefits)) {
            return null;
        }
        return benefits.stream().map(benefit -> convertToBenefitIdInfoDTO(activityId, strategyId, benefit)).collect(Collectors.toList());
    }

    private BenefitIdInfoDTO convertToBenefitIdInfoDTO(Long activityId, Long strategyId, BenefitIdWithConfDto benefit) {
        return BenefitIdInfoDTO.builder()
                .activityId(activityId)
                .strategyId(strategyId)
                .benefitGroupId(benefit.getBenefitGroupId())
                .benefitId(benefit.getId())
                .build();
    }

    /**
     * 判断是否可领取
     *
     * @param limitCount   可领取数量
     * @param receiveCount 已领取数量
     * @param freezeCount  冻结数量
     * @return 是否可领取
     */
    protected boolean isCanReceive(Long limitCount, Long receiveCount, Long freezeCount) {
        // 没有频次限制
        if (Objects.isNull(limitCount) || Objects.isNull(receiveCount)) {
            return true;
        }
        // 存在冻结的数据
        if (Objects.nonNull(freezeCount) && freezeCount > 0) {
            return true;
        }
        return limitCount > receiveCount;
    }
}
