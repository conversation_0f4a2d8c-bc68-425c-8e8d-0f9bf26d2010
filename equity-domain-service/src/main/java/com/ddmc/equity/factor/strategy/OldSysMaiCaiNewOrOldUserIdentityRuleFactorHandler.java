package com.ddmc.equity.factor.strategy;

import com.ddmc.equity.common.enums.OldSysMaiCaiUserNewOrOldIdentityEnum;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.factor.AbstractRuleFactorHandler;
import com.ddmc.equity.infra.rpc.user.UserProxy;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 老买菜系统新老用户身份规则
 *
 * <AUTHOR>
 */
@Component
public class OldSysMaiCaiNewOrOldUserIdentityRuleFactorHandler extends AbstractRuleFactorHandler {

    @Resource
    private UserProxy userProxy;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.OLD_SYS_MAI_CAI_NEW_OR_OLD_USER_RULE.getCode();
    }

    @Override
    public boolean needQueryFactorValueFromBaseSystem() {
        return true;
    }

    @Override
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        String userId = getUserId(engineContextDTO);
        if (StringUtils.isBlank(userId)) {
            return;
        }
        Boolean isNewUser = userProxy.isNew(userId);
        Integer oldSysMaiCaiUserNewOrOldIdentity = Objects.isNull(isNewUser) ? null :
                isNewUser ? OldSysMaiCaiUserNewOrOldIdentityEnum.NEW.getCode() : OldSysMaiCaiUserNewOrOldIdentityEnum.OLD.getCode();
        ruleConditionFactorValueDTO.setOldSysMaiCaiUserNewOrOldIdentity(oldSysMaiCaiUserNewOrOldIdentity);
    }
}
