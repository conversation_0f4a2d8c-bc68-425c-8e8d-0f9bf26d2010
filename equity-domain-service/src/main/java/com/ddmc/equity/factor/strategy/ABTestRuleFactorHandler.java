package com.ddmc.equity.factor.strategy;

import com.ddmc.abtest.dto.AbTestHitResultDTO;
import com.ddmc.equity.common.apollo.RuleConstants;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.common.util.MapUtils;
import com.ddmc.equity.common.util.ThreadsUtils;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.condition.ABTestRuleDTO;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.factor.AbstractRuleFactorHandler;
import com.ddmc.equity.infra.rpc.ab.AbProxy;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * AB 实验规则
 *
 * <AUTHOR>
 */
@Component
public class ABTestRuleFactorHandler extends AbstractRuleFactorHandler {

    @Autowired
    private AbProxy abProxy;
    @Autowired
    private RuleConstants ruleConstants;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.AB_TEST_RULE.getCode();
    }

    @Override
    public boolean needQueryFactorValueFromBaseSystem() {
        return true;
    }

    @Override
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        String userId = getUserId(engineContextDTO);
        if (StringUtils.isBlank(userId)) {
            return;
        }
        // 如果所有活动都没有 Ab 实验规则，则无需获取用户命中的实验结果规则因子值
        List<ABTestRuleDTO> abTestRuleDTOList = RuleConvertEntity.convertToFormatRuleDTOList(rules, ABTestRuleDTO.class);
        if (CollectionUtils.isEmpty(abTestRuleDTOList)) {
            return;
        }

        // 获取用户命中的实验结果
        List<String> layerIds = abTestRuleDTOList.stream().map(ABTestRuleDTO::getLayerId)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String /* layerId */, AbTestHitResultDTO> abTestHitMap = splitBatchQueryUserAbTestHitMap(engineContextDTO, layerIds);

        // 组装用户命中的实验组规则
        Map<String /* layerId */, ABTestRuleDTO> abTestRuleMap = Maps.newHashMap();
        abTestRuleDTOList.forEach(abTestRuleDTO -> {
            String layerId = abTestRuleDTO.getLayerId();
            abTestRuleDTO.setUserHitGroup(getUserHitAbTestGroups(abTestHitMap, abTestRuleDTO.getDefaultGroup(), layerId));
            abTestRuleMap.put(layerId, abTestRuleDTO);
        });
        ruleConditionFactorValueDTO.setUserHitAbTestRuleMap(abTestRuleMap);
    }

    /**
     * 分割异步查询用户命中的 AB 实验信息
     *
     * @param engineContextDTO 上下文
     * @param layerIds         实验 ids
     * @return 用户命中的实验结果
     */
    private Map<String /* layerId */, AbTestHitResultDTO> splitBatchQueryUserAbTestHitMap(EngineContextDTO engineContextDTO,
                                                                                          List<String> layerIds) {
        if (CollectionUtils.isEmpty(layerIds)) {
            return Maps.newHashMap();
        }

        // 当前实验 ids 不超过限制数量，直接调用 proxy 查询
        if (CollectionUtils.size(layerIds) <= ruleConstants.getAbTestBatchQueryLimitSize()) {
            return queryUserAbTestHitMap(engineContextDTO, layerIds);
        }

        // 当前实验 ids 超过限制数量，分页异步调用 proxy 查询
        Map<String /* layerId */, AbTestHitResultDTO> batchMap = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> asyncTasks = Lists.newArrayList();
        ListUtils.partition(layerIds, ruleConstants.getAbTestBatchQueryLimitSize()).forEach(sub -> {
            CompletableFuture<Void> future = ThreadsUtils.runAsync(() -> queryUserAbTestHitMapAndSetResult(engineContextDTO, sub, batchMap),
                    ThreadsUtils.getCoreThreadPoll(), Constants.USER_TAG_MATCH_QUERY);
            asyncTasks.add(future);
        });

        // 等待获取任务处理结果，任务为空则直接返回
        if (CollectionUtils.isEmpty(asyncTasks)) {
            return Maps.newHashMap();
        }
        ThreadsUtils.getCompletableFutureList(asyncTasks, ruleConstants.getAbTestBatchQueryWaitMsTime(), TimeUnit.MILLISECONDS);
        return batchMap;
    }

    /**
     * 查询用户命中的 AB 实验信息，并设置 batchAbTestHitMap 结果
     *
     * @param engineContextDTO  上下文
     * @param layerIdList       实验 ids
     * @param batchAbTestHitMap 用户命中的实验结果
     */
    private void queryUserAbTestHitMapAndSetResult(EngineContextDTO engineContextDTO, List<String> layerIdList,
                                                   Map<String /* layerId */, AbTestHitResultDTO> batchAbTestHitMap) {
        Map<String, AbTestHitResultDTO> abTestHitMap = queryUserAbTestHitMap(engineContextDTO, layerIdList);
        if (MapUtils.isNotEmpty(abTestHitMap)) {
            batchAbTestHitMap.putAll(abTestHitMap);
        }
    }

    /**
     * 查询用户命中的 AB 实验信息
     *
     * @param engineContextDTO 上下文
     * @param layerIds         实验 ids
     * @return 用户命中的实验结果
     */
    private Map<String /* layerId */, AbTestHitResultDTO> queryUserAbTestHitMap(EngineContextDTO engineContextDTO, List<String> layerIds) {
        return abProxy.getAbInfoNew(engineContextDTO.getNativeVersion(), engineContextDTO.getDeviceId(),
                engineContextDTO.getStationId(), engineContextDTO.getAppClientId(), engineContextDTO.getUserId(),
                engineContextDTO.getCityNumber(), layerIds);
    }

    /**
     * 获取用户命中的 AB 实验组，默认为兜底组
     *
     * @param abTestHitMap 用户命中的实验结果
     * @param defaultGroup 兜底组
     * @param layerId      实验 id
     * @return 用户命中的 AB 实验组
     */
    private List<String> getUserHitAbTestGroups(Map<String /* layerId */, AbTestHitResultDTO> abTestHitMap,
                                                List<String> defaultGroup, String layerId) {
        if (MapUtils.isEmpty(abTestHitMap)) {
            return defaultGroup;
        }
        AbTestHitResultDTO abTestHitResultDTO = abTestHitMap.get(layerId);
        if (Objects.isNull(abTestHitResultDTO)) {
            return defaultGroup;
        }
        return Lists.newArrayList(abTestHitResultDTO.getGroup());
    }
}
