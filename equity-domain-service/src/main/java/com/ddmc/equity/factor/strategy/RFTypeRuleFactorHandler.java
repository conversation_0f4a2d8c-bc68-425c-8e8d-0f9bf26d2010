package com.ddmc.equity.factor.strategy;

import com.ddmc.equity.common.enums.RuleDimensionEnum;
import com.ddmc.equity.common.enums.RuleTypeEnum;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.rule.GeneralRuleCacheDTO;
import com.ddmc.equity.domain.dto.rule.RuleConditionFactorValueDTO;
import com.ddmc.equity.domain.dto.rule.UserRfTypeDTO;
import com.ddmc.equity.factor.AbstractRuleFactorHandler;
import com.ddmc.equity.infra.rpc.tag.EquityUserTagsProxy;
import com.ddmc.equity.infra.rpc.tag.dto.UserRfTypeRespDTO;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 人群标签
 *
 * <AUTHOR>
 */
@Component
public class RFTypeRuleFactorHandler extends AbstractRuleFactorHandler {

    @Autowired
    private EquityUserTagsProxy equityUserTagsProxy;

    @Override
    public Integer getRuleDimension() {
        return RuleDimensionEnum.COMMON.getCode();
    }

    @Override
    public Integer getRuleType() {
        return RuleTypeEnum.RF_TYPE_RULE.getCode();
    }

    @Override
    public boolean needQueryFactorValueFromBaseSystem() {
        return true;
    }

    @Override
    public void getRuleFactorValue(EngineContextDTO engineContextDTO, List<SceneActivityCacheDto> sceneActivityDTOList,
                                   List<GeneralRuleCacheDTO> rules, RuleConditionFactorValueDTO ruleConditionFactorValueDTO) {
        String userId = getUserId(engineContextDTO);
        if (StringUtils.isBlank(userId)) {
            return;
        }
        ruleConditionFactorValueDTO.setUserRfTypeDTO(getUserRfTypeDTO(userId));
    }

    private UserRfTypeDTO getUserRfTypeDTO(String userId) {
        UserRfTypeRespDTO userRfTypeRespDTO = equityUserTagsProxy.getUserRfTypeResp(userId);
        if (Objects.isNull(userRfTypeRespDTO)) {
            return null;
        }
        return UserRfTypeDTO.builder()
                .rfFirstTypeName(userRfTypeRespDTO.getRfFirstTypeName())
                .rfTypeName(userRfTypeRespDTO.getRfTypeName())
                .build();
    }
}
