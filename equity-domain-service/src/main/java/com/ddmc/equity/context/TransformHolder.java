package com.ddmc.equity.context;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.Optional;

/**
 * @className: TransformHolder
 * @description:
 * @author: yloopdaed
 * @date: 2022/9/8
 **/
public class TransformHolder {

    private static final TransformNamedThreadLocal<CommonParamContext> contextHolder
            = new TransformNamedThreadLocal<>("transform context");

    /*
     * @Description: 静态内部ThreadLocal类 传输用
     *
     * @return: ThreadLocal
     * @Author: yloopdaed
     * @Date: 9:05 下午 2021/8/19
     */
    public static class TransformNamedThreadLocal<T> extends ThreadLocal<T> {

        private final String name;

        public TransformNamedThreadLocal(String name) {
            Assert.hasText(name, "Name must not be empty");
            this.name = name;
        }

        @Override
        public String toString() {
            return this.name;
        }

    }

    /* action */
    public static void setContext(CommonParamContext commonParam) {
        contextHolder.set(commonParam);
    }

    private static CommonParamContext getContext() {
        return contextHolder.get();
    }

    public static void clearContext() {
        contextHolder.remove();
    }

    /* dynamic */
    public static String getBrandUid() {
        return Optional.ofNullable(getContext()).map(CommonParamContext::getUid).orElse(StringUtils.EMPTY);
    }

}
