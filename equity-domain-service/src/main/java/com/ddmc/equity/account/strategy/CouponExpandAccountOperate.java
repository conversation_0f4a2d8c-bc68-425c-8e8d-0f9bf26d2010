package com.ddmc.equity.account.strategy;

import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.ThreadsUtils;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.FallbackEquityDto;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.entity.account.CouponExpandAccountConvertEntity;
import com.ddmc.equity.domain.service.coupon_expand.CouponExpandAccountCollectDomainService;
import com.ddmc.equity.domain.service.coupon_expand.CouponExpandAccountDomainService;
import com.ddmc.equity.domain.service.coupon_expand.CouponExpandAccountRecordDomainService;
import com.ddmc.equity.infra.repository.dao.CouponExpandAccountDO;
import com.ddmc.equity.infra.repository.dao.CouponExpandAccountRecordDO;
import com.ddmc.equity.infra.rpc.voucher.VoucherSendTicketProxy;
import com.ddmc.equity.infra.rpc.voucher.dto.SendTicketResDTO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.QueryEquityDto;
import com.ddmc.equity.model.vo.CouponExpandReceiveVO;
import com.ddmc.equity.mq.rocket.producer.CouponExpandReceiveProducer;
import com.ddmc.vouchercore.client.request.ExpandTicketRequest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/26 16:42
 * @description
 */
@Slf4j
@Component
public class CouponExpandAccountOperate extends AbstractEquityAccountOperate {

    @Value("${spring.application.name}")
    private String applicationName;

    @Autowired
    private CouponExpandReceiveProducer couponExpandReceiveProducer;
    @Autowired
    private TransactionUtil transactionUtil;
    @Autowired
    private VoucherSendTicketProxy voucherSendTicketProxy;
    @Autowired
    private CouponExpandAccountDomainService couponExpandAccountDomainService;
    @Autowired
    private CouponExpandAccountRecordDomainService couponExpandAccountRecordDomainService;
    @Autowired
    private CouponExpandAccountCollectDomainService couponExpandAccountCollectDomainService;

    @Override
    public boolean checkUseEquity(EquityAccountContext equityAccountContext) {
        return false;
    }

    @Override
    public int doUseEquity(EquityAccountContext equityAccountContext) {
        return 0;
    }

    @Override
    public boolean checkFallbackEquity(EquityAccountContext equityAccountContext) {
        return false;
    }

    @Override
    public int doProvideEquity(EquityAccountContext equityAccountContext) {
        String userId = equityAccountContext.getUid();
        Long accountRecordId = equityAccountContext.getAccountRecordId();
        CouponExpandAccountDO accountDO = CouponExpandAccountConvertEntity.createAccountDO(equityAccountContext);

        transactionUtil.transactional(s -> {
            // 1 生成权益子账户
            couponExpandAccountDomainService.insertAccountDO(accountDO);

            // 2 更新权益子账户操作流水结果
            equityAccountContext.setAccountId(accountDO.getId());
            boolean updateAccountRecordResult = updateRecordSuccessById(accountRecordId, StatusEnum.SUCCESS.getCode(), equityAccountContext);
            if (!updateAccountRecordResult) {
                log.warn("doProvideEquity updateRecordSuccessById failure. userId={}, accountRecordId={}", userId, accountRecordId);
                throw AdminExceptionBuilder.build(ExceptionEnum.ACCOUNT_RECORD_UPDATE_FAIL);
            }
        });

        ThreadsUtils.runAsync(() -> {
            // 创建券膨胀权益子账户聚合表记录，用户 B 端聚合查询使用
            couponExpandAccountCollectDomainService.insertAccountCollectDO(accountDO);
            // 发送领取成功消息
            CouponExpandReceiveVO couponExpandReceiveVO = CouponExpandAccountConvertEntity.concertToCouponExpandReceiveVO(equityAccountContext);
            couponExpandReceiveProducer.send(couponExpandReceiveVO);
        }, ThreadsUtils.getOrdinaryThreadPoll(), Constants.COUPON_EXPAND_AFTER_DO_PROVIDE_EQUITY);

        return Constants.ONE;
    }

    @Override
    public List<AccountInfoDTO> doProvideEquityAndGetResult(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public List<AccountInfoDTO> retryDoProvideEquityAndGetResult(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public EquityRpcDto doProvideEquityRpc(EquityAccountContext equityAccountContext) {
        ExpandTicketRequest request = CouponExpandAccountConvertEntity.getExpandTicketRequest(equityAccountContext, applicationName);
        SendTicketResDTO resp = voucherSendTicketProxy.couponExpand(request);
        if (Objects.isNull(resp)) {
            EquityRpcDto equityRpcDto = new EquityRpcDto();
            equityRpcDto.setStatusEnum(StatusEnum.PROCESSING);
            equityRpcDto.setCode(ExceptionEnum.COMMON_ERROR.getCode());
            equityRpcDto.setMessage(ExceptionEnum.COMMON_ERROR.getMessage());
            return equityRpcDto;
        }
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        equityRpcDto.setStatusEnum(resp.getStatusEnum());
        equityRpcDto.setCode(resp.getCode());
        equityRpcDto.setMessage(resp.getMsg());
        equityRpcDto.setValue(resp.getUserTicketId());
        return equityRpcDto;
    }

    @Override
    public boolean updateRecordSuccessById(Long recordId, int status, EquityAccountContext equityAccountContext) {
        if (Objects.isNull(equityAccountContext)) {
            return false;
        }
        Map<String, Object> ruleLimitInfoMap = CouponExpandAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext);
        return couponExpandAccountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), status, equityAccountContext.getEquityRpcDto(), ruleLimitInfoMap);
    }

    @Override
    public boolean updateRecordExceptionById(Long recordId, int status, EquityAccountContext equityAccountContext) {
        if (Objects.isNull(equityAccountContext)) {
            return false;
        }
        Map<String, Object> ruleLimitInfoMap = CouponExpandAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext);
        return couponExpandAccountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), status, equityAccountContext.getEquityRpcDto(), ruleLimitInfoMap);
    }

    @Override
    public int doFallbackEquity(EquityAccountContext equityAccountContext) {
        return 0;
    }

    @Override
    public Long initRecord(int operateType, EquityAccountContext equityAccountContext) {
        CouponExpandAccountRecordDO accountRecordDO = CouponExpandAccountConvertEntity.createAccountRecordDO(operateType, equityAccountContext);
        couponExpandAccountRecordDomainService.insertAccountRecordDO(accountRecordDO);
        return accountRecordDO.getId();
    }

    @Override
    public FallbackEquityDto getFallbackEquityId(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public boolean havingBusinessData(EquityAccountContext equityAccountContext, Integer operateType, List<Integer> statusList) {
        return false;
    }


    @Override
    protected ReceiveBenefitResDTO receiveSceneBenefitRetry(EquityAccountContext equityAccountContext) {
        String userId = equityAccountContext.getUid();
        String serialNumber = equityAccountContext.getSerialNumber();
        // 获取非失败状态的操作流水
        List<Integer> statuses = Lists.newArrayList(StatusEnum.INIT.getCode(), StatusEnum.SUCCESS.getCode(), StatusEnum.PROCESSING.getCode());
        CouponExpandAccountRecordDO existRecord = couponExpandAccountRecordDomainService.queryAccountRecordByUniqueKeyAndStatuses(userId,
                OperateTypeEnum.PROVIDE.getCode(), serialNumber, statuses);
        if (Objects.isNull(existRecord)) {
            log.warn("couponExpandAccountOperate receiveSceneBenefitRetry existRecord is null. userId={}, serialNumber={}", userId, serialNumber);
            CsossUtils.logEvent(MonitorConstants.RECEIVE_SCENE_BENEFIT_RETRY, "duplication_error");
            return ReceiveBenefitResDTO.fail(ExceptionEnum.DUPLICATION_ERROR);
        }
        Long recordId = existRecord.getId();
        equityAccountContext.setAccountRecordId(recordId);

        // 成功
        Integer existRecordStatus = existRecord.getStatus();
        if (StatusEnum.SUCCESS.getCode().equals(existRecordStatus)) {
            EquityRpcDto equityRpcDto = new EquityRpcDto();
            equityRpcDto.setValue(existRecord.getExpandUserTicketId());
            return ReceiveBenefitResDTO.success(equityRpcDto, recordId);
        }

        // 处理中
        if (StatusEnum.INIT.getCode().equals(existRecordStatus) || StatusEnum.PROCESSING.getCode().equals(existRecordStatus)) {
            return receiveRetry(equityAccountContext);
        }
        return ReceiveBenefitResDTO.processing(ExceptionEnum.COMMON_ERROR);
    }

    @Override
    protected void updateRecordFreezeReceiveLimitId(Long recordId, Long freezeReceiveLimitId, String userId) {

    }

    @Override
    protected void updateRecordProcessing(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        Map<String, Object> ruleLimitInfoMap = CouponExpandAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext);
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        equityRpcDto.setCode(exceptionEnum.getCode());
        equityRpcDto.setMessage(exceptionEnum.getMessage());
        couponExpandAccountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), StatusEnum.PROCESSING.getCode(), equityRpcDto, ruleLimitInfoMap);
    }

    @Override
    protected void updateRecordFail(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        Map<String, Object> ruleLimitInfoMap = CouponExpandAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext);
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        equityRpcDto.setCode(exceptionEnum.getCode());
        equityRpcDto.setMessage(exceptionEnum.getMessage());
        couponExpandAccountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), StatusEnum.FAIL.getCode(), equityRpcDto, ruleLimitInfoMap);
    }

    @Override
    public QueryEquityDto equityQuery(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public AccountInfoDTO queryEquityAccountInfo(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public List<AccountInfoDTO> queryEquityAccountInfos(EquityAccountContext equityAccountContext) {
        List<CouponExpandAccountDO>  couponExpandAccountDOList = couponExpandAccountDomainService.queryAccountRecordByUserIdAndActivityIdsAndBegTimeAndEndTime(equityAccountContext.getUid(),equityAccountContext.getActivityIds(),equityAccountContext.getStartDate(), equityAccountContext.getEndDate());
        return CouponExpandAccountConvertEntity.convertToAccountInfoDTOList(couponExpandAccountDOList);
    }

    @Override
    public Boolean hasSucceededByUniqueKey(String userId, Integer operateType, String reqNo) {
        return null;
    }

    private ReceiveBenefitResDTO receiveRetry(EquityAccountContext equityAccountContext) {
        String userId = equityAccountContext.getUid();
        Long accountRecordId = equityAccountContext.getAccountRecordId();

        // 远程调用领取权益
        EquityRpcDto equityRpcDto = doProvideEquityRpc(equityAccountContext);
        equityAccountContext.setEquityRpcDto(equityRpcDto);

        if (Objects.isNull(equityRpcDto) || StatusEnum.FAIL.equals(equityRpcDto.getStatusEnum())) {
            log.warn("couponExpandAccountOperate receiveSceneBenefitRetry receiveRetry failure. userId={}, accountRecordId={}",
                    userId, accountRecordId);
            updateRecordFail(accountRecordId, equityAccountContext, ExceptionEnum.RETRY_CALL_DISTRIBUTE_BENEFIT_FAILURE);
            return ReceiveBenefitResDTO.fail(ExceptionEnum.RETRY_CALL_DISTRIBUTE_BENEFIT_FAILURE);
        }

        if (StatusEnum.SUCCESS.equals(equityRpcDto.getStatusEnum())) {
            int doProvideEquityResult = doProvideEquity(equityAccountContext);
            log.info("couponExpandAccountOperate receiveSceneBenefitRetry receiveRetry success. userId={}, accountRecordId={}, doProvideEquityResult={}",
                    userId, accountRecordId, doProvideEquityResult);
            // 老账户领取成功后的动作（发送领取成功 Pulsar 消息）
            if (doProvideEquityResult == Constants.ONE) {
                oldAccountDoAfterSceneReceiveSuccess(equityAccountContext);
            }
            return doProvideEquityResult != Constants.ONE ? ReceiveBenefitResDTO.processing(ExceptionEnum.COMMON_ERROR) :
                    ReceiveBenefitResDTO.success(equityRpcDto, accountRecordId);
        }
        return null;
    }
}
