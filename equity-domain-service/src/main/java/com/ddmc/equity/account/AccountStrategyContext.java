package com.ddmc.equity.account;


import com.ddmc.equity.common.util.SpringContextUtil;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.entity.account.EquityAccountConvertEntity;
import com.ddmc.equity.enums.AccountType;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.QueryEquityDto;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
public class AccountStrategyContext {

    /****
     * 权益子账户类型
     */
    private final int accountType;

    private AccountStrategyContext(int accountType) {
        this.accountType = accountType;
    }

    public static AccountStrategyContext builder(int accountType) {
        return new AccountStrategyContext(accountType);
    }

    public static AccountStrategyContext builderByBenefitType(int benefitType) {
        Integer accountType = EquityAccountConvertEntity.checkAndConvertToAccountType(benefitType);
        return new AccountStrategyContext(accountType);
    }

    /**
     * 获取权益子账户操作类
     */
    private EquityAccountOperate getAccountOperate() {
        String beanName = Optional.ofNullable(AccountType.getByAccountType(accountType)).map(AccountType::getBeanName).orElse(null);
        return SpringContextUtil.getBean(beanName, EquityAccountOperate.class);
    }

    /**
     * 通过权益类型获取对应权益 id
     */
    public Long queryEquityBenefitId(Integer benefitType) {
        EquityAccountOperate accountOperate = getAccountOperate();
        return accountOperate.queryEquityBenefitId(benefitType);
    }

    /**
     * 通过权益类型获取对应权益信息
     */
    public EquityBenefitDO queryEquityBenefitDO(Integer benefitType) {
        EquityAccountOperate accountOperate = getAccountOperate();
        return accountOperate.queryEquityBenefitDO(benefitType);
    }

    /****
     * 权益发放
     */
    public boolean provideEquity(EquityAccountContext equityAccountContext) {
        EquityAccountOperate accountOperate = getAccountOperate();
        return accountOperate.provideEquity(equityAccountContext);
    }

    /****
     * 批量发放权益，并获取发放成功的权益子账户信息列表（要么全部成功、要么全部失败）
     */
    public List<AccountInfoDTO> provideEquityAndGetResult(EquityAccountContext equityAccountContext) {
        EquityAccountOperate accountOperate = getAccountOperate();
        return accountOperate.provideEquityAndGetResult(equityAccountContext);
    }

    /****
     * 权益回退
     */
    public boolean fallbackEquity(EquityAccountContext equityAccountContext) {
        EquityAccountOperate accountOperate = getAccountOperate();
        return accountOperate.fallbackEquity(equityAccountContext);
    }

    /***
     * 权益使用
     */
    public boolean useEquity(EquityAccountContext equityAccountContext) {
        EquityAccountOperate accountOperate = getAccountOperate();
        return accountOperate.useEquity(equityAccountContext);
    }

    /***
     * 权益查询。返回用户是否有对应权益、可使用权益数量、已使用权益数量（通过 userId 查询，优先从 redis 缓存中查询）
     */
    public QueryEquityDto equityQuery(EquityAccountContext equityAccountContext) {
        EquityAccountOperate accountOperate = getAccountOperate();
        return accountOperate.equityQuery(equityAccountContext);
    }

    /**
     * 查询权益。通过 userId 查询权益子账户信息，目前只有首单免运费权益在使用（一个用户只发放一次）
     */
    public AccountInfoDTO queryEquityAccountInfo(EquityAccountContext equityAccountContext) {
        EquityAccountOperate accountOperate = getAccountOperate();
        return accountOperate.queryEquityAccountInfo(equityAccountContext);
    }

    /**
     * 指定领取场景领取权益
     */
    public ReceiveBenefitResDTO receiveSceneBenefit(EquityAccountContext equityAccountContext) {
        EquityAccountOperate accountOperate = getAccountOperate();
        return accountOperate.receiveSceneBenefit(equityAccountContext);
    }

    /**
     * 查询权益。返回权益子账户信息列表（通过 userId、权益组 id、权益 id、状态、创建时间、抽签码来源）
     */
    public List<AccountInfoDTO> queryEquityAccountInfos(EquityAccountContext equityAccountContext) {
        EquityAccountOperate accountOperate = getAccountOperate();
        return accountOperate.queryEquityAccountInfos(equityAccountContext);
    }

    /**
     * 判断对应业务流水号是否已操作成功
     */
    public Boolean hasSucceededByUniqueKey(String userId, Integer operateType, String reqNo) {
        EquityAccountOperate accountOperate = getAccountOperate();
        return accountOperate.hasSucceededByUniqueKey(userId, operateType, reqNo);
    }

    /**
     * 指定场景领取（新）
     */
    public ReceiveBenefitResDTO sceneReceiveNew(EquityAccountContext equityAccountContext) {
        EquityAccountOperate accountOperate = getAccountOperate();
        return accountOperate.sceneReceiveNew(equityAccountContext);
    }

    /**
     * 获取对应权益账户操作类
     */
    public AbstractEquityAccountOperate getAbstractAccountOperate() {
        String beanName = Optional.ofNullable(AccountType.getByAccountType(accountType)).map(AccountType::getBeanName).orElse(null);
        return SpringContextUtil.getBean(beanName, AbstractEquityAccountOperate.class);
    }
}
