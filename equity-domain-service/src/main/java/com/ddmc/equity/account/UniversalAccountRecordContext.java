package com.ddmc.equity.account;

import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class UniversalAccountRecordContext {

    private String userId;


    private Date startTime;

    private Date endTime;


    private Set<Long> benefitIds;

    /**
     * 状态 0:初始化 1:成功 2:失败 3:处理中
     */
    private Integer status;

    private List<Integer> operateTypes;

    private Integer page;

    private Integer pageSize;

    private Set<Integer> benefitTypes;

    private Long activityId;
}
