package com.ddmc.equity.account.strategy;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.account.AbstractUniversalAccountStrategy;
import com.ddmc.equity.account.DirectAccountContext;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.domain.dto.account.UniversalAccountRecordInnerExtDTO;
import com.ddmc.equity.domain.entity.common.DirectAccountContextEntity;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountDetailDomainService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountDomainService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountRecordDomainService;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;
import com.ddmc.equity.model.vo.UniversalAccountDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 直接操作、不冻结策略
 */
@Service
@Slf4j
public class DirectUniversalAccountStrategy extends AbstractUniversalAccountStrategy {

    /**
     * 使用权益账户明细，开关打开
     */
    @Value("${equity.useAccountDetails.switchOn:true}")
    private Boolean useAccountDetailsSwitchOn;

    @Autowired
    private UniversalAccountDomainService universalAccountDomainService;
    @Autowired
    private UniversalAccountRecordDomainService universalAccountRecordDomainService;

    @Autowired
    private UniversalAccountDetailDomainService universalAccountDetailDomainService;

    @Autowired
    private TransactionUtil transactionUtil;

    @Override
    protected boolean doUseEquityCore(DirectAccountContext directAccountContext, List<UniversalAccountDO> accountDOList, List<UniversalAccountDetailVO> detailVOList) {
        //组装流水记录
        UniversalAccountRecordDO recordDO = DirectAccountContextEntity.convertUniversalAccountRecordDO(directAccountContext);
        UniversalAccountRecordInnerExtDTO extDTO = new UniversalAccountRecordInnerExtDTO();
        extDTO.setAccountDetailVOList(detailVOList);
        recordDO.setInnerExt(JSON.toJSONString(extDTO));
        try {
            //保存流水记录,通过唯一键控重复请求
            universalAccountRecordDomainService.add(recordDO);
            Map<Long, UniversalAccountDO> accountMap = accountDOList.stream().collect(Collectors.toMap(UniversalAccountDO::getId, Function.identity()));
            Map<Long, List<UniversalAccountDetailVO>> detailVOMap = detailVOList.stream().collect(Collectors.groupingBy(UniversalAccountDetailVO::getAccountId));
            transactionUtil.transactional(s -> {
                for (Map.Entry<Long, List<UniversalAccountDetailVO>> entry : detailVOMap.entrySet()) {
                    Long accountId = entry.getKey();
                    List<UniversalAccountDetailVO> detailVOS = entry.getValue();
                    int accountDeductSumCount = detailVOS.stream().mapToInt(UniversalAccountDetailVO::getDeductCount).sum();
                    UniversalAccountDO accountDO = accountMap.get(accountId);
                    int update = universalAccountDomainService.deductAvailableCount(accountDO.getUserId(), accountDO.getId(), accountDeductSumCount, accountDO.getVersion());
                    if (update <= 0) {
                        throw AdminExceptionBuilder.build(ExceptionEnum.ACCOUNT_USE_FAIL);
                    }
                    if (Boolean.TRUE.equals(useAccountDetailsSwitchOn)) {
                        CsossUtils.logEvent("useAccountDetailsSwitchOn", "true");
                        for (UniversalAccountDetailVO vo : detailVOS) {
                            int detailUpdate = universalAccountDetailDomainService.deductDetailAvailableCount(vo.getUserId(), vo.getId(), vo.getDeductCount(), vo.getVersion());
                            if (detailUpdate <= 0) {
                                throw AdminExceptionBuilder.build(ExceptionEnum.ACCOUNT_USE_FAIL);
                            }
                        }
                    } else {
                        CsossUtils.logEvent("useAccountDetailsSwitchOn", "false");
                    }
                }
            });
            recordDO.setRpcCode(0);
            recordDO.setRpcMsg(ExceptionEnum.SUCCESS.getMessage());
            recordDO.setStatus(StatusEnum.SUCCESS.getCode());
            universalAccountRecordDomainService.update(recordDO);
            CsossUtils.logEvent(MonitorConstants.UNIVERSAL_DIRECT_USE_BENEFIT_TYPE, MonitorConstants.SUCCESS);
            return true;
        } catch (DuplicateKeyException e) {
            CsossUtils.logEventFail(MonitorConstants.UNIVERSAL_DIRECT_USE_BENEFIT_TYPE, MonitorConstants.DUPLICATE_KEY_EXCEPTION, JSON.toJSONString(directAccountContext));
            //直接返回上次的结果
            log.warn("universalUseBenefitType DuplicateKey {}", JSON.toJSONString(directAccountContext));
            UniversalAccountRecordDO recordDB = universalAccountRecordDomainService.getOneByUniqueKey(recordDO);
            return Objects.equals(recordDB.getStatus(), StatusEnum.SUCCESS.getCode());
        } catch (Exception e) {
            recordDO.setStatus(StatusEnum.FAIL.getCode());
            recordDO.setAuxKey(String.valueOf(recordDO.getId()));
            universalAccountRecordDomainService.update(recordDO);
            CsossUtils.logEventFail(MonitorConstants.UNIVERSAL_DIRECT_USE_BENEFIT_TYPE, MonitorConstants.FAIL, JSON.toJSONString(directAccountContext));
            log.error("universalUseBenefitType Exception {}", JSON.toJSONString(directAccountContext), e);
        }

        return false;
    }

    @Override
    protected boolean doUseEquityByDetailCore(DirectAccountContext directAccountContext, UniversalAccountDO accountDO, UniversalAccountDetailDO accountDetailDO) {
        //组装流水记录
        UniversalAccountRecordDO recordDO = DirectAccountContextEntity.convertUniversalAccountRecordDO(directAccountContext);
        try {
            //保存流水记录,通过唯一键控重复请求
            universalAccountRecordDomainService.add(recordDO);
            transactionUtil.transactional(s -> {
                int update = universalAccountDomainService.deductAvailableCount(accountDO.getUserId(), accountDO.getId(), accountDetailDO.getAvailableCount(), accountDO.getVersion());
                if (update <= 0) {
                    throw AdminExceptionBuilder.build(ExceptionEnum.ACCOUNT_USE_FAIL);
                }
                int detailUpdate = universalAccountDetailDomainService.deductDetailAvailableCount(accountDetailDO.getUserId(), accountDetailDO.getId(), accountDetailDO.getAvailableCount(), accountDetailDO.getVersion());
                if (detailUpdate <= 0) {
                    throw AdminExceptionBuilder.build(ExceptionEnum.ACCOUNT_USE_FAIL);
                }
            });
            recordDO.setRpcCode(0);
            recordDO.setRpcMsg(ExceptionEnum.SUCCESS.getMessage());
            recordDO.setStatus(StatusEnum.SUCCESS.getCode());
            universalAccountRecordDomainService.update(recordDO);
            CsossUtils.logEvent(MonitorConstants.UNIVERSAL_DIRECT_USE_BENEFIT_DETAIL, MonitorConstants.DUPLICATE_KEY_EXCEPTION);
            return true;
        } catch (DuplicateKeyException e) {
            CsossUtils.logEventFail(MonitorConstants.UNIVERSAL_DIRECT_USE_BENEFIT_DETAIL, MonitorConstants.DUPLICATE_KEY_EXCEPTION, JSON.toJSONString(directAccountContext));
            //直接返回上次的结果
            log.warn("universalUseBenefitDetail DuplicateKey {}", JSON.toJSONString(directAccountContext));
            UniversalAccountRecordDO recordDB = universalAccountRecordDomainService.getOneByUniqueKey(recordDO);
            return Objects.equals(recordDB.getStatus(), StatusEnum.SUCCESS.getCode());
        } catch (Exception e) {
            recordDO.setStatus(StatusEnum.FAIL.getCode());
            recordDO.setAuxKey(String.valueOf(recordDO.getId()));
            universalAccountRecordDomainService.update(recordDO);
            CsossUtils.logEventFail(MonitorConstants.UNIVERSAL_DIRECT_USE_BENEFIT_DETAIL, MonitorConstants.FAIL, JSON.toJSONString(directAccountContext));
            log.error("universalUseBenefitDetail Exception {}", JSON.toJSONString(directAccountContext), e);
        }
        return false;
    }

    @Override
    public boolean doBackEquity(DirectAccountContext directAccountContext) {
        return false;
    }


}
