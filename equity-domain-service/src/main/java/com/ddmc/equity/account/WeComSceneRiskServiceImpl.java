package com.ddmc.equity.account;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.risk.RiskCheckUtils;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.enums.SceneCodeEnum;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.infra.rpc.risk.RiskEngineProxy;
import com.ddmc.equity.infra.rpc.user.UserProxy;
import com.ddmc.equity.infra.rpc.voucher.VoucherSendTicketProxy;
import com.ddmc.equity.model.dto.ActivityCacheDto;
import com.ddmc.equity.model.dto.risk.WecomRiskStrategyDataDTO;
import com.ddmc.risk.engine.dto.RequestDto;
import com.ddmc.risk.engine.vo.ResponseVO;
import com.ddmc.usercenter.open.entity.dto.UserDTO;
import com.ddmc.vouchercore.client.dto.TicketPackageDTO;
import com.ddmc.vouchercore.client.dto.sub.TicketPackageCouponsDTO;
import com.ddmc.vouchercore.client.request.GetTicketPackageRequest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@Slf4j
public class WeComSceneRiskServiceImpl implements SceneRiskService {

    @Autowired
    private VoucherSendTicketProxy voucherSendTicketProxy;
    @Autowired
    private UserProxy userProxy;
    @Autowired
    private RiskEngineProxy riskEngineProxy;

    @Value("${wecom.risk.scene.code:activity_invite_return_wecom}")
    private String wecomRiskSceneCode;
    @Value("${wecom.risk.appid:activity_invite_return_wecom}")
    private String wecomRiskAppId;
    @Value("${wecom.risk.token:caa796f103874563a933fe98eca3af16}")
    private String wecomRiskToken;
    @Value("${wecom.risk.need:true}")
    private boolean needRisk;

    @Resource
    private LocalCacheManager localCacheManager;

    @Override
    public EquityRpcDto doRiskBeforeProvideEquityRpc(EquityAccountContext equityAccountContext) {
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        if (needRisk) {
            if (Objects.equals(equityAccountContext.getBenefitType(), BenefitTypeEnum.TICKET.getId())) {
                return weComShareReceiveCouponRisk(equityAccountContext);
            }
            try {
                GetTicketPackageRequest getTicketPackageRequest = new GetTicketPackageRequest();
                getTicketPackageRequest.setTicket_package_id(equityAccountContext.getEquityValue());
                TicketPackageDTO ticketPackageDTO = voucherSendTicketProxy.getTicketPackage(getTicketPackageRequest);
                if (Objects.nonNull(ticketPackageDTO) && CollectionUtils.isNotEmpty(ticketPackageDTO.getCoupons())) {
                    RequestDto requestDto = buildRiskRequest(equityAccountContext, ticketPackageDTO);
                    ResponseVO responseVO = riskEngineProxy.riskRule(requestDto);
                    if (Objects.nonNull(responseVO) && Objects.nonNull(responseVO.getData()) && Objects.equals(responseVO.getData().getResolutionMeans(), "2")) {
                        equityRpcDto.setCode(BenefitUnableReceiveReasonType.HIT_RISK.getCode());
                        equityRpcDto.setMessage(BenefitUnableReceiveReasonType.HIT_RISK.getDesc());
                        equityRpcDto.setStatusEnum(StatusEnum.FAIL);
                        CsossUtils.logEvent(MonitorConstants.WECOM_RISK, MonitorConstants.FAIL);
                        log.warn("ticketPackageAccountOperate doProvideEquityRpc riskRule nopass. context={}, request={}, response={}",
                                JSON.toJSONString(equityAccountContext), JSON.toJSONString(requestDto), JSON.toJSONString(responseVO));
                        return equityRpcDto;
                    }
                }
                CsossUtils.logEvent(MonitorConstants.WECOM_RISK, MonitorConstants.SUCCESS);
            } catch (Exception e) {
                CsossUtils.logEvent(MonitorConstants.WECOM_RISK, MonitorConstants.EXCEPTION);
                log.error("doRiskBeforeProvideEquityRpc equityAccountContext={}", JSON.toJSONString(equityAccountContext), e);
            }

        }
        return equityRpcDto;
    }

    @Override
    public String getSceneCode() {
        return SceneCodeEnum.WECOM.getCode();
    }


    private RequestDto buildRiskRequest(EquityAccountContext equityAccountContext, TicketPackageDTO ticketPackageDTO) {

        WecomRiskStrategyDataDTO riskStrategyData = new WecomRiskStrategyDataDTO();
        String userMobile = getUserMobile(equityAccountContext.getUid());
        riskStrategyData.setUserMobile(userMobile);
        ActivityCacheDto activityCacheDto = localCacheManager.getActivityCacheDtoById(equityAccountContext.getActivityId());
        if (activityCacheDto != null) {
            riskStrategyData.setActivityId(activityCacheDto.getExternalId());
            riskStrategyData.setActivityName(activityCacheDto.getActivityName());
        }

        riskStrategyData.setUserId(equityAccountContext.getUid());
        riskStrategyData.setAwardType("1");
        List<WecomRiskStrategyDataDTO.AwardDTO> awardDTOList = new ArrayList<>();
        for (TicketPackageCouponsDTO couponsDTO : ticketPackageDTO.getCoupons()) {
            WecomRiskStrategyDataDTO.AwardDTO awardDTO = new WecomRiskStrategyDataDTO.AwardDTO();
            awardDTO.setName(couponsDTO.getName());
            awardDTO.setIdNumber(couponsDTO.getId());
            awardDTOList.add(awardDTO);
        }
        riskStrategyData.setAwardDTOList(awardDTOList);

        RequestDto request = new RequestDto();
        request.setSceneCode(wecomRiskSceneCode);
        request.setAppId(wecomRiskAppId);
        request.setToken(wecomRiskToken);
        request.setRiskStrategyData(JSONObject.parseObject(JSON.toJSONString(riskStrategyData)));
        return request;
    }

    private EquityRpcDto weComShareReceiveCouponRisk(EquityAccountContext equityAccountContext) {
        RequestDto riskRequest = buildReceiveCouponRiskRequest(equityAccountContext);
        ResponseVO riskResponse = riskEngineProxy.riskRule(riskRequest);
        if (RiskCheckUtils.riskNoPass(riskResponse)) {
            log.warn("weComShareReceiveCouponRisk riskRule riskNoPass riskRequest={}, riskResponse={}",
                    JSON.toJSONString(riskRequest), JSON.toJSONString(riskResponse));
            CsossUtils.logEvent(MonitorConstants.WECOM_RISK, MonitorConstants.FAIL);
            return EquityRpcDto.fail(BenefitUnableReceiveReasonType.HIT_RISK.getCode(), "企微 1v1 风控校验不通过");
        }
        CsossUtils.logEvent(MonitorConstants.WECOM_RISK, MonitorConstants.SUCCESS);
        return EquityRpcDto.success(null);
    }

    private RequestDto buildReceiveCouponRiskRequest(EquityAccountContext equityAccountContext) {
        WecomRiskStrategyDataDTO.AwardDTO awardDTO = new WecomRiskStrategyDataDTO.AwardDTO();
        awardDTO.setIdNumber(equityAccountContext.getEquityValue());
        awardDTO.setName(equityAccountContext.getBenefitName());
        WecomRiskStrategyDataDTO riskStrategyData = new WecomRiskStrategyDataDTO();
        riskStrategyData.setUserId(equityAccountContext.getUid());
        riskStrategyData.setUserMobile(getUserMobile(equityAccountContext.getUid()));
        riskStrategyData.setActivityId(equityAccountContext.getExternalId());
        riskStrategyData.setActivityName(equityAccountContext.getActivityName());
        riskStrategyData.setAwardType("1");
        riskStrategyData.setAwardDTOList(Lists.newArrayList(awardDTO));

        RequestDto request = new RequestDto();
        request.setSceneCode(wecomRiskSceneCode);
        request.setAppId(wecomRiskAppId);
        request.setToken(wecomRiskToken);
        // RiskStrategyDataDTO 使用了 fastjson 别名，所以只能用 fastjson 转换
        request.setRiskStrategyData(JSONObject.parseObject(JSON.toJSONString(riskStrategyData)));
        return request;
    }

    private String getUserMobile(String userId) {
        UserDTO userDTO = userProxy.queryById(userId);
        return Optional.ofNullable(userDTO).map(UserDTO::getMobile).orElse(null);
    }
}
