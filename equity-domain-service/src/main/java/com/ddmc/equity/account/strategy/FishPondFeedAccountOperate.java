package com.ddmc.equity.account.strategy;

import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.entity.account.FishPondFeedAccountConvertEntity;
import com.ddmc.equity.infra.rpc.farm.FarmProxy;
import com.ddmc.farm.request.api.task.FarmTaskRewardRequestVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/5/23 16:09
 * @description
 */
@Slf4j
@Component
public class FishPondFeedAccountOperate extends AbstractEquityAccountOperate {

    @Value("${spring.application.name}")
    private String applicationName;

    @Resource
    private FarmProxy farmProxy;

    @Override
    public EquityRpcDto doProvideEquityRpc(EquityAccountContext equityAccountContext) {
        FarmTaskRewardRequestVo req = FishPondFeedAccountConvertEntity
                .convertToFarmSendRewardReq(equityAccountContext, applicationName);

        return farmProxy.sendReward(req);
    }
}
