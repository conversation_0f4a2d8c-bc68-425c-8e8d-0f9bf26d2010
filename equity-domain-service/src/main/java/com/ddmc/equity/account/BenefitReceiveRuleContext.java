package com.ddmc.equity.account;

import com.ddmc.equity.domain.dto.rule.condition.ReceiveLimitRuleDTO;
import com.ddmc.equity.domain.dto.rule.condition.StockLimitRuleDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/6/28 15:26
 * @description 权益领取需要的频次限制、库存限制规则
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class BenefitReceiveRuleContext {

    // ===== 领取频次限制相关
    /**
     * 是否有领取频次限制，活动维度
     */
    @Builder.Default
    private Boolean hasActivityReceiveLimit = Boolean.FALSE;
    /**
     * 领取频次限制，活动维度
     */
    private ReceiveLimitRuleDTO activityReceiveLimitRuleDTO;
    /**
     * 是否有领取频次限制，策略维度
     */
    @Builder.Default
    private Boolean hasStrategyReceiveLimit = Boolean.FALSE;
    /**
     * 领取频次限制，策略维度
     */
    private ReceiveLimitRuleDTO strategyReceiveLimitRuleDTO;
    /**
     * 是否有领取频次限制，权益维度
     */
    @Builder.Default
    private Boolean hasBenefitReceiveLimit = Boolean.FALSE;
    /**
     * 领取频次限制，权益维度
     */
    private ReceiveLimitRuleDTO benefitReceiveLimitRuleDTO;

    // ===== 领取库存限制相关
    /**
     * 是否有领取库存限制，活动维度
     */
    @Builder.Default
    private Boolean hasActivityStockLimit = Boolean.FALSE;
    /**
     * 领取库存限制，活动维度
     */
    private StockLimitRuleDTO activityStockLimitRuleDTO;
    /**
     * 是否有领取库存限制，策略维度
     */
    @Builder.Default
    private Boolean hasStrategyStockLimit = Boolean.FALSE;
    /**
     * 领取库存限制，策略维度
     */
    private StockLimitRuleDTO strategyStockLimitRuleDTO;
    /**
     * 是否有领取库存限制，权益维度
     */
    @Builder.Default
    private Boolean hasBenefitStockLimit = Boolean.FALSE;
    /**
     * 领取库存限制，权益维度
     */
    private StockLimitRuleDTO benefitStockLimitRuleDTO;
}
