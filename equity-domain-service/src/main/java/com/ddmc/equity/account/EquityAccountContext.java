package com.ddmc.equity.account;

import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.dto.business.PageListReqDTO;
import com.ddmc.equity.dto.customer.ReceiveExternalInfoDTO;
import com.ddmc.equity.model.dto.UseBenefitDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class EquityAccountContext extends PageListReqDTO {

    /**
     * 请求来源 appId
     */
    private String appId;
    /***
     * 请求来源页面 id
     */
    private String pageId;
    /**
     * 请求来源
     */
    private String source;
    /**
     * 城市 code
     */
    private String cityCode;
    /**
     * 站点 id
     */
    private String stationId;
    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 业务流水号
     */
    private String serialNumber;
    /**
     * 场景 code
     */
    private String sceneCode;
    /**
     * 用户 id
     */
    private String uid;
    /**
     * 权益子账户类型
     */
    private Integer accountType;
    /**
     * 操作类型
     */
    private Integer operateType;
    /**
     * 操作数量
     */
    private BigDecimal operateCount;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 活动 id
     */
    private Long activityId;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 外部关联类型。1-玩法；
     */
    private Integer externalType;
    /**
     * 外部关联 id（一般是外部关联方的活动 id，如 promo.activity.activityId）
     */
    private String externalId;
    /**
     * 策略 id
     */
    private Long strategyId;
    /**
     * 策略外部关联 id。如果是来源玩法的活动，则为活动 prizeId；如果是膨胀券活动，则为母券券模板 id；
     */
    private String strategyExternalId;
    /**
     * 权益组 id
     */
    private Long benefitGroupId;
    /**
     * 权益 id
     */
    private Long benefitId;
    /**
     * 权益名称
     */
    private String benefitName;
    /**
     * 权益类型
     *
     * @see com.ddmc.equity.enums.BenefitTypeEnum
     */
    private Integer benefitType;
    /***
     * 权益 id = benefitId
     * <p>
     * 后续使用 benefitId 字段
     * 不知道原来为什么要弄两个字段，But 历史原因，目前领券活动领取使用的是 equityId 字段，不能动历史代码
     */
    @Deprecated
    private Long equityId;
    /**
     * 权益值
     * 如果是优惠券，则为券模板 id
     */
    private String equityValue;
    /**
     * 记录在 accountDetail 表中的权益实际发放数量
     * <p>
     * 对于每次发放数量必须为 1 的权益类型（如优惠券等），sendAmount = 1
     * 发放固定数量权益时，sendAmount = benefit.benefitValue
     * 发放不固定数量权益时，sendAmount = reqSendAmount
     * 发放随机数量权益时，sendAmount = benefit.benefitValue 范围内随机的数量
     */
    private String sendAmount;
    /**
     * 余额金额，保留两位小数。如果权益类型为余额（固定余额、随机余额），才有该字段
     */
    private BigDecimal balanceMoney;
    /**
     * 使用时的活动 id
     */
    private Long useActivityId;
    /***
     * 券 id
     */
    private Long ticketId;
    /**
     * 主券用户券 id，为用户膨胀前的券 userTicketId
     */
    private String masterUserTicketId;
    /**
     * 商品 id。权益类型为单价促销品或者总价促销品时，该字段不为空
     */
    private String productId;
    /**
     * 来源任务的 missionInstanceId
     */
    private Long missionInstanceId;
    /**
     * 领取时关联的外部信息
     */
    private ReceiveExternalInfoDTO receiveExternalInfoDTO;
    /**
     * 发放优惠券时的场景（发放优惠券包时的场景也用该字段）
     */
    private Integer sendTicketScene;
    /**
     * 发放优惠券时透传的 activity。如果为空，则用权益的 activityId
     */
    private String sendTicketActivity;
    /**
     * 发放优惠券时透传的 prize。如果为空，则用权益的 strategyId_benefitId
     */
    private String sendTicketPrize;
    /**
     * 券是否已读，透传自前端
     */
    private Boolean isTicketIsRead;
    /**
     * 是否用前端的流水号
     * <p>
     * 如果为 1，则调用优惠券接口使用前端流水号；
     * 如果为空或者不为 1，则使用冻结权益维度频次返回的流水号（如果没有权益维度频次限制，也会有该流水号）；
     */
    private Integer isUseSerialNumberSave;
    /**
     * 消耗权益类型。仅适用于需要消耗指定数量权益才能发放权益的活动
     *
     * @see com.ddmc.equity.enums.BenefitTypeEnum
     */
    private Integer consumeBenefitType;
    /**
     * 消耗权益数量。仅适用于需要消耗指定数量权益才能发放权益的活动
     */
    private String consumeBenefitAmount;
    /**
     * 消耗权益，冻结记录 ID
     */
    private String consumeBenefitFreezeLogId;
    /**
     * 发放权益时调用下游透传的自定义参数
     */
    private Map<String, String> rpcReqCustomMap;
    /**
     * 发放余额时的场景
     */
    private String sendBalanceScene;
    /**
     * 发放余额时的描述
     */
    private String sendBalanceDesc;
    /**
     * 发放积分时的场景
     */
    private Integer sendPointScene;
    /**
     * 发放积分时的描述
     */
    private String sendPointDesc;
    /**
     * 发放积分时的来源
     */
    private Integer sendPointSource;
    /**
     * 发放会员天数时的场景
     */
    private Integer sendVipDaysScene;

    /**
     * 权益子账户状态。0-未使用；1-已使用；2-已过期；
     *
     * @see com.ddmc.equity.common.enums.SubAccountStatusEnum
     */
    private Integer status;
    /***
     * 子账户 id
     */
    private Long accountId;
    /**
     * 账户明细 id
     */
    private Long accountDetailId;
    /**
     * 账户记录表 id
     */
    private Long accountRecordId;
    /**
     * 调用三方唯一请求流水号
     */
    private String doRpcUniqueSerialNumber;
    /***
     * 调取三方返回的结果内容
     */
    private EquityRpcDto equityRpcDto;
    /***
     * 扩展信息
     */
    private String extValue;

    /**
     * 是否存在处理中的记录
     */
    private boolean isHasFreezeReceiveLimitProcessingRecord;
    /**
     * 冻结数据库成功更新后的 id，失败为 null
     */
    private Long freezeReceiveLimitResultId;
    /**
     * 成功为：用户单活动领取频次限制表 id；失败为：null
     */
    private Long activityReceiveLimitId;

    /**
     * 发放时的抽签码来源
     *
     * @see com.ddmc.equity.enums.DrawCodeSourceEnum
     */
    private Integer drawCodeSource;
    /**
     * 发放数量。如果不指定数量，默认数量为 1
     */
    private Integer distributeCount;
    /**
     * 账户关联拓展信息。如果账户类型为抽签码，则含关联的评价 id（来源试吃评价）和会员订单号（来源会员开卡）；
     * <p>
     * 账户类型为抽签码，对应的 key 请看：com.ddmc.equity.enums.DrawCodeAccountExtKeys
     */
    private Map<String, Object> extMap;

    /**
     * 使用的权益列表
     */
    private List<UseBenefitDTO> useBenefits;

    /**
     * 活动 id。可以指定活动，不指定则查询该场景下的所有
     */
    private List<Long> activityIds;
    /**
     * 开始时间。可以指定开始时间（创建时间）查询，不指定则查询所有
     */
    private Date startDate;
    /**
     * 结束时间。可以指定结束时间（创建时间）查询，不指定则查询所有
     */
    private Date endDate;
    /**
     * 权益子账户状态。0-未使用；1-已使用；2-已过期；
     *
     * @see com.ddmc.equity.common.enums.SubAccountStatusEnum
     */
    private List<Integer> statuses;
    /**
     * 抽签码来源。查询抽签码权益时，可以指定抽签码来源，不指定则查询所有
     *
     * @see com.ddmc.equity.enums.DrawCodeSourceEnum
     */
    private List<Integer> drawCodeSources;

    private String openId;

}
