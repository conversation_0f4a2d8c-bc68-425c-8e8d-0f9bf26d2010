package com.ddmc.equity.account.strategy;

import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/27 19:34
 * @description
 */
@Slf4j
@Component
public class FixedStepsAccountOperate extends AbstractEquityAccountOperate {

    @Override
    public EquityRpcDto doProvideEquityRpc(EquityAccountContext equityAccountContext) {
        return super.doProvideEquityRpc(equityAccountContext);
    }
}
