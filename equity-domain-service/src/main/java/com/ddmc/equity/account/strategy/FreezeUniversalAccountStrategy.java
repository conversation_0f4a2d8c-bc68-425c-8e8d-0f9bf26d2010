package com.ddmc.equity.account.strategy;

import com.ddmc.equity.account.AbstractUniversalAccountStrategy;
import com.ddmc.equity.account.DirectAccountContext;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;
import com.ddmc.equity.model.vo.UniversalAccountDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class FreezeUniversalAccountStrategy extends AbstractUniversalAccountStrategy {


    @Override
    protected boolean doUseEquityCore(DirectAccountContext directAccountContext, List<UniversalAccountDO> accountDOList, List<UniversalAccountDetailVO> detailVOList) {
        //1、数据库冻结
        //2、RPC调用
        //3、根据RPC调用结果，决定接下来的步骤
        return false;
    }

    @Override
    protected boolean doUseEquityByDetailCore(DirectAccountContext directAccountContext, UniversalAccountDO accountDO, UniversalAccountDetailDO accountDetailDO) {
        return false;
    }

    @Override
    public boolean doBackEquity(DirectAccountContext directAccountContext) {
        return false;
    }
}
