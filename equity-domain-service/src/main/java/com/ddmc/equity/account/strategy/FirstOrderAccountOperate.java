package com.ddmc.equity.account.strategy;

import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.apollo.BenefitConstants;
import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.FirstOrderAccountStatus;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.exception.ApiBusinessException;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.common.util.business.BenefitUtil;
import com.ddmc.equity.domain.dto.FallbackEquityDto;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.service.firstOrderAccount.FirstOrderAccountService;
import com.ddmc.equity.domain.service.firstOrderAccountRecord.FirstOrderAccountRecordService;
import com.ddmc.equity.domain.valueobject.validity_calculation.ValidityCalculationRespVal;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.repository.dao.FirstOrderAccountDO;
import com.ddmc.equity.infra.repository.dao.FirstOrderAccountRecordDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.QueryEquityDto;
import com.ddmc.equity.model.entity.FirstOrderAccountEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class FirstOrderAccountOperate extends AbstractEquityAccountOperate {

    @Autowired
    private FirstOrderAccountService firstOrderAccountService;
    @Autowired
    private FirstOrderAccountRecordService firstOrderAccountRecordService;
    @Autowired
    private TransactionUtil transactionUtil;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private BenefitConstants benefitConstants;

    @Override
    public int doFallbackEquity(EquityAccountContext equityAccountContext) {
        try {
            transactionUtil.transactional(s -> fallbackAccountAndUpdateSuccessRecord(equityAccountContext));
            //重置缓存
            String key = String.format(CacheKeyConstants.FIRST_ACCOUNT_CACHE, equityAccountContext.getUid());
            redisCache.setValue(key, Constants.ONE, benefitConstants.getUserFirstOrderAccountCacheSeconds(), TimeUnit.SECONDS);
            CsossUtils.logEventSuccess("firstOrder","fallback",equityAccountContext.getUid());
            return Constants.ONE;
        } catch (Exception e) {
            log.error("FirstOrderAccountOperate.doFallbackEquity.e;req={}", JsonUtil.toJsonString(equityAccountContext), e);
        }
        return Constants.ZERO;
    }

    /**
     * 回退
     *
     * @param equityAccountContext 上下文
     */
    private void fallbackAccountAndUpdateSuccessRecord(EquityAccountContext equityAccountContext) {
        //回退券
        boolean isSuccess = firstOrderAccountService.fallBackAccount(equityAccountContext.getUid(), equityAccountContext.getAccountId(), equityAccountContext.getSerialNumber());
        if (!isSuccess) {
            throw new ApiBusinessException(ExceptionEnum.ACCOUNT_FALLBACK_FAIL);
        }
        isSuccess = updateRecordSuccessById(equityAccountContext.getAccountRecordId(), StatusEnum.SUCCESS.getCode(), equityAccountContext);
        if (!isSuccess) {
            throw new ApiBusinessException(ExceptionEnum.ACCOUNT_RECORD_UPDATE_FAIL);
        }
    }

    @Override
    public boolean checkUseEquity(EquityAccountContext equityAccountContext) {
        FirstOrderAccountDO firstOrderAccountDO = firstOrderAccountService.queryFirstOrderAccountDOByUserId(equityAccountContext.getUid());
        if (Objects.isNull(firstOrderAccountDO)) {
            return false;
        }
        equityAccountContext.setAccountId(firstOrderAccountDO.getId());
        return Objects.equals(firstOrderAccountDO.getStatus(), FirstOrderAccountStatus.INIT.getCode());
    }

    @Override
    public int doUseEquity(EquityAccountContext equityAccountContext) {
        try {
            transactionUtil.transactional(s -> useAccountAndUpdateSuccessRecord(equityAccountContext));
            String key = String.format(CacheKeyConstants.FIRST_ACCOUNT_CACHE, equityAccountContext.getUid());
            redisCache.setValue(key, Constants.ZERO, benefitConstants.getUserFirstOrderAccountCacheSeconds(), TimeUnit.SECONDS);
            CsossUtils.logEventSuccess("firstOrder","use",equityAccountContext.getUid());
            return Constants.ONE;
        } catch (Exception e) {
            log.error("FirstOrderAccountOperate.doUseEquity.e;req={}", JsonUtil.toJsonString(equityAccountContext), e);
        }
        return Constants.ZERO;
    }

    /**
     * 权益使用,并更新record
     *
     * @param equityAccountContext 上下文
     */
    private void useAccountAndUpdateSuccessRecord(EquityAccountContext equityAccountContext) {
        //使用权益
        boolean isSuccess = firstOrderAccountService.useAccount(equityAccountContext.getUid(), equityAccountContext.getSerialNumber());
        if (!isSuccess) {
            throw new ApiBusinessException(ExceptionEnum.ACCOUNT_USE_FAIL);
        }
        //更新记录
        isSuccess = updateRecordSuccess(equityAccountContext.getAccountRecordId(), equityAccountContext.getAccountId(), equityAccountContext);
        if (!isSuccess) {
            throw new ApiBusinessException(ExceptionEnum.ACCOUNT_RECORD_UPDATE_FAIL);
        }
    }

    /**
     * 更新操作记录
     *
     * @param accountRecordId      操作记录主键id
     * @param accountId            账户id
     * @param equityAccountContext 上下文
     * @return 更新结果
     */
    private boolean updateRecordSuccess(Long accountRecordId, Long accountId, EquityAccountContext equityAccountContext) {
        if (Objects.isNull(accountRecordId) || Objects.isNull(accountId) || Objects.isNull(equityAccountContext)
                || StringUtils.isBlank(equityAccountContext.getUid())) {
            return false;
        }
        return firstOrderAccountRecordService.updateRecordStatus(equityAccountContext.getUid(), accountRecordId, accountId, StatusEnum.SUCCESS.getCode(), equityAccountContext.getSerialNumber(), equityAccountContext.getEquityRpcDto());
    }

    @Override
    public boolean checkFallbackEquity(EquityAccountContext equityAccountContext) {
        return true;
    }

    @Override
    public int doProvideEquity(EquityAccountContext equityAccountContext) {
        Long benefitId = equityAccountContext.getBenefitId();
        FirstOrderAccountDO firstOrderAccountDO = new FirstOrderAccountDO();
        firstOrderAccountDO.setUserId(equityAccountContext.getUid());
        firstOrderAccountDO.setBenefitId(benefitId);
        firstOrderAccountDO.setLastReqNo(equityAccountContext.getSerialNumber());
        //权益有效期计算
        ValidityCalculationRespVal validityCalculationRespVal = BenefitUtil.calculateValidityByBenefitId(benefitId, new Date());
        if (!Objects.isNull(validityCalculationRespVal)) {
            firstOrderAccountDO.setValidBeginDate(validityCalculationRespVal.getStartTime());
            firstOrderAccountDO.setValidEndDate(validityCalculationRespVal.getEndTime());
        }
        try {
            transactionUtil.transactional(s -> saveAccountAndUpdateSuccessRecord(firstOrderAccountDO, equityAccountContext));
            String key = String.format(CacheKeyConstants.FIRST_ACCOUNT_CACHE, equityAccountContext.getUid());
            redisCache.setValue(key, Constants.ONE, benefitConstants.getUserFirstOrderAccountCacheSeconds(), TimeUnit.SECONDS);
            CsossUtils.logEventSuccess("firstOrder","send",equityAccountContext.getUid());
            return Constants.ONE;
        } catch (Exception e) {
            updateRecordFail(equityAccountContext.getAccountRecordId(), equityAccountContext, ExceptionEnum.DB_OPERATE_ERROR);
            log.error("FirstOrderAccountOperate.doProvideEquity.e;req={}", JsonUtil.toJsonString(equityAccountContext), e);
        }
        return Constants.ZERO;
    }

    @Override
    public List<AccountInfoDTO> doProvideEquityAndGetResult(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public List<AccountInfoDTO> retryDoProvideEquityAndGetResult(EquityAccountContext equityAccountContext) {
        return null;
    }

    /**
     * 账户信息保存，操作记录状态修改
     *
     * @param firstOrderAccountDO  账户
     * @param equityAccountContext 请求上下文
     */
    private void saveAccountAndUpdateSuccessRecord(FirstOrderAccountDO firstOrderAccountDO, EquityAccountContext equityAccountContext) {
        //账户保存
        int result = firstOrderAccountService.saveFirstOrderAccountDO(firstOrderAccountDO);
        if (result != 1) {
            throw new ApiBusinessException(ExceptionEnum.SAVE_ACCOUNT_FAIL);
        }
        equityAccountContext.setAccountId(firstOrderAccountDO.getId());
        //更新记录
        boolean isSuccess = updateRecordSuccessById(equityAccountContext.getAccountRecordId(), StatusEnum.SUCCESS.getCode(), equityAccountContext);
        if (!isSuccess) {
            throw new ApiBusinessException(ExceptionEnum.ACCOUNT_RECORD_UPDATE_FAIL);
        }
    }

    @Override
    public QueryEquityDto equityQuery(EquityAccountContext equityAccountContext) {
        //先查缓存
        String key = String.format(CacheKeyConstants.FIRST_ACCOUNT_CACHE, equityAccountContext.getUid());
        String activeCountStr = redisCache.getValue(key);
        if (StringUtils.isNotBlank(activeCountStr)) {
            int activeCount = Integer.parseInt(activeCountStr);
            boolean isHaveEquity = activeCount > 0;
            int useCount = activeCount > 0 ? 0 : 1;
            QueryEquityDto queryEquityDto = new QueryEquityDto();
            queryEquityDto.setHaveEquity(isHaveEquity);
            queryEquityDto.setUseCount(useCount);
            queryEquityDto.setEquityCount(activeCount);
            return queryEquityDto;
        }
        FirstOrderAccountDO firstOrderAccountDO = firstOrderAccountService.queryFirstOrderAccountDOByUserId(equityAccountContext.getUid());
        FirstOrderAccountEntity firstOrderAccountEntity = FirstOrderAccountEntity.builder().build();
        QueryEquityDto queryEquityDto = firstOrderAccountEntity.createQueryEquityDto(firstOrderAccountDO);
        if (Objects.nonNull(queryEquityDto)) {
            redisCache.setValue(key, queryEquityDto.getEquityCount(), benefitConstants.getUserFirstOrderAccountCacheSeconds(), TimeUnit.SECONDS);
        }
        return queryEquityDto;
    }

    @Override
    public AccountInfoDTO queryEquityAccountInfo(EquityAccountContext equityAccountContext) {
        FirstOrderAccountDO firstOrderAccountDO = firstOrderAccountService.queryFirstOrderAccountDOByUserId(equityAccountContext.getUid());
        FirstOrderAccountEntity firstOrderAccountEntity = FirstOrderAccountEntity.builder().build();
        return firstOrderAccountEntity.createAccountInfoDTO(firstOrderAccountDO);
    }

    @Override
    public List<AccountInfoDTO> queryEquityAccountInfos(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public Boolean hasSucceededByUniqueKey(String userId, Integer operateType, String reqNo) {
        return null;
    }

    @Override
    public Long initRecord(int operateType, EquityAccountContext equityAccountContext) {
        FirstOrderAccountEntity firstOrderAccountEntity = FirstOrderAccountEntity.builder().build();
        FirstOrderAccountRecordDO firstOrderAccountRecordDO = firstOrderAccountEntity.initRecordDo(operateType, equityAccountContext);
        firstOrderAccountRecordService.addFirstOrderAccountRecordDO(firstOrderAccountRecordDO);
        return firstOrderAccountRecordDO.getId();
    }

    @Override
    public boolean updateRecordSuccessById(Long id, int status, EquityAccountContext equityAccountContext) {
        if (Objects.isNull(id) || Objects.isNull(equityAccountContext) || StringUtils.isBlank(equityAccountContext.getUid())) {
            return false;
        }
        return firstOrderAccountRecordService.updateRecordStatus(equityAccountContext.getUid(), id, equityAccountContext.getAccountId(), status, equityAccountContext.getSerialNumber(), equityAccountContext.getEquityRpcDto());
    }

    @Override
    public boolean updateRecordExceptionById(Long id, int status, EquityAccountContext equityAccountContext) {
        if (Objects.isNull(id) || Objects.isNull(equityAccountContext) || StringUtils.isBlank(equityAccountContext.getUid())) {
            return false;
        }
        return firstOrderAccountRecordService.updateRecord(equityAccountContext.getUid(), id, status, equityAccountContext.getSerialNumber(), equityAccountContext.getEquityRpcDto()) != 0;
    }

    @Override
    public FallbackEquityDto getFallbackEquityId(EquityAccountContext equityAccountContext) {
        FirstOrderAccountEntity firstOrderAccountEntity = FirstOrderAccountEntity.builder().build();
        FirstOrderAccountRecordDO firstOrderAccountRecordDO = firstOrderAccountEntity.createFirstOrderAccountRecordDO(equityAccountContext, OperateTypeEnum.USE.getCode());
        firstOrderAccountRecordDO = firstOrderAccountRecordService.queryFirstOrderAccountRecordDOByDO(firstOrderAccountRecordDO);
        return firstOrderAccountEntity.createFallbackEquityDto(firstOrderAccountRecordDO);
    }

    @Override
    public boolean havingBusinessData(EquityAccountContext equityAccountContext, Integer operateType, List<Integer> statusList) {
        FirstOrderAccountEntity firstOrderAccountEntity = FirstOrderAccountEntity.builder().build();
        FirstOrderAccountRecordDO firstOrderAccountRecordDO = firstOrderAccountEntity.createQueryAccountRecordDO(equityAccountContext.getUid(), equityAccountContext.getSerialNumber(), operateType);
        //发送记录
        FirstOrderAccountRecordDO existRecord = firstOrderAccountRecordService.queryFirstOrderAccountRecordDOByDO(firstOrderAccountRecordDO, statusList);
        equityAccountContext.setAccountRecordId(existRecord.getId());
        equityAccountContext.setAccountId(existRecord.getAccountId());
        //成功
        if (StatusEnum.SUCCESS.getCode().equals(existRecord.getStatus())) {
            return true;
        }
        //发放
        if (OperateTypeEnum.PROVIDE.getCode() == operateType) {
            return doProvideEquity(equityAccountContext) != 0;
        }
        //使用
        if (OperateTypeEnum.USE.getCode() == operateType) {
            return doUseEquity(equityAccountContext) != 0;
        }
        //回退
        if (OperateTypeEnum.FALLBACK.getCode() == operateType) {
            return doFallbackEquity(equityAccountContext) != 0;
        }
        return false;
    }


    @Override
    protected ReceiveBenefitResDTO receiveSceneBenefitRetry(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    protected void updateRecordFreezeReceiveLimitId(Long recordId, Long freezeReceiveLimitId,
                                                    String userId) {
    }

    @Override
    protected void updateRecordProcessing(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        String code = exceptionEnum.getCode();
        String msg = exceptionEnum.getMessage();
        if (!Objects.isNull(equityAccountContext.getEquityRpcDto())) {
            if (StringUtils.isNotBlank(equityAccountContext.getEquityRpcDto().getCode())) {
                code = equityAccountContext.getEquityRpcDto().getCode();
            }
            if (StringUtils.isNotBlank(equityAccountContext.getEquityRpcDto().getMessage())) {
                msg = equityAccountContext.getEquityRpcDto().getMessage();
            }
        }
        firstOrderAccountRecordService.updateRecordByIdAndUserId(recordId, equityAccountContext.getUid(), equityAccountContext.getSerialNumber(), equityAccountContext.getAccountId(), StatusEnum.PROCESSING.getCode(), code, msg);
    }

    /**
     * 记录表更新为失败
     *
     * @param recordId             记录id
     * @param equityAccountContext 请求上下文
     * @param exceptionEnum        异常信息
     */
    @Override
    protected void updateRecordFail(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        firstOrderAccountRecordService.updateRecordByIdAndUserId(recordId, equityAccountContext.getUid(), equityAccountContext.getSerialNumber(), equityAccountContext.getAccountId(), StatusEnum.FAIL.getCode(), exceptionEnum.getCode(), exceptionEnum.getMessage());
    }
}
