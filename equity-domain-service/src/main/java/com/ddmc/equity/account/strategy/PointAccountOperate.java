package com.ddmc.equity.account.strategy;

import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.entity.account.PointAccountConvertEntity;
import com.ddmc.equity.infra.rpc.user_point.UserPointProxy;
import com.ddmc.userpoint.api.request.IncreaseRequestReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/1/19 15:17
 * @description
 */
@Slf4j
@Component
public class PointAccountOperate extends AbstractEquityAccountOperate {

    @Autowired
    private UserPointProxy userPointProxy;

    @Override
    public EquityRpcDto doProvideEquityRpc(EquityAccountContext equityAccountContext) {
        IncreaseRequestReq request = PointAccountConvertEntity.convertToIncreaseRequest(equityAccountContext);
        return userPointProxy.increase(request);
    }
}
