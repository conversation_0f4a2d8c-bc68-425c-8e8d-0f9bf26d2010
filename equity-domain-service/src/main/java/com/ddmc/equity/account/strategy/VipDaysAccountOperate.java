package com.ddmc.equity.account.strategy;

import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.entity.account.VipDaysAccountConvertEntity;
import com.ddmc.equity.infra.rpc.vip.VipRechargeProxy;
import com.ddmc.vip.app.request.internal_api.ExchangeVipReqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/4/18 15:39
 * @description
 */
@Slf4j
@Component
public class VipDaysAccountOperate extends AbstractEquityAccountOperate {

    @Autowired
    private VipRechargeProxy vipRechargeProxy;

    @Override
    public EquityRpcDto doProvideEquityRpc(EquityAccountContext equityAccountContext) {
        ExchangeVipReqDTO req = VipDaysAccountConvertEntity.convertToExchangeVipReqDTO(equityAccountContext);
        EquityRpcDto equityRpcDto = vipRechargeProxy.exchangeVipDays(req);
        if (Objects.nonNull(equityRpcDto) && Objects.equals(StatusEnum.SUCCESS, equityRpcDto.getStatusEnum())) {
            equityRpcDto.setRpcResponseExtDTO(VipDaysAccountConvertEntity.convertToAccountRpcResponseExtDTO(equityRpcDto));
        }
        return equityRpcDto;
    }
}
