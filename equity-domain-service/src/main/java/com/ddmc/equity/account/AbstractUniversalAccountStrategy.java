package com.ddmc.equity.account;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.entity.account.UniversalAccountConvertEntity;
import com.ddmc.equity.domain.entity.common.DirectAccountContextEntity;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountDetailDomainService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountDomainService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountRecordDomainService;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;
import com.ddmc.equity.model.vo.BenefitReceiveSuccessMsgVO;
import com.ddmc.equity.model.vo.UniversalAccountDetailVO;
import com.ddmc.equity.mq.pulsar.producer.BenefitReceiveSuccessMsgProducer;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Slf4j
public abstract class AbstractUniversalAccountStrategy implements UniversalAccountStrategy {

    @Autowired
    private UniversalAccountDomainService universalAccountDomainService;
    @Autowired
    private UniversalAccountRecordDomainService universalAccountRecordDomainService;
    @Autowired
    private UniversalAccountDetailDomainService universalAccountDetailDomainService;
    @Autowired
    private BenefitReceiveSuccessMsgProducer benefitReceiveSuccessMsgProducer;
    @Autowired
    private TransactionUtil transactionUtil;

    @Override
    public ReceiveBenefitResDTO doReceiveEquity(DirectAccountContext directAccountContext) {
        //组装流水记录
        UniversalAccountRecordDO recordDO = DirectAccountContextEntity.convertUniversalAccountRecordDO(directAccountContext);
        try {
            //保存流水记录,通过唯一键控重复请求
            universalAccountRecordDomainService.add(recordDO);
            //根据权益类型不同，获取不同的service来发放权益
            return directBenefitCore(directAccountContext, recordDO);
        } catch (DuplicateKeyException e) {
            CsossUtils.logEventFail(MonitorConstants.UNIVERSAL_DIRECT_RECEIVE_BENEFIT, MonitorConstants.DUPLICATE_KEY_EXCEPTION, JSON.toJSONString(directAccountContext));
            log.warn("directBenefit duplicateKey context={}", JSON.toJSONString(directAccountContext));
            UniversalAccountRecordDO recordDB = universalAccountRecordDomainService.getOneByUniqueKey(recordDO);
            if (Objects.equals(recordDB.getStatus(), StatusEnum.PROCESSING.getCode()) ||
                    Objects.equals(recordDB.getStatus(), StatusEnum.INIT.getCode())) {
                // 需要修改 equityAccountContext 中领取的权益内容。外层需要领取成功的权益信息；重试也需要使用 existRecord 的参数进行重试；
                DirectAccountContextEntity.changeDirectAccountContextWhileRetry(directAccountContext, recordDB);
                return directBenefitCore(directAccountContext, recordDB);
            }
            return UniversalAccountConvertEntity.convertToExistSuccessReceiveResp(recordDB);
        } catch (Exception e) {
            CsossUtils.logEventFail(MonitorConstants.UNIVERSAL_DIRECT_RECEIVE_BENEFIT, MonitorConstants.EXCEPTION, JSON.toJSONString(directAccountContext));
            log.error("directBenefit exception context={}", JSON.toJSONString(directAccountContext), e);
            recordDO.setStatus(StatusEnum.PROCESSING.getCode());
            universalAccountRecordDomainService.update(recordDO);
            throw AdminExceptionBuilder.build(ExceptionEnum.HANDLE_TIMEOUT);
        }
    }

    /**
     * 使用权益分为冻结和不冻结两种操作
     *
     * @param directAccountContext
     * @return
     */
    @Override
    public boolean doUseEquity(DirectAccountContext directAccountContext) {
        List<UniversalAccountDO> accountDOList = universalAccountDomainService.queryAvailableUniversalAccounts(directAccountContext.getUserId(), Lists.newArrayList(directAccountContext.getUseActivityId(), 0L), directAccountContext.getBenefitType());
        Assert.notEmpty(accountDOList, ExceptionEnum.ACCOUNT_USE_FAIL);
        int accountSum = accountDOList.stream().mapToInt(UniversalAccountDO::getAvailableCount).sum();
        Assert.mustTrue(accountSum >= directAccountContext.getOperateCount(), ExceptionEnum.ACCOUNT_USE_FAIL);
        List<UniversalAccountDetailDO> accountDetailDOList = universalAccountDetailDomainService.queryAvailableAccountDetails(directAccountContext.getUserId(), Lists.newArrayList(directAccountContext.getUseActivityId(), 0L), directAccountContext.getBenefitType());
        Assert.notEmpty(accountDetailDOList, ExceptionEnum.ACCOUNT_USE_FAIL);
        int accountDetailSum = accountDetailDOList.stream().mapToInt(UniversalAccountDetailDO::getAvailableCount).sum();
        Assert.mustTrue(accountDetailSum >= directAccountContext.getOperateCount(), ExceptionEnum.ACCOUNT_USE_FAIL);
        Comparator<UniversalAccountDetailDO> detailDOComparator = getSortComparator(directAccountContext);
        accountDetailDOList.sort(detailDOComparator);
        List<UniversalAccountDetailVO> detailVOList = pickAccountDetailDOList(accountDetailDOList, directAccountContext);
        return doUseEquityCore(directAccountContext, accountDOList, detailVOList);

    }

    protected abstract boolean doUseEquityCore(DirectAccountContext directAccountContext, List<UniversalAccountDO> accountDOList, List<UniversalAccountDetailVO> detailVOList);

    @Override
    public boolean doUseEquityByDetail(DirectAccountContext directAccountContext) {
        UniversalAccountDetailDO accountDetailDO = universalAccountDetailDomainService.queryById(directAccountContext.getUserId(), directAccountContext.getAccountDetailId());
        Assert.notNull(accountDetailDO, ExceptionEnum.ACCOUNT_USE_FAIL);
        Assert.mustTrue(accountDetailDO.getAvailableCount() > 0, ExceptionEnum.ACCOUNT_USE_FAIL);
        directAccountContext.setAccountId(accountDetailDO.getAccountId());
        directAccountContext.setUseActivityId(accountDetailDO.getUseActivityId());
        directAccountContext.setBenefitId(accountDetailDO.getBenefitId());
        directAccountContext.setOperateCount(accountDetailDO.getAvailableCount());
        directAccountContext.setBenefitValue(accountDetailDO.getBenefitValue());
        UniversalAccountDO accountDO = universalAccountDomainService.queryById(directAccountContext.getUserId(), accountDetailDO.getAccountId());
        Assert.notNull(accountDO, ExceptionEnum.ACCOUNT_USE_FAIL);
        Assert.mustTrue(accountDO.getAvailableCount() > 0, ExceptionEnum.ACCOUNT_USE_FAIL);
        return doUseEquityByDetailCore(directAccountContext, accountDO, accountDetailDO);
    }

    protected abstract boolean doUseEquityByDetailCore(DirectAccountContext directAccountContext, UniversalAccountDO accountDO, UniversalAccountDetailDO accountDetailDO);


    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


    private ReceiveBenefitResDTO directBenefitCore(DirectAccountContext directAccountContext, UniversalAccountRecordDO recordDO) {
        // 调用 rpc 发放权益，并且奖 rpcResult 设置到上下文中
        EquityRpcDto rpcDto = doProvideBenefitRpcAndSetResult(directAccountContext);
        if (NumberUtil.isInteger(rpcDto.getCode())) {
            recordDO.setRpcCode(Integer.valueOf(rpcDto.getCode()));
        }
        recordDO.setRpcMsg(rpcDto.getMessage());
        recordDO.setStatus(rpcDto.getStatusEnum().getCode());
        recordDO.setRpcResponseExt(JsonUtil.toString(rpcDto.getRpcResponseExtDTO()));
        if (rpcDto.getStatusEnum().equals(StatusEnum.SUCCESS)) {
            //组装账户信息
            UniversalAccountDO universalAccountDO = DirectAccountContextEntity.convertUniversalAccountDO(directAccountContext);
            //组装账户详细信息
            UniversalAccountDetailDO universalAccountDetailDO = DirectAccountContextEntity.convertUniversalAccountDetailDO(directAccountContext);
            transactionUtil.transactional(s -> {
                //保存或更新账户表
                universalAccountDomainService.addOrUpdate(universalAccountDO);
                universalAccountDetailDO.setAccountId(universalAccountDO.getId());
                //保存账户信息详情表
                universalAccountDetailDomainService.add(universalAccountDetailDO);
                recordDO.setAccountId(universalAccountDO.getId());
                //更新记录表
                universalAccountRecordDomainService.update(recordDO);
            });

            // 设置账户明细 id 和账户 id 到上下文中，方便后续使用
            directAccountContext.setAccountDetailId(universalAccountDetailDO.getId());
            directAccountContext.setAccountId(universalAccountDO.getId());
            doAfterDirectReceiveSuccess(directAccountContext);
            CsossUtils.logEventSuccess(MonitorConstants.UNIVERSAL_DIRECT_RECEIVE_BENEFIT, MonitorConstants.SUCCESS);
            return ReceiveBenefitResDTO.success(rpcDto, directAccountContext.getAccountDetailId(),
                    directAccountContext.getAccountDetailId());
        } else if (rpcDto.getStatusEnum().equals(StatusEnum.FAIL)) {
            log.warn("directBenefitCore rpcFail context={}, rpcDto={}", JSON.toJSONString(directAccountContext),
                    JSON.toJSONString(rpcDto));
            CsossUtils.logEventFail(MonitorConstants.UNIVERSAL_DIRECT_RECEIVE_BENEFIT, MonitorConstants.FAIL, JSON.toJSONString(directAccountContext));
            recordDO.setAuxKey(String.valueOf(recordDO.getId()));
            universalAccountRecordDomainService.update(recordDO);
            return ReceiveBenefitResDTO.fail(ExceptionEnum.BENEFIT_SEND_FAIL);
        } else {
            log.warn("directBenefitCore rpcTimeOut context={}, rpcDto={}", JSON.toJSONString(directAccountContext),
                    JSON.toJSONString(rpcDto));
            throw AdminExceptionBuilder.build(ExceptionEnum.HANDLE_TIMEOUT);
        }
    }

    private Comparator<UniversalAccountDetailDO> getSortComparator(DirectAccountContext directAccountContext) {
        return Comparator.comparing(UniversalAccountDetailDO::getExpireTime);
    }

    private List<UniversalAccountDetailVO> pickAccountDetailDOList(List<UniversalAccountDetailDO> accountDetailDOList, DirectAccountContext directAccountContext) {
        List<UniversalAccountDetailVO> detailVOS = new ArrayList<>();
        int operateCount = directAccountContext.getOperateCount();
        for (UniversalAccountDetailDO detailDO : accountDetailDOList) {
            if (operateCount == 0) {
                break;
            }
            UniversalAccountDetailVO vo = new UniversalAccountDetailVO();
            vo.setId(detailDO.getId());
            vo.setUserId(detailDO.getUserId());
            vo.setVersion(detailDO.getVersion());
            vo.setAccountId(detailDO.getAccountId());
            int availableCount = detailDO.getAvailableCount();
            int deductCount = Math.min(operateCount, availableCount);
            vo.setDeductCount(deductCount);
            detailVOS.add(vo);
            operateCount -= deductCount;
        }

        return detailVOS;
    }

    private EquityRpcDto doProvideBenefitRpcAndSetResult(DirectAccountContext context) {
        Integer benefitType = context.getBenefitType();
        EquityAccountContext provideAccountContext = DirectAccountContextEntity.convertToProvideAccountContext(context);
        EquityRpcDto equityRpcDto = AccountStrategyContext.builderByBenefitType(benefitType)
                .getAbstractAccountOperate()
                .doProvideEquityRpc(provideAccountContext);
        context.setEquityRpcDto(equityRpcDto);
        return equityRpcDto;
    }

    private void doAfterDirectReceiveSuccess(DirectAccountContext directAccountContext) {
        // 异步发送领取成功 Pulsar 消息
        BenefitReceiveSuccessMsgVO msgVO = DirectAccountContextEntity
                .convertToDirectBenefitReceiveSuccessMsgVO(directAccountContext);
        benefitReceiveSuccessMsgProducer.asyncSendBenefitReceiveSuccessMsg(msgVO);
    }
}
