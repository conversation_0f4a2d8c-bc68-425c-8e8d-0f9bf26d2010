package com.ddmc.equity.account.strategy;

import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.infra.rpc.promocore.PromocoreProxy;
import com.ddmc.promocore.client.base.request.lottery.CountIncreaseRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/1/22 10:42
 * @description
 */
@Slf4j
@Component
public class FlopCountOperate extends AbstractEquityAccountOperate {

    @Autowired
    private PromocoreProxy promocoreProxy;

    @Override
    public EquityRpcDto doProvideEquityRpc(EquityAccountContext equityAccountContext) {
        CountIncreaseRequest request = new CountIncreaseRequest();
        request.setUserId(equityAccountContext.getUid());
        request.setActivityId(equityAccountContext.getExternalId());
        request.setBizFrom(equityAccountContext.getSource());
        request.setBizNo(equityAccountContext.getSerialNumber());
        request.setCount(NumberUtils.convertToInteger(equityAccountContext.getEquityValue()));
        return promocoreProxy.increase(request);
    }
}
