package com.ddmc.equity.account;

import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;

/**
 * 直塞发放、通用使用、通用回退
 */
public interface UniversalAccountStrategy {

    /**
     * 直塞发放权益（没有活动规则）
     * <p>
     * rpc 发放结果放在 directAccountContext.equityRpcDto 里面
     *
     * @param directAccountContext 上下文
     * @return 发放结果
     */
    ReceiveBenefitResDTO doReceiveEquity(DirectAccountContext directAccountContext);

    /**
     * 使用权益，使用结果放在 directAccountContext.equityRpcDto 里面
     * 通用
     *
     * @param directAccountContext
     */
    boolean doUseEquity(DirectAccountContext directAccountContext);

    /**
     * 指定具体某一条权益去使用，使用结果放在 directAccountContext.equityRpcDto 里面
     * 通用
     *
     * @param directAccountContext
     */
    boolean doUseEquityByDetail(DirectAccountContext directAccountContext);

    /**
     * 回退权益，回退结果放在 directAccountContext.equityRpcDto 里面
     * 通用
     *
     * @param directAccountContext
     */
    boolean doBackEquity(DirectAccountContext directAccountContext);
}
