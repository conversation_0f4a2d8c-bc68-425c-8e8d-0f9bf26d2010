package com.ddmc.equity.account.strategy;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.enums.SubAccountStatusEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.FallbackEquityDto;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.entity.account.DrawCodeAccountConvertEntity;
import com.ddmc.equity.domain.service.code.GenerateCodeService;
import com.ddmc.equity.domain.service.draw_code.DrawCodeAccountRecordService;
import com.ddmc.equity.domain.service.draw_code.DrawCodeAccountService;
import com.ddmc.equity.infra.repository.dao.DrawCodeAccountDO;
import com.ddmc.equity.infra.repository.dao.DrawCodeAccountRecordDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.QueryEquityDto;
import com.ddmc.equity.model.dto.UseBenefitDTO;
import com.ddmc.utils.constant.CommonConstants;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/5/30 16:03
 * @description
 */
@Slf4j
@Component
public class DrawCodeAccountOperate extends AbstractEquityAccountOperate {

    @Autowired
    private TransactionUtil transactionUtil;
    @Autowired
    private GenerateCodeService generateCodeService;
    @Autowired
    private DrawCodeAccountService drawCodeAccountService;
    @Autowired
    private DrawCodeAccountRecordService drawCodeAccountRecordService;

    @Override
    public boolean checkUseEquity(EquityAccountContext equityAccountContext) {
        List<UseBenefitDTO> useBenefits = equityAccountContext.getUseBenefits();
        if (CollectionUtils.isEmpty(useBenefits)) {
            log.warn("checkUseEquity useBenefits is null. context={}", JSON.toJSONString(equityAccountContext));
            throw AdminExceptionBuilder.build(ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码使用校验，传入的权益列表为空");
        }
        List<String> drawCodes = useBenefits.stream().map(UseBenefitDTO::getDrawCode).collect(Collectors.toList());
        if (drawCodes.stream().anyMatch(StringUtils::isBlank)) {
            log.warn("checkUseEquity useBenefits is null. context={}", JSON.toJSONString(equityAccountContext));
            throw AdminExceptionBuilder.build(ExceptionEnum.ILLEGAL_ARGS.getCode(), "抽签码使用校验，传入的权益列表抽签码不能为空");
        }

        List<DrawCodeAccountDO> accountDOList = drawCodeAccountService.queryAccountsByUserIdAndDrawCodes(equityAccountContext.getUid(), drawCodes);
        Map<String /* drawCode */, DrawCodeAccountDO> dbAccountMap = CollectionUtils.isEmpty(accountDOList) ? Maps.newHashMap() :
                accountDOList.stream().collect(Collectors.toMap(DrawCodeAccountDO::getDrawCode, Function.identity(), (v1, v2) -> v1));
        Set<String> notExistDrawCodes = Sets.newHashSet();
        Set<String> unavailableDrawCodes = Sets.newHashSet();
        equityAccountContext.getUseBenefits().forEach(e -> {
            String drawCode = e.getDrawCode();
            DrawCodeAccountDO dbAccountDO = dbAccountMap.get(drawCode);
            if (Objects.isNull(dbAccountDO)) {
                notExistDrawCodes.add(drawCode);
                return;
            }
            if (!SubAccountStatusEnum.UNUSED.getCode().equals(dbAccountDO.getStatus())) {
                unavailableDrawCodes.add(drawCode);
                return;
            }
            // 设置权益子账户 id 和权益 id
            e.setAccountId(dbAccountDO.getId());
            e.setBenefitId(dbAccountDO.getBenefitId());
        });

        Map<String, Object> eventData = Maps.newHashMap();
        eventData.put("equityAccountContext", equityAccountContext);
        if (CollectionUtils.isNotEmpty(notExistDrawCodes)) {
            log.warn("checkUseEquity notExistDrawCodes isn't null. context={}, notExistDrawCodes={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(notExistDrawCodes));
            eventData.put("notExistDrawCodes", notExistDrawCodes);
            CsossUtils.logEvent(MonitorConstants.CHECK_USE_EQUITY_EXCEPTION, "notExistDrawCodes",
                    CommonConstants.IS_SUCCESS_CODE, JSON.toJSONString(eventData));
            throw AdminExceptionBuilder.build(ExceptionEnum.ILLEGAL_ARGS.getCode(),
                    "抽签码使用校验，以下抽签码未找到=" + StringUtils.join(notExistDrawCodes, ","));
        }
        if (CollectionUtils.isNotEmpty(unavailableDrawCodes)) {
            log.warn("checkUseEquity unavailableDrawCodes isn't null. context={}, unavailableDrawCodes={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(unavailableDrawCodes));
            eventData.put("unavailableDrawCodes", unavailableDrawCodes);
            CsossUtils.logEvent(MonitorConstants.CHECK_USE_EQUITY_EXCEPTION, "unavailableDrawCodes",
                    CommonConstants.IS_SUCCESS_CODE, JSON.toJSONString(eventData));
            throw AdminExceptionBuilder.build(ExceptionEnum.ILLEGAL_ARGS.getCode(),
                    "抽签码使用校验，以下抽签码不可用=" + StringUtils.join(unavailableDrawCodes, ","));
        }
        return true;
    }

    @Override
    public int doUseEquity(EquityAccountContext equityAccountContext) {
        String userId = equityAccountContext.getUid();
        Long accountRecordId = equityAccountContext.getAccountRecordId();
        transactionUtil.transactional(t -> {
            // 1 更新权益子账户状态
            List<Long> accountIds = equityAccountContext.getUseBenefits().stream().map(UseBenefitDTO::getAccountId).collect(Collectors.toList());
            boolean updateAccountResult = drawCodeAccountService.updateAccountsStatus(userId, accountIds, SubAccountStatusEnum.UNUSED.getCode(),
                    SubAccountStatusEnum.USED.getCode(), equityAccountContext.getSerialNumber());
            if (!updateAccountResult) {
                log.warn("doUseEquity updateAccountsStatus failure. userId={}, accountIds={}", userId, accountIds);
                throw AdminExceptionBuilder.build(ExceptionEnum.ACCOUNT_USE_FAIL);
            }

            // 2 更新权益子账户操作流水结果
            // 如果一次操作多条账户，权益子账户操作流水设置 accountId=0L
            equityAccountContext.setAccountId(CollectionUtils.size(accountIds) > 1 ? 0L : accountIds.get(0));
            boolean updateAccountRecordResult = updateRecordSuccessById(accountRecordId, StatusEnum.SUCCESS.getCode(), equityAccountContext);
            if (!updateAccountRecordResult) {
                log.warn("doUseEquity updateRecordSuccessById failure. userId={}, accountRecordId={}", userId, accountRecordId);
                throw AdminExceptionBuilder.build(ExceptionEnum.ACCOUNT_RECORD_UPDATE_FAIL);
            }
        });
        return Constants.ONE;
    }

    @Override
    public boolean checkFallbackEquity(EquityAccountContext equityAccountContext) {
        return false;
    }

    @Override
    public int doProvideEquity(EquityAccountContext equityAccountContext) {
        return 0;
    }

    @Override
    public List<AccountInfoDTO> doProvideEquityAndGetResult(EquityAccountContext equityAccountContext) {
        String userId = equityAccountContext.getUid();
        Long accountRecordId = equityAccountContext.getAccountRecordId();

        // 1 生成抽签码，并构建 provideAccountDOList
        List<String> drawCodes = generateCodeService.batchGenerateAndInsertDrawCode(equityAccountContext.getDistributeCount());
        List<DrawCodeAccountDO> provideAccountDOList = DrawCodeAccountConvertEntity.createProvideAccountDOList(equityAccountContext, drawCodes);

        transactionUtil.transactional(t -> {
            // 2 批量生成权益子账户
            drawCodeAccountService.batchInsertAccountDO(provideAccountDOList);

            // 3 更新权益子账户操作流水结果
            // 如果一次操作多条账户，权益子账户操作流水设置 accountId=0L
            equityAccountContext.setAccountId(CollectionUtils.size(provideAccountDOList) > 1 ? 0L : provideAccountDOList.get(0).getId());
            boolean updateAccountRecordResult = updateRecordSuccessById(accountRecordId, StatusEnum.SUCCESS.getCode(), equityAccountContext);
            if (!updateAccountRecordResult) {
                log.warn("doProvideEquityAndGetResult updateRecordSuccessById failure. userId={}, accountRecordId={}", userId, accountRecordId);
                throw AdminExceptionBuilder.build(ExceptionEnum.ACCOUNT_RECORD_UPDATE_FAIL);
            }
        });
        return DrawCodeAccountConvertEntity.convertToAccountInfoDTOList(provideAccountDOList);
    }

    @Override
    public List<AccountInfoDTO> retryDoProvideEquityAndGetResult(EquityAccountContext equityAccountContext) {
        String userId = equityAccountContext.getUid();
        String reqNo = equityAccountContext.getSerialNumber();

        // 1 获取非失败状态的操作流水
        List<Integer> statusList = Lists.newArrayList(StatusEnum.SUCCESS.getCode(), StatusEnum.INIT.getCode(), StatusEnum.PROCESSING.getCode());
        DrawCodeAccountRecordDO existRecord = drawCodeAccountRecordService.queryAccountRecordByUniqueKeyAndStatuses(userId,
                equityAccountContext.getOperateType(), reqNo, statusList);
        if (Objects.isNull(existRecord)) {
            log.warn("retryDoProvideEquityAndGetResult existRecord is null. context={}", JSON.toJSONString(equityAccountContext));
            return null;
        }

        // 2 如果操作流水状态为成功，则直接查询并返回上一次操作结果
        if (StatusEnum.SUCCESS.getCode().equals(existRecord.getStatus())) {
            List<DrawCodeAccountDO> drawCodeAccounts = drawCodeAccountService.queryAccountsByUserIdAndReqNo(userId, reqNo);
            List<AccountInfoDTO> result = DrawCodeAccountConvertEntity.convertToAccountInfoDTOList(drawCodeAccounts);
            log.info("retryDoProvideEquityAndGetResult existRecord success. context={}, result={}", JSON.toJSONString(equityAccountContext),
                    JSON.toJSONString(result));
            return result;
        }

        // 3 重试
        equityAccountContext.setAccountRecordId(existRecord.getId());
        return doProvideEquityAndGetResult(equityAccountContext);
    }

    @Override
    public boolean updateRecordSuccessById(Long recordId, int status, EquityAccountContext equityAccountContext) {
        if (Objects.isNull(equityAccountContext)) {
            return false;
        }
        return drawCodeAccountRecordService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), status, equityAccountContext.getEquityRpcDto());
    }

    @Override
    public boolean updateRecordExceptionById(Long recordId, int status, EquityAccountContext equityAccountContext) {
        if (Objects.isNull(equityAccountContext)) {
            return false;
        }
        return drawCodeAccountRecordService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), status, equityAccountContext.getEquityRpcDto());
    }

    @Override
    public int doFallbackEquity(EquityAccountContext equityAccountContext) {
        return 0;
    }

    @Override
    public Long initRecord(int operateType, EquityAccountContext equityAccountContext) {
        DrawCodeAccountRecordDO accountRecordDO = DrawCodeAccountConvertEntity.createAccountRecordDO(operateType, equityAccountContext);
        drawCodeAccountRecordService.insertAccountRecordDO(accountRecordDO);
        return accountRecordDO.getId();
    }

    @Override
    public FallbackEquityDto getFallbackEquityId(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public boolean havingBusinessData(EquityAccountContext equityAccountContext, Integer operateType, List<Integer> statusList) {
        // 1 获取非失败状态的操作流水
        DrawCodeAccountRecordDO existRecord = drawCodeAccountRecordService.queryAccountRecordByUniqueKeyAndStatuses(equityAccountContext.getUid(),
                equityAccountContext.getOperateType(), equityAccountContext.getSerialNumber(), statusList);
        if (Objects.isNull(existRecord)) {
            log.warn("havingBusinessData existRecord is null. context={}", JSON.toJSONString(equityAccountContext));
            return false;
        }

        // 2 如果操作流水状态为成功，则返回成功
        if (StatusEnum.SUCCESS.getCode().equals(existRecord.getStatus())) {
            return true;
        }

        // 3 重试
        equityAccountContext.setAccountRecordId(existRecord.getId());
        if (Objects.equals(operateType, OperateTypeEnum.USE.getCode())) {
            return doUseEquity(equityAccountContext) != 0;
        }
        return false;
    }


    @Override
    protected ReceiveBenefitResDTO receiveSceneBenefitRetry(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    protected void updateRecordFreezeReceiveLimitId(Long recordId, Long freezeReceiveLimitId, String userId) {

    }

    @Override
    protected void updateRecordProcessing(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        equityRpcDto.setCode(exceptionEnum.getCode());
        equityRpcDto.setMessage(exceptionEnum.getMessage());
        drawCodeAccountRecordService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), StatusEnum.PROCESSING.getCode(), equityRpcDto);
    }

    @Override
    protected void updateRecordFail(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        equityRpcDto.setCode(exceptionEnum.getCode());
        equityRpcDto.setMessage(exceptionEnum.getMessage());
        drawCodeAccountRecordService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), StatusEnum.FAIL.getCode(), equityRpcDto);
    }

    @Override
    public QueryEquityDto equityQuery(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public AccountInfoDTO queryEquityAccountInfo(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public List<AccountInfoDTO> queryEquityAccountInfos(EquityAccountContext equityAccountContext) {
        List<DrawCodeAccountDO> drawCodeAccountDOList = drawCodeAccountService.queryAccountsByUserId(equityAccountContext.getUid(),
                equityAccountContext.getBenefitId(), equityAccountContext.getDrawCodeSources(), equityAccountContext.getStatuses(),
                equityAccountContext.getStartDate(), equityAccountContext.getEndDate());
        return DrawCodeAccountConvertEntity.convertToAccountInfoDTOList(drawCodeAccountDOList);
    }

    @Override
    public Boolean hasSucceededByUniqueKey(String userId, Integer operateType, String reqNo) {
        List<Integer> statuses = Lists.newArrayList(StatusEnum.SUCCESS.getCode());
        DrawCodeAccountRecordDO existAccountRecordDO = drawCodeAccountRecordService
                .queryAccountRecordByUniqueKeyAndStatuses(userId, operateType, reqNo, statuses);
        return Objects.nonNull(existAccountRecordDO);
    }
}
