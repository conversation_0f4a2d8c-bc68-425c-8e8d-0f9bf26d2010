package com.ddmc.equity.account.strategy;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.BenefitReceiveRuleContext;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.CommonEnum;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.exception.ApiBusinessException;
import com.ddmc.equity.common.util.CatMonitorUtil;
import com.ddmc.equity.common.util.IntegerUtil;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.common.util.LongUtils;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.common.util.business.BenefitUtil;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.FallbackEquityDto;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcResponseExtDTO;
import com.ddmc.equity.domain.entity.account.TicketAccountConvertEntity;
import com.ddmc.equity.domain.entity.common.BenefitOperateEntity;
import com.ddmc.equity.domain.service.core.ReceiveAndStockLimitCoreService;
import com.ddmc.equity.domain.service.ticketAccount.TicketAccountService;
import com.ddmc.equity.domain.service.ticketAccountRecord.TicketAccountRecordService;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.infra.repository.dao.TicketAccountDO;
import com.ddmc.equity.infra.repository.dao.TicketAccountRecordDO;
import com.ddmc.equity.infra.rpc.voucher.VoucherSendTicketProxy;
import com.ddmc.equity.infra.rpc.voucher.dto.SendTicketResDTO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.ActivityCacheDto;
import com.ddmc.equity.model.dto.QueryEquityDto;
import com.ddmc.equity.model.entity.TicketAccountEntity;
import com.ddmc.vouchercore.client.request.SendTicketRequest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class TicketAccountOperate extends AbstractEquityAccountOperate {

    @Autowired
    private TicketAccountService ticketAccountService;
    @Autowired
    private TicketAccountRecordService ticketAccountRecordService;
    @Autowired
    private TransactionUtil transactionUtil;
    @Autowired
    private VoucherSendTicketProxy voucherSendTicketProxy;
    @Autowired
    private CatMonitorUtil catMonitorUtil;
    @Resource
    private LocalCacheManager localCacheManager;
    @Autowired
    private ReceiveAndStockLimitCoreService receiveAndStockLimitCoreService;

    @Override
    public boolean checkUseEquity(EquityAccountContext equityAccountContext) {
        return false;
    }

    @Override
    public int doUseEquity(EquityAccountContext equityAccountContext) {
        return 0;
    }

    @Override
    public boolean checkFallbackEquity(EquityAccountContext equityAccountContext) {
        return false;
    }

    /**
     * 权益发放
     *
     * @param equityAccountContext 请求上下文
     * @return 状态
     */
    @Override
    public int doProvideEquity(EquityAccountContext equityAccountContext) {
        //实体转换
        TicketAccountEntity ticketAccountEntity = TicketAccountEntity.builder().build();
        TicketAccountDO ticketAccountDO = ticketAccountEntity.convertToTicketAccountDO(equityAccountContext);
        try {
            transactionUtil.transactional(s -> saveAccountAndUpdateRecordSuccess(ticketAccountDO, equityAccountContext));
            return Constants.ONE;
        } catch (Exception e) {
            log.error("TicketAccountOperate.doProvideEquity.e;req={}", JsonUtil.toJsonString(equityAccountContext), e);
        }
        return 0;
    }

    @Override
    public List<AccountInfoDTO> doProvideEquityAndGetResult(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public List<AccountInfoDTO> retryDoProvideEquityAndGetResult(EquityAccountContext equityAccountContext) {
        return null;
    }

    /**
     * 保存账户并更新record
     *
     * @param ticketAccountDO      账户
     * @param equityAccountContext 请求上下文
     */
    private void saveAccountAndUpdateRecordSuccess(TicketAccountDO ticketAccountDO, EquityAccountContext equityAccountContext) {
        //保存账户信息
        boolean isSuccess = ticketAccountService.save(ticketAccountDO);
        if (!isSuccess) {
            throw new ApiBusinessException(ExceptionEnum.DB_OPERATE_ERROR);
        }
        Long accountId = ticketAccountDO.getId();
        equityAccountContext.setAccountId(accountId);
        String code = ExceptionEnum.SUCCESS.getCode();
        String msg = ExceptionEnum.SUCCESS.getMessage();
        String userTicketId = Constants.EMPTY_STRING;
        String innerSerialNumber = equityAccountContext.getDoRpcUniqueSerialNumber();
        EquityRpcDto equityRpcDto = equityAccountContext.getEquityRpcDto();
        if (Objects.nonNull(equityRpcDto)) {
            userTicketId = equityRpcDto.getValue();
        }
        //操作记录更新
        isSuccess = ticketAccountRecordService.updateRecord(
                equityAccountContext.getAccountRecordId(), equityAccountContext.getUid(),
                StatusEnum.SUCCESS.getCode(), accountId,
                innerSerialNumber, userTicketId,
                equityAccountContext.getSerialNumber(),
                code, msg,
                equityAccountContext.getFreezeReceiveLimitResultId(),
                equityAccountContext.getSendTicketScene());
        if (!isSuccess) {
            throw new ApiBusinessException(ExceptionEnum.DB_OPERATE_ERROR);
        }
    }

    @Override
    public boolean updateRecordSuccessById(Long id, int status, EquityAccountContext equityAccountContext) {
        return false;
    }

    @Override
    public boolean updateRecordExceptionById(Long id, int status, EquityAccountContext equityAccountContext) {
        return false;
    }

    @Override
    public int doFallbackEquity(EquityAccountContext equityAccountContext) {
        return 0;
    }

    /**
     * 权益发放
     *
     * @param equityAccountContext 请求上下文
     * @return 结果
     */
    @Override
    public EquityRpcDto doProvideEquityRpc(EquityAccountContext equityAccountContext) {
        SendTicketRequest request = new SendTicketRequest();
        request.setAppid(Constants.EQUITY_CUSTOMER_SERVICE);
        // isUseSerialNumberSave 如果为 1，则调用优惠券接口使用前端流水号；如果为空或者不为 1，则使用冻结权益维度频次返回的流水号（如果没有权益维度频次限制，也会有该流水号）；
        if (CommonEnum.INTEGER_BOOL.YES.getCode().equals(equityAccountContext.getIsUseSerialNumberSave())) {
            request.setExternal_unique_id(equityAccountContext.getSerialNumber());
        } else {
            request.setExternal_unique_id(equityAccountContext.getDoRpcUniqueSerialNumber());
        }

        // 设置调用券平台接口发券时的 activity、prize
        Long activityId = equityAccountContext.getActivityId();
        Long strategyId = equityAccountContext.getStrategyId();
        Long benefitId = equityAccountContext.getBenefitId();
        request.setActivity(TicketAccountConvertEntity.getSendTicketActivity(equityAccountContext));
        request.setPrize(TicketAccountConvertEntity.getSendTicketPrize(equityAccountContext));
        // 如果为领券活动（即场景为领券中心），则设置发券时的 activity = activity.externalId、prize = benefit.mappingId
        if (BenefitUtil.isSpecSceneSendTicket(equityAccountContext.getSceneCode())) {
            ActivityCacheDto activityCacheDto = localCacheManager.getActivityCacheDtoById(activityId);
            request.setActivity(Optional.ofNullable(activityCacheDto).map(ActivityCacheDto::getExternalId).orElse(null));
            request.setPrize(localCacheManager.getMappingIdByBenefitIdAndStrategyById(benefitId, strategyId));
        }

        request.setUid(equityAccountContext.getUid());
        request.setTicket_id(equityAccountContext.getEquityValue());
        request.setType("send_coupon");
        request.setScene(equityAccountContext.getSendTicketScene());
        // scene in (12,17,24,108) 无论 isRead 传值是什么，券平台写死为已读；其他 scene 取传值，为空默认为已读
        request.setIsRead(equityAccountContext.getIsTicketIsRead());
        SendTicketResDTO sendTicketResDTO = voucherSendTicketProxy.sendTicketSync(request);
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        equityRpcDto.setCode(sendTicketResDTO.getCode());
        equityRpcDto.setMessage(sendTicketResDTO.getMsg());
        //成功
        if (StatusEnum.SUCCESS.equals(sendTicketResDTO.getStatusEnum())) {
            equityRpcDto.setStatusEnum(StatusEnum.SUCCESS);
            equityRpcDto.setValue(sendTicketResDTO.getUserTicketId());
            UniversalAccountRpcResponseExtDTO rpcResponseExtDTO = UniversalAccountRpcResponseExtDTO.builder()
                    .userTicketId(sendTicketResDTO.getUserTicketId())
                    .build();
            equityRpcDto.setRpcResponseExtDTO(rpcResponseExtDTO);
            return equityRpcDto;
        }
        //失败
        if (StatusEnum.FAIL.equals(sendTicketResDTO.getStatusEnum())) {
            equityRpcDto.setStatusEnum(StatusEnum.FAIL);
            log.error("ticketAccountOperate doProvideEquityRpc failure. context={}, request={}, response={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(request), JSON.toJSONString(sendTicketResDTO));
            return equityRpcDto;
        }
        equityRpcDto.setStatusEnum(StatusEnum.PROCESSING);
        return equityRpcDto;
    }

    /**
     * record 初始化
     *
     * @param operateType          操作类型
     * @param equityAccountContext 请求上下文
     * @return 记录主键id
     */
    @Override
    public Long initRecord(int operateType, EquityAccountContext equityAccountContext) {
        //实体声明
        TicketAccountEntity ticketAccountEntity = TicketAccountEntity.builder().build();
        //实体转换
        TicketAccountRecordDO recordDO = ticketAccountEntity.convertToTicketAccountRecordDO(operateType, equityAccountContext);
        //数据保存
        ticketAccountRecordService.save(recordDO);
        return recordDO.getId();
    }

    @Override
    public FallbackEquityDto getFallbackEquityId(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public boolean havingBusinessData(EquityAccountContext equityAccountContext, Integer operateType, List<Integer> statusList) {
        return false;
    }

    @Override
    protected ReceiveBenefitResDTO handlerHistoryFreezeReceiveProcessingRecord(EquityAccountContext equityAccountContext) {
        String uid = equityAccountContext.getUid();
        Long activityId = equityAccountContext.getActivityId();
        Long strategyId = equityAccountContext.getStrategyId();
        Long benefitId = equityAccountContext.getBenefitId();
        Long recordId = equityAccountContext.getAccountRecordId();
        String sceneCode = equityAccountContext.getSceneCode();
        String serialNumber = equityAccountContext.getSerialNumber();
        Long freezeReceiveLimitId = equityAccountContext.getFreezeReceiveLimitResultId();
        //冻结id为空
        if (Objects.isNull(freezeReceiveLimitId)) {
            ticketAccountRecordService.updateRecordStatus(recordId, uid, StatusEnum.FAIL.getCode(),
                    serialNumber, ExceptionEnum.FREEZE_RECEIVE_LIMIT_RESULT_ID_IS_NULL.getCode(),
                    ExceptionEnum.FREEZE_RECEIVE_LIMIT_RESULT_ID_IS_NULL.getMessage(), null);
            return ReceiveBenefitResDTO.fail(ExceptionEnum.BENEFIT_SEND_FAIL);
        }
        List<Integer> statusList = Lists.newArrayList();
        statusList.add(StatusEnum.INIT.getCode());
        statusList.add(StatusEnum.PROCESSING.getCode());
        // 查询
        List<TicketAccountRecordDO> ticketAccountRecordDOList = ticketAccountRecordService.select(uid, activityId, strategyId,
                benefitId, statusList, OperateTypeEnum.PROVIDE.getCode(), freezeReceiveLimitId);
        if (CollectionUtils.isEmpty(ticketAccountRecordDOList)) {
            log.error("TicketAccountOperate.handlerHistoryProcessingRecord.exist.error.data;uid={};activityId={};strategyId={};benefitId={}", uid, activityId, strategyId, benefitId);
            ticketAccountRecordService.updateRecordStatus(recordId, uid, StatusEnum.FAIL.getCode(), serialNumber, ExceptionEnum.RECEIVE_SCENE_BENEFIT_PROCESSING_IS_NULL.getCode(), ExceptionEnum.RECEIVE_SCENE_BENEFIT_PROCESSING_IS_NULL.getMessage(), freezeReceiveLimitId);
            return ReceiveBenefitResDTO.fail(ExceptionEnum.BENEFIT_SEND_FAIL);
        }
        // 查找一条
        TicketAccountRecordDO ticketAccountRecordDO = ticketAccountRecordDOList.get(0);
        // 实体转换
        BenefitOperateEntity benefitOperateEntity = BenefitOperateEntity.builder().build();
        EquityAccountContext newEquityAccountContext = benefitOperateEntity.convertToTicketEquityAccountContext(ticketAccountRecordDO, sceneCode);
        BenefitReceiveRuleContext benefitReceiveRuleContext = super.gainBenefitReceiveRuleContext(newEquityAccountContext);
        //发送权益
        EquityRpcDto equityRpcDto = doProvideEquityRpc(newEquityAccountContext);
        newEquityAccountContext.setEquityRpcDto(equityRpcDto);
        //成功
        if (StatusEnum.SUCCESS.equals(equityRpcDto.getStatusEnum())) {
            //数据保存
            boolean isSuccess = saveAccountAndUpdateProcessRecordAndUpdateNewRecord(ticketAccountRecordDO, serialNumber, recordId, newEquityAccountContext);
            if (isSuccess) {
                // 频次扣减
                receiveAndStockLimitCoreService.deductBenefitReceiveLimit(newEquityAccountContext, benefitReceiveRuleContext);
                return ReceiveBenefitResDTO.success(equityRpcDto, newEquityAccountContext.getAccountRecordId());
            }

        }
        //失败
        if (StatusEnum.FAIL.equals(equityRpcDto.getStatusEnum())) {
            try {
                //记录置为失败
                transactionUtil.transactional(s -> updateFreeRecordAndNewRecordFail(ticketAccountRecordDO.getId(), ticketAccountRecordDO.getReqNo(),
                        newEquityAccountContext, recordId, serialNumber, equityRpcDto));
                //释放频次
                receiveAndStockLimitCoreService.releaseBenefitReceiveLimit(newEquityAccountContext, benefitReceiveRuleContext);
                return ReceiveBenefitResDTO.fail(ExceptionEnum.SEND_TICKET_FAIL);
            } catch (Exception e) {
                log.error("TicketAccountOperate.handlerHistoryFreezeReceiveProcessingRecord.e;uid={};activityId={};strategyId={};benefitId={}", uid, activityId, strategyId, benefitId, e);
            }
        }
        ticketAccountRecordService.updateRecord(recordId, newEquityAccountContext.getUid(), StatusEnum.FAIL.getCode(),
                null, serialNumber, null, null, ExceptionEnum.SEND_TICKET_FAIL.getCode(),
                ExceptionEnum.SEND_TICKET_FAIL.getMessage(), null, newEquityAccountContext.getSendTicketScene());
        return ReceiveBenefitResDTO.fail(ExceptionEnum.SEND_TICKET_FAIL);
    }

    /**
     * 置为失败
     *
     * @param freezeRecordId       冻结记录id
     * @param reqNo                请求流水号
     * @param equityAccountContext 请求上下文
     * @param newRecordId          新记录id
     * @param serialNumber         请求流水好
     * @param equityRpcDto         请求返回
     */
    private void updateFreeRecordAndNewRecordFail(Long freezeRecordId, String reqNo, EquityAccountContext equityAccountContext, Long newRecordId, String serialNumber, EquityRpcDto equityRpcDto) {
        String code = equityRpcDto.getCode();
        String msg = equityRpcDto.getMessage();
        if (StringUtils.isBlank(code)) {
            code = ExceptionEnum.SEND_TICKET_FAIL.getCode();
        }
        if (StringUtils.isBlank(msg)) {
            msg = ExceptionEnum.SEND_TICKET_FAIL.getMessage();
        }
        //是否更新成功
        boolean isSuccess = ticketAccountRecordService.updateRecord(
                freezeRecordId, equityAccountContext.getUid(),
                StatusEnum.FAIL.getCode(), null,
                null, null,
                reqNo,
                code, msg, null,
                equityAccountContext.getSendTicketScene());

        if (!isSuccess) {
            throw new ApiBusinessException(ExceptionEnum.DB_OPERATE_ERROR);
        }

        isSuccess = ticketAccountRecordService.updateRecord(
                newRecordId, equityAccountContext.getUid(),
                StatusEnum.FAIL.getCode(), null,
                null, null,
                serialNumber,
                code, msg, null,
                equityAccountContext.getSendTicketScene());

        if (!isSuccess) {
            throw new ApiBusinessException(ExceptionEnum.DB_OPERATE_ERROR);
        }
    }


    /**
     * 保存权益
     *
     * @param ticketAccountRecordDO 原始记录
     * @param serialNumber          新的流水号
     * @param newRecordId           新的id
     * @param equityAccountContext  请求上下文
     */
    private boolean saveAccountAndUpdateProcessRecordAndUpdateNewRecord(TicketAccountRecordDO ticketAccountRecordDO, String serialNumber, Long newRecordId, EquityAccountContext equityAccountContext) {
        try {
            transactionUtil.transactional(s -> saveAccountAndUpdateSuccessRecord(ticketAccountRecordDO, serialNumber, newRecordId, equityAccountContext));
            return true;
        } catch (Exception e) {
            log.error("saveAccountAndUpdateProcessRecordAndUpdateNewRecord.e;serialNumber={};newRecordId={};userId={};e", serialNumber, newRecordId, equityAccountContext.getUid(), e);
        }
        return false;
    }

    private void saveAccountAndUpdateSuccessRecord(TicketAccountRecordDO ticketAccountRecordDO, String serialNumber, Long newRecordId, EquityAccountContext equityAccountContext) {
        //实体转换
        TicketAccountEntity ticketAccountEntity = TicketAccountEntity.builder().build();
        TicketAccountDO ticketAccountDO = ticketAccountEntity.convertToTicketAccountDO(equityAccountContext);
        //权益账户生成,领取记录状态更新
        saveAccountAndUpdateRecordSuccess(ticketAccountDO, equityAccountContext);
        //更新
        boolean isSuccess = ticketAccountRecordService.updateRecordColumn(
                equityAccountContext.getUid(), newRecordId, serialNumber,
                equityAccountContext.getEquityRpcDto().getValue(),
                Constants.ONE, ticketAccountRecordDO.getReqNo(),
                StatusEnum.SUCCESS.getCode(), ExceptionEnum.SUCCESS.getCode(),
                ExceptionEnum.SUCCESS.getMessage(), equityAccountContext.getSendTicketScene());

        if (!isSuccess) {
            throw new ApiBusinessException(ExceptionEnum.DB_OPERATE_ERROR);
        }
    }

    @Override
    protected ReceiveBenefitResDTO receiveSceneBenefitRetry(EquityAccountContext equityAccountContext) {
        String uid = equityAccountContext.getUid();
        Long strategyId = equityAccountContext.getStrategyId();
        Long benefitId = equityAccountContext.getBenefitId();
        String sceneCode = equityAccountContext.getSceneCode();
        String serialNumber = equityAccountContext.getSerialNumber();
        Integer sendTicketScene = equityAccountContext.getSendTicketScene();
        //查询记录
        TicketAccountRecordDO ticketAccountRecordDO = ticketAccountRecordService.selectByUk(uid, serialNumber, strategyId,
                benefitId, OperateTypeEnum.PROVIDE.getCode(), serialNumber);
        if (Objects.isNull(ticketAccountRecordDO)) {
            log.error("TicketAccountOperate.receiveSceneBenefitRetry.retry.record.is.null;uid={};serialNumber={};strategyId={};benefitId={}",
                    uid, serialNumber, strategyId, benefitId);
            catMonitorUtil.logEvent(MonitorConstants.SEND_SCENE_TICKET_RETRY, MonitorConstants.DB_QUERY_ERROR);
            return ReceiveBenefitResDTO.processing(ExceptionEnum.DB_OPERATE_ERROR);
        }
        // 如果入参有新的发券场景，尽量用入参的发券场景
        if (IntegerUtil.isFalse(ticketAccountRecordDO.getSendTicketScene()) && null != sendTicketScene) {
            ticketAccountRecordDO.setSendTicketScene(sendTicketScene);
        }
        //成功
        if (StatusEnum.SUCCESS.getCode().equals(ticketAccountRecordDO.getStatus())) {
            EquityRpcDto equityRpcDto = new EquityRpcDto();
            equityRpcDto.setValue(ticketAccountRecordDO.getUserTicketId());
            return ReceiveBenefitResDTO.success(equityRpcDto, ticketAccountRecordDO.getId());
        }
        //处理中
        if (StatusEnum.PROCESSING.getCode().equals(ticketAccountRecordDO.getStatus()) ||
                StatusEnum.INIT.getCode().equals(ticketAccountRecordDO.getStatus())) {
            //实体转换
            BenefitOperateEntity benefitOperateEntity = BenefitOperateEntity.builder().build();
            EquityAccountContext newEquityAccountContext = benefitOperateEntity.convertToTicketEquityAccountContext(
                    ticketAccountRecordDO, sceneCode);
            //发送权益
            EquityRpcDto equityRpcDto = doProvideEquityRpc(newEquityAccountContext);
            newEquityAccountContext.setEquityRpcDto(equityRpcDto);
            BenefitReceiveRuleContext benefitReceiveRuleContext = super.gainBenefitReceiveRuleContext(newEquityAccountContext);
            //成功
            if (StatusEnum.SUCCESS.equals(equityRpcDto.getStatusEnum())) {
                //数据保存
                int result = doProvideEquity(newEquityAccountContext);
                if (result != 1) {
                    return ReceiveBenefitResDTO.processing(ExceptionEnum.COMMON_ERROR);
                }
                //频次扣减
                receiveAndStockLimitCoreService.deductBenefitStock(newEquityAccountContext, benefitReceiveRuleContext);

                // 老账户领取成功后的动作（发送领取成功 Pulsar 消息）
                oldAccountDoAfterSceneReceiveSuccess(equityAccountContext);
                return ReceiveBenefitResDTO.success(equityRpcDto, newEquityAccountContext.getAccountRecordId());
            }
            //失败
            if (StatusEnum.FAIL.equals(equityRpcDto.getStatusEnum())) {
                updateRecordFail(ticketAccountRecordDO.getId(), newEquityAccountContext, ExceptionEnum.SEND_TICKET_FAIL);
                //释放频次
                receiveAndStockLimitCoreService.releaseBenefitReceiveLimit(newEquityAccountContext, benefitReceiveRuleContext);
                return ReceiveBenefitResDTO.fail(ExceptionEnum.SEND_TICKET_FAIL);
            }
        }
        return ReceiveBenefitResDTO.processing(ExceptionEnum.COMMON_ERROR);
    }

    @Override
    protected void updateRecordFreezeReceiveLimitId(Long recordId, Long freezeReceiveLimitId,
                                                    String userId) {
        if (LongUtils.isFalse(freezeReceiveLimitId)) {
            log.error("更新Record表频次ID出错, freezeReceiveLimitId为空 recordId:{}", recordId);
        }
        ticketAccountRecordService.updateRecordFreezeReceiveLimitId(recordId, freezeReceiveLimitId, userId);
    }

    @Override
    protected void updateRecordProcessing(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        Long accountId = equityAccountContext.getAccountId();
        String code = exceptionEnum.getCode();
        String msg = exceptionEnum.getMessage();
        String userTicketId = Constants.EMPTY_STRING;
        String innerSerialNumber = equityAccountContext.getDoRpcUniqueSerialNumber();
        EquityRpcDto equityRpcDto = equityAccountContext.getEquityRpcDto();
        if (Objects.nonNull(equityRpcDto)) {
            if (StringUtils.isNotBlank(equityRpcDto.getCode())) {
                code = equityRpcDto.getCode();
            }
            if (StringUtils.isNotBlank(equityRpcDto.getMessage())) {
                msg = equityRpcDto.getMessage();
            }
            userTicketId = equityRpcDto.getValue();
        }
        //操作记录更新
        ticketAccountRecordService.updateRecord(
                recordId, equityAccountContext.getUid(),
                StatusEnum.PROCESSING.getCode(), accountId,
                innerSerialNumber, userTicketId,
                equityAccountContext.getSerialNumber(),
                code, msg, equityAccountContext.getFreezeReceiveLimitResultId(),
                equityAccountContext.getSendTicketScene());

    }

    @Override
    protected void updateRecordFail(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        Long accountId = equityAccountContext.getAccountId();
        String code = exceptionEnum.getCode();
        String msg = exceptionEnum.getMessage();
        String userTicketId = Constants.EMPTY_STRING;
        String innerSerialNumber = equityAccountContext.getDoRpcUniqueSerialNumber();
        EquityRpcDto equityRpcDto = equityAccountContext.getEquityRpcDto();
        if (Objects.nonNull(equityRpcDto)) {
            if (StringUtils.isNotBlank(equityRpcDto.getCode())) {
                code = equityRpcDto.getCode();
            }
            if (StringUtils.isNotBlank(equityRpcDto.getMessage())) {
                msg = equityRpcDto.getMessage();
            }
            userTicketId = equityRpcDto.getValue();
        }
        //操作记录更新
        ticketAccountRecordService.updateRecord(
                recordId, equityAccountContext.getUid(),
                StatusEnum.FAIL.getCode(), accountId,
                innerSerialNumber, userTicketId,
                equityAccountContext.getSerialNumber(),
                code, msg,
                equityAccountContext.getFreezeReceiveLimitResultId(),
                equityAccountContext.getSendTicketScene());
    }

    @Override
    public QueryEquityDto equityQuery(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public AccountInfoDTO queryEquityAccountInfo(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public List<AccountInfoDTO> queryEquityAccountInfos(EquityAccountContext equityAccountContext) {
        List<TicketAccountDO> accountDOList = ticketAccountService.queryAccountsByUserId(equityAccountContext.getUid(),
                equityAccountContext.getActivityIds(), equityAccountContext.getStatuses(),
                equityAccountContext.getStartDate(), equityAccountContext.getEndDate());
        return TicketAccountEntity.convertToAccountInfoDTOList(accountDOList);
    }

    @Override
    public PageListRespDTO<AccountInfoDTO> queryEquityAccountInfosByPage(EquityAccountContext equityAccountContext) {
        PageListRespDTO<TicketAccountDO> pageResult = ticketAccountService.queryAccountsByUserId(equityAccountContext.getUid(),
                equityAccountContext.getActivityIds(), equityAccountContext.getStatuses(),
                equityAccountContext.getStartDate(), equityAccountContext.getEndDate(),
                equityAccountContext);

        return Objects.isNull(pageResult) ? PageListRespDTO.<AccountInfoDTO>builder().build() :
                PageListRespDTO.<AccountInfoDTO>builder()
                        .list(TicketAccountEntity.convertToAccountInfoDTOList(pageResult.getList()))
                        .total(pageResult.getTotal())
                        .build();
    }

    @Override
    public Boolean hasSucceededByUniqueKey(String userId, Integer operateType, String reqNo) {
        return null;
    }
}
