package com.ddmc.equity.account;

import com.ddmc.equity.domain.dto.EquityRpcDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class DirectAccountContext {

    /**
     * 请求来源 appId
     */
    private String appId;
    /***
     * 请求来源页面 id
     */
    private String pageId;
    /**
     * 请求来源
     */
    private String source;
    /**
     * 城市 code
     */
    private String cityCode;
    /**
     * 站点 id
     */
    private String stationId;
    /**
     * 业务流水号
     */
    private String reqNo;
    /**
     * 场景 code
     */
    private String sceneCode;
    /**
     * 用户 id
     */
    private String userId;

    /**
     * 操作类型
     */
    private Integer operateType;
    /**
     * 操作数量
     */
    private Integer operateCount;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 活动 id
     */
    private Long activityId;

    /**
     * 活动外部关联 id。
     */
    private String externalId;

    /**
     * 活动外部关联 id类型。
     */
    private Integer externalType;
    /**
     * 策略 id
     */
    private Long strategyId;
    /**
     * 策略外部关联 id。如果是来源玩法的活动，则为活动 prizeId；如果是膨胀券活动，则为母券券模板 id；
     */
    private String strategyExternalId;
    /**
     * 权益组 id
     */
    private Long benefitGroupId;
    /**
     * 权益 id
     */
    private Long benefitId;

    /**
     * 权益 类型
     */
    private Integer benefitType;

    /**
     * 权益值
     */
    private String benefitValue;

    /**
     * 权益名称
     */
    private String benefitName;

    /**
     * 记录在 accountDetail 表中的权益实际发放数量
     * <p>
     * 对于每次发放数量必须为 1 的权益类型（如优惠券等），sendAmount = 1
     * 发放固定数量权益时，sendAmount = benefit.benefitValue
     * 发放不固定数量权益时，sendAmount = reqSendAmount
     * 发放随机数量权益时，sendAmount = benefit.benefitValue 范围内随机的数量
     */
    private String sendAmount;

    /**
     * 余额金额，保留两位小数。如果权益类型为余额（固定余额、随机余额），才有该字段
     */
    private BigDecimal balanceMoney;

    /**
     * 发放权益的过期时间
     */
    private Date expireTime;

    /***
     * 账户 id
     */
    private Long accountId;
    /**
     * 账户详细 id
     */
    private Long accountDetailId;

    /***
     * 调取三方返回的结果内容
     */
    private EquityRpcDto equityRpcDto;

    /**
     * 使用时的活动 id
     */
    private Long useActivityId;

    /**
     * 外部 rpc 调用请求入参拓展信息（包含调用 rpc 发放权益需要的参数）
     * @see com.ddmc.equity.enums.DirectProvideBenefitExtKeys
     */
    private Map<String, Object> extMap;

    /**
     * 内部拓展信息。会在权益领取成功消息体透传
     *
     * @see com.ddmc.equity.enums.DirectProvideBenefitInnerExtKeys
     */
    private Map<String, Object> innerExtMap;
}
