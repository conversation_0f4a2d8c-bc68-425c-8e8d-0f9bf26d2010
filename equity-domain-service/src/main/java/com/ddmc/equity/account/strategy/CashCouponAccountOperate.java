package com.ddmc.equity.account.strategy;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.BenefitReceiveRuleContext;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.entity.account.BalanceAccountConvertEntity;
import com.ddmc.equity.domain.entity.account.CashCouponAccountConvertEntity;
import com.ddmc.equity.domain.service.cash.CashCouponAccountDomainService;
import com.ddmc.equity.domain.service.cash.CashCouponAccountRecordDomainService;
import com.ddmc.equity.domain.service.core.ReceiveAndStockLimitCoreService;
import com.ddmc.equity.infra.repository.dao.CashCouponAccountDO;
import com.ddmc.equity.infra.repository.dao.CashCouponAccountRecordDO;
import com.ddmc.equity.infra.rpc.voucher.VoucherSendTicketProxy;
import com.ddmc.equity.infra.rpc.voucher.dto.SendTicketResDTO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.SubAccountRecordDTO;
import com.ddmc.voucherprod.dto.wechatcash.request.SendWechatCashTicketRequest;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * hulei
 */
@Slf4j
@Component
public class CashCouponAccountOperate extends AbstractEquityAccountOperate {


    @Autowired
    private TransactionUtil transactionUtil;
    @Autowired
    private VoucherSendTicketProxy voucherSendTicketProxy;
    @Autowired
    private CashCouponAccountDomainService accountDomainService;
    @Autowired
    private CashCouponAccountRecordDomainService accountRecordDomainService;

    @Autowired
    private ReceiveAndStockLimitCoreService receiveAndStockLimitCoreService;


    @Override
    public Long initRecord(int operateType, EquityAccountContext equityAccountContext) {
        CashCouponAccountRecordDO accountRecordDO = CashCouponAccountConvertEntity.createAccountRecordDO(operateType, equityAccountContext);
        accountRecordDomainService.insertAccountRecordDO(accountRecordDO);
        equityAccountContext.setAccountRecordId(accountRecordDO.getId());
        return accountRecordDO.getId();
    }

    @Override
    public int doProvideEquity(EquityAccountContext equityAccountContext) {
        Long accountRecordId = equityAccountContext.getAccountRecordId();
        CashCouponAccountDO accountDO = CashCouponAccountConvertEntity.createAccountDO(equityAccountContext);
        transactionUtil.transactional(s -> {
            // 1 生成权益子账户
            accountDomainService.insertAccountDO(accountDO);

            // 2 更新权益子账户操作流水结果
            equityAccountContext.setAccountId(accountDO.getId());
            boolean updateAccountRecordResult = updateRecordSuccessById(accountRecordId, StatusEnum.SUCCESS.getCode(), equityAccountContext);
            if (!updateAccountRecordResult) {
                log.warn("CashCouponAccountOperate doProvideEquity updateRecordSuccessById failure. userId={}, accountRecordId={}",
                        equityAccountContext.getUid(), accountRecordId);
                throw AdminExceptionBuilder.build(ExceptionEnum.ACCOUNT_RECORD_UPDATE_FAIL);
            }
        });
        return Constants.ONE;
    }

    @Override
    public boolean updateRecordSuccessById(Long recordId, int status, EquityAccountContext equityAccountContext) {
        return accountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), status, equityAccountContext.getEquityRpcDto(),
                BalanceAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext));
    }

    @Override
    public boolean updateRecordExceptionById(Long recordId, int status, EquityAccountContext equityAccountContext) {
        return accountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), status, equityAccountContext.getEquityRpcDto(),
                CashCouponAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext));
    }

    @Override
    protected void updateRecordFail(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        accountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), StatusEnum.FAIL.getCode(), EquityRpcDto.fail(exceptionEnum),
                CashCouponAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext));
    }

    @Override
    protected void updateRecordProcessing(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        accountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), StatusEnum.PROCESSING.getCode(), EquityRpcDto.fail(exceptionEnum),
                CashCouponAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext));
    }


    @Override
    public List<AccountInfoDTO> queryEquityAccountInfos(EquityAccountContext equityAccountContext) {
        List<CashCouponAccountDO> accountDOList = accountDomainService.queryAccountsByUserId(equityAccountContext.getUid(),
                equityAccountContext.getActivityIds(), equityAccountContext.getStatuses(),
                equityAccountContext.getStartDate(), equityAccountContext.getEndDate());
        return CashCouponAccountConvertEntity.convertToAccountInfoDTOList(accountDOList);
    }

    @Override
    public SubAccountRecordDTO queryExistAccountRecord(String userId, Integer operateType, String reqNo) {
        List<Integer> statuses = Lists.newArrayList(StatusEnum.INIT.getCode(), StatusEnum.SUCCESS.getCode(), StatusEnum.PROCESSING.getCode());
        CashCouponAccountRecordDO existRecord = accountRecordDomainService.queryAccountRecordByUniqueKeyAndStatuses(userId,
                operateType, reqNo, statuses);
        return Objects.isNull(existRecord) ? null : CashCouponAccountConvertEntity.convertToSubAccountRecordDTO(existRecord);
    }

    //////////////////////////////////////


    @Override
    public EquityRpcDto doProvideEquityRpc(EquityAccountContext equityAccountContext) {
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        SendWechatCashTicketRequest request = new SendWechatCashTicketRequest();
        request.setRequestNo(equityAccountContext.getSerialNumber());
        request.setOpenId(equityAccountContext.getOpenId());
        request.setUserId(equityAccountContext.getUid());
        request.setTicketId(Long.valueOf(equityAccountContext.getEquityValue()));
        SendTicketResDTO sendTicketResDTO = voucherSendTicketProxy.sendWechatCashTicketSync(request);

        equityRpcDto.setCode(sendTicketResDTO.getCode());
        equityRpcDto.setMessage(sendTicketResDTO.getMsg());
        //成功
        if (StatusEnum.SUCCESS.equals(sendTicketResDTO.getStatusEnum())) {
            equityRpcDto.setStatusEnum(StatusEnum.SUCCESS);
            equityRpcDto.setValue(sendTicketResDTO.getUserTicketId());
            return equityRpcDto;
        }
        //失败
        if (StatusEnum.FAIL.equals(sendTicketResDTO.getStatusEnum())) {
            equityRpcDto.setStatusEnum(StatusEnum.FAIL);
            log.error("CashCouponAccountOperate doProvideEquityRpc failure. context={}, request={}, response={}",
                    JSON.toJSONString(equityAccountContext), JSON.toJSONString(request), JSON.toJSONString(sendTicketResDTO));
            return equityRpcDto;
        }
        equityRpcDto.setStatusEnum(StatusEnum.PROCESSING);
        return equityRpcDto;
    }

    @Override
    protected ReceiveBenefitResDTO handlerHistoryFreezeReceiveProcessingRecord(EquityAccountContext equityAccountContext) {
        String uid = equityAccountContext.getUid();
        Long activityId = equityAccountContext.getActivityId();
        Long strategyId = equityAccountContext.getStrategyId();
        Long benefitId = equityAccountContext.getBenefitId();
        Long recordId = equityAccountContext.getAccountRecordId();
        Long freezeReceiveLimitId = equityAccountContext.getFreezeReceiveLimitResultId();
        //冻结id为空
        if (Objects.isNull(freezeReceiveLimitId)) {
            updateRecordFail(recordId, equityAccountContext, ExceptionEnum.FREEZE_RECEIVE_LIMIT_RESULT_ID_IS_NULL);
            return ReceiveBenefitResDTO.fail(ExceptionEnum.BENEFIT_SEND_FAIL);
        }

        // 查询
        List<CashCouponAccountRecordDO> accountRecordDOList = accountRecordDomainService.queryAccountRecordByStrategyIdAndStatus(uid, strategyId, benefitId, OperateTypeEnum.PROVIDE.getCode(), freezeReceiveLimitId, StatusEnum.PROCESSING.getCode());
        if (CollectionUtils.isEmpty(accountRecordDOList)) {
            log.error("CashCouponAccountOperate.handlerHistoryProcessingRecord.exist.error.data;uid={};activityId={};strategyId={};benefitId={}", uid, activityId, strategyId, benefitId);
            updateRecordFail(recordId, equityAccountContext, ExceptionEnum.RECEIVE_SCENE_BENEFIT_PROCESSING_IS_NULL);
            return ReceiveBenefitResDTO.fail(ExceptionEnum.BENEFIT_SEND_FAIL);
        }

        // 查找一条
        CashCouponAccountRecordDO ticketAccountRecordDB = accountRecordDOList.get(0);
        equityAccountContext.setSerialNumber(ticketAccountRecordDB.getReqNo());
        //发送权益
        EquityRpcDto equityRpcDto = doProvideEquityRpc(equityAccountContext);
        equityAccountContext.setEquityRpcDto(equityRpcDto);

        //成功
        if (StatusEnum.SUCCESS.equals(equityRpcDto.getStatusEnum())) {
            CashCouponAccountDO accountDO = CashCouponAccountConvertEntity.createAccountDO(equityAccountContext);
            transactionUtil.transactional(s -> {
                // 1 生成权益子账户
                accountDomainService.insertAccountDO(accountDO);

                // 2 更新权益子账户操作流水结果
                equityAccountContext.setAccountId(accountDO.getId());
                updateRecordSuccessById(recordId, StatusEnum.SUCCESS.getCode(), equityAccountContext);
                //3 老数据处理中的更新为成功
                updateRecordSuccessById(ticketAccountRecordDB.getId(), StatusEnum.SUCCESS.getCode(), equityAccountContext);
            });
            BenefitReceiveRuleContext benefitReceiveRuleContext = super.gainBenefitReceiveRuleContext(equityAccountContext);
            //扣减频次
            receiveAndStockLimitCoreService.deductBenefitReceiveLimit(equityAccountContext, benefitReceiveRuleContext);
            return ReceiveBenefitResDTO.success(equityRpcDto, equityAccountContext.getAccountRecordId());

        }

        updateRecordFail(recordId, equityAccountContext, ExceptionEnum.SEND_TICKET_FAIL);
        return ReceiveBenefitResDTO.fail(ExceptionEnum.SEND_TICKET_FAIL);
    }

    @Override
    protected void updateRecordFreezeReceiveLimitId(Long recordId, Long freezeReceiveLimitId,
                                                    String userId) {
        accountRecordDomainService.updateAccountRecordFreezeId(userId, recordId, freezeReceiveLimitId);
    }


}
