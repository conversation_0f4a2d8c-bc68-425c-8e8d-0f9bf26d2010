package com.ddmc.equity.account.strategy;

import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.entity.account.TotalPriceAccountConvertEntity;
import com.ddmc.equity.domain.service.total_price.TotalPriceAccountDomainService;
import com.ddmc.equity.domain.service.total_price.TotalPriceAccountRecordDomainService;
import com.ddmc.equity.infra.repository.dao.TotalPriceAccountDO;
import com.ddmc.equity.infra.repository.dao.TotalPriceAccountRecordDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.SubAccountRecordDTO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/16 14:02
 * @description
 */
@Slf4j
@Component
public class TotalPriceAccountOperate extends AbstractEquityAccountOperate {

    @Autowired
    private TransactionUtil transactionUtil;
    @Autowired
    private TotalPriceAccountDomainService accountDomainService;
    @Autowired
    private TotalPriceAccountRecordDomainService accountRecordDomainService;

    @Override
    public Long initRecord(int operateType, EquityAccountContext equityAccountContext) {
        TotalPriceAccountRecordDO accountRecordDO = TotalPriceAccountConvertEntity.createAccountRecordDO(operateType, equityAccountContext);
        accountRecordDomainService.insertAccountRecordDO(accountRecordDO);
        return accountRecordDO.getId();
    }

    @Override
    public int doProvideEquity(EquityAccountContext equityAccountContext) {
        Long accountRecordId = equityAccountContext.getAccountRecordId();
        TotalPriceAccountDO accountDO = TotalPriceAccountConvertEntity.createAccountDO(equityAccountContext);
        transactionUtil.transactional(s -> {
            // 1 生成权益子账户
            accountDomainService.insertAccountDO(accountDO);

            // 2 更新权益子账户操作流水结果
            equityAccountContext.setAccountId(accountDO.getId());
            boolean updateAccountRecordResult = updateRecordSuccessById(accountRecordId, StatusEnum.SUCCESS.getCode(), equityAccountContext);
            if (!updateAccountRecordResult) {
                log.warn("totalPrice doProvideEquity updateRecordSuccessById failure. userId={}, accountRecordId={}",
                        equityAccountContext.getUid(), accountRecordId);
                throw AdminExceptionBuilder.build(ExceptionEnum.ACCOUNT_RECORD_UPDATE_FAIL);
            }
        });
        return Constants.ONE;
    }

    @Override
    public boolean updateRecordSuccessById(Long recordId, int status, EquityAccountContext equityAccountContext) {
        return accountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), status, equityAccountContext.getEquityRpcDto(),
                TotalPriceAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext));
    }

    @Override
    public boolean updateRecordExceptionById(Long recordId, int status, EquityAccountContext equityAccountContext) {
        return accountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), status, equityAccountContext.getEquityRpcDto(),
                TotalPriceAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext));
    }

    @Override
    protected void updateRecordFail(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        accountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), StatusEnum.FAIL.getCode(), EquityRpcDto.fail(exceptionEnum),
                TotalPriceAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext));
    }

    @Override
    protected void updateRecordProcessing(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        accountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), StatusEnum.PROCESSING.getCode(), EquityRpcDto.fail(exceptionEnum),
                TotalPriceAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext));
    }

    @Override
    public List<AccountInfoDTO> queryEquityAccountInfos(EquityAccountContext equityAccountContext) {
        List<TotalPriceAccountDO> accountDOList = accountDomainService.queryAccountsByUserId(equityAccountContext.getUid(),
                equityAccountContext.getActivityIds(), equityAccountContext.getStatuses(),
                equityAccountContext.getStartDate(), equityAccountContext.getEndDate());
        return TotalPriceAccountConvertEntity.convertToAccountInfoDTOList(accountDOList);
    }

    @Override
    public SubAccountRecordDTO queryExistAccountRecord(String userId, Integer operateType, String reqNo) {
        List<Integer> statuses = Lists.newArrayList(StatusEnum.INIT.getCode(), StatusEnum.SUCCESS.getCode(), StatusEnum.PROCESSING.getCode());
        TotalPriceAccountRecordDO existRecord = accountRecordDomainService.queryAccountRecordByUniqueKeyAndStatuses(userId,
                operateType, reqNo, statuses);
        return Objects.isNull(existRecord) ? null : TotalPriceAccountConvertEntity.convertToSubAccountRecordDTO(existRecord);
    }
}
