package com.ddmc.equity.account;

import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.QueryEquityDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EquityAccountOperate {

    /**
     * 发放权益
     *
     * @param equityAccountContext 上下文
     * @return 是否发放成功
     */
    boolean provideEquity(EquityAccountContext equityAccountContext);

    /**
     * 发放权益，并获取发放成功的权益子账户信息列表（要么全部成功、要么全部失败）
     *
     * @param equityAccountContext 上下文
     * @return 发放成功的权益子账户信息列表
     */
    List<AccountInfoDTO> provideEquityAndGetResult(EquityAccountContext equityAccountContext);

    /**
     * 权益回退
     *
     * @param equityAccountContext 上下文
     * @return 是否回退成功
     */
    boolean fallbackEquity(EquityAccountContext equityAccountContext);

    /**
     * 权益使用
     *
     * @param equityAccountContext 上下文
     * @return 是否使用成功
     */
    boolean useEquity(EquityAccountContext equityAccountContext);

    /**
     * 权益查询。返回用户是否有对应权益、可使用权益数量、已使用权益数量（通过 userId 查询，优先从 redis 缓存中查询）
     *
     * @param equityAccountContext 上下文
     * @return 查询结果
     */
    QueryEquityDto equityQuery(EquityAccountContext equityAccountContext);

    /**
     * 指定领取场景领取权益
     *
     * @param equityAccountContext 上下文
     * @return 领取结果
     */
    ReceiveBenefitResDTO receiveSceneBenefit(EquityAccountContext equityAccountContext);

    /**
     * 通过权益类型获取对应权益 id
     *
     * @param benefitType 权益类型
     * @return 权益 id
     */
    Long queryEquityBenefitId(Integer benefitType);

    /**
     * 通过权益类型获取对应权益信息
     *
     * @param benefitType 权益类型
     * @return 权益信息
     */
    EquityBenefitDO queryEquityBenefitDO(Integer benefitType);

    /**
     * 查询权益。通过 userId 查询权益子账户信息，目前只有首单免运费权益在使用（一个用户只发放一次）
     *
     * @param equityAccountContext 上下文
     * @return 权益子账户信息
     */
    AccountInfoDTO queryEquityAccountInfo(EquityAccountContext equityAccountContext);

    /**
     * 查询权益。返回权益子账户信息列表（通过 userId、权益组 id、权益 id、状态、创建时间、抽签码来源）
     *
     * @param equityAccountContext 上下文
     * @return 权益子账户信息列表
     */
    List<AccountInfoDTO> queryEquityAccountInfos(EquityAccountContext equityAccountContext);

    /**
     * 分页查询权益。目前只支持余额，优惠券
     *
     * @param equityAccountContext 上下文
     * @return 权益子账户信息列表
     */
    PageListRespDTO<AccountInfoDTO> queryEquityAccountInfosByPage(EquityAccountContext equityAccountContext);

    /**
     * 判断对应业务流水号是否已操作成功
     *
     * @param userId      用户 id
     * @param operateType 操作类型
     * @param reqNo       业务流水号
     * @return 已操作成功返回 true
     */
    Boolean hasSucceededByUniqueKey(String userId, Integer operateType, String reqNo);

    /**
     * 指定场景领取（新）
     *
     * @param equityAccountContext 上下文
     * @return 领取结果
     */
    ReceiveBenefitResDTO sceneReceiveNew(EquityAccountContext equityAccountContext);
}
