package com.ddmc.equity.account;

import com.alibaba.fastjson.JSON;
import com.csoss.monitor.api.trace.Span;
import com.csoss.monitor.api.trace.Traces;
import com.csoss.monitor.sdk.resource.Status;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.ddmc.equity.common.constant.CacheKeyConstants;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.interceptor.annotation.MonitorSpan;
import com.ddmc.equity.common.util.CatMonitorUtil;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.JsonUtil;
import com.ddmc.equity.common.util.LongUtils;
import com.ddmc.equity.common.util.MapUtils;
import com.ddmc.equity.common.util.SpringContextUtil;
import com.ddmc.equity.common.util.ThreadsUtils;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.domain.dto.EquityBenefitSendMsgDTO;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.FallbackEquityDto;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcResponseExtDTO;
import com.ddmc.equity.domain.entity.account.AccountConvertEntity;
import com.ddmc.equity.domain.entity.account.EquityAccountConvertEntity;
import com.ddmc.equity.domain.entity.account.UniversalAccountConvertEntity;
import com.ddmc.equity.domain.entity.rule.RuleConvertEntity;
import com.ddmc.equity.domain.service.benefit.EquityBenefitDomainService;
import com.ddmc.equity.domain.service.core.ReceiveAndStockLimitCoreService;
import com.ddmc.equity.domain.service.equityAccount.EquityAccountService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountDetailDomainService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountDomainService;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountRecordDomainService;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.infra.cache.redis.RedisCache;
import com.ddmc.equity.infra.repository.dao.EquityAccountDO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountDetailDO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.ActivityRuleCacheDTO;
import com.ddmc.equity.model.dto.BenefitRuleCacheDTO;
import com.ddmc.equity.model.dto.QueryEquityDto;
import com.ddmc.equity.model.dto.StrategyRuleCacheDTO;
import com.ddmc.equity.model.dto.SubAccountRecordDTO;
import com.ddmc.equity.model.vo.BenefitReceiveSuccessMsgVO;
import com.ddmc.equity.mq.pulsar.producer.BenefitReceiveSuccessMsgProducer;
import com.ddmc.equity.mq.rocket.producer.CouponExpandReceiveProducer;
import com.ddmc.utils.constant.CommonConstants;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractEquityAccountOperate implements EquityAccountOperate {

    @ApolloJsonValue("${equity.retry.map:{}}")
    private Map<String, List<String>> sceneMethod;

    @Resource
    private RedisCache redisCache;
    @Autowired
    private CatMonitorUtil catMonitorUtil;
    @Autowired
    private EquityAccountService equityAccountService;
    @Autowired
    private EquityBenefitDomainService equityBenefitDomainService;
    @Autowired
    private LocalCacheManager localCacheManager;
    @Autowired
    private ReceiveAndStockLimitCoreService receiveAndStockLimitCoreService;
    @Autowired
    private List<SceneRiskService> sceneRiskOperates;
    @Autowired
    private TransactionUtil transactionUtil;
    @Autowired
    private UniversalAccountDomainService universalAccountDomainService;
    @Autowired
    private UniversalAccountDetailDomainService universalAccountDetailDomainService;
    @Autowired
    private UniversalAccountRecordDomainService universalAccountRecordDomainService;
    @Autowired
    private BenefitReceiveSuccessMsgProducer benefitReceiveSuccessMsgProducer;

    private static final Map<String, SceneRiskService> SCENE_RISK_OPERATE_MAP = Maps.newConcurrentMap();

    @PostConstruct
    public void init() {
        if (CollectionUtils.isNotEmpty(sceneRiskOperates)) {
            for (SceneRiskService riskOperate : sceneRiskOperates) {
                SCENE_RISK_OPERATE_MAP.put(riskOperate.getSceneCode(), riskOperate);
            }
        }
    }

    @Override
    public boolean provideEquity(EquityAccountContext equityAccountContext) {
        Long recordId = null;
        try {
            // 1 生成权益子账户操作流水
            recordId = initRecord(OperateTypeEnum.PROVIDE.getCode(), equityAccountContext);
            equityAccountContext.setAccountRecordId(recordId);

            // 2 异步生成权益总账户（一个用户 + 一种权益类型，对应只有一个总账户）
            createMasterAccountAsync(equityAccountContext);

            // 3 远程调用发送权益（如果是首单免运费权益、抽签码权益，无需调用第三方发送权益）
            EquityRpcDto equityRpcDto = doProvideEquityRpc(equityAccountContext);
            equityAccountContext.setEquityRpcDto(equityRpcDto);

            // 4 生成权益子账户，并更新权益子账户操作流水结果
            return doProvideEquity(equityAccountContext) != Constants.ZERO;
        } catch (DuplicateKeyException e) {
            log.warn("equityAccountOperate provideEquity duplicateKeyExp. context={}", JSON.toJSONString(equityAccountContext), e);
            catMonitorUtil.logEventWithSpan(MonitorConstants.OPERATE_BENEFIT_PROVIDE, MonitorConstants.DUPLICATE_EXCEPTION,
                    CommonConstants.IS_SUCCESS_CODE, JSON.toJSONString(equityAccountContext));
            List<Integer> statusList = Lists.newArrayList(StatusEnum.SUCCESS.getCode(), StatusEnum.INIT.getCode(), StatusEnum.PROCESSING.getCode());
            return havingBusinessData(equityAccountContext, OperateTypeEnum.PROVIDE.getCode(), statusList);
        } catch (Exception e) {
            log.error("equityAccountOperate provideEquity e. context={}", JSON.toJSONString(equityAccountContext), e);
            // 更新状态为失败时需要设置 auxKey = recordId
            catMonitorUtil.logEventWithSpan(MonitorConstants.OPERATE_BENEFIT_PROVIDE, MonitorConstants.EXCEPTION,
                    CommonConstants.IS_SUCCESS_CODE, JSON.toJSONString(equityAccountContext));
            updateRecordExceptionById(recordId, getExceptionStatus(equityAccountContext, "provideEquity"), equityAccountContext);
            return false;
        }
    }

    @Override
    public List<AccountInfoDTO> provideEquityAndGetResult(EquityAccountContext equityAccountContext) {
        Long recordId = null;
        try {
            // 1 生成权益子账户操作流水
            recordId = initRecord(OperateTypeEnum.PROVIDE.getCode(), equityAccountContext);
            equityAccountContext.setAccountRecordId(recordId);

            // 2 异步生成权益总账户（一个用户 + 一种权益类型，对应只有一个总账户）
            createMasterAccountAsync(equityAccountContext);

            // 3 远程调用发送权益（如果是首单免运费权益、抽签码权益，无需调用第三方发送权益）
            EquityRpcDto equityRpcDto = doProvideEquityRpc(equityAccountContext);
            equityAccountContext.setEquityRpcDto(equityRpcDto);

            // 4 生成权益子账户，并更新权益子账户操作流水结果
            return doProvideEquityAndGetResult(equityAccountContext);
        } catch (DuplicateKeyException e) {
            log.warn("equityAccountOperate provideEquityAndGetResult duplicateKeyExp. context={}", JSON.toJSONString(equityAccountContext), e);
            CsossUtils.logEvent(MonitorConstants.OPERATE_BENEFIT_PROVIDE_AND_GET_RESULT, MonitorConstants.DUPLICATE_KEY_EXCEPTION,
                    CommonConstants.IS_SUCCESS_CODE, JSON.toJSONString(equityAccountContext));
            return retryDoProvideEquityAndGetResult(equityAccountContext);
        } catch (Exception e) {
            log.error("equityAccountOperate provideEquityAndGetResult e. context={}", JSON.toJSONString(equityAccountContext), e);
            CsossUtils.logEvent(MonitorConstants.OPERATE_BENEFIT_PROVIDE_AND_GET_RESULT, MonitorConstants.EXCEPTION,
                    CommonConstants.IS_SUCCESS_CODE, JSON.toJSONString(equityAccountContext));
            updateRecordExceptionById(recordId, getExceptionStatus(equityAccountContext, "provideEquity"), equityAccountContext);
            return null;
        }
    }

    /**
     * 异步创建权益总账户（一个用户 + 一种权益类型，对应只有一个总账户）
     */
    private void createMasterAccountAsync(EquityAccountContext equityAccountContext) {
        ThreadsUtils.runAsync(() -> createMasterAccount(equityAccountContext), ThreadsUtils.getOrdinaryThreadPoll(), Constants.SAVE_USER_ACCOUNT);
    }

    private void createMasterAccount(EquityAccountContext equityAccountContext) {
        // 1 从 Redis 缓存中判断权益总账户是否存在，如果存在则无需重复创建
        if (havingAccount(equityAccountContext)) {
            return;
        }
        // 2 从 DB 中判断权益总账户是否存在，如果存在则无需重复创建
        EquityAccountDO existEquityAccountDO = equityAccountService.queryEquityAccountDOByUserIdAndAccountType(equityAccountContext.getUid(),
                equityAccountContext.getAccountType());
        if (Objects.nonNull(existEquityAccountDO)) {
            return;
        }
        // 3 创建权益总账户
        try {
            equityAccountService.createEquityAccountByDO(EquityAccountConvertEntity.createEquityAccountDO(equityAccountContext));
            setAccountTypeToRedis(equityAccountContext);
        } catch (Exception e) {
            log.error("equityAccountOperate createEquityAccountDO e. context={}", JSON.toJSONString(equityAccountContext), e);
        }
    }

    private void setAccountTypeToRedis(EquityAccountContext equityAccountContext) {
        String redisKey = String.format(CacheKeyConstants.USER_ACCOUNT_KEY, equityAccountContext.getUid());
        Map<String, String> accountMap = redisCache.hGetValueAll(redisKey);
        if (MapUtils.isEmpty(accountMap)) {
            accountMap = new HashMap<>();
        }
        accountMap.put(equityAccountContext.getAccountType() + "", "1");
        redisCache.hSetAll(redisKey, accountMap, 30, TimeUnit.DAYS);
    }

    private boolean havingAccount(EquityAccountContext equityAccountContext) {
        String redisKey = String.format(CacheKeyConstants.USER_ACCOUNT_KEY, equityAccountContext.getUid());
        Map<String, String> accountMap = redisCache.hGetValueAll(redisKey);
        if (MapUtils.isEmpty(accountMap)) {
            return false;
        }
        // 原来账户 redis 未设置过期时间，所以需要重新设置上过期时间。后续这段代码可以删除
        resetAccountRedisExpireTime(redisKey);
        String value = accountMap.get(String.valueOf(equityAccountContext.getAccountType()));
        return Objects.nonNull(value);
    }

    private void resetAccountRedisExpireTime(String redisKey) {
        try {
            Long remainTimeToLive = redisCache.getExpire(redisKey);
            if (Objects.nonNull(remainTimeToLive) && remainTimeToLive == -1) {
                CsossUtils.logEvent(MonitorConstants.RESET_ACCOUNT_REDIS_EXPIRE_TIME, "has_no_expire_time");
                redisCache.expire(redisKey, 30L, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            log.error("resetAccountRedisExpireTime exception. redisKey={}, e=", redisKey, e);
            CsossUtils.logEvent(MonitorConstants.RESET_ACCOUNT_REDIS_EXPIRE_TIME, "exception");
        }
    }

    @Override
    public boolean fallbackEquity(EquityAccountContext equityAccountContext) {
        Long id = null;
        try {
            //查询使用记录
            FallbackEquityDto fallbackEquityDto = getFallbackEquityId(equityAccountContext);
            equityAccountContext.setAccountId(fallbackEquityDto.getAccountId());
            //判断是否可回退
            if (checkFallbackEquity(equityAccountContext) && fallbackEquityDto.isAllowFallback()) {
                //初始化记录
                id = initRecord(OperateTypeEnum.FALLBACK.getCode(), equityAccountContext);
                equityAccountContext.setTicketId(fallbackEquityDto.getFallbackDataId());
                equityAccountContext.setAccountRecordId(id);
                //远程调用回退
                EquityRpcDto equityRpcDto = doFallbackEquityRpc(equityAccountContext);
                equityAccountContext.setEquityRpcDto(equityRpcDto);
                //库状态更新
                return doFallbackEquity(equityAccountContext) != 0;
            }
            log.info("AbstractEquityAccountOperate.fallbackEquity.fallback.fail;req={}", JsonUtil.toJsonString(equityAccountContext));
            catMonitorUtil.logEventWithSpan(MonitorConstants.OPERATE_BENEFIT_FALLBACK, MonitorConstants.FAIL, CommonConstants.IS_SUCCESS_CODE, JsonUtil.toJsonString(equityAccountContext));
        } catch (DuplicateKeyException ex) {
            log.error("AbstractEquityAccountOperate.fallbackEquity.DuplicateKeyException;req={}", JsonUtil.toJsonString(equityAccountContext), ex);
            catMonitorUtil.logEventWithSpan(MonitorConstants.OPERATE_BENEFIT_FALLBACK, MonitorConstants.DUPLICATE_EXCEPTION, CommonConstants.IS_SUCCESS_CODE, JsonUtil.toJsonString(equityAccountContext));
            List<Integer> statusList = Arrays.asList(StatusEnum.SUCCESS.getCode(), StatusEnum.INIT.getCode(), StatusEnum.PROCESSING.getCode());
            return havingBusinessData(equityAccountContext, OperateTypeEnum.FALLBACK.getCode(), statusList);
        } catch (Exception ex) {
            log.error("AbstractEquityAccountOperate.fallbackEquity.e;req={}", JsonUtil.toJsonString(equityAccountContext), ex);
            catMonitorUtil.logEventWithSpan(MonitorConstants.OPERATE_BENEFIT_FALLBACK, MonitorConstants.EXCEPTION, CommonConstants.IS_SUCCESS_CODE, JsonUtil.toJsonString(equityAccountContext));
            updateRecordExceptionById(id, getExceptionStatus(equityAccountContext, "fallbackEquity"), equityAccountContext);
            return false;
        }
        return false;
    }

    @Override
    public boolean useEquity(EquityAccountContext equityAccountContext) {
        Long recordId = null;
        boolean isLocked = false;
        String lockKey = String.format(CacheKeyConstants.USER_ACCOUNT_USE_LOCK, equityAccountContext.getUid(), equityAccountContext.getAccountType());
        try {
            isLocked = redisCache.lock(lockKey, 10, TimeUnit.SECONDS);
            if (!isLocked) {
                log.warn("equityAccountOperate useEquity lock failure. context={}", JSON.toJSONString(equityAccountContext));
                return false;
            }

            // 1 生成权益子账户操作流水
            recordId = initRecord(OperateTypeEnum.USE.getCode(), equityAccountContext);
            equityAccountContext.setAccountRecordId(recordId);

            // 2 检查权益是否已使用
            if (!checkUseEquity(equityAccountContext)) {
                // 已使用过，又拿新流水号请求
                log.warn("equityAccountOperate useEquity noCanUseAccount. context={}", JSON.toJSONString(equityAccountContext));
                updateRecordFail(recordId, equityAccountContext, ExceptionEnum.USER_HAS_NO_AUTH_USE_BENEFIT);
                catMonitorUtil.logEvent(MonitorConstants.OPERATE_BENEFIT_USE, MonitorConstants.FAIL);
                return false;
            }

            // 3 调用三方使用（核销）权益
            EquityRpcDto equityRpcDto = doUseEquityRpc(equityAccountContext);
            equityAccountContext.setEquityRpcDto(equityRpcDto);

            // 4 使用（核销）权益。修改权益子账户状态，修改对应权益子账户操作流水结果
            try {
                return doUseEquity(equityAccountContext) != Constants.ZERO;
            } catch (Exception e) {
                // 中断线程
                log.error("equityAccountOperate useEquity doUseEquity e. context={}", JSON.toJSONString(equityAccountContext), e);
                Thread.currentThread().interrupt();
                throw AdminExceptionBuilder.build(ExceptionEnum.COMMON_ERROR);
            }
        } catch (DuplicateKeyException e) {
            log.error("equityAccountOperate useEquity duplicateKeyException. context={}", JSON.toJSONString(equityAccountContext), e);
            catMonitorUtil.logEventWithSpan(MonitorConstants.OPERATE_BENEFIT_USE, MonitorConstants.DUPLICATE_EXCEPTION,
                    CommonConstants.IS_SUCCESS_CODE, JSON.toJSONString(equityAccountContext));
            List<Integer> statusList = Lists.newArrayList(StatusEnum.SUCCESS.getCode(), StatusEnum.INIT.getCode(), StatusEnum.PROCESSING.getCode());
            return havingBusinessData(equityAccountContext, OperateTypeEnum.USE.getCode(), statusList);
        } catch (Exception e) {
            log.error("equityAccountOperate useEquity e. context={}", JSON.toJSONString(equityAccountContext), e);
            catMonitorUtil.logEventWithSpan(MonitorConstants.OPERATE_BENEFIT_USE, MonitorConstants.EXCEPTION,
                    CommonConstants.IS_SUCCESS_CODE, JSON.toJSONString(equityAccountContext));
            updateRecordExceptionById(recordId, getExceptionStatus(equityAccountContext, "useEquity"), equityAccountContext);
            return false;
        } finally {
            redisCache.unlock(lockKey, isLocked);
        }
    }

    /***
     * 获取执行异常时，更新对应子账户操作流水状态
     *
     * @param equityAccountContext 上下文
     * @param methodName 执行操作方法
     * @return 对应子账户操作流水状态
     */
    public int getExceptionStatus(EquityAccountContext equityAccountContext, String methodName) {
        String sceneCode = equityAccountContext.getSceneCode();
        List<String> methodList = sceneMethod.get(sceneCode);
        if (CollectionUtils.isEmpty(methodList)) {
            return StatusEnum.FAIL.getCode();
        }
        if (methodList.indexOf(methodName) > 0) {
            return StatusEnum.PROCESSING.getCode();
        }
        return StatusEnum.FAIL.getCode();
    }

    /**
     * 插入一条权益子账户操作流水
     */
    public Long initRecord(int operateType, EquityAccountContext equityAccountContext) {
        return null;
    }

    /**
     * 校验权益子账户的用户权益是否可以使用（核销）。如果是抽签码，校验不通过直接抛异常
     * <p>
     * 1 判断用户权益是否存在，数量是否和传入一致
     * 2 判断权益状态是否都为未使用
     */
    public boolean checkUseEquity(EquityAccountContext equityAccountContext) {
        return false;
    }

    /**
     * 使用权益
     */
    public int doUseEquity(EquityAccountContext equityAccountContext) {
        return 0;
    }

    /**
     * 判断权益是否可以回退
     */
    public boolean checkFallbackEquity(EquityAccountContext equityAccountContext) {
        return false;
    }

    /***
     * 不同权益子账户发放权益
     *
     * @param equityAccountContext 上下文
     * @return 发放结果
     */
    public int doProvideEquity(EquityAccountContext equityAccountContext) {
        return 0;
    }

    /**
     * 发放权益，并返回发放成功的结果
     *
     * @param equityAccountContext 上下文
     * @return 发放成功的权益子账户信息列表
     */
    public List<AccountInfoDTO> doProvideEquityAndGetResult(EquityAccountContext equityAccountContext) {
        return null;
    }

    /**
     * 重试发放权益，并返回发放成功的结果
     *
     * @param equityAccountContext 上下文
     * @return 发放成功的权益子账户信息列表
     */
    public List<AccountInfoDTO> retryDoProvideEquityAndGetResult(EquityAccountContext equityAccountContext) {
        return null;
    }

    /***
     * 调用三方回退权益
     *
     * @param equityAccountContext 上下文
     * @return 调用三方结果
     */
    public EquityRpcDto doFallbackEquityRpc(EquityAccountContext equityAccountContext) {
        return null;
    }

    /***
     * 调用三方使用（核销）权益
     *
     * @param equityAccountContext 上下文
     * @return 调用三方结果
     */
    public EquityRpcDto doUseEquityRpc(EquityAccountContext equityAccountContext) {
        return null;
    }

    /***
     * 调用第三方发放权益
     * @param equityAccountContext 上下文
     * @return 调用三方结果
     */
    public EquityRpcDto doProvideEquityRpc(EquityAccountContext equityAccountContext) {
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        equityRpcDto.setStatusEnum(StatusEnum.SUCCESS);
        equityRpcDto.setCode(ExceptionEnum.SUCCESS.getCode());
        equityRpcDto.setMessage(ExceptionEnum.SUCCESS.getMessage());
        return equityRpcDto;
    }

    /**
     * 通过 recordId 更新权益子账户操作流水为成功
     */
    public boolean updateRecordSuccessById(Long recordId, int status, EquityAccountContext equityAccountContext) {
        return false;
    }

    /**
     * 通过 recordId 更新权益子账户操作流水为异常
     */
    public boolean updateRecordExceptionById(Long recordId, int status, EquityAccountContext equityAccountContext) {
        return false;
    }

    /**
     * 子账户操作流水更新为失败
     *
     * @param recordId             记录 id
     * @param equityAccountContext 请求上下文
     * @param exceptionEnum        异常信息
     */
    protected void updateRecordFail(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {

    }

    /**
     * 子账户操作流水更新为处理中
     *
     * @param recordId             记录 id
     * @param equityAccountContext 请求上下文
     * @param exceptionEnum        异常信息
     */
    protected void updateRecordProcessing(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {

    }

    /**
     * 回退权益
     */
    public int doFallbackEquity(EquityAccountContext equityAccountContext) {
        return 0;
    }

    /**
     * 查询本次需要回退的权益
     */
    public FallbackEquityDto getFallbackEquityId(EquityAccountContext equityAccountContext) {
        return null;
    }

    /**
     * 发放、使用、回退权益重复操作时处理（出现数据库唯一索引冲突）
     */
    public boolean havingBusinessData(EquityAccountContext equityAccountContext, Integer operateType, List<Integer> statusList) {
        return false;
    }

    @Override
    public ReceiveBenefitResDTO receiveSceneBenefit(EquityAccountContext equityAccountContext) {
        String userId = equityAccountContext.getUid();
        Long strategyId = equityAccountContext.getStrategyId();
        Long benefitId = equityAccountContext.getBenefitId();
        boolean isLocked = false;
        String lockKey = String.format(CacheKeyConstants.RECEIVE_SCENE_STRATEGY_BENEFIT_LOCK, userId, strategyId, benefitId);
        Long recordId;
        try {
            // 加锁
            isLocked = redisCache.lock(lockKey, 10, TimeUnit.SECONDS);
            if (!isLocked) {
                log.warn("equityAccountOperate receiveSceneBenefit lock failure. context={}", JSON.toJSONString(equityAccountContext));
                return ReceiveBenefitResDTO.fail(ExceptionEnum.TOO_FREQUENTING);
            }

            // 1 填充权益领取需要的频次限制、库存限制规则

            Span gainStrategyInfoSpan = Traces.spanBuilder("gainBenefitReceiveRuleContext").startSpan();
            BenefitReceiveRuleContext benefitReceiveRuleContext;
            try {
                benefitReceiveRuleContext = gainBenefitReceiveRuleContext(equityAccountContext);
                if (Objects.isNull(benefitReceiveRuleContext)) {
                    gainStrategyInfoSpan.addEvent("gainBenefitReceiveRuleContextFailure");
                    log.info("equityAccountOperate receiveSceneBenefit gainBenefitReceiveRuleContext failure. context={}",
                            JSON.toJSONString(equityAccountContext));
                    return ReceiveBenefitResDTO.fail(ExceptionEnum.GAIN_BENEFIT_RECEIVE_RULE_CONTEXT_FAILURE);
                }
            } finally {
                gainStrategyInfoSpan.end();
            }

            // 2 生成记录
            Span initRecordSpan = Traces.spanBuilder("initRecord").startSpan();
            try {
                recordId = initRecord(OperateTypeEnum.PROVIDE.getCode(), equityAccountContext);
                equityAccountContext.setAccountRecordId(recordId);
            } finally {
                initRecordSpan.end();
            }

            // 3 领取频次限制、领取库存限制
            // 3.1 冻结频次，权益维度
            Span freezeUserBenefitLimitSpan = Traces.spanBuilder("freezeBenefitReceiveLimit").startSpan();
            try {
                if (!receiveAndStockLimitCoreService.freezeBenefitReceiveLimit(equityAccountContext, benefitReceiveRuleContext)) {
                    log.warn("equityAccountOperate receiveSceneBenefit freezeBenefitReceiveLimit failure. context={}",
                            JSON.toJSONString(equityAccountContext));
                    freezeUserBenefitLimitSpan.addEvent("freezeBenefitReceiveLimitFail");
                    // 存在处理中的数据
                    if (equityAccountContext.isHasFreezeReceiveLimitProcessingRecord()) {
                        freezeUserBenefitLimitSpan.addEvent("HandleFreezeProcessRecord");
                        return handlerHistoryFreezeReceiveProcessingRecord(equityAccountContext);
                    }
                    // 更新失败
                    updateRecordFail(recordId, equityAccountContext, ExceptionEnum.RECEIVE_OVER_LIMIT);
                    freezeUserBenefitLimitSpan.addEvent("UpdateRecordFail");
                    return ReceiveBenefitResDTO.fail(ExceptionEnum.RECEIVE_OVER_LIMIT);
                } else {
                    // 如果频次 id 不为空，将其更新到 record 表上
                    if (LongUtils.isTrue(equityAccountContext.getFreezeReceiveLimitResultId())) {
                        updateRecordFreezeReceiveLimitId(recordId, equityAccountContext.getFreezeReceiveLimitResultId(), userId);
                    }
                }
            } finally {
                freezeUserBenefitLimitSpan.end();
            }
            // 3.2 扣减频次，活动维度
            Span deductActivityReceiveLimitSpan = Traces.spanBuilder("deductActivityReceiveLimit").startSpan();
            try {
                if (!receiveAndStockLimitCoreService.deductActivityReceiveLimit(equityAccountContext, benefitReceiveRuleContext)) {
                    log.warn("equityAccountOperate receiveSceneBenefit deductActivityReceiveLimit failure. context={}",
                            JSON.toJSONString(equityAccountContext));
                    deductActivityReceiveLimitSpan.addEvent("deductActivityReceiveLimitFail");
                    // 更新失败
                    updateRecordFail(recordId, equityAccountContext, ExceptionEnum.RECEIVE_OVER_LIMIT);
                    // 回滚频次
                    receiveAndStockLimitCoreService.releaseBenefitReceiveLimit(equityAccountContext, benefitReceiveRuleContext);
                    return ReceiveBenefitResDTO.fail(ExceptionEnum.RECEIVE_OVER_LIMIT);
                }
            } finally {
                deductActivityReceiveLimitSpan.end();
            }
            // 3.3  扣减库存，权益维度
            Span deductBenefitStockSpan = Traces.spanBuilder("deductBenefitStock").startSpan();
            try {
                if (!receiveAndStockLimitCoreService.deductBenefitStock(equityAccountContext, benefitReceiveRuleContext)) {
                    log.warn("equityAccountOperate receiveSceneBenefit deductBenefitStock failure. context={}",
                            JSON.toJSONString(equityAccountContext));
                    deductBenefitStockSpan.addEvent("deductBenefitStockFail");
                    // 更新失败
                    updateRecordFail(recordId, equityAccountContext, ExceptionEnum.STOCK_OVER_LIMIT);
                    // 回滚频次
                    receiveAndStockLimitCoreService.releaseBenefitReceiveLimit(equityAccountContext, benefitReceiveRuleContext);
                    // 释放频次，活动维度
                    receiveAndStockLimitCoreService.releaseActivityReceiveLimit(equityAccountContext, benefitReceiveRuleContext);
                    return ReceiveBenefitResDTO.fail(ExceptionEnum.STOCK_OVER_LIMIT);
                }
            } finally {
                deductBenefitStockSpan.end();
            }

            log.info("equityAccountOperate receiveSceneBenefit start. context={}", JSON.toJSONString(equityAccountContext));

            // 调用风控
            EquityRpcDto equityRiskRpcDto = doRiskBeforeProvideEquityRpc(equityAccountContext);
            if (StatusEnum.FAIL.equals(equityRiskRpcDto.getStatusEnum())) {
                equityAccountContext.setEquityRpcDto(equityRiskRpcDto);
                return receiveRpcFailOrProcessingHandle(equityAccountContext, benefitReceiveRuleContext);
            }
            // 4 远程调用领取权益
            EquityRpcDto equityRpcDto = doProvideEquityRpc(equityAccountContext);
            equityAccountContext.setEquityRpcDto(equityRpcDto);
            // 远程调用不成功处理
            if (Objects.isNull(equityRpcDto) || !StatusEnum.SUCCESS.equals(equityRpcDto.getStatusEnum())) {
                return receiveRpcFailOrProcessingHandle(equityAccountContext, benefitReceiveRuleContext);
            }

            // 5 保存权益
            Span doProvideEquitySpan = Traces.spanBuilder("DoProvideEquity").startSpan();
            try {
                int doProvideEquityResult = doProvideEquity(equityAccountContext);
                if (doProvideEquityResult != Constants.ONE) {
                    doProvideEquitySpan.addEvent("DoProvideEquityFail");
                    log.warn("equityAccountOperate receiveSceneBenefit doProvideEquity failure. context={}",
                            JSON.toJSONString(equityAccountContext));
                    catMonitorUtil.logEventWithSpan(MonitorConstants.RECEIVE_SCENE_BENEFIT, MonitorConstants.DB_SAVE_ERROR,
                            CommonConstants.IS_SUCCESS_CODE, JSON.toJSONString(equityAccountContext));
                    updateRecordProcessing(recordId, equityAccountContext, ExceptionEnum.DB_OPERATE_ERROR);
                    return ReceiveBenefitResDTO.processing(ExceptionEnum.HANDLE_TIMEOUT);
                }
            } finally {
                doProvideEquitySpan.end();
            }

            // 6 扣减频次，权益维度
            Span deductReceiveLimitSpan = Traces.spanBuilder("deductBenefitReceiveLimit").startSpan();
            try {
                receiveAndStockLimitCoreService.deductBenefitReceiveLimit(equityAccountContext, benefitReceiveRuleContext);
            } finally {
                deductReceiveLimitSpan.end();
            }

            // 7 生成权益大账户
            createMasterAccountAsync(equityAccountContext);

            // 8 老账户领取成功后的动作（发送领取成功 Pulsar 消息）
            AbstractEquityAccountOperate self = (AbstractEquityAccountOperate) AopContext.currentProxy();
            self.oldAccountDoAfterSceneReceiveSuccess(equityAccountContext);
            return ReceiveBenefitResDTO.success(equityRpcDto, recordId);
        } catch (DuplicateKeyException e) {
            log.error("equityAccountOperate receiveSceneBenefit duplicateKeyException. context={}", JSON.toJSONString(equityAccountContext), e);
            catMonitorUtil.logEventWithSpan(MonitorConstants.RECEIVE_SCENE_BENEFIT, MonitorConstants.DUPLICATE_EXCEPTION,
                    CommonConstants.IS_SUCCESS_CODE, JSON.toJSONString(equityAccountContext));
            return receiveSceneBenefitRetry(equityAccountContext);
        } catch (Exception e) {
            log.error("equityAccountOperate receiveSceneBenefit exception. context={}", JSON.toJSONString(equityAccountContext), e);
            catMonitorUtil.logEventWithSpan(MonitorConstants.RECEIVE_SCENE_BENEFIT, MonitorConstants.EXCEPTION,
                    CommonConstants.IS_SUCCESS_CODE, JSON.toJSONString(equityAccountContext));
            return ReceiveBenefitResDTO.fail(ExceptionEnum.BENEFIT_SEND_FAIL);
        } finally {
            redisCache.unlock(lockKey, isLocked);
            log.info("equityAccountOperate receiveSceneBenefit end. context={}", JSON.toJSONString(equityAccountContext));
        }
    }

    /**
     * 处理历史处理中的数据
     */
    protected ReceiveBenefitResDTO handlerHistoryFreezeReceiveProcessingRecord(EquityAccountContext equityAccountContext) {
        return null;
    }

    /**
     * 场景领取重试
     *
     * @param equityAccountContext 上下文
     * @return 重试返回结果
     */
    protected ReceiveBenefitResDTO receiveSceneBenefitRetry(EquityAccountContext equityAccountContext) {
        String userId = equityAccountContext.getUid();
        String serialNumber = equityAccountContext.getSerialNumber();
        // 获取已经存在的操作流水
        SubAccountRecordDTO existRecord = queryExistAccountRecord(userId, OperateTypeEnum.PROVIDE.getCode(), serialNumber);
        if (Objects.isNull(existRecord)) {
            log.warn("receiveSceneBenefitRetry existRecord is null. userId={}, serialNumber={}", userId, serialNumber);
            CsossUtils.logEvent(MonitorConstants.RECEIVE_SCENE_BENEFIT_RETRY, "duplication_error");
            return ReceiveBenefitResDTO.fail(ExceptionEnum.DUPLICATION_ERROR);
        }
        Integer existRecordStatus = existRecord.getStatus();
        if (StatusEnum.unfinishedStatus(existRecordStatus)) {
            return receiveRetry(equityAccountContext, existRecord);
        }
        if (StatusEnum.SUCCESS.getCode().equals(existRecordStatus)) {
            UniversalAccountRpcResponseExtDTO rpcResponseExtDTO = existRecord.getRpcResponseExtDTO();
            EquityRpcDto equityRpcDTO = EquityRpcDto.success(existRecord.getThirdResNo(), rpcResponseExtDTO);
            return ReceiveBenefitResDTO.success(equityRpcDTO, existRecord.getId());
        }
        return ReceiveBenefitResDTO.fail(ExceptionEnum.COMMON_ERROR);
    }

    private ReceiveBenefitResDTO receiveRetry(EquityAccountContext equityAccountContext, SubAccountRecordDTO existRecord) {
        // 重试需要使用 existRecord 的参数进行重试，所以需要修改 equityAccountContext 中领取的权益内容
        AccountConvertEntity.changeEquityAccountContextWhileRetry(equityAccountContext, existRecord);
        // 远程调用领取权益
        EquityRpcDto equityRpcDto = doProvideEquityRpc(equityAccountContext);
        equityAccountContext.setEquityRpcDto(equityRpcDto);
        String userId = equityAccountContext.getUid();
        Long accountRecordId = equityAccountContext.getAccountRecordId();
        if (Objects.isNull(equityRpcDto) || StatusEnum.FAIL.equals(equityRpcDto.getStatusEnum())) {
            log.warn("receiveSceneBenefitRetry receiveRetry rpc failure. userId={}, accountRecordId={}", userId, accountRecordId);
            updateRecordFail(accountRecordId, equityAccountContext, ExceptionEnum.RETRY_CALL_DISTRIBUTE_BENEFIT_FAILURE);
            return ReceiveBenefitResDTO.fail(ExceptionEnum.RETRY_CALL_DISTRIBUTE_BENEFIT_FAILURE);
        }
        if (StatusEnum.SUCCESS.equals(equityRpcDto.getStatusEnum())) {
            // 如果远程调用领取权益成功，则重试 doProvideEquity（生成权益子账户，并更新权益子账户操作流水结果）
            int doProvideEquityResult = doProvideEquity(equityAccountContext);
            log.info("receiveSceneBenefitRetry receiveRetry rpc success. userId={}, accountRecordId={}, doProvideEquityResult={}",
                    userId, accountRecordId, doProvideEquityResult);
            BenefitReceiveRuleContext benefitReceiveRuleContext = gainBenefitReceiveRuleContext(equityAccountContext);
            receiveAndStockLimitCoreService.deductBenefitReceiveLimit(equityAccountContext, benefitReceiveRuleContext);

            // 老账户领取成功后的动作（发送领取成功 Pulsar 消息）
            oldAccountDoAfterSceneReceiveSuccess(equityAccountContext);
            return ReceiveBenefitResDTO.success(equityRpcDto, accountRecordId);
        }
        log.warn("receiveSceneBenefitRetry receiveRetry rpc unfinished. userId={}, accountRecordId={}", userId, accountRecordId);
        return ReceiveBenefitResDTO.processing(ExceptionEnum.COMMON_ERROR);
    }

    /**
     * 将冻结成功的频次 id 更新到 record 表上
     */
    protected void updateRecordFreezeReceiveLimitId(Long recordId, Long freezeReceiveLimitId, String userId) {

    }

    /**
     * 权益调用三方领取失败或者处理中
     */
    private ReceiveBenefitResDTO receiveRpcFailOrProcessingHandle(EquityAccountContext equityAccountContext,
                                                                  BenefitReceiveRuleContext benefitReceiveRuleContext) {
        Long recordId = equityAccountContext.getAccountRecordId();
        EquityRpcDto equityRpcDto = equityAccountContext.getEquityRpcDto();

        // 调用失败
        if (Objects.isNull(equityRpcDto) || StatusEnum.FAIL.equals(equityRpcDto.getStatusEnum())) {
            log.warn("equityAccountOperate receiveSceneBenefit receiveRpcFailOrProcessingHandle failure., context={}",
                    JSON.toJSONString(equityAccountContext));
            catMonitorUtil.logEvent(MonitorConstants.RECEIVE_SCENE_BENEFIT, MonitorConstants.FAIL);
            // 更新失败
            updateRecordFail(recordId, equityAccountContext, ExceptionEnum.BENEFIT_SEND_FAIL);
            // 频次回滚
            receiveAndStockLimitCoreService.releaseBenefitReceiveLimit(equityAccountContext, benefitReceiveRuleContext);
            // 释放频次，活动维度
            receiveAndStockLimitCoreService.releaseActivityReceiveLimit(equityAccountContext, benefitReceiveRuleContext);
            // 释放库存
            receiveAndStockLimitCoreService.releaseStockLimit(equityAccountContext, benefitReceiveRuleContext);

            if (Objects.equals(BenefitUnableReceiveReasonType.HIT_RISK.getCode(), equityRpcDto.getCode())) {
                return ReceiveBenefitResDTO.fail(BenefitUnableReceiveReasonType.HIT_RISK.getCode(), BenefitUnableReceiveReasonType.HIT_RISK.getDesc());
            }

            return ReceiveBenefitResDTO.fail(ExceptionEnum.BENEFIT_SEND_FAIL);
        }

        // 处理中
        if (StatusEnum.PROCESSING.equals(equityRpcDto.getStatusEnum())) {
            log.warn("equityAccountOperate receiveSceneBenefit receiveRpcFailOrProcessingHandle processing., context={}",
                    JSON.toJSONString(equityAccountContext));
            catMonitorUtil.logEvent(MonitorConstants.RECEIVE_SCENE_BENEFIT, MonitorConstants.PROCESSING);
            updateRecordProcessing(recordId, equityAccountContext, ExceptionEnum.HANDLE_TIMEOUT);
            return ReceiveBenefitResDTO.processing(ExceptionEnum.HANDLE_TIMEOUT);
        }
        return null;
    }

    /**
     * 填充权益领取需要的频次限制、库存限制规则
     */
    @MonitorSpan(name = "gainBenefitReceiveRuleContext")
    protected BenefitReceiveRuleContext gainBenefitReceiveRuleContext(EquityAccountContext equityAccountContext) {
        Long activityId = equityAccountContext.getActivityId();
        Long strategyId = equityAccountContext.getStrategyId();
        Long benefitGroupId = equityAccountContext.getBenefitGroupId();
        Long benefitId = equityAccountContext.getBenefitId();
        List<ActivityRuleCacheDTO> activityRuleList = localCacheManager.getActivityRuleCacheDTOList(activityId);
        List<StrategyRuleCacheDTO> strategyRuleList = localCacheManager.getStrategyRuleCacheDTOList(strategyId);
        List<BenefitRuleCacheDTO> benefitRuleList = localCacheManager.getBenefitRuleCacheDTOList(strategyId, benefitGroupId, benefitId);
        return RuleConvertEntity.convertToBenefitReceiveRuleContext(activityRuleList, strategyRuleList, benefitRuleList);
    }

    @Override
    public QueryEquityDto equityQuery(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public Long queryEquityBenefitId(Integer benefitType) {
        EquityBenefitDO equityBenefitDO = queryEquityBenefitDO(benefitType);
        return Optional.ofNullable(equityBenefitDO).map(EquityBenefitDO::getId).orElse(null);
    }

    @Override
    public EquityBenefitDO queryEquityBenefitDO(Integer benefitType) {
        // 不指定 externalId 时，默认查询 externalId = '' 的权益
        String paramExternalId = StringUtils.EMPTY;
        List<EquityBenefitDO> equityBenefitList = localCacheManager.getEquityBenefitListByBenefitType(benefitType);
        EquityBenefitDO matchBenefitDO = CollectionUtils.isEmpty(equityBenefitList) ? null : equityBenefitList.stream()
                .filter(e -> Objects.equals(e.getExternalId(), paramExternalId)).findFirst().orElse(null);
        if (matchBenefitDO != null) {
            CsossUtils.logEvent(MonitorConstants.GET_BENEFIT_DO_BY_TYPE, "get_from_local_cache");
            return matchBenefitDO;
        }

        // 如果本地缓存无法查询到指定权益类型的权益列表，则查询 DB 做兜底
        log.warn("queryEquityBenefitDO from localCache failure. benefitType={}", benefitType);
        equityBenefitList = equityBenefitDomainService.queryEquityBenefitsByType(benefitType);
        matchBenefitDO = CollectionUtils.isEmpty(equityBenefitList) ? null : equityBenefitList.stream()
                .filter(e -> Objects.equals(e.getExternalId(), paramExternalId)).findFirst().orElse(null);
        if (matchBenefitDO != null) {
            CsossUtils.logEvent(MonitorConstants.GET_BENEFIT_DO_BY_TYPE, "get_from_db");
            // 从 DB 主库中获取后，需要异步 road benefitDO 到本地缓存
            localCacheManager.asyncLoadBenefitDO(matchBenefitDO.getId());
            return matchBenefitDO;
        }

        CsossUtils.logEvent(MonitorConstants.GET_BENEFIT_DO_BY_TYPE, "get_benefit_is_null");
        log.warn("queryEquityBenefitDO from localDB failure. benefitType={}", benefitType);
        return null;
    }

    @Override
    public AccountInfoDTO queryEquityAccountInfo(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public List<AccountInfoDTO> queryEquityAccountInfos(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public PageListRespDTO<AccountInfoDTO> queryEquityAccountInfosByPage(EquityAccountContext equityAccountContext) {
        return null;
    }

    @Override
    public Boolean hasSucceededByUniqueKey(String userId, Integer operateType, String reqNo) {
        return null;
    }

    /**
     * 查询对应业务流水号是否有已经存在的操作流水
     *
     * @param userId      用户 id
     * @param operateType 操作类型
     * @param reqNo       业务流水号
     * @return 已经存在的操作流水
     */
    public SubAccountRecordDTO queryExistAccountRecord(String userId, Integer operateType, String reqNo) {
        return null;
    }

    private EquityRpcDto doRiskBeforeProvideEquityRpc(EquityAccountContext equityAccountContext) {
        SceneRiskService sceneRiskService = SCENE_RISK_OPERATE_MAP.get(equityAccountContext.getSceneCode());
        if (Objects.nonNull(sceneRiskService)) {
            return sceneRiskService.doRiskBeforeProvideEquityRpc(equityAccountContext);
        }
        //调用三方只有正常返回才能返回EquityRpcDto，其他情况都是抛异常
        EquityRpcDto equityRpcDto = new EquityRpcDto();
        equityRpcDto.setStatusEnum(StatusEnum.SUCCESS);
        equityRpcDto.setCode(ExceptionEnum.SUCCESS.getCode());
        equityRpcDto.setMessage(ExceptionEnum.SUCCESS.getMessage());
        return equityRpcDto;
    }

    @Override
    @MonitorSpan(name = "sceneReceiveNew")
    public ReceiveBenefitResDTO sceneReceiveNew(EquityAccountContext equityAccountContext) {
        AbstractEquityAccountOperate self = (AbstractEquityAccountOperate) AopContext.currentProxy();
        String lockKey = getSceneReceiveLockKey(equityAccountContext);
        boolean locked = false;
        try {
            // 0 加锁
            locked = redisCache.lock(lockKey, 10, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("sceneReceiveNew lock failure context={}", JSON.toJSONString(equityAccountContext));
                CsossUtils.logEvent(MonitorConstants.SCENE_RECEIVE_NEW, "locked_failure", Status.SUCCESS,
                        JSON.toJSONString(equityAccountContext));
                return ReceiveBenefitResDTO.fail(ExceptionEnum.TOO_FREQUENTING);
            }

            // 1 填充权益领取需要的频次限制、库存限制规则
            BenefitReceiveRuleContext benefitReceiveRuleContext = self.gainBenefitReceiveRuleContext(equityAccountContext);
            if (Objects.isNull(benefitReceiveRuleContext)) {
                log.warn("sceneReceiveNew gainBenefitReceiveRuleContext failure context={}", JSON.toJSONString(equityAccountContext));
                CsossUtils.logEvent(MonitorConstants.SCENE_RECEIVE_NEW, "gain_rule_context_failure", Status.SUCCESS,
                        JSON.toJSONString(equityAccountContext));
                return ReceiveBenefitResDTO.fail(ExceptionEnum.GAIN_BENEFIT_RECEIVE_RULE_CONTEXT_FAILURE);
            }

            // 2 生成操作记录，状态为初始化
            Long accountRecordId = self.initUniversalAccountRecord(equityAccountContext, OperateTypeEnum.PROVIDE.getCode());
            equityAccountContext.setAccountRecordId(accountRecordId);

            // 3 扣减领取频次限制、领取库存限制
            ReceiveBenefitResDTO deductLimitBeforeAndHandlerResult = self.deductLimitBeforeAndHandlerResult(equityAccountContext,
                    benefitReceiveRuleContext);
            if (Objects.nonNull(deductLimitBeforeAndHandlerResult)) {
                return deductLimitBeforeAndHandlerResult;
            }

            // 4 rpc 调用领取权益以及之后逻辑
            // 4.1 rpc 调用领取权益
            // 4.2 保存权益账户 + 权益账户明细
            // 4.3 扣减频次，权益维度（权益频次之前只是冻结，等所有流程处理完之后才真正去扣减）
            return self.sceneReceiveNewRpcAndAfter(equityAccountContext, benefitReceiveRuleContext);
        } catch (DuplicateKeyException e) {
            log.error("sceneReceiveNew duplicateKeyException context={}", JSON.toJSONString(equityAccountContext), e);
            CsossUtils.logEvent(MonitorConstants.SCENE_RECEIVE_NEW, MonitorConstants.DUPLICATE_KEY_EXCEPTION, Status.SUCCESS,
                    JSON.toJSONString(equityAccountContext));
            return self.sceneReceiveNewRetry(equityAccountContext);
        } catch (Exception e) {
            log.error("sceneReceiveNew exception context={}", JSON.toJSONString(equityAccountContext), e);
            CsossUtils.logEvent(MonitorConstants.SCENE_RECEIVE_NEW, MonitorConstants.EXCEPTION, Status.SUCCESS,
                    JSON.toJSONString(equityAccountContext));
            return ReceiveBenefitResDTO.fail(ExceptionEnum.BENEFIT_SEND_FAIL);
        } finally {
            log.info("sceneReceiveNew end context={}", JSON.toJSONString(equityAccountContext));
            redisCache.unlock(lockKey, locked);
        }
    }

    @MonitorSpan(name = "sceneReceiveNewRetry")
    protected ReceiveBenefitResDTO sceneReceiveNewRetry(EquityAccountContext equityAccountContext) {
        AbstractEquityAccountOperate self = (AbstractEquityAccountOperate) AopContext.currentProxy();
        String userId = equityAccountContext.getUid();
        String sceneCode = equityAccountContext.getSceneCode();
        String serialNumber = equityAccountContext.getSerialNumber();
        // 获取已经存在的账户操作记录
        UniversalAccountRecordDO existRecord = queryExistUniversalAccountRecord(userId, sceneCode,
                OperateTypeEnum.PROVIDE.getCode(), serialNumber, serialNumber);
        if (Objects.isNull(existRecord)) {
            log.warn("sceneReceiveNewRetry existRecord is null userId={}, sceneCode={}, serialNumber={}",
                    userId, sceneCode, serialNumber);
            return ReceiveBenefitResDTO.fail(ExceptionEnum.DUPLICATION_ERROR);
        }
        // 需要修改 equityAccountContext 中领取的权益内容。外层需要领取成功的权益信息；重试也需要使用 existRecord 的参数进行重试；
        UniversalAccountConvertEntity.changeEquityAccountContextWhileRetry(equityAccountContext, existRecord);
        Integer existRecordStatus = existRecord.getStatus();
        if (StatusEnum.unfinishedStatus(existRecordStatus)) {
            // 填充权益领取需要的频次限制、库存限制规则
            BenefitReceiveRuleContext benefitReceiveRuleContext = self.gainBenefitReceiveRuleContext(equityAccountContext);
            return self.sceneReceiveNewRpcAndAfter(equityAccountContext, benefitReceiveRuleContext);
        }
        if (StatusEnum.SUCCESS.getCode().equals(existRecordStatus)) {
            return UniversalAccountConvertEntity.convertToExistSuccessReceiveResp(existRecord);
        }
        return ReceiveBenefitResDTO.fail(ExceptionEnum.COMMON_ERROR);
    }

    @MonitorSpan(name = "sceneReceiveNewRpcAndAfter")
    protected ReceiveBenefitResDTO sceneReceiveNewRpcAndAfter(EquityAccountContext equityAccountContext,
                                                              BenefitReceiveRuleContext benefitReceiveRuleContext) {
        AbstractEquityAccountOperate self = (AbstractEquityAccountOperate) AopContext.currentProxy();
        // 1 rpc 调用领取权益。如果 rpc 调用领取权益失败或者处理中，则返回处理结果
        ReceiveBenefitResDTO rpcAndHandlerResult = self.rpcAndHandlerResult(equityAccountContext, benefitReceiveRuleContext);
        if (Objects.nonNull(rpcAndHandlerResult)) {
            return rpcAndHandlerResult;
        }

        // 2 保存权益账户 + 权益账户明细
        String userId = equityAccountContext.getUid();
        Long accountRecordId = equityAccountContext.getAccountRecordId();
        boolean saveUniversalAccountResult = self.saveUniversalAccount(equityAccountContext);
        if (!saveUniversalAccountResult) {
            log.warn("sceneReceiveNewRpcAndAfter saveUniversalAccount failure context={}", JSON.toJSONString(equityAccountContext));
            CsossUtils.logEvent(MonitorConstants.SCENE_RECEIVE_NEW, "save_universal_account_failure", Status.SUCCESS,
                    JSON.toJSONString(equityAccountContext));
            // 更新操作记录为处理中
            updateUniversalAccountRecordProcessing(userId, accountRecordId, EquityRpcDto.fail(ExceptionEnum.HANDLE_TIMEOUT));
            return ReceiveBenefitResDTO.processing(ExceptionEnum.HANDLE_TIMEOUT);
        }

        // 3 扣减频次，权益维度（权益频次之前只是冻结，等所有流程处理完之后才真正去扣减）
        self.deductLimitAfter(equityAccountContext, benefitReceiveRuleContext);

        // 4 领取成功后的动作（发送领取成功 Pulsar 消息）
        self.doAfterSceneReceiveSuccess(equityAccountContext);

        return ReceiveBenefitResDTO.success(equityAccountContext.getEquityRpcDto(), accountRecordId,
                equityAccountContext.getAccountDetailId());
    }

    @MonitorSpan(name = "doAfterSceneReceiveSuccess")
    protected void doAfterSceneReceiveSuccess(EquityAccountContext equityAccountContext) {
        // 领取成功打点（按活动场景纬度打点）
        String benefitTypeEnumName = Optional.ofNullable(equityAccountContext.getBenefitType())
                .map(BenefitTypeEnum::getById)
                .map(Enum::name)
                .map(String::toLowerCase)
                .orElse("unknown_benefit_type");
        CsossUtils.logEvent(MonitorConstants.SCENE_RECEIVE_SUCCESS + equityAccountContext.getSceneCode(), benefitTypeEnumName);

        // 异步发送领取成功 Pulsar 消息
        BenefitReceiveSuccessMsgVO msgVO = UniversalAccountConvertEntity
                .convertToSceneBenefitReceiveSuccessMsgVO(equityAccountContext);
        benefitReceiveSuccessMsgProducer.asyncSendBenefitReceiveSuccessMsg(msgVO);
    }

    @MonitorSpan(name = "rpcAndHandlerResult")
    protected ReceiveBenefitResDTO rpcAndHandlerResult(EquityAccountContext equityAccountContext,
                                                       BenefitReceiveRuleContext benefitReceiveRuleContext) {
        String userId = equityAccountContext.getUid();
        Long accountRecordId = equityAccountContext.getAccountRecordId();

        // rpc 调用领取权益
        EquityRpcDto equityRpc = doProvideEquityRpc(equityAccountContext);
        equityAccountContext.setEquityRpcDto(equityRpc);

        if (Objects.isNull(equityRpc) || StatusEnum.FAIL.equals(equityRpc.getStatusEnum())) {
            log.warn("rpcAndHandlerResult failure context={}, equityRpc={}", JSON.toJSONString(equityAccountContext),
                    JSON.toJSONString(equityRpc));
            CsossUtils.logEvent(MonitorConstants.DO_RPC_RESULT, "do_rpc_failure", Status.SUCCESS,
                    JSON.toJSONString(equityAccountContext));
            // 更新操作记录为失败
            updateUniversalAccountRecordFailure(userId, accountRecordId, EquityRpcDto.fail(ExceptionEnum.BENEFIT_SEND_FAIL));
            // 回滚频次，权益维度
            receiveAndStockLimitCoreService.releaseBenefitReceiveLimit(equityAccountContext, benefitReceiveRuleContext);
            // 释放频次，活动维度
            receiveAndStockLimitCoreService.releaseActivityReceiveLimit(equityAccountContext, benefitReceiveRuleContext);
            // 释放库存，权益维度
            receiveAndStockLimitCoreService.releaseStockLimit(equityAccountContext, benefitReceiveRuleContext);
            return ReceiveBenefitResDTO.fail(ExceptionEnum.BENEFIT_SEND_FAIL);
        }

        if (StatusEnum.PROCESSING.equals(equityRpc.getStatusEnum())) {
            log.warn("rpcAndHandlerResult processing context={}, equityRpc={}", JSON.toJSONString(equityAccountContext),
                    JSON.toJSONString(equityRpc));
            CsossUtils.logEvent(MonitorConstants.DO_RPC_RESULT, "do_rpc_processing", Status.SUCCESS,
                    JSON.toJSONString(equityAccountContext));
            // 更新操作记录为处理中
            updateUniversalAccountRecordProcessing(userId, accountRecordId, EquityRpcDto.fail(ExceptionEnum.HANDLE_TIMEOUT));
            return ReceiveBenefitResDTO.processing(ExceptionEnum.HANDLE_TIMEOUT);
        }

        return null;
    }

    @MonitorSpan(name = "deductLimitBeforeAndHandlerResult")
    protected ReceiveBenefitResDTO deductLimitBeforeAndHandlerResult(EquityAccountContext equityAccountContext,
                                                                     BenefitReceiveRuleContext benefitReceiveRuleContext) {
        AbstractEquityAccountOperate self = (AbstractEquityAccountOperate) AopContext.currentProxy();
        String userId = equityAccountContext.getUid();
        Long accountRecordId = equityAccountContext.getAccountRecordId();

        // 1 冻结频次，权益维度
        if (!receiveAndStockLimitCoreService.freezeBenefitReceiveLimit(equityAccountContext, benefitReceiveRuleContext)) {
            // 存在冻结中的频次，则优先处理未完成的操作记录
            if (equityAccountContext.isHasFreezeReceiveLimitProcessingRecord() && Objects.nonNull(equityAccountContext.getFreezeReceiveLimitResultId())) {
                log.info("deductLimitBeforeAndHandlerResult freezeBenefitReceiveLimit hadFrozenRecord context={}", JSON.toJSONString(equityAccountContext));
                return self.sceneReceiveNewHandleFrozenRecord(equityAccountContext);
            }
            log.warn("deductLimitBeforeAndHandlerResult freezeBenefitReceiveLimit failure context={}", JSON.toJSONString(equityAccountContext));
            CsossUtils.logEvent(MonitorConstants.DEDUCT_LIMIT_BEFORE, "freeze_benefit_frequency_failure", Status.SUCCESS,
                    JSON.toJSONString(equityAccountContext));
            // 更新操作记录为失败
            updateUniversalAccountRecordFailure(userId, accountRecordId, EquityRpcDto.fail(ExceptionEnum.RECEIVE_OVER_LIMIT));
            return ReceiveBenefitResDTO.fail(ExceptionEnum.RECEIVE_OVER_LIMIT);
        }

        // 2 扣减频次，活动维度
        if (!receiveAndStockLimitCoreService.deductActivityReceiveLimit(equityAccountContext, benefitReceiveRuleContext)) {
            log.warn("deductLimitBeforeAndHandlerResult deductActivityReceiveLimit failure context={}", JSON.toJSONString(equityAccountContext));
            CsossUtils.logEvent(MonitorConstants.DEDUCT_LIMIT_BEFORE, "deduct_activity_frequency_failure", Status.SUCCESS,
                    JSON.toJSONString(equityAccountContext));
            // 更新操作记录为失败
            updateUniversalAccountRecordFailure(userId, accountRecordId, EquityRpcDto.fail(ExceptionEnum.RECEIVE_OVER_LIMIT));
            // 回滚频次，权益维度
            receiveAndStockLimitCoreService.releaseBenefitReceiveLimit(equityAccountContext, benefitReceiveRuleContext);
            return ReceiveBenefitResDTO.fail(ExceptionEnum.RECEIVE_OVER_LIMIT);
        }

        // 3 扣减库存，权益维度
        if (!receiveAndStockLimitCoreService.deductBenefitStock(equityAccountContext, benefitReceiveRuleContext)) {
            log.warn("deductLimitBeforeAndHandlerResult deductBenefitStock failure context={}", JSON.toJSONString(equityAccountContext));
            CsossUtils.logEvent(MonitorConstants.DEDUCT_LIMIT_BEFORE, "deduct_benefit_stock_failure", Status.SUCCESS,
                    JSON.toJSONString(equityAccountContext));
            // 更新操作记录为失败
            updateUniversalAccountRecordFailure(userId, accountRecordId, EquityRpcDto.fail(ExceptionEnum.STOCK_OVER_LIMIT));
            // 回滚频次，权益维度
            receiveAndStockLimitCoreService.releaseBenefitReceiveLimit(equityAccountContext, benefitReceiveRuleContext);
            // 释放频次，活动维度
            receiveAndStockLimitCoreService.releaseActivityReceiveLimit(equityAccountContext, benefitReceiveRuleContext);
            return ReceiveBenefitResDTO.fail(ExceptionEnum.STOCK_OVER_LIMIT);
        }

        // 4 更新通用账户操作记录内部唯一流水号、活动维度领取频次限制表 id、权益维度领取频次限制表 id
        universalAccountRecordDomainService.updateInnerReqNoAndRuleLimitInfo(equityAccountContext.getUid(),
                equityAccountContext.getAccountRecordId(), equityAccountContext.getDoRpcUniqueSerialNumber(),
                equityAccountContext.getActivityReceiveLimitId(), equityAccountContext.getFreezeReceiveLimitResultId());
        return null;
    }

    @MonitorSpan(name = "sceneReceiveNewHandleFrozenRecord")
    protected ReceiveBenefitResDTO sceneReceiveNewHandleFrozenRecord(EquityAccountContext equityAccountContext) {
        AbstractEquityAccountOperate self = (AbstractEquityAccountOperate) AopContext.currentProxy();
        // 查询未完成状态的操作记录
        String userId = equityAccountContext.getUid();
        Long accountRecordId = equityAccountContext.getAccountRecordId();
        String currReqNo = equityAccountContext.getSerialNumber();

        UniversalAccountRecordDO existUnfinishedRecord = queryExistUnfinishedUniversalAccountRecord(userId,
                equityAccountContext.getStrategyId(), equityAccountContext.getBenefitId(),
                OperateTypeEnum.PROVIDE.getCode(), equityAccountContext.getFreezeReceiveLimitResultId());
        if (Objects.isNull(existUnfinishedRecord)) {
            log.warn("sceneReceiveNewHandleFrozenRecord existUnfinishedRecord is null context={}", JSON.toJSONString(equityAccountContext));
            // 更新操作记录为失败
            updateUniversalAccountRecordFailure(userId, accountRecordId, EquityRpcDto.fail(ExceptionEnum.UNFINISHED_RECORD_IS_NULL));
            return ReceiveBenefitResDTO.fail(ExceptionEnum.UNFINISHED_RECORD_IS_NULL);
        }

        // 重试需要使用 existUnfinishedRecord 的参数进行重试，所以需要修改 equityAccountContext 中领取的权益内容
        UniversalAccountConvertEntity.changeEquityAccountContextWhileRetry(equityAccountContext, existUnfinishedRecord);
        // 填充权益领取需要的频次限制、库存限制规则
        BenefitReceiveRuleContext benefitReceiveRuleContext = self.gainBenefitReceiveRuleContext(equityAccountContext);
        ReceiveBenefitResDTO receiveBenefitResDTO = self.sceneReceiveNewRpcAndAfter(equityAccountContext, benefitReceiveRuleContext);

        if (receiveBenefitResDTO != null && receiveBenefitResDTO.getStatusEnum() == StatusEnum.SUCCESS) {
            // 更新 newAccountRecord 为成功，并且关联 existUnfinishedRecord 的流水号
            boolean updateResult = universalAccountRecordDomainService.updateRecordStatusAndRpcResult(userId,
                    accountRecordId, StatusEnum.SUCCESS.getCode(), null, equityAccountContext.getEquityRpcDto(),
                    existUnfinishedRecord.getReqNo());
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_RECEIVE_HANDLE_FROZEN_RECORD,
                    updateResult ? "update_to_success_record_success" : "update_to_success_record_failure");
        } else {
            // 更新 newAccountRecord 为失败
            boolean updateResult = universalAccountRecordDomainService.updateRecordStatusAndRpcResult(userId,
                    accountRecordId, StatusEnum.FAIL.getCode(), null, equityAccountContext.getEquityRpcDto(),
                    existUnfinishedRecord.getReqNo());
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_RECEIVE_HANDLE_FROZEN_RECORD,
                    updateResult ? "update_to_fail_record_success" : "update_to_fail_record_failure");
        }
        log.info("sceneReceiveNewHandleFrozenRecord updateNewRecordStatusAndRpcResult end userId={}" +
                        ", currReqNo={}, accountRecordId={}, existUnfinishedRecordReqNo={}, existUnfinishedRecordId={}" +
                        ", receiveBenefitResDTO={}",
                userId, currReqNo, accountRecordId, existUnfinishedRecord.getReqNo(), existUnfinishedRecord.getId(),
                JSON.toJSONString(receiveBenefitResDTO));

        return receiveBenefitResDTO;
    }

    @MonitorSpan(name = "deductLimitAfter")
    protected void deductLimitAfter(EquityAccountContext equityAccountContext, BenefitReceiveRuleContext benefitReceiveRuleContext) {
        // 扣减频次，权益维度（权益频次之前只是冻结，等所有流程处理完之后才真正去扣减）
        receiveAndStockLimitCoreService.deductBenefitReceiveLimit(equityAccountContext, benefitReceiveRuleContext);
    }

    @MonitorSpan(name = "initUniversalAccountRecord")
    protected Long initUniversalAccountRecord(EquityAccountContext equityAccountContext, Integer operateType) {
        UniversalAccountRecordDO recordDO = UniversalAccountConvertEntity.convertToAccountRecordDO(equityAccountContext, operateType);
        universalAccountRecordDomainService.add(recordDO);
        return recordDO.getId();
    }

    @MonitorSpan(name = "saveUniversalAccount")
    protected boolean saveUniversalAccount(EquityAccountContext equityAccountContext) {
        try {
            String userId = equityAccountContext.getUid();
            Long accountRecordId = equityAccountContext.getAccountRecordId();
            EquityRpcDto equityRpc = equityAccountContext.getEquityRpcDto();
            UniversalAccountDetailDO detailDO = UniversalAccountConvertEntity.convertToAccountDetailDO(equityAccountContext);
            UniversalAccountDO accountDO = UniversalAccountConvertEntity.convertToAccountDO(detailDO);
            boolean saveUniversalAccount = transactionUtil.transactionalAndCatch(s -> {
                // 1 保存权益通用账户
                universalAccountDomainService.addOrUpdate(accountDO);
                // 2 保存权益通用账户明细
                detailDO.setAccountId(accountDO.getId());
                universalAccountDetailDomainService.add(detailDO);
                // 3 更新权益通用账户操作记录为成功
                boolean updateAccountRecordResult = universalAccountRecordDomainService.updateStatusAndRpcResult(userId,
                        accountRecordId, StatusEnum.SUCCESS.getCode(), accountDO.getId(), equityRpc);
                if (!updateAccountRecordResult) {
                    log.warn("saveUniversalAccount updateStatusAndRpcResult failure userId={}, accountRecordId={}",
                            userId, accountRecordId);
                    throw AdminExceptionBuilder.build(ExceptionEnum.ACCOUNT_RECORD_UPDATE_FAIL, "更新权益通用账户操作记录状态失败，请稍后再试～");
                }
            });
            // 设置账户明细 id 和账户 id 到上下文中，方便后续使用
            equityAccountContext.setAccountDetailId(saveUniversalAccount ? detailDO.getId() : null);
            equityAccountContext.setAccountId(saveUniversalAccount ? accountDO.getId() : null);
            return saveUniversalAccount;
        } catch (Exception e) {
            log.error("saveUniversalAccount exception equityAccountContext={}", JSON.toJSONString(equityAccountContext), e);
            return false;
        }
    }

    private String getSceneReceiveLockKey(EquityAccountContext equityAccountContext) {
        String userId = equityAccountContext.getUid();
        Long strategyId = equityAccountContext.getStrategyId();
        Long benefitId = equityAccountContext.getBenefitId();
        return String.format(CacheKeyConstants.RECEIVE_SCENE_STRATEGY_BENEFIT_LOCK, userId, strategyId, benefitId);
    }

    private UniversalAccountRecordDO queryExistUniversalAccountRecord(String userId, String sceneCode, Integer operateType,
                                                                      String reqNo, String auxKey) {
        return universalAccountRecordDomainService.queryOneByUniqueKey(userId, sceneCode, operateType, reqNo, auxKey);
    }

    private UniversalAccountRecordDO queryExistUnfinishedUniversalAccountRecord(String userId, Long strategyId, Long benefitId,
                                                                                Integer operateType, Long freezeReceiveLimitId) {
        if (Objects.isNull(freezeReceiveLimitId)) {
            return null;
        }
        List<Integer> statuses = Lists.newArrayList(StatusEnum.INIT.getCode(), StatusEnum.PROCESSING.getCode());
        List<UniversalAccountRecordDO> recordDOList = universalAccountRecordDomainService.queryExistAccountRecords(userId,
                strategyId, benefitId, operateType, null, null, statuses);
        if (CollectionUtils.isEmpty(recordDOList)) {
            return null;
        }
        return recordDOList.stream().filter(e -> Objects.equals(e.getFreezeReceiveLimitId(), freezeReceiveLimitId))
                .findFirst().orElse(null);
    }

    private void updateUniversalAccountRecordProcessing(String userId, Long accountRecordId, EquityRpcDto equityRpc) {
        universalAccountRecordDomainService.updateStatusAndRpcResult(userId, accountRecordId, StatusEnum.PROCESSING.getCode(),
                null, equityRpc);
    }

    private void updateUniversalAccountRecordFailure(String userId, Long accountRecordId, EquityRpcDto equityRpc) {
        universalAccountRecordDomainService.updateStatusAndRpcResult(userId, accountRecordId, StatusEnum.FAIL.getCode(),
                null, equityRpc);
    }

    @MonitorSpan(name = "oldAccountDoAfterSceneReceiveSuccess")
    protected void oldAccountDoAfterSceneReceiveSuccess(EquityAccountContext context) {
        try {
            // 领取成功打点（按活动场景纬度打点）
            Integer benefitTypeByAccountType = AccountConvertEntity.convertToBenefitTypeByAccountType(context.getAccountType());
            Integer benefitType = ObjectUtils.defaultIfNull(context.getBenefitType(), benefitTypeByAccountType);
            String benefitTypeEnumName = Optional.ofNullable(benefitType)
                    .map(BenefitTypeEnum::getById)
                    .map(Enum::name)
                    .map(String::toLowerCase)
                    .orElse("unknown_benefit_type");
            CsossUtils.logEvent(MonitorConstants.SCENE_RECEIVE_SUCCESS + context.getSceneCode(), benefitTypeEnumName);

            // 异步发送领取成功 Pulsar 消息
            BenefitReceiveSuccessMsgVO msgVO = AccountConvertEntity.convertToBenefitReceiveSuccessMsgVO(context);
            benefitReceiveSuccessMsgProducer.asyncSendBenefitReceiveSuccessMsg(msgVO);

            // 老账户权益领取成功后，发送 MQ 消息，用于核对
            ThreadsUtils.runAsync(() -> {
                EquityBenefitSendMsgDTO benefitSendMsgDTO = AccountConvertEntity.convertToBenefitSendMsgDTO(context);
                CouponExpandReceiveProducer couponExpandReceiveProducer = SpringContextUtil.getBean(CouponExpandReceiveProducer.class);
                couponExpandReceiveProducer.send(benefitSendMsgDTO, "equity_send_benefit_check", StringUtils.EMPTY);
            }, ThreadsUtils.getOrdinaryThreadPoll(), Constants.BENEFIT_CHECK);
        } catch (Exception e) {
            log.error("oldAccountDoAfterSceneReceiveSuccess exception context={}", JSON.toJSONString(context), e);
        }
    }
}
