package com.ddmc.equity.account.strategy;

import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * hulei
 * 金豆
 */
@Slf4j
@Component
public class GoldenBeanAccountOperate extends AbstractEquityAccountOperate {

    @Override
    public EquityRpcDto doProvideEquityRpc(EquityAccountContext equityAccountContext) {
        return super.doProvideEquityRpc(equityAccountContext);
    }
}
