package com.ddmc.equity.account.strategy;

import com.ddmc.equity.account.AbstractEquityAccountOperate;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.util.TransactionUtil;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.entity.account.BalanceAccountConvertEntity;
import com.ddmc.equity.domain.service.balance.BalanceAccountDomainService;
import com.ddmc.equity.domain.service.balance.BalanceAccountRecordDomainService;
import com.ddmc.equity.dto.business.PageListRespDTO;
import com.ddmc.equity.infra.repository.dao.BalanceAccountDO;
import com.ddmc.equity.infra.repository.dao.BalanceAccountRecordDO;
import com.ddmc.equity.infra.rpc.balance.BalanceProxy;
import com.ddmc.equity.model.dto.AccountInfoDTO;
import com.ddmc.equity.model.dto.SubAccountRecordDTO;
import com.ddmc.trade.balance.dto.req.AutoBalanceRechargeReq;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/10/16 14:01
 * @description
 */
@Slf4j
@Component
public class BalanceAccountOperate extends AbstractEquityAccountOperate {

    @Autowired
    private TransactionUtil transactionUtil;
    @Autowired
    private BalanceAccountDomainService accountDomainService;
    @Autowired
    private BalanceAccountRecordDomainService accountRecordDomainService;
    @Autowired
    private BalanceProxy balanceProxy;

    @Override
    public Long initRecord(int operateType, EquityAccountContext equityAccountContext) {
        BalanceAccountRecordDO accountRecordDO = BalanceAccountConvertEntity.createAccountRecordDO(operateType, equityAccountContext);
        accountRecordDomainService.insertAccountRecordDO(accountRecordDO);
        return accountRecordDO.getId();
    }

    @Override
    public int doProvideEquity(EquityAccountContext equityAccountContext) {
        Long accountRecordId = equityAccountContext.getAccountRecordId();
        BalanceAccountDO accountDO = BalanceAccountConvertEntity.createAccountDO(equityAccountContext);
        transactionUtil.transactional(s -> {
            // 1 生成权益子账户
            accountDomainService.insertAccountDO(accountDO);

            // 2 更新权益子账户操作流水结果
            equityAccountContext.setAccountId(accountDO.getId());
            boolean updateAccountRecordResult = updateRecordSuccessById(accountRecordId, StatusEnum.SUCCESS.getCode(), equityAccountContext);
            if (!updateAccountRecordResult) {
                log.warn("balance doProvideEquity updateRecordSuccessById failure. userId={}, accountRecordId={}",
                        equityAccountContext.getUid(), accountRecordId);
                throw AdminExceptionBuilder.build(ExceptionEnum.ACCOUNT_RECORD_UPDATE_FAIL);
            }
        });
        return Constants.ONE;
    }

    @Override
    public boolean updateRecordSuccessById(Long recordId, int status, EquityAccountContext equityAccountContext) {
        return accountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), status, equityAccountContext.getEquityRpcDto(),
                BalanceAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext));
    }

    @Override
    public boolean updateRecordExceptionById(Long recordId, int status, EquityAccountContext equityAccountContext) {
        return accountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), status, equityAccountContext.getEquityRpcDto(),
                BalanceAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext));
    }

    @Override
    protected void updateRecordFail(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        accountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), StatusEnum.FAIL.getCode(), EquityRpcDto.fail(exceptionEnum),
                BalanceAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext));
    }

    @Override
    protected void updateRecordProcessing(Long recordId, EquityAccountContext equityAccountContext, ExceptionEnum exceptionEnum) {
        accountRecordDomainService.updateAccountRecordStatusAndRpcResult(equityAccountContext.getUid(), recordId,
                equityAccountContext.getAccountId(), StatusEnum.PROCESSING.getCode(), EquityRpcDto.fail(exceptionEnum),
                BalanceAccountConvertEntity.getRuleLimitInfoMap(equityAccountContext));
    }

    @Override
    public EquityRpcDto doProvideEquityRpc(EquityAccountContext equityAccountContext) {
        AutoBalanceRechargeReq request = BalanceAccountConvertEntity.getAutoBalanceRechargeReq(equityAccountContext);
        return balanceProxy.recharge(request);
    }

    @Override
    public List<AccountInfoDTO> queryEquityAccountInfos(EquityAccountContext equityAccountContext) {
        List<BalanceAccountDO> accountDOList = accountDomainService.queryAccountsByUserId(equityAccountContext.getUid(),
                equityAccountContext.getActivityIds(), equityAccountContext.getStatuses(),
                equityAccountContext.getStartDate(), equityAccountContext.getEndDate());
        return BalanceAccountConvertEntity.convertToAccountInfoDTOList(accountDOList);
    }

    @Override
    public PageListRespDTO<AccountInfoDTO> queryEquityAccountInfosByPage(EquityAccountContext equityAccountContext) {
        PageListRespDTO<BalanceAccountDO> pageResult = accountDomainService.queryAccountsByUserId(equityAccountContext.getUid(),
                equityAccountContext.getActivityIds(), equityAccountContext.getStatuses(),
                equityAccountContext.getStartDate(), equityAccountContext.getEndDate(),
                equityAccountContext);

        return Objects.isNull(pageResult) ? PageListRespDTO.<AccountInfoDTO>builder().build() :
                PageListRespDTO.<AccountInfoDTO>builder()
                        .list(BalanceAccountConvertEntity.convertToAccountInfoDTOList(pageResult.getList()))
                        .total(pageResult.getTotal())
                        .build();
    }

    @Override
    public SubAccountRecordDTO queryExistAccountRecord(String userId, Integer operateType, String reqNo) {
        List<Integer> statuses = Lists.newArrayList(StatusEnum.INIT.getCode(), StatusEnum.SUCCESS.getCode(), StatusEnum.PROCESSING.getCode());
        BalanceAccountRecordDO existRecord = accountRecordDomainService.queryAccountRecordByUniqueKeyAndStatuses(userId,
                operateType, reqNo, statuses);
        return Objects.isNull(existRecord) ? null : BalanceAccountConvertEntity.convertToSubAccountRecordDTO(existRecord);
    }
}
