package com.ddmc.equity.processor.scene_action.v1.processor.default_processor.confirm_consume_bft;

import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ProcessStatus;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.processor.consume_benefit.dto.ConsumeBenefitContext;
import com.ddmc.equity.processor.consume_benefit.strategy.ConsumeBenefitStrategy;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessContext;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessResult;
import com.ddmc.equity.processor.scene_action.v1.enums.SceneActionProcessTypeEnum;
import com.ddmc.equity.processor.scene_action.v1.processor.AbstractConsumeBenefitProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 默认确认用户可用权益（消耗权益）数量（成功时确认扣减，失败时释放）处理器
 */
@Slf4j
@Component
public class DefaultConfirmConsumeBftProcessor extends AbstractConsumeBenefitProcessor {

    @Override
    public SceneActionProcessTypeEnum getProcessTypeEnum() {
        return SceneActionProcessTypeEnum.CONFIRM_CONSUME_BFT;
    }

    @Override
    public boolean isEnabled(SceneActionProcessContext context) {
        // 如果需要消耗权益，启用该节点
        return super.isConsumeBenefitRequired(context.getSceneCode());
    }

    @Override
    public SceneActionProcessResult process(SceneActionProcessContext context) {
        // 1 检查消耗权益基础参数有效性
        SceneActionProcessResult checkResult = checkConsumeBenefit(context);
        if (checkResult != null && !checkResult.isSuccess()) {
            log.warn("sceneAction confirmConsumeBft checkConsumeBenefitFailure userId={}, sceneCode={}, reqNo={}" +
                    ", checkResult={}", context.getUserId(), context.getSceneCode(), context.getReqNo(), checkResult);
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_CONFIRM_CONSUME_BFT, "checkConsumeBenefitFailure");
            return checkResult;
        }

        // 2 创建消耗权益上下文
        ConsumeBenefitContext benefitContext = ConsumeBenefitContext.buildConsumeBenefitContext(context);

        // 3 获取对应的权益策略
        Integer consumeBenefitType = context.getConsumeBenefitType();
        ConsumeBenefitStrategy strategy = strategyFactory.getStrategy(consumeBenefitType);
        if (strategy == null) {
            log.warn("sceneAction confirmConsumeBft getStrategyFailure userId={}, sceneCode={}, reqNo={}" +
                            ", consumeBenefitType={}",
                    context.getUserId(), context.getSceneCode(), context.getReqNo(), consumeBenefitType);
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_CONFIRM_CONSUME_BFT, "getStrategyFailure");
            return SceneActionProcessResult.failure("未找到对应的消耗权益策略，消耗权益类型：" + consumeBenefitType, null);
        }

        // 20270716 讨论结果，因为权益纬度频次限制有冻结功能的特殊处理逻辑，如果有 frozenRecordReqNo
        // 需要释放当前 reqNo 冻结的消耗权益；
        // 且使用 frozenRecordReqNo 作为流水号确认消耗权益；
        String frozenRecordReqNo = context.getBizData().getFrozenRecordReqNo();
        if (StringUtils.isNotBlank(frozenRecordReqNo)) {
            log.info("sceneAction confirmConsumeBft hasFrozenRecordReqNo userId={}, sceneCode={}, reqNo={}" +
                            ", frozenRecordReqNo={}",
                    context.getUserId(), context.getSceneCode(), context.getReqNo(), frozenRecordReqNo);
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_CONFIRM_CONSUME_BFT, "hasFrozenRecordReqNo");

            // 释放当前 reqNo 冻结的权益
            strategy.releaseConsumeBenefit(benefitContext);

            // 使用 frozenRecordReqNo 作为流水号确认消耗权益
            benefitContext.setReqNo(frozenRecordReqNo);
            // 使用 frozenRecordConsumeBenefitFreezeLogId 用来确认消耗权益（确认扣减 or 释放）
            benefitContext.getBizParams().setFreezeLogId(context.getBizData().getFrozenRecordConsumeBenefitFreezeLogId());
        }

        // 4 执行确认消耗权益
        if (ProcessStatus.SUCCESS.equals(context.getBizData().getExecuteBftResultStatus())) {
            // 执行确认扣减消耗权益（确认扣减消耗权益结果，里面有统一打点并配置告警，无需重复打点）
            strategy.confirmConsumeBenefit(benefitContext);

            // 转换为场景处理结果（失败不影响后续流程，所以 return success）
            return SceneActionProcessResult.success(null);
        }

        if (ProcessStatus.FAILURE.equals(context.getBizData().getExecuteBftResultStatus())) {
            // 执行释放消耗权益（释放消耗权益结果，里面有统一打点并配置告警，无需重复打点）
            strategy.releaseConsumeBenefit(benefitContext);

            // 转换为场景处理结果（失败不影响后续流程，所以 return success）
            return SceneActionProcessResult.success(null);
        }

        // 5 权益执行状态无需确认
        log.info("sceneAction confirmConsumeBft executeBftResultStatus noNeedConfirm userId={}, consumeBenefitType={}" +
                        ", executeBftResultStatus={}",
                context.getUserId(), consumeBenefitType, context.getBizData().getExecuteBftResultStatus());
        CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_CONFIRM_CONSUME_BFT, "statusNoNeedConfirm");
        return SceneActionProcessResult.success(null);
    }

    @Override
    public boolean shouldChangeResult(SceneActionProcessContext context) {
        // 该节点处理器执行完成，不需要更新流程结果
        return false;
    }
}
