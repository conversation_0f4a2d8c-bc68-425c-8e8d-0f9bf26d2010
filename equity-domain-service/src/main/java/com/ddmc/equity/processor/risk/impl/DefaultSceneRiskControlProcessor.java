package com.ddmc.equity.processor.risk.impl;

import com.alibaba.fastjson.JSONObject;
import com.ddmc.equity.common.util.MapUtils;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.enums.ExternalTypeEnum;
import com.ddmc.equity.model.dto.risk.DefaultSceneRiskStrategyDataDTO;
import com.ddmc.equity.processor.risk.AbstractRiskControlProcessor;
import com.ddmc.equity.processor.risk.RiskControlContextDTO;
import com.ddmc.equity.processor.risk.RiskControlSceneCodeTypes;
import com.google.common.base.CaseFormat;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/5/28 10:23
 * @description
 */
@Slf4j
@Service
public class DefaultSceneRiskControlProcessor extends AbstractRiskControlProcessor {

    @Override
    protected List<String> getSceneCodes() {
        return Lists.newArrayList(RiskControlSceneCodeTypes.DEFAULT_SCENE);
    }

    @Override
    protected JSONObject buildSceneStrategyData(RiskControlContextDTO context) {
        FullBenefitInfoDTO benefitInfoDTO = context.getBenefitInfoDTO();
        DefaultSceneRiskStrategyDataDTO riskStrategyDataDTO = DefaultSceneRiskStrategyDataDTO.builder()
                .activityId(getRiskActivityId(benefitInfoDTO))
                .activityName(Objects.isNull(benefitInfoDTO) ? null : benefitInfoDTO.getActivityName())
                .build();
        // 填充发放奖励信息
        fillRiskStrategyDataAwardInfo(riskStrategyDataDTO, context.getBenefitInfoDTO());
        JSONObject riskStrategyDataJsonObj = parseToJsonObject(riskStrategyDataDTO);
        // 填充调用方携带的场景特殊参数（参数 key 需要驼峰转下划线）
        fillRiskSceneStrategyDataMap(riskStrategyDataJsonObj, context.getRiskSceneStrategyDataMap());
        return riskStrategyDataJsonObj;
    }

    private String getRiskActivityId(FullBenefitInfoDTO benefitInfoDTO) {
        if (Objects.isNull(benefitInfoDTO)) {
            return null;
        }
        return Objects.equals(benefitInfoDTO.getExternalType(), ExternalTypeEnum.INNER.getType()) ?
                String.valueOf(benefitInfoDTO.getActivityId()) : benefitInfoDTO.getExternalId();
    }

    private void fillRiskStrategyDataAwardInfo(DefaultSceneRiskStrategyDataDTO riskStrategyDataDTO,
                                               FullBenefitInfoDTO benefitInfoDTO) {
        BenefitTypeEnum benefitTypeEnum = Optional.ofNullable(benefitInfoDTO)
                .map(FullBenefitInfoDTO::getBenefitType).map(BenefitTypeEnum::getById).orElse(null);
        String benefitValue = Optional.ofNullable(benefitInfoDTO).map(FullBenefitInfoDTO::getBenefitValue).orElse(null);
        String benefitName = Optional.ofNullable(benefitInfoDTO).map(FullBenefitInfoDTO::getBenefitName).orElse(null);
        if (Objects.isNull(benefitTypeEnum)) {
            return;
        }
        switch (benefitTypeEnum) {
            case POINT:
                riskStrategyDataDTO.setAwardType("points");
                riskStrategyDataDTO.setPointsAmount(benefitValue);
                break;
            case BALANCE:
            case RANDOM_BALANCE:
                BigDecimal balanceMoney = benefitInfoDTO.getBalanceMoney();
                riskStrategyDataDTO.setAwardType("balance");
                riskStrategyDataDTO.setBalanceMoney(Objects.nonNull(balanceMoney) ? balanceMoney.toPlainString() : benefitValue);
                break;
            case TICKET:
                riskStrategyDataDTO.setAwardType("ticket");
                riskStrategyDataDTO.setTicketId(benefitValue);
                riskStrategyDataDTO.setAwardName(benefitName);
                break;
            case EMPTY_PRIZE:
                riskStrategyDataDTO.setAwardType("empty_prize");
                break;
            case PHYSICAL_ITEM:
                riskStrategyDataDTO.setAwardType("physical_item");
                riskStrategyDataDTO.setAwardName(benefitName);
                break;
            case BLESSING:
                riskStrategyDataDTO.setAwardType("blessing");
                riskStrategyDataDTO.setAwardName(benefitName);
                break;
            case VIP_DAYS:
                riskStrategyDataDTO.setAwardType("vip_days");
                riskStrategyDataDTO.setVipDays(benefitValue);
                break;
            default:
                break;
        }
    }

    private void fillRiskSceneStrategyDataMap(JSONObject riskStrategyDataJsonObj,
                                              Map<String, Object> riskSceneStrategyDataMap) {
        if (MapUtils.isEmpty(riskSceneStrategyDataMap)) {
            return;
        }
        // 传给风控参数 key 需要驼峰转下划线
        riskSceneStrategyDataMap.forEach((key, value) -> {
            if (StringUtils.isBlank(key) || Objects.isNull(value)) {
                return;
            }
            riskStrategyDataJsonObj.put(CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, key), value);
        });
    }
}
