package com.ddmc.equity.processor.consume_benefit.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/7/18 10:22
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class ConsumeBenefitBizDataDTO {

    /**
     * 用户可用权益数量
     */
    private String availableAmount;
    /**
     * 冻结记录 ID
     */
    private String freezeLogId;
}
