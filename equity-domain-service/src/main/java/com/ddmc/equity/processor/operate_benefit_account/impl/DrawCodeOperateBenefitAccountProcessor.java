package com.ddmc.equity.processor.operate_benefit_account.impl;

import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.entity.account.DrawCodeAccountConvertEntity;
import com.ddmc.equity.domain.service.core.EquityAccountCoreService;
import com.ddmc.equity.dto.customer.account.AccountDirectBenefitReqDTO;
import com.ddmc.equity.dto.customer.account.AccountDirectBenefitRespDTO;
import com.ddmc.equity.dto.customer.account.AccountDistributeBenefitReqDTO;
import com.ddmc.equity.dto.customer.account.AccountDistributeBenefitRespDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/7/31 15:29
 * @description
 */
@Slf4j
@Component
public class DrawCodeOperateBenefitAccountProcessor extends AbstractOperateBenefitAccountProcessor {

    @Autowired
    private EquityAccountCoreService equityAccountCoreService;

    @Override
    public Integer getBenefitType() {
        return BenefitTypeEnum.DRAW_CODE.getId();
    }

    @Override
    public AccountDirectBenefitRespDTO directReceiveBenefit(AccountDirectBenefitReqDTO req, EquityBenefitDO benefitDO) {
        Assert.mustTrue(Objects.nonNull(req), ExceptionEnum.PARAMS_ERROR, "请求 req 为空");
        Assert.mustTrue(Objects.nonNull(benefitDO), ExceptionEnum.BENEFIT_IS_NOT_EXIST, "权益信息为空");

        AccountDistributeBenefitReqDTO distributeBenefitReq = DrawCodeAccountConvertEntity.createAccountDistributeBenefitReq(req);
        AccountDistributeBenefitRespDTO distributeBenefitRespDTO = equityAccountCoreService.distributeBenefit(distributeBenefitReq);
        boolean receiveSuccess = Objects.nonNull(distributeBenefitRespDTO) && CollectionUtils.isNotEmpty(distributeBenefitRespDTO.getBenefits());
        return AccountDirectBenefitRespDTO.builder()
                .receiveSuccess(receiveSuccess)
                .benefitId(benefitDO.getId())
                .build();
    }
}
