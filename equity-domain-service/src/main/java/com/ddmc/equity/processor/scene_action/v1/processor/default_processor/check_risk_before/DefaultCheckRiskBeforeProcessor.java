package com.ddmc.equity.processor.scene_action.v1.processor.default_processor.check_risk_before;

import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.risk.RiskCheckUtils;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.ddmc.equity.domain.entity.scene_action.SceneActionProcessConvertEntity;
import com.ddmc.equity.domain.service.core.BenefitCoreService;
import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.ddmc.equity.dto.customer.RiskControlInterceptRuleDTO;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.model.convert.BaseRequestConvert;
import com.ddmc.equity.processor.risk.RiskControlContextDTO;
import com.ddmc.equity.processor.risk.RiskControlFactory;
import com.ddmc.equity.processor.risk.RiskControlResponseDTO;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionBizDataDTO;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessContext;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessResult;
import com.ddmc.equity.processor.scene_action.v1.enums.SceneActionProcessTypeEnum;
import com.ddmc.equity.processor.scene_action.v1.processor.SceneActionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 默认检查前置风控处理器
 */
@Slf4j
@Component
public class DefaultCheckRiskBeforeProcessor implements SceneActionProcessor {

    @Resource
    private RiskControlFactory riskControlFactory;
    @Resource
    private BenefitCoreService benefitCoreService;

    @Override
    public SceneActionProcessTypeEnum getProcessTypeEnum() {
        return SceneActionProcessTypeEnum.CHECK_RISK_BEFORE;
    }

    @Override
    public SceneActionProcessResult process(SceneActionProcessContext context) {
        FullBenefitInfoDTO availableBenefit = context.getConsultAvailableBenefit();
        RiskControlContextDTO riskControlContextDTO = buildBeforeRiskControlContextDTO(context);
        // 风控结果，里面有统一打点并配置告警，无需重复打点
        RiskControlResponseDTO riskControlResponseDTO = riskControlFactory.process(riskControlContextDTO);

        // 风控结果里面有
        String riskControlResult = riskControlResponseDTO == null ? null : riskControlResponseDTO.getRiskControlResult();
        List<RiskControlInterceptRuleDTO> riskControlInterceptRuleDTOList = riskControlResponseDTO == null ?
                null : riskControlResponseDTO.getInterceptRuleDTOList();

        // 2.1 风控通过，奖励不变
        if (RiskCheckUtils.riskPass(riskControlResult)) {
            return SceneActionProcessResult.success(null);
        }

        // 2.2 风控不通过，不发奖励
        if (RiskCheckUtils.riskNoPrize(riskControlResult)) {
            List<UnableReceiveBenefitDTO> unableReceiveBenefitDTOList = SceneActionProcessConvertEntity
                    .buildUnableReceiveBenefitDTOList(availableBenefit, BenefitUnableReceiveReasonType.HIT_RISK.getCode());

            SceneActionBizDataDTO bizDataDTO = SceneActionBizDataDTO.builder()
                    .unableReceiveBenefitDTOList(unableReceiveBenefitDTOList)
                    .riskControlInterceptRuleDTOList(riskControlInterceptRuleDTOList)
                    .build();
            return SceneActionProcessResult.failure("风控不通过，不发奖励", bizDataDTO);
        }

        // 2.3 风控不通过。命中 [黑灰产] 用户，替换成黑灰产策略奖奖励；命中 [普通风控] 用户，替换成兜底策略奖励（如果本来抽中的是兜底策略奖励，则可以不替换）
        String sceneCode = context.getSceneCode();
        Long activityId = availableBenefit.getActivityId();
        FullBenefitInfoDTO riskReplaceBenefit = getRiskReplaceBenefit(sceneCode, activityId, riskControlResult);
        if (riskReplaceBenefit == null) {
            log.error("sceneAction checkRiskBefore riskReplaceBenefit isNull sceneCode={}, activityId={}, riskControlResult={}",
                    sceneCode, activityId, riskControlResult);
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_CHECK_RISK_BEFORE, "riskReplaceBenefitIsNull");

            SceneActionBizDataDTO bizDataDTO = SceneActionBizDataDTO.builder()
                    .riskControlInterceptRuleDTOList(riskControlInterceptRuleDTOList)
                    .build();
            return SceneActionProcessResult.failure("获取兜底/黑灰产策略奖励失败，", bizDataDTO);
        }

        CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_CHECK_RISK_BEFORE, "hasRiskReplaceBenefit");

        SceneActionBizDataDTO bizDataDTO = SceneActionBizDataDTO.builder()
                .riskControlInterceptRuleDTOList(riskControlInterceptRuleDTOList)
                .riskReplaceBenefit(riskReplaceBenefit)
                .build();
        return SceneActionProcessResult.success(bizDataDTO);
    }

    private FullBenefitInfoDTO getRiskReplaceBenefit(String sceneCode, Long activityId, String riskControlResult) {
        List<FullBenefitInfoDTO> riskReplaceBenefits = benefitCoreService.getFullBenefitInfosByStrategyType(sceneCode,
                activityId, RiskCheckUtils.getRiskNoPassUserReceiveStrategyType(riskControlResult));
        return CollectionUtils.isEmpty(riskReplaceBenefits) ? null : riskReplaceBenefits.get(0);
    }

    private RiskControlContextDTO buildBeforeRiskControlContextDTO(SceneActionProcessContext context) {
        FullBenefitInfoDTO availableBenefit = context.getConsultAvailableBenefit();

        BaseRequestDTO baseRequestDTO = BaseRequestConvert.INSTANCE.convertToBaseRequestDTO(context);
        return RiskControlContextDTO.builder()
                .baseRequestDTO(baseRequestDTO)
                .sceneCode(context.getBizParams().getRiskSceneCode())
                .activityId(String.valueOf(availableBenefit.getActivityId()))
                .activityName(availableBenefit.getActivityName())
                .benefitInfoDTO(availableBenefit)
                .riskSceneStrategyDataMap(context.getBizParams().getRiskSceneStrategyDataMap())
                .receiveExternalInfoDTO(context.getBizParams().getReceiveExternalInfoDTO())
                .build();
    }
}
