package com.ddmc.equity.processor.risk;

import com.ddmc.equity.common.interceptor.annotation.MonitorSpan;
import com.ddmc.equity.processor.risk.impl.DefaultSceneRiskControlProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/25 20:28
 * @description
 */
@Slf4j
@Component
public class RiskControlFactory {

    @Autowired
    private List<AbstractRiskControlProcessor> riskControlProcessors;
    @Autowired
    private DefaultSceneRiskControlProcessor defaultSceneRiskControlProcessor;

    @MonitorSpan(name = "riskControlFactory.process")
    public RiskControlResponseDTO process(RiskControlContextDTO context) {
        String sceneCode = context.getSceneCode();
        // sceneCode 为空，则无需调用风控
        if (StringUtils.isBlank(sceneCode)) {
            return null;
        }
        String userId = context.getBaseRequestDTO().getUserId();
        AbstractRiskControlProcessor processor = riskControlProcessors.stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getSceneCodes()) && e.getSceneCodes().contains(sceneCode))
                .findFirst()
                .orElse(defaultSceneRiskControlProcessor);
        if (Objects.isNull(processor)) {
            log.warn("riskControlFactory processor is null sceneCode={}, userId={}", sceneCode, userId);
            return null;
        }
        return processor.process(context);
    }
}
