package com.ddmc.equity.processor.consume_benefit.strategy;

import com.ddmc.equity.processor.consume_benefit.dto.ConsumeBenefitContext;
import com.ddmc.equity.processor.consume_benefit.dto.ConsumeBenefitResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/7/1 21:14
 * @description 抽象消耗权益策略类
 */
@Slf4j
public abstract class AbstractConsumeBenefitStrategy implements ConsumeBenefitStrategy {

    /**
     * 检查用户 ID
     *
     * @param context 消耗权益上下文
     * @return 校验结果，如果用户 ID 为空则返回失败结果，否则返回 null 表示校验通过
     */
    protected ConsumeBenefitResult checkUserId(ConsumeBenefitContext context) {
        if (StringUtils.isBlank(context.getUserId())) {
            log.warn("checkUserId userId is null");
            return ConsumeBenefitResult.paramsErrorFailure("用户 ID 为空", null);
        }
        return null;
    }

    /**
     * 检查基本参数
     * 验证消耗权益操作所需的基本参数：用户 ID、请求流水号、消耗权益数量
     *
     * @param context 消耗权益上下文
     * @return 校验结果，如果任一参数为空则返回失败结果，否则返回 null 表示校验通过
     */
    protected ConsumeBenefitResult checkParams(ConsumeBenefitContext context) {
        ConsumeBenefitResult result = checkUserId(context);
        if (result != null) {
            return result;
        }

        if (StringUtils.isBlank(context.getReqNo())) {
            log.warn("checkParams reqNo is null");
            return ConsumeBenefitResult.paramsErrorFailure("请求流水号为空", null);
        }

        if (StringUtils.isBlank(context.getConsumeBenefitAmount())) {
            log.warn("checkParams consumeBenefitAmount is null");
            return ConsumeBenefitResult.paramsErrorFailure("消耗权益数量为空", null);
        }

        return null;
    }

    /**
     * 检查确认操作参数
     * 验证权益确认扣减或释放操作所需的参数，包括基本参数校验和冻结日志 ID 校验。
     *
     * @param context 消耗权益上下文
     * @return 校验结果，如果任一参数为空则返回失败结果，否则返回 null 表示校验通过
     */
    protected ConsumeBenefitResult checkConfirmParams(ConsumeBenefitContext context) {
        ConsumeBenefitResult checkResult = checkParams(context);
        if (checkResult != null) {
            return checkResult;
        }

        String freezeLogId = context.getBizParams().getFreezeLogId();
        if (StringUtils.isBlank(freezeLogId)) {
            log.warn("checkFreezeLogId freezeLogId is null userId={}", context.getUserId());
            return ConsumeBenefitResult.paramsErrorFailure("冻结记录 ID 为空", null);
        }
        return null;
    }
}
