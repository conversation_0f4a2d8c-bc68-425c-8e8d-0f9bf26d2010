package com.ddmc.equity.processor.consume_benefit.strategy.impl;

import com.ddmc.equity.processor.consume_benefit.strategy.AbstractConsumeBenefitStrategy;
import com.ddmc.equity.processor.consume_benefit.dto.ConsumeBenefitContext;
import com.ddmc.equity.processor.consume_benefit.dto.ConsumeBenefitResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 默认消耗权益策略实现 - 当没有找到对应策略时使用
 */
@Slf4j
@Component
public class DefaultConsumeBenefitStrategy extends AbstractConsumeBenefitStrategy {

    @Override
    public Integer getSupportedConsumeBenefitType() {
        // 默认策略不支持特定类型，返回 null
        return null;
    }

    @Override
    public ConsumeBenefitResult checkConsumeBenefit(ConsumeBenefitContext context) {
        // 默认实现：直接返回成功
        return ConsumeBenefitResult.success(null);
    }

    @Override
    public ConsumeBenefitResult deductConsumeBenefit(ConsumeBenefitContext context) {
        // 默认实现：直接返回成功
        return ConsumeBenefitResult.success(null);
    }

    @Override
    public ConsumeBenefitResult confirmConsumeBenefit(ConsumeBenefitContext context) {
        // 默认实现：直接返回成功
        return ConsumeBenefitResult.success(null);
    }

    @Override
    public ConsumeBenefitResult releaseConsumeBenefit(ConsumeBenefitContext context) {
        // 默认实现：直接返回成功
        return ConsumeBenefitResult.success(null);
    }
}
