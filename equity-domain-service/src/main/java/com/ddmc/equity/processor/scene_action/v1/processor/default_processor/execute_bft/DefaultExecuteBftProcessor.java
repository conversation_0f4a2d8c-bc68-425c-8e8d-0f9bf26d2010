package com.ddmc.equity.processor.scene_action.v1.processor.default_processor.execute_bft;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.account.AccountStrategyContext;
import com.ddmc.equity.account.EquityAccountContext;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.ProcessStatus;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.exception.AdminExceptionBuilder;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.business.BenefitUtil;
import com.ddmc.equity.domain.converter.common.EquityAccountContextConverter;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcResponseExtDTO;
import com.ddmc.equity.domain.entity.common.BenefitOperateConvertEntity;
import com.ddmc.equity.domain.entity.scene_action.SceneActionProcessConvertEntity;
import com.ddmc.equity.domain.service.core.BenefitCoreService;
import com.ddmc.equity.dto.customer.SendBenefitSceneDTO;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.model.dto.ActivityCacheDto;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionBizDataDTO;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessContext;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessResult;
import com.ddmc.equity.processor.scene_action.v1.enums.SceneActionProcessTypeEnum;
import com.ddmc.equity.processor.scene_action.v1.processor.SceneActionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 默认执行权益（分布式锁、写记录、扣减频次库存、发放权益等）处理器
 */
@Slf4j
@Component
public class DefaultExecuteBftProcessor implements SceneActionProcessor {

    /**
     * 指定的场景调用发放优惠券需要关联外部 id
     */
    @Value("${equity.send.ticket.need.external.scenes:}")
    private Set<String> sendTicketNeedExternalScenes;

    @Resource
    private LocalCacheManager localCacheManager;
    @Resource
    private BenefitCoreService benefitCoreService;

    @Override
    public SceneActionProcessTypeEnum getProcessTypeEnum() {
        return SceneActionProcessTypeEnum.EXECUTE_BFT;
    }

    @Override
    public SceneActionProcessResult process(SceneActionProcessContext context) {
        // 1 创建领取权益上下文
        EquityAccountContext receiveContextDTO = buildReceiveContextDTO(context, sendTicketNeedExternalScenes);

        // 2 执行权益领取
        AccountStrategyContext accountStrategy = AccountStrategyContext.builderByBenefitType(receiveContextDTO.getBenefitType());
        ReceiveBenefitResDTO receiveBenefitResp = accountStrategy.sceneReceiveNew(receiveContextDTO);

        // 3 转换为场景处理结果
        return convertToSceneActionProcessResultAndDot(context, receiveContextDTO, receiveBenefitResp);
    }

    @Override
    public boolean shouldBreakAfterProcess(SceneActionProcessContext context, SceneActionProcessResult result) {
        // 执行权益不中断（不管结果是什么），继续执行下一个节点
        return false;
    }

    private EquityAccountContext buildReceiveContextDTO(SceneActionProcessContext context,
                                                        Set<String> sendTicketNeedExternalScenes) {
        FullBenefitInfoDTO receiveBenefit = context.getReceiveBenefitInfoDTO();

        // 设置具体发放的 sendAmount + balanceMoney
        String reqSendAmount = context.getBizParams().getSendAmount();
        BenefitOperateConvertEntity.fillReceiveBenefitSendAmount(receiveBenefit, reqSendAmount);

        EquityAccountContext receiveContext = EquityAccountContextConverter.INSTANCE.convertToEquityAccountContext(context);

        receiveContext.setSerialNumber(context.getReqNo());
        receiveContext.setCityCode(context.getCityNumber());
        receiveContext.setStationId(context.getStationId());
        receiveContext.setUid(context.getUserId());
        receiveContext.setOperator(context.getOperator());

        receiveContext.setSceneCode(context.getSceneCode());
        receiveContext.setActivityId(receiveBenefit.getActivityId());
        receiveContext.setExternalType(receiveBenefit.getExternalType());
        receiveContext.setExternalId(receiveBenefit.getExternalId());
        receiveContext.setActivityName(receiveBenefit.getActivityName());
        receiveContext.setStrategyId(receiveBenefit.getStrategyId());
        receiveContext.setStrategyExternalId(receiveBenefit.getStrategyExternalId());
        receiveContext.setBenefitGroupId(receiveBenefit.getBenefitGroupId());
        receiveContext.setBenefitId(receiveBenefit.getBenefitId());
        receiveContext.setBenefitName(receiveBenefit.getBenefitName());
        receiveContext.setBenefitType(receiveBenefit.getBenefitType());
        receiveContext.setEquityValue(receiveBenefit.getBenefitValue());
        receiveContext.setSendAmount(receiveBenefit.getSendAmount());
        receiveContext.setBalanceMoney(receiveBenefit.getBalanceMoney());

        // 设置使用活动 ID
        Integer externalType = context.getBizParams().getExternalType();
        String useExternalId = context.getBizParams().getUseExternalId();
        ActivityCacheDto useActivityCacheDTO = getUseActivityCacheDTO(externalType, useExternalId);
        receiveContext.setUseActivityId(useActivityCacheDTO == null ? null : useActivityCacheDTO.getId());

        // 设置消耗权益相关字段
        receiveContext.setConsumeBenefitType(context.getConsumeBenefitType());
        receiveContext.setConsumeBenefitAmount(context.getConsumeBenefitAmount());
        receiveContext.setConsumeBenefitFreezeLogId(context.getBizData().getConsumeBenefitFreezeLogId());

        // 设置领取时关联的外部信息
        receiveContext.setReceiveExternalInfoDTO(context.getBizParams().getReceiveExternalInfoDTO());

        // 设置 RPC 调用相关字段
        SendBenefitSceneDTO sendBenefitSceneDTO = context.getBizParams().getSendBenefitSceneDTO();
        receiveContext.setSendPointScene(sendBenefitSceneDTO == null ? null : sendBenefitSceneDTO.getSendPointScene());
        receiveContext.setSendPointDesc(sendBenefitSceneDTO == null ? null : sendBenefitSceneDTO.getSendPointDesc());
        receiveContext.setSendPointSource(sendBenefitSceneDTO == null ? null : sendBenefitSceneDTO.getSendPointSource());
        receiveContext.setSendBalanceScene(sendBenefitSceneDTO == null ? null : sendBenefitSceneDTO.getSendBalanceScene());
        receiveContext.setSendBalanceDesc(sendBenefitSceneDTO == null ? null : sendBenefitSceneDTO.getSendBalanceDesc());
        receiveContext.setSendTicketScene(sendBenefitSceneDTO == null ? null : sendBenefitSceneDTO.getSendTicketScene());
        receiveContext.setSendVipDaysScene(sendBenefitSceneDTO == null ? null : sendBenefitSceneDTO.getSendVipDaysScene());
        receiveContext.setRpcReqCustomMap(context.getBizParams().getRpcReqCustomMap());
        // 指定的场景调用发放优惠券需要关联外部 id
        if (CollectionUtils.isNotEmpty(sendTicketNeedExternalScenes) && sendTicketNeedExternalScenes.contains(context.getSceneCode())) {
            receiveContext.setSendTicketActivity(receiveBenefit.getExternalId());
            receiveContext.setSendTicketPrize(receiveBenefit.getStrategyExternalId());
        }
        return receiveContext;
    }

    private ActivityCacheDto getUseActivityCacheDTO(Integer externalType, String useExternalId) {
        if (Objects.isNull(externalType) || StringUtils.isBlank(useExternalId)) {
            return null;
        }
        ActivityCacheDto useActivityCacheDTO = localCacheManager.getActivityCacheByExternal(useExternalId, externalType);
        Assert.notNull(useActivityCacheDTO, "使用活动不存在～");
        return useActivityCacheDTO;
    }

    private SceneActionProcessResult convertToSceneActionProcessResultAndDot(SceneActionProcessContext context,
                                                                             EquityAccountContext receiveContextDTO,
                                                                             ReceiveBenefitResDTO receiveBenefitResp) {
        String userId = context.getUserId();
        String sceneCode = context.getSceneCode();
        String reqNo = context.getReqNo();
        Integer receiveResultStatus = Optional.ofNullable(receiveBenefitResp)
                .map(ReceiveBenefitResDTO::getStatusEnum)
                .map(StatusEnum::getCode)
                .orElse(null);
        String receiveResultMessage = Optional.ofNullable(receiveBenefitResp)
                .map(ReceiveBenefitResDTO::getMsg)
                .orElse(null);
        Long accountDetailId = Optional.ofNullable(receiveBenefitResp)
                .map(ReceiveBenefitResDTO::getAccountDetailId)
                .orElse(null);
        UniversalAccountRpcResponseExtDTO rpcResponseExtDTO = Optional.ofNullable(receiveBenefitResp)
                .map(ReceiveBenefitResDTO::getRpcResponseExtDTO)
                .orElse(null);


        // 如果领取时幂等，直接返回原来领取的结果，对应权益有可能发生变更所以需要替换掉 receiveBenefit
        FullBenefitInfoDTO receiveBenefitBefore = context.getReceiveBenefitInfoDTO();
        FullBenefitInfoDTO receiveBenefitAfter = getActualReceiveBenefit(receiveBenefitBefore, receiveContextDTO);
        SceneActionBizDataDTO bizDataDTO = SceneActionBizDataDTO.builder()
                .actualReceiveBenefit(receiveBenefitAfter)
                .build();

        if (hasFrozenRecordReqNo(receiveContextDTO, reqNo)) {
            // ******** 讨论结果，因为权益纬度频次限制有冻结功能的特殊处理逻辑
            // 处理已存在的冻结记录，调用下游的流水号也会变成原来已存在的冻结记录请求流水号
            log.info("sceneAction executeBft hasFrozenRecordReqNo userId={}, sceneCode={}, reqNo={}" +
                            ", receiveBenefitResp={}, receiveContextDTO={}",
                    userId, sceneCode, reqNo, JSON.toJSONString(receiveBenefitResp),
                    JSON.toJSONString(receiveContextDTO));
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_EXECUTE_BFT, "hasFrozenRecordReqNo");

            bizDataDTO.setFrozenRecordReqNo(receiveContextDTO.getSerialNumber());
            bizDataDTO.setFrozenRecordConsumeBenefitFreezeLogId(receiveContextDTO.getConsumeBenefitFreezeLogId());
        }

        if (StatusEnum.SUCCESS.getCode().equals(receiveResultStatus)) {
            // 领取成功
            bizDataDTO.setExecuteBftResultStatus(ProcessStatus.SUCCESS);
            bizDataDTO.setAccountDetailId(accountDetailId);
            bizDataDTO.setRpcResponseExtDTO(rpcResponseExtDTO);
            return SceneActionProcessResult.success(bizDataDTO);
        } else if (StatusEnum.FAIL.getCode().equals(receiveResultStatus)) {
            // 领取失败
            log.warn("sceneAction executeBft failure userId={}, sceneCode={}, reqNo={}, receiveBenefitResp={}",
                    userId, sceneCode, reqNo, JSON.toJSONString(receiveBenefitResp));
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_EXECUTE_BFT, "receiveFailure");

            List<UnableReceiveBenefitDTO> unableReceiveBenefitDTOList = SceneActionProcessConvertEntity
                    .buildUnableReceiveBenefitDTOList(receiveBenefitAfter,
                            BenefitUnableReceiveReasonType.EXECUTE_BFT_FAILURE.getCode());
            bizDataDTO.setExecuteBftResultStatus(ProcessStatus.FAILURE);
            bizDataDTO.setUnableReceiveBenefitDTOList(unableReceiveBenefitDTOList);

            String failureMessage = StringUtils.defaultIfBlank(receiveResultMessage, "执行权益领取失败");
            return SceneActionProcessResult.failure(failureMessage, bizDataDTO);
        } else {
            // 领取处理中
            log.warn("sceneAction executeBft processing userId={}, sceneCode={}, reqNo={}, receiveBenefitResp={}",
                    userId, sceneCode, reqNo, JSON.toJSONString(receiveBenefitResp));
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_EXECUTE_BFT, "receiveProcessing");

            List<UnableReceiveBenefitDTO> unableReceiveBenefitDTOList = SceneActionProcessConvertEntity
                    .buildUnableReceiveBenefitDTOList(receiveBenefitAfter,
                            BenefitUnableReceiveReasonType.EXECUTE_BFT_PROCESSING.getCode());
            bizDataDTO.setExecuteBftResultStatus(ProcessStatus.PROCESSING);
            bizDataDTO.setUnableReceiveBenefitDTOList(unableReceiveBenefitDTOList);

            String processingMessage = StringUtils.defaultIfBlank(receiveResultMessage, "执行权益领取处理中");
            return SceneActionProcessResult.failure(processingMessage, bizDataDTO);
        }
    }

    private FullBenefitInfoDTO getActualReceiveBenefit(FullBenefitInfoDTO actualReceiveBenefitBefore,
                                                       EquityAccountContext receiveContext) {
        // 如果领取时幂等，直接返回原来领取的结果，对应权益有可能发生变更所以需要替换掉 balanceMoney
        BenefitOperateConvertEntity.changeReceiveBenefitSendAmount(actualReceiveBenefitBefore, receiveContext);

        Long resultActivityId = receiveContext.getActivityId();
        Long resultStrategyId = receiveContext.getStrategyId();
        Long resultBenefitGroupId = receiveContext.getBenefitGroupId();
        Long resultBenefitId = receiveContext.getBenefitId();
        if (resultActivityId == null || resultStrategyId == null || resultBenefitGroupId == null || resultBenefitId == null) {
            return actualReceiveBenefitBefore;
        }

        String benefitUniBefore = BenefitUtil.getBenefitUni(actualReceiveBenefitBefore);
        String resultBenefitUni = BenefitUtil.getBenefitUni(resultStrategyId, resultBenefitGroupId, resultBenefitId);
        if (StringUtils.equals(benefitUniBefore, resultBenefitUni)) {
            return actualReceiveBenefitBefore;
        }

        FullBenefitInfoDTO resultBenefit = benefitCoreService.getOneFullBenefitInfoDTO(resultActivityId,
                resultStrategyId, resultBenefitGroupId, resultBenefitId);
        if (Objects.isNull(resultBenefit)) {
            log.warn("getActualReceiveBenefit resultBenefit isNull resultBenefitUni={}, actualReceiveBenefitBefore={}",
                    resultBenefitUni, JSON.toJSONString(actualReceiveBenefitBefore));
            throw AdminExceptionBuilder.build(ExceptionEnum.SCENE_RECEIVE_BENEFIT_IS_NULL, "领取奖励信息为空");
        }

        // 如果领取时幂等，直接返回原来领取的结果，对应权益有可能发生变更所以需要替换掉 balanceMoney
        BenefitOperateConvertEntity.changeReceiveBenefitSendAmount(resultBenefit, receiveContext);
        return resultBenefit;
    }

    private boolean hasFrozenRecordReqNo(EquityAccountContext receiveContextDTO, String reqNo) {
        return receiveContextDTO.isHasFreezeReceiveLimitProcessingRecord() &&
                receiveContextDTO.getFreezeReceiveLimitResultId() != null &&
                !StringUtils.equals(reqNo, receiveContextDTO.getSerialNumber());
    }
}
