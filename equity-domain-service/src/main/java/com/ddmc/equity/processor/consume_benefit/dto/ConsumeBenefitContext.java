package com.ddmc.equity.processor.consume_benefit.dto;

import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessContext;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 消耗权益上下文 - 权益策略执行时的上下文信息
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class ConsumeBenefitContext {

    /**
     * 用户 ID
     */
    private String userId;

    /**
     * 消耗权益类型
     */
    private Integer consumeBenefitType;

    /**
     * 消耗权益数量
     */
    private String consumeBenefitAmount;

    /**
     * 请求流水号
     */
    private String reqNo;

    /**
     * 请求参数
     */
    @Builder.Default
    @NotNull
    private ConsumeBenefitBizParamsDTO bizParams = ConsumeBenefitBizParamsDTO.builder().build();

    /**
     * 创建消耗权益上下文
     *
     * @param userId               用户 ID
     * @param consumeBenefitType   消耗权益类型
     * @param consumeBenefitAmount 消耗权益数量
     * @param reqNo                请求流水号
     * @param bizParams            请求参数
     * @return 消耗权益上下文
     */
    public static ConsumeBenefitContext of(String userId, Integer consumeBenefitType, String consumeBenefitAmount,
                                           String reqNo, ConsumeBenefitBizParamsDTO bizParams) {
        return ConsumeBenefitContext.builder()
                .userId(userId)
                .consumeBenefitType(consumeBenefitType)
                .consumeBenefitAmount(consumeBenefitAmount)
                .reqNo(reqNo)
                .bizParams(bizParams)
                .build();
    }

    /**
     * 获取消耗权益数量（整数类型）
     *
     * @return 消耗权益数量整数值
     * @throws IllegalArgumentException 当消耗权益数量小于等于 0 时抛出异常
     */
    public Integer getConsumeBenefitAmountInt() {
        Integer consumeBenefitAmountInt = NumberUtils.convertToInteger(consumeBenefitAmount);
        Assert.isTrue(consumeBenefitAmountInt > 0, "消耗权益数量必须大于 0");
        return consumeBenefitAmountInt;
    }

    /**
     * 创建消耗权益上下文
     *
     * @param sceneActionProcessContext 场景动作流程上下文
     * @return 消耗权益上下文
     */
    public static ConsumeBenefitContext buildConsumeBenefitContext(SceneActionProcessContext sceneActionProcessContext) {
        ConsumeBenefitBizParamsDTO bizParamsDTO = ConsumeBenefitBizParamsDTO.builder()
                .consumeBenefitSceneDTO(sceneActionProcessContext.getBizParams().getConsumeBenefitSceneDTO())
                .freezeLogId(sceneActionProcessContext.getBizData().getConsumeBenefitFreezeLogId())
                .build();

        return ConsumeBenefitContext.of(sceneActionProcessContext.getUserId(),
                sceneActionProcessContext.getConsumeBenefitType(), sceneActionProcessContext.getConsumeBenefitAmount(),
                sceneActionProcessContext.getReqNo(), bizParamsDTO);
    }
}
