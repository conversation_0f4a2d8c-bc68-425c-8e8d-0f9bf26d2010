package com.ddmc.equity.processor.risk.impl;

import com.alibaba.fastjson.JSONObject;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.model.dto.risk.ShareRedPacketRiskStrategyDataDTO;
import com.ddmc.equity.processor.risk.AbstractRiskControlProcessor;
import com.ddmc.equity.processor.risk.RiskControlContextDTO;
import com.ddmc.equity.processor.risk.RiskControlSceneCodeTypes;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/25 20:07
 * @description
 */
@Slf4j
@Service
public class ShareRedPacketRiskControlProcessor extends AbstractRiskControlProcessor {

    @Override
    public List<String> getSceneCodes() {
        return Lists.newArrayList(RiskControlSceneCodeTypes.ACTIVITY_BAG_LAUNCH_FRONT,
                RiskControlSceneCodeTypes.ACTIVITY_BAG_LAUNCH_AFTER);
    }

    @Override
    public JSONObject buildSceneStrategyData(RiskControlContextDTO context) {
        FullBenefitInfoDTO benefitInfoDTO = context.getBenefitInfoDTO();
        ShareRedPacketRiskStrategyDataDTO riskStrategyDataDTO = ShareRedPacketRiskStrategyDataDTO.builder()
                .activityId(context.getActivityId())
                .activityName(context.getActivityName())
                .ticketId(Optional.ofNullable(benefitInfoDTO).map(FullBenefitInfoDTO::getBenefitValue).orElse(null))
                .build();
        return parseToJsonObject(riskStrategyDataDTO);
    }
}
