package com.ddmc.equity.processor.operate_benefit_account.impl;

import com.ddmc.equity.account.DirectAccountContext;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountDTO;
import com.ddmc.equity.domain.entity.common.BenefitOperateConvertEntity;
import com.ddmc.equity.domain.entity.common.DirectAccountContextEntity;
import com.ddmc.equity.domain.service.core.EquityAccountCoreService;
import com.ddmc.equity.domain.service.core.UniversalAccountCoreService;
import com.ddmc.equity.dto.customer.account.AccountDirectBenefitReqDTO;
import com.ddmc.equity.dto.customer.account.AccountDirectBenefitRespDTO;
import com.ddmc.equity.dto.customer.account.UniversalUseBenefitTypeDTO;
import com.ddmc.equity.infra.cache.local.LocalCacheManager;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;
import com.ddmc.equity.model.dto.ActivityCacheDto;
import com.ddmc.equity.processor.operate_benefit_account.OperateBenefitAccountProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/7/31 15:15
 * @description
 */
@Slf4j
public abstract class AbstractOperateBenefitAccountProcessor implements OperateBenefitAccountProcessor {

    @Autowired
    private LocalCacheManager localCacheManager;
    @Autowired
    private UniversalAccountCoreService universalAccountCoreService;
    @Autowired
    private EquityAccountCoreService equityAccountCoreService;

    @Override
    public AccountDirectBenefitRespDTO directReceiveBenefit(AccountDirectBenefitReqDTO req, EquityBenefitDO benefitDO) {
        Assert.mustTrue(Objects.nonNull(req), ExceptionEnum.PARAMS_ERROR, "请求 req 为空");
        Assert.mustTrue(Objects.nonNull(benefitDO), ExceptionEnum.BENEFIT_IS_NOT_EXIST, "权益信息为空");

        // 使用时的外部活动 id => 权益活动 id
        ActivityCacheDto useActivityCacheDTO = getUseActivityCacheDTO(req.getExternalType(), req.getExternalId());
        if (Objects.nonNull(useActivityCacheDTO)) {
            req.setUseActivityId(useActivityCacheDTO.getId());
        }

        // 组装上下文，直接发放权益
        Long benefitId = benefitDO.getId();
        DirectAccountContext directAccountContext = DirectAccountContextEntity.convertDirectAccountContext(req, benefitDO);
        ReceiveBenefitResDTO receiveResp = equityAccountCoreService.receiveDirectBenefit(directAccountContext);
        return AccountDirectBenefitRespDTO.builder()
                .receiveSuccess(Objects.nonNull(receiveResp) && Objects.equals(receiveResp.getStatusEnum(), StatusEnum.SUCCESS))
                .benefitId(benefitId)
                .rpcResponseExtDTO(BenefitOperateConvertEntity.convertToBenefitAccountRpcResponseExtDTO(receiveResp))
                .build();
    }

    @Override
    public List<UniversalAccountDTO> getUserAccounts(String userId, Long useActivityId, List<Integer> benefitTypes) {
        Assert.mustTrue(Objects.nonNull(userId), ExceptionEnum.PARAMS_ERROR, "用户 id 为空");
        Assert.mustTrue(CollectionUtils.isNotEmpty(benefitTypes), ExceptionEnum.PARAMS_ERROR, "权益类型列表为空");

        return universalAccountCoreService.queryUniversalAccountDTOList(userId, useActivityId, benefitTypes);
    }

    @Override
    public Boolean deductAccount(UniversalUseBenefitTypeDTO req) {
        Assert.mustTrue(Objects.nonNull(req), ExceptionEnum.PARAMS_ERROR, "请求 req 为空");

        // 使用时的外部活动 id => 权益活动 id
        ActivityCacheDto useActivityCacheDTO = getUseActivityCacheDTO(req.getExternalType(), req.getExternalId());
        if (Objects.nonNull(useActivityCacheDTO)) {
            req.setUseActivityId(useActivityCacheDTO.getId());
        }

        // 组装上下文，扣减权益
        DirectAccountContext directAccountContext = DirectAccountContextEntity.convertUseAccountContext(req);
        return equityAccountCoreService.universalUseBenefitType(directAccountContext);
    }

    private ActivityCacheDto getUseActivityCacheDTO(Integer externalType, String useExternalId) {
        if (Objects.isNull(externalType) || StringUtils.isBlank(useExternalId)) {
            return null;
        }
        ActivityCacheDto useActivityCacheDTO = localCacheManager.getActivityCacheByExternal(useExternalId, externalType);
        Assert.notNull(useActivityCacheDTO, ExceptionEnum.ACTIVITY_IS_NOT_EXIST.getCode(), "使用活动不存在～");
        return useActivityCacheDTO;
    }
}
