package com.ddmc.equity.processor.risk;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/25 16:53
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class RiskControlConfigDTO {

    @ApiModelProperty(value = "调用风控 sceneCode")
    private String sceneCode;

    @ApiModelProperty(value = "调用风控 appId")
    private String appId;

    @ApiModelProperty("调用风控 token")
    private String token;
}
