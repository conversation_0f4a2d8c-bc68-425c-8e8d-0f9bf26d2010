package com.ddmc.equity.processor.scene_action.v1.enums;

import com.ddmc.equity.enums.SceneActionConsultFlowType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 场景动作流程类型枚举
 */
@AllArgsConstructor
@Getter
public enum SceneActionProcessTypeEnum {

    FLOW_GLOBAL("FLOW_GLOBAL", "全局流程异常标识，用于标识无法确定具体异常步骤的流程级异常"),
    CONSULT_ACTIVITY("CONSULT_ACTIVITY", "咨询活动"),
    CHECK_CONSUME_BFT("CHECK_CONSUME_BFT", "检查用户可用权益（消耗权益）数量是否足够"),
    CHECK_RISK_BEFORE("CHECK_RISK_BEFORE", "检查前置风控"),
    DEDUCT_CONSUME_BFT("DEDUCT_CONSUME_BFT", "扣减用户可用权益（消耗权益）数量（冻结或直接扣减）"),
    EXECUTE_BFT("EXECUTE_BFT", "执行权益（分布式锁、写记录、扣减频次库存、发放权益等）"),
    CONFIRM_CONSUME_BFT("CONFIRM_CONSUME_BFT", "确认用户可用权益（消耗权益）数量（成功时确认扣减，失败时释放）"),
    REPORT_RISK_AFTER("REPORT_RISK_AFTER", "上报后置风控"),
    ;

    private final String code;
    private final String desc;

    /**
     * 咨询流程定义
     */
    private static final List<SceneActionProcessTypeEnum> CONSULT_FLOWS = Collections.singletonList(
            CONSULT_ACTIVITY         // 1 咨询活动
    );

    /**
     * 咨询流程定义，用于触发时的咨询流程
     */
    private static final List<SceneActionProcessTypeEnum> CONSULT_FLOWS_FOR_TRIGGER = Arrays.asList(
            CONSULT_ACTIVITY,         // 1 咨询活动
            CHECK_CONSUME_BFT         // 2 检查用户可用权益（消耗权益）数量是否足够
    );

    /**
     * 领取流程定义
     */
    private static final List<SceneActionProcessTypeEnum> RECEIVE_FLOWS = Arrays.asList(
            CONSULT_ACTIVITY,         // 1 咨询活动
            CHECK_CONSUME_BFT,        // 2 检查用户可用权益（消耗权益）数量是否足够
            CHECK_RISK_BEFORE,        // 3 检查前置风控
            DEDUCT_CONSUME_BFT,       // 4 扣减用户可用权益（消耗权益）数量（冻结或直接扣减）
            EXECUTE_BFT,              // 5 执行权益（分布式锁、写记录、扣减频次库存、发放权益等）
            CONFIRM_CONSUME_BFT,      // 6 确认用户可用权益（消耗权益）数量（成功时确认扣减，失败时释放）
            REPORT_RISK_AFTER         // 7 上报后置风控
    );

    /**
     * 获取咨询流程定义
     *
     * @return 咨询流程定义的副本
     */
    public static List<SceneActionProcessTypeEnum> getConsultFlows(String consultFlowType) {
        if (StringUtils.equals(consultFlowType, SceneActionConsultFlowType.TRIGGER.getCode())) {
            return new ArrayList<>(CONSULT_FLOWS_FOR_TRIGGER);
        }

        return new ArrayList<>(CONSULT_FLOWS);
    }

    /**
     * 获取领取流程定义
     *
     * @return 领取流程定义的副本
     */
    public static List<SceneActionProcessTypeEnum> getReceiveFlows() {
        return new ArrayList<>(RECEIVE_FLOWS);
    }
}
