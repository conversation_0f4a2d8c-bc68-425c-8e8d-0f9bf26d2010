package com.ddmc.equity.processor.risk.impl;

import com.alibaba.fastjson.JSONObject;
import com.ddmc.equity.common.interceptor.annotation.MonitorSpan;
import com.ddmc.equity.common.util.risk.RiskCheckUtils;
import com.ddmc.equity.model.dto.risk.CnyStreetBeforeRiskStrategyDataDTO;
import com.ddmc.equity.processor.risk.AbstractRiskControlProcessor;
import com.ddmc.equity.processor.risk.RiskControlContextDTO;
import com.ddmc.equity.processor.risk.RiskControlSceneCodeTypes;
import com.ddmc.risk.engine.vo.ResponseVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/25 20:07
 * @description
 */
@Slf4j
@Service
public class CnyStreetBeforeRiskControlProcessor extends AbstractRiskControlProcessor {

    @Override
    public List<String> getSceneCodes() {
        return Lists.newArrayList(RiskControlSceneCodeTypes.CNY_CHEST_FRONT, RiskControlSceneCodeTypes.CNY_BUSINESS_FRONT);
    }

    @Override
    public JSONObject buildSceneStrategyData(RiskControlContextDTO context) {
        CnyStreetBeforeRiskStrategyDataDTO riskStrategyDataDTO = CnyStreetBeforeRiskStrategyDataDTO.builder()
                .activityId(context.getActivityId())
                .activityName(context.getActivityName())
                .build();
        return parseToJsonObject(riskStrategyDataDTO);
    }

    @Override
    @MonitorSpan(name = "cnyStreetBefore.handleRiskResponse")
    public String handleRiskResponse(ResponseVO riskResponse) {
        // cny 领取前调用风控不通过，发黑灰产奖励
        if (RiskCheckUtils.rickException(riskResponse)) {
            return RiskCheckUtils.RISK_EXCEPTION;
        }
        if (RiskCheckUtils.riskPass(riskResponse)) {
            return RiskCheckUtils.RISK_PASS;
        }
        return RiskCheckUtils.RISK_BLACK_MARKET;
    }
}
