package com.ddmc.equity.processor.risk;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.csoss.monitor.sdk.resource.Status;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.interceptor.annotation.MonitorSpan;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.common.util.risk.RiskCheckUtils;
import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.ddmc.equity.dto.customer.RiskControlInterceptRuleDTO;
import com.ddmc.equity.infra.rpc.risk.RiskEngineProxy;
import com.ddmc.equity.infra.rpc.user.UserProxy;
import com.ddmc.equity.model.dto.risk.CommonRiskStrategyDataDTO;
import com.ddmc.risk.engine.dto.RequestDto;
import com.ddmc.risk.engine.vo.ResponseVO;
import com.ddmc.risk.engine.vo.RuleResponseVO;
import com.ddmc.usercenter.open.entity.dto.UserDTO;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/25 15:20
 * @description
 */
@Slf4j
public abstract class AbstractRiskControlProcessor {

    /**
     * 调用风控降级场景 codes
     */
    @Value("${equity.risk.control.degrade.scene.codes:}")
    private Set<String> riskControlDegradeSceneCodes;
    /**
     * 调用风控配置
     */
    @ApolloJsonValue("${equity.risk.control.configs:[\n" +
            "    {\"sceneCode\":\"cny_chest_front\",\"appId\":\"cny_chest_front\",\"token\":\"8e8fff73cd564f9d965b6e9fe60b592d\"},\n" +
            "    {\"sceneCode\":\"cny_chest_after\",\"appId\":\"cny_chest_after\",\"token\":\"81f4231eaed447be9e59c289ae93c036\"},\n" +
            "    {\"sceneCode\":\"cny_business_front\",\"appId\":\"cny_business_front\",\"token\":\"1f025bc5aab748d694ec24aeb4020c4e\"},\n" +
            "    {\"sceneCode\":\"cny_business_after\",\"appId\":\"cny_business_after\",\"token\":\"39d59d1fc4aa4c599e3ff82511920dc2\"}\n" +
            "]}")
    private List<RiskControlConfigDTO> riskControlConfigs;

    @Autowired
    private UserProxy userProxy;
    @Autowired
    private RiskEngineProxy riskEngineProxy;

    /**
     * 获取调用风控 sceneCodes。通过 sceneCode 匹配具体使用哪个实现类
     *
     * @return 调用风控 sceneCodes
     */
    protected abstract List<String> getSceneCodes();

    /**
     * 构建调用风控 request
     *
     * @param context           上下文
     * @param riskControlConfig 调用风控配置
     * @return 调用风控 request
     */
    @MonitorSpan(name = "buildRiskRequest")
    public RequestDto buildRiskRequest(RiskControlContextDTO context, RiskControlConfigDTO riskControlConfig) {
        RequestDto request = new RequestDto();
        request.setSceneCode(riskControlConfig.getSceneCode());
        request.setAppId(riskControlConfig.getAppId());
        request.setToken(riskControlConfig.getToken());
        request.setRiskStrategyData(buildStrategyData(context));
        return request;
    }

    /**
     * 构建调用风控 risk_strategy_data
     *
     * @param context 上下文
     * @return 调用风控 risk_strategy_data
     */
    protected JSONObject buildStrategyData(RiskControlContextDTO context) {
        JSONObject strategyData = new JSONObject();
        // 通用参数
        JSONObject baseStrategyData = buildCommonStrategyData(context.getBaseRequestDTO());
        if (Objects.nonNull(baseStrategyData)) {
            strategyData.putAll(baseStrategyData);
        }
        // 场景特殊参数
        JSONObject sceneStrategyData = buildSceneStrategyData(context);
        if (Objects.nonNull(sceneStrategyData)) {
            strategyData.putAll(sceneStrategyData);
        }
        return strategyData;
    }

    /**
     * 构建调用风控通用 risk_strategy_data
     *
     * @param baseRequestDTO 基础请求
     * @return 调用风控通用 risk_strategy_data
     */
    protected JSONObject buildCommonStrategyData(BaseRequestDTO baseRequestDTO) {
        // 如果请求参数中未携带手机号，需要调用用户接口获取用户的手机号
        if (StringUtils.isBlank(baseRequestDTO.getMobile())) {
            AbstractRiskControlProcessor self = (AbstractRiskControlProcessor) AopContext.currentProxy();
            String userMobile = self.getUserMobile(baseRequestDTO.getUserId());
            baseRequestDTO.setMobile(userMobile);
        }
        CommonRiskStrategyDataDTO commonRiskStrategyDataDTO = CommonRiskStrategyDataDTO.builder()
                .userId(baseRequestDTO.getUserId())
                .userMobile(StringUtils.defaultIfBlank(baseRequestDTO.getMobile(), StringUtils.EMPTY))
                .headers(baseRequestDTO.getHeaders())
                .cityNumber(baseRequestDTO.getCityNumber())
                .deviceToken(baseRequestDTO.getDeviceToken())
                .build();
        return parseToJsonObject(commonRiskStrategyDataDTO);
    }

    /**
     * 构建调用风控场景特殊 risk_strategy_data
     *
     * @param context 上下文
     * @return 调用风控场景特殊 risk_strategy_data
     */
    protected JSONObject buildSceneStrategyData(RiskControlContextDTO context) {
        return null;
    }

    /**
     * 调用风控结果处理。默认无须关注黑产用户，只需返回通过/不通过
     *
     * @param riskResponse 调用风控结果
     * @return 风控结果。pass，通过；no_pass，风控不通过；black_user，黑产用户；
     */
    @MonitorSpan(name = "handleRiskResponse")
    protected String handleRiskResponse(ResponseVO riskResponse) {
        if (RiskCheckUtils.rickException(riskResponse)) {
            return RiskCheckUtils.RISK_EXCEPTION;
        }
        if (RiskCheckUtils.riskPass(riskResponse)) {
            return RiskCheckUtils.RISK_PASS;
        }
        // 风控不通过，不发奖励
        return RiskCheckUtils.RISK_NO_PRIZE;
    }

    /**
     * 调用风控接口
     *
     * @param context 上下文
     * @return 风控处理响应结果 DTO
     */
    protected RiskControlResponseDTO process(RiskControlContextDTO context) {
        String sceneCode = context.getSceneCode();
        // 1 调用风控开关，对于关闭的场景直接返回成功
        if (CollectionUtils.isNotEmpty(riskControlDegradeSceneCodes) && riskControlDegradeSceneCodes.contains(sceneCode)) {
            return RiskControlResponseDTO.builder().riskControlResult(RiskCheckUtils.RISK_PASS).build();
        }

        // 2 获取调用风控配置，如果为空，则直接返回成功
        RiskControlConfigDTO riskControlConfig = getRiskControlConfig(sceneCode);
        if (Objects.isNull(riskControlConfig)) {
            log.warn("riskControlFactory process riskControlConfig is null sceneCode={}", sceneCode);
            return RiskControlResponseDTO.builder().riskControlResult(RiskCheckUtils.RISK_PASS).build();
        }

        // 3 构建调用风控参数
        AbstractRiskControlProcessor self = (AbstractRiskControlProcessor) AopContext.currentProxy();
        RequestDto riskRequest = self.buildRiskRequest(context, riskControlConfig);

        // 4 调用风控接口
        ResponseVO riskResponse = riskEngineProxy.riskRule(riskRequest);

        // 5 调用风控结果处理
        String riskControlResult = self.handleRiskResponse(riskResponse);

        // 6 提取规则列表
        List<RiskControlInterceptRuleDTO> interceptRuleDTOList = extractInterceptRuleDTOList(riskResponse);

        // 7 构建返回结果
        RiskControlResponseDTO riskControlResponseDTO = RiskControlResponseDTO.builder()
                .riskControlResult(riskControlResult)
                .interceptRuleDTOList(interceptRuleDTOList)
                .build();

        // 结果打点
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("userId", Optional.ofNullable(context.getBaseRequestDTO()).map(BaseRequestDTO::getUserId).orElse(null));
        dataMap.put("sceneCode", sceneCode);
        dataMap.put("activityId", context.getActivityId());
        dataMap.put("riskControlResponseDTO", riskControlResponseDTO);
        CsossUtils.logEvent(MonitorConstants.RISK_CONTROL_RESULT + "_" + sceneCode, riskControlResult, Status.SUCCESS, dataMap);
        return riskControlResponseDTO;
    }

    @MonitorSpan(name = "getUserMobile")
    protected String getUserMobile(String userId) {
        UserDTO userDTO = userProxy.queryById(userId);
        return Optional.ofNullable(userDTO).map(UserDTO::getMobile).orElse(null);
    }

    private RiskControlConfigDTO getRiskControlConfig(String sceneCode) {
        Map<String, RiskControlConfigDTO> riskControlConfigMap = riskControlConfigs.stream()
                .collect(Collectors.toMap(RiskControlConfigDTO::getSceneCode, Function.identity(), (var1, var2) -> var1));
        return riskControlConfigMap.get(sceneCode);
    }

    protected JSONObject parseToJsonObject(Object riskStrategyDataDTO) {
        // RiskStrategyDataDTO 使用了 fastjson 别名，所以只能用 fastjson 转换
        return JSON.parseObject(JSON.toJSONString(riskStrategyDataDTO));
    }

    /**
     * 从风控响应中提取风控拦截规则列表
     *
     * @param riskResponse 风控响应
     * @return 风控拦截规则列表
     */
    private List<RiskControlInterceptRuleDTO> extractInterceptRuleDTOList(ResponseVO riskResponse) {
        if (Objects.isNull(riskResponse) || Objects.isNull(riskResponse.getData())) {
            return null;
        }
        List<RuleResponseVO> ruleList = riskResponse.getData().getRuleList();
        if (CollectionUtils.isEmpty(ruleList)) {
            return null;
        }

        return ruleList.stream().map(rule -> RiskControlInterceptRuleDTO.builder()
                .ruleCode(rule.getRuleCode())
                .reasonCode(rule.getReasonCode())
                .build()
        ).collect(Collectors.toList());
    }
}
