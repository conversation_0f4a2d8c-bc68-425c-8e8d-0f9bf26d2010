package com.ddmc.equity.processor.consume_benefit.dto;

import com.ddmc.equity.dto.customer.ConsumeBenefitSceneDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/7/18 10:25
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class ConsumeBenefitBizParamsDTO {

    // ================ 消耗权益参数 ================
    /**
     * 消耗权益时调用下游透传的场景相关参数
     */
    private ConsumeBenefitSceneDTO consumeBenefitSceneDTO;

    // ================ 冻结参数 ================
    /**
     * 冻结记录 ID
     */
    private String freezeLogId;
}
