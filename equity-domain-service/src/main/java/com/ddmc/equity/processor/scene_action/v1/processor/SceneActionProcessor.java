package com.ddmc.equity.processor.scene_action.v1.processor;

import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessContext;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessResult;
import com.ddmc.equity.processor.scene_action.v1.enums.SceneActionProcessTypeEnum;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 场景动作流程节点处理器接口
 */
public interface SceneActionProcessor {

    /**
     * 获取活动场景码
     *
     * @return 活动场景码
     */
    default String getSceneCode() {
        return "default";
    }

    /**
     * 获取处理器类型
     *
     * @return 处理器类型代码
     */
    SceneActionProcessTypeEnum getProcessTypeEnum();

    /**
     * 是否启用该节点
     *
     * @param context 场景流程上下文
     * @return true=启用，false=跳过
     */
    default boolean isEnabled(SceneActionProcessContext context) {
        return true;
    }

    /**
     * 处理逻辑
     *
     * @param context 场景流程上下文
     * @return 处理结果
     */
    SceneActionProcessResult process(SceneActionProcessContext context);

    /**
     * 判断该节点执行完是否需要更新流程结果
     * 某些步骤（如消息发送等）只执行副作用操作，不需要改变流程状态
     *
     * @param context 场景流程上下文
     * @return true=需要更新流程结果，false=不更新流程结果，使用上一轮结果
     */
    default boolean shouldChangeResult(SceneActionProcessContext context) {
        // 默认逻辑：需要更新流程结果
        return true;
    }

    /**
     * 判断该节点执行完是否需要中断流程
     *
     * @param context 场景流程上下文
     * @param result  当前节点执行结果
     * @return true=中断流程，false=继续执行下一个节点
     */
    default boolean shouldBreakAfterProcess(SceneActionProcessContext context, SceneActionProcessResult result) {
        // 默认逻辑：不成功（失败和处理中）都需要中断
        return !result.isSuccess();
    }
}
