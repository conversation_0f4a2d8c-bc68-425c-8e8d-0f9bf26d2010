package com.ddmc.equity.processor.consume_benefit.strategy.impl;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.domain.dto.RpcRespDTO;
import com.ddmc.equity.dto.customer.ConsumeBenefitSceneDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.rpc.user_point.PointUserClientProxy;
import com.ddmc.equity.infra.rpc.user_point.UserPointProxy;
import com.ddmc.equity.processor.consume_benefit.dto.ConsumeBenefitContext;
import com.ddmc.equity.processor.consume_benefit.dto.ConsumeBenefitResult;
import com.ddmc.equity.processor.consume_benefit.dto.ConsumeBenefitBizDataDTO;
import com.ddmc.equity.processor.consume_benefit.strategy.AbstractConsumeBenefitStrategy;
import com.ddmc.userpoint.api.request.DeductUserPointDTO;
import com.ddmc.userpoint.api.request.FreezeUserPointDTO;
import com.ddmc.userpoint.api.request.UnfreezeUserPointDTO;
import com.ddmc.userpoint.api.response.PointFreezeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/7/1 20:36
 * @description 积分-消耗权益策略类
 */
@Slf4j
@Component
public class PointConsumeBenefitStrategy extends AbstractConsumeBenefitStrategy {

    @Resource
    private UserPointProxy userPointProxy;
    @Resource
    private PointUserClientProxy pointUserClientProxy;

    @Override
    public Integer getSupportedConsumeBenefitType() {
        return BenefitTypeEnum.POINT.getId();
    }

    @Override
    public ConsumeBenefitResult checkConsumeBenefit(ConsumeBenefitContext context) {
        ConsumeBenefitResult checkResult = checkUserId(context);
        if (checkResult != null) {
            CsossUtils.logEventWithSpan(MonitorConstants.CHECK_CONSUME_BENEFIT, "pointParamError");
            return checkResult;
        }

        // 获取用户可用积分
        String userId = context.getUserId();
        Integer availableAmount = userPointProxy.getUserTotalPoint(userId);

        // 用户可用积分为空
        if (availableAmount == null) {
            log.warn("checkConsumeBenefit availableAmount is null userId={}", userId);
            CsossUtils.logEventWithSpan(MonitorConstants.CHECK_CONSUME_BENEFIT, "pointAvailableAmountIsNull");

            return ConsumeBenefitResult.failureFailure("用户可用积分为空", null);
        }

        ConsumeBenefitBizDataDTO bizData = ConsumeBenefitBizDataDTO.builder()
                .availableAmount(String.valueOf(availableAmount))
                .build();

        // 用户可用积分不足
        if (availableAmount < context.getConsumeBenefitAmountInt()) {
            log.info("checkConsumeBenefit availableAmount not enough userId={}, consumeBenefitAmount={}",
                    userId, context.getConsumeBenefitAmount());
            CsossUtils.logEventWithSpan(MonitorConstants.CHECK_CONSUME_BENEFIT, "pointAvailableAmountNotEnough");

            return ConsumeBenefitResult.consumeBenefitNotEnoughFailure("用户可用积分不足", bizData);
        }

        return ConsumeBenefitResult.success(bizData);
    }

    @Override
    public ConsumeBenefitResult deductConsumeBenefit(ConsumeBenefitContext context) {
        ConsumeBenefitResult checkResult = checkParams(context);
        if (checkResult != null) {
            CsossUtils.logEventWithSpan(MonitorConstants.DEDUCT_CONSUME_BENEFIT, "pointParamError");
            return checkResult;
        }

        // 冻结用户积分
        FreezeUserPointDTO freezeReq = buildFreezeUserPointDTO(context);
        RpcRespDTO<PointFreezeVO> freezeResp = pointUserClientProxy.userPointFreeze(freezeReq);

        // 冻结用户积分异常
        if (freezeResp == null || freezeResp.getStatus() == null ||
                freezeResp.getStatus() == RpcRespDTO.RpcStatus.EXCEPTION) {
            log.warn("deductConsumeBenefit freeze exception freezeReq={}, freezeResp={}",
                    JSON.toJSONString(freezeReq), JSON.toJSONString(freezeResp));
            CsossUtils.logEventWithSpan(MonitorConstants.DEDUCT_CONSUME_BENEFIT, "pointFreezeException");

            return ConsumeBenefitResult.exceptionFailure("冻结用户积分异常", null);
        }

        // 冻结用户积分成功
        if (freezeResp.getStatus() == RpcRespDTO.RpcStatus.SUCCESS && freezeResp.getData() != null &&
                freezeResp.getData().getLogId() != null) {
            // 保存冻结记录 ID，用于后续确认或释放
            ConsumeBenefitBizDataDTO bizData = ConsumeBenefitBizDataDTO.builder()
                    .freezeLogId(String.valueOf(freezeResp.getData().getLogId()))
                    .build();
            return ConsumeBenefitResult.success(bizData);
        }

        // 用户可用积分数量不足，code = "5"
        if (freezeResp.getStatus() == RpcRespDTO.RpcStatus.FAILURE && freezeResp.getCode().equals("5")) {
            log.warn("deductConsumeBenefit freeze pointAvailableAmountNotEnough freezeReq={}, freezeResp={}",
                    JSON.toJSONString(freezeReq), JSON.toJSONString(freezeResp));
            CsossUtils.logEventWithSpan(MonitorConstants.DEDUCT_CONSUME_BENEFIT, "pointAvailableAmountNotEnough");

            return ConsumeBenefitResult.consumeBenefitNotEnoughFailure("用户可用积分数量不足", null);
        }

        // 冻结用户积分失败
        log.warn("deductConsumeBenefit freeze failure freezeReq={}, freezeResp={}",
                JSON.toJSONString(freezeReq), JSON.toJSONString(freezeResp));
        CsossUtils.logEventWithSpan(MonitorConstants.DEDUCT_CONSUME_BENEFIT, "pointFreezeFailure");
        return ConsumeBenefitResult.failureFailure("冻结用户积分失败", null);
    }

    @Override
    public ConsumeBenefitResult confirmConsumeBenefit(ConsumeBenefitContext context) {
        ConsumeBenefitResult checkResult = checkConfirmParams(context);
        if (checkResult != null) {
            CsossUtils.logEventWithSpan(MonitorConstants.CONFIRM_CONSUME_BENEFIT, "pointParamError");
            return checkResult;
        }

        // 确认扣减用户积分
        DeductUserPointDTO deductReq = buildDeductUserPointDTO(context);
        RpcRespDTO<Boolean> deductResp = pointUserClientProxy.userPointDeduct(deductReq);

        // 确认扣减用户积分异常
        if (deductResp == null || deductResp.getStatus() == null ||
                deductResp.getStatus() == RpcRespDTO.RpcStatus.EXCEPTION) {
            log.warn("confirmConsumeBenefit confirmDeduct exception deductReq={}, deductResp={}",
                    JSON.toJSONString(deductReq), JSON.toJSONString(deductResp));
            CsossUtils.logEventWithSpan(MonitorConstants.CONFIRM_CONSUME_BENEFIT, "pointConfirmDeductException");
            return ConsumeBenefitResult.exceptionFailure("确认扣减用户积分异常", null);
        }

        // 确认扣减用户积分成功
        if (deductResp.getStatus() == RpcRespDTO.RpcStatus.SUCCESS && Boolean.TRUE.equals(deductResp.getData())) {
            return ConsumeBenefitResult.success(null);
        }

        // 确认扣减用户积分失败
        log.warn("deductConsumeBenefit confirmDeduct failure deductReq={}, deductResp={}",
                JSON.toJSONString(deductReq), JSON.toJSONString(deductResp));
        CsossUtils.logEventWithSpan(MonitorConstants.CONFIRM_CONSUME_BENEFIT, "pointConfirmDeductFailure");
        return ConsumeBenefitResult.failureFailure("确认扣减用户积分失败", null);
    }

    @Override
    public ConsumeBenefitResult releaseConsumeBenefit(ConsumeBenefitContext context) {
        ConsumeBenefitResult checkResult = checkConfirmParams(context);
        if (checkResult != null) {
            CsossUtils.logEventWithSpan(MonitorConstants.RELEASE_CONSUME_BENEFIT, "pointParamError");
            return checkResult;
        }

        // 解冻用户积分
        UnfreezeUserPointDTO unfreezeReq = buildUnfreezeUserPointDTO(context);
        RpcRespDTO<Boolean> unfreezeResp = pointUserClientProxy.userPointUnfreeze(unfreezeReq);

        // 解冻用户积分异常
        if (unfreezeResp == null || unfreezeResp.getStatus() == null ||
                unfreezeResp.getStatus() == RpcRespDTO.RpcStatus.EXCEPTION) {
            log.warn("confirmConsumeBenefit unfreeze exception unfreezeReq={}, unfreezeResp={}",
                    JSON.toJSONString(unfreezeReq), JSON.toJSONString(unfreezeResp));
            CsossUtils.logEventWithSpan(MonitorConstants.RELEASE_CONSUME_BENEFIT, "pointUnfreezeException");
            return ConsumeBenefitResult.exceptionFailure("解冻用户积分异常", null);
        }

        // 解冻用户积分成功
        if (unfreezeResp.getStatus() == RpcRespDTO.RpcStatus.SUCCESS && Boolean.TRUE.equals(unfreezeResp.getData())) {
            return ConsumeBenefitResult.success(null);
        }

        // 解冻用户积分失败
        log.warn("deductConsumeBenefit unfreeze failure unfreezeReq={}, unfreezeResp={}",
                JSON.toJSONString(unfreezeReq), JSON.toJSONString(unfreezeResp));
        CsossUtils.logEventWithSpan(MonitorConstants.RELEASE_CONSUME_BENEFIT, "pointUnfreezeFailure");
        return ConsumeBenefitResult.failureFailure("解冻用户积分失败", null);
    }

    private FreezeUserPointDTO buildFreezeUserPointDTO(ConsumeBenefitContext context) {
        FreezeUserPointDTO freezeReq = new FreezeUserPointDTO();
        freezeReq.setUserId(context.getUserId());
        freezeReq.setReqNo(context.getReqNo());
        freezeReq.setFromType(getConsumePointScene(context));
        // 冻结积分，暂时不需要描述
//        freezeReq.setDesc(getConsumePointDesc(context));
        freezeReq.setSource(getConsumePointSource(context));
        freezeReq.setPoint(context.getConsumeBenefitAmountInt());
        return freezeReq;
    }

    private DeductUserPointDTO buildDeductUserPointDTO(ConsumeBenefitContext context) {
        DeductUserPointDTO deductReq = new DeductUserPointDTO();
        deductReq.setUserId(context.getUserId());
        deductReq.setReqNo(context.getReqNo());
        deductReq.setFromType(getConsumePointScene(context));
        deductReq.setDescription(getConsumePointDesc(context));
        deductReq.setLogId(Long.valueOf(context.getBizParams().getFreezeLogId()));
        return deductReq;
    }

    private UnfreezeUserPointDTO buildUnfreezeUserPointDTO(ConsumeBenefitContext context) {
        UnfreezeUserPointDTO unfreezeReq = new UnfreezeUserPointDTO();
        unfreezeReq.setUserId(context.getUserId());
        unfreezeReq.setReqNo(context.getReqNo());
        unfreezeReq.setFromType(getConsumePointScene(context));
        unfreezeReq.setLogId(Long.valueOf(context.getBizParams().getFreezeLogId()));
        return unfreezeReq;
    }

    private Integer getConsumePointScene(ConsumeBenefitContext context) {
        ConsumeBenefitSceneDTO consumeBenefitSceneDTO = context.getBizParams().getConsumeBenefitSceneDTO();
        return consumeBenefitSceneDTO == null ? null : consumeBenefitSceneDTO.getConsumePointScene();
    }

    private String getConsumePointDesc(ConsumeBenefitContext context) {
        ConsumeBenefitSceneDTO consumeBenefitSceneDTO = context.getBizParams().getConsumeBenefitSceneDTO();
        return consumeBenefitSceneDTO == null ? null : consumeBenefitSceneDTO.getConsumePointDesc();
    }

    private Integer getConsumePointSource(ConsumeBenefitContext context) {
        ConsumeBenefitSceneDTO consumeBenefitSceneDTO = context.getBizParams().getConsumeBenefitSceneDTO();
        return consumeBenefitSceneDTO == null ? null : consumeBenefitSceneDTO.getConsumePointSource();
    }
}
