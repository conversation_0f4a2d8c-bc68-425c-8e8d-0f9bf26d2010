package com.ddmc.equity.processor.scene_action.v1.dto;

import com.ddmc.equity.dto.customer.ConsumeBenefitSceneDTO;
import com.ddmc.equity.dto.customer.ReceiveExternalInfoDTO;
import com.ddmc.equity.dto.customer.SendBenefitSceneDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/7/18 16:31
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class SceneActionBizParamsDTO {

    // ========== 活动相关参数 ==========
    /**
     * 外部活动类型
     */
    private Integer externalType;
    /**
     * 外部活动 IDList
     */
    private List<String> externalIdList;
    /**
     * 外部策略 IDList
     */
    private List<String> strategyExternalIdList;
    /**
     * 权益 IDList
     */
    private List<Long> benefitIdList;
    /**
     * 外部使用活动 ID
     */
    private String useExternalId;

    // ========== 咨询时相关参数 ==========
    /**
     * 是否领取
     */
    private Boolean isReceive;
    /**
     * 活动过滤类型
     */
    private Integer activityFilterType;
    /**
     * 是否需要不可领取原因
     */
    private Boolean needUnableReceiveReason;
    /**
     * 活动上的发放类型，用于决定策略的发放方式
     */
    private Integer activitySendType;

    // ========== 领取时相关参数 ==========
    /**
     * 请求流水号
     */
    private String reqNo;
    /**
     * 发放不固定数量权益时，指定的数量（积分数、余额金额、活动次数、步数、会员天数、金豆数等）
     */
    private String sendAmount;
    /**
     * 领取时关联的外部信息
     */
    private ReceiveExternalInfoDTO receiveExternalInfoDTO;
    /**
     * 消耗权益时调用下游透传的场景相关参数
     */
    private ConsumeBenefitSceneDTO consumeBenefitSceneDTO;
    /**
     * 发放权益时调用下游透传的场景相关参数
     */
    private SendBenefitSceneDTO sendBenefitSceneDTO;
    /**
     * 发放权益时调用下游透传的自定义参数
     */
    private Map<String, String> rpcReqCustomMap;

    // ========== 风控相关参数 ==========
    /**
     * 调用风控 sceneCode
     */
    private String riskSceneCode;
    /**
     * 调用风控 sceneCodeAfter
     */
    private String riskSceneCodeAfter;
    /**
     * 调用风控场景特殊参数
     */
    private Map<String, Object> riskSceneStrategyDataMap;
}
