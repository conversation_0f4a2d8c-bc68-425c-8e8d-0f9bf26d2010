package com.ddmc.equity.processor.operate_benefit_account.impl;

import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.common.util.Assert;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.account.UniversalAccountDTO;
import com.ddmc.equity.domain.entity.account.PointAccountConvertEntity;
import com.ddmc.equity.dto.customer.account.UniversalUseBenefitTypeDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.infra.rpc.user_point.UserPointProxy;
import com.ddmc.userpoint.api.request.DecreaseRequestReq;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/8/1 15:06
 * @description
 */
@Slf4j
@Service
public class PointOperateBenefitAccountProcessor extends AbstractOperateBenefitAccountProcessor {

    @Autowired
    private UserPointProxy userPointProxy;

    @Override
    public Integer getBenefitType() {
        return BenefitTypeEnum.POINT.getId();
    }

    @Override
    public List<UniversalAccountDTO> getUserAccounts(String userId, Long useActivityId, List<Integer> benefitTypes) {
        Assert.mustTrue(Objects.nonNull(userId), ExceptionEnum.PARAMS_ERROR, "用户 id 为空");

        Integer userTotalPoint = userPointProxy.getUserTotalPoint(userId);
        UniversalAccountDTO accountDTO = PointAccountConvertEntity.convertToPointAccountDTO(userId, userTotalPoint);
        return Lists.newArrayList(accountDTO);
    }

    @Override
    public Boolean deductAccount(UniversalUseBenefitTypeDTO req) {
        Assert.mustTrue(Objects.nonNull(req), ExceptionEnum.PARAMS_ERROR, "请求 req 为空");

        DecreaseRequestReq decreaseReq = PointAccountConvertEntity.convertToDecreaseRequest(req);
        EquityRpcDto decreaseResp = userPointProxy.decrease(decreaseReq);
        return Objects.nonNull(decreaseResp) && Objects.equals(decreaseResp.getStatusEnum(), StatusEnum.SUCCESS);
    }
}