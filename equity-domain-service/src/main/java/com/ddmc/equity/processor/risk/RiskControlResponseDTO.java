package com.ddmc.equity.processor.risk;

import com.ddmc.equity.dto.customer.RiskControlInterceptRuleDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/25 15:20
 * @description 风控处理响应结果 DTO
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RiskControlResponseDTO {

    /**
     * 风控结果。pass，通过；no_pass，风控不通过；black_user，黑产用户；
     */
    private String riskControlResult;

    /**
     * 风控拦截规则列表
     */
    private List<RiskControlInterceptRuleDTO> interceptRuleDTOList;
} 