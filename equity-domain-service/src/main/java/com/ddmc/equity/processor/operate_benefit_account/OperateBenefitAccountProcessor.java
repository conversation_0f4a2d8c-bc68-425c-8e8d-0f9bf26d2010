package com.ddmc.equity.processor.operate_benefit_account;

import com.ddmc.equity.domain.dto.account.UniversalAccountDTO;
import com.ddmc.equity.dto.customer.account.AccountDirectBenefitReqDTO;
import com.ddmc.equity.dto.customer.account.AccountDirectBenefitRespDTO;
import com.ddmc.equity.dto.customer.account.UniversalUseBenefitTypeDTO;
import com.ddmc.equity.infra.repository.dao.EquityBenefitDO;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/7/31 15:10
 * @description
 */
public interface OperateBenefitAccountProcessor {

    /**
     * 通过 benefitType 匹配具体使用哪个实现类
     *
     * @return 权益类型
     */
    Integer getBenefitType();

    /**
     * 直塞发放权益（没有活动规则）
     *
     * @param req       请求 req
     * @param benefitDO 权益信息
     * @return 是否发放成功
     */
    AccountDirectBenefitRespDTO directReceiveBenefit(AccountDirectBenefitReqDTO req, EquityBenefitDO benefitDO);

    /**
     * 查询用户权益账户列表
     *
     * @param userId        用户 id
     * @param useActivityId 使用活动 id
     * @param benefitTypes  权益类型列表
     * @return 用户权益账户列表
     */
    List<UniversalAccountDTO> getUserAccounts(String userId, Long useActivityId, List<Integer> benefitTypes);

    /**
     * 扣减权益账户
     *
     * @param req 请求 req
     * @return 是否扣减成功
     */
    Boolean deductAccount(UniversalUseBenefitTypeDTO req);
}
