package com.ddmc.equity.processor.operate_benefit_account.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/8/1 19:04
 * @description
 */
@Slf4j
@Component
public class DefaultOperateBenefitAccountProcessor extends AbstractOperateBenefitAccountProcessor {

    @Override
    public Integer getBenefitType() {
        return null;
    }
}
