package com.ddmc.equity.processor.scene_action.v1.processor.default_processor.consult_activity;

import com.alibaba.fastjson2.JSON;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.domain.converter.common.EngineContextConverter;
import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.engine.EngineContextDTO;
import com.ddmc.equity.domain.dto.engine.EngineResultContextDTO;
import com.ddmc.equity.domain.service.core.SceneActivityBenefitCoreService;
import com.ddmc.equity.enums.ActivityFilterTypeEnum;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionBizDataDTO;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessContext;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessResult;
import com.ddmc.equity.processor.scene_action.v1.enums.SceneActionProcessTypeEnum;
import com.ddmc.equity.processor.scene_action.v1.processor.SceneActionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 默认咨询活动处理器
 */
@Slf4j
@Component
public class DefaultConsultActivityProcessor implements SceneActionProcessor {

    @Resource
    private SceneActivityBenefitCoreService sceneActivityBenefitCoreService;

    @Override
    public SceneActionProcessTypeEnum getProcessTypeEnum() {
        return SceneActionProcessTypeEnum.CONSULT_ACTIVITY;
    }

    @Override
    public SceneActionProcessResult process(SceneActionProcessContext context) {
        // 1 创建咨询上下文
        EngineContextDTO consultEngineContextDTO = buildConsultContextDTO(context);

        // 2 咨询活动信息
        String sceneCode = context.getSceneCode();
        List<SceneActivityCacheDto> consultActivityList = sceneActivityBenefitCoreService
                .consultSceneBenefit(sceneCode, consultEngineContextDTO);

        // 3 转换为场景处理结果
        return convertToSceneActionProcessResultAndDot(context, consultActivityList, consultEngineContextDTO.getResultContextDTO());
    }

    private EngineContextDTO buildConsultContextDTO(SceneActionProcessContext context) {
        Integer activityFilterType = context.getBizParams().getActivityFilterType();

        EngineContextDTO engineContextDTO = EngineContextConverter.INSTANCE.convertToEngineContextDTO(context);
        engineContextDTO.setExternalType(context.getBizParams().getExternalType());
        engineContextDTO.setExternalIdList(context.getBizParams().getExternalIdList());
        engineContextDTO.setStrategyExternalIdList(context.getBizParams().getStrategyExternalIdList());
        engineContextDTO.setBenefitIdList(context.getBizParams().getBenefitIdList());
        engineContextDTO.setIsReceive(context.getBizParams().getIsReceive());
        engineContextDTO.setActFilterType(activityFilterType == null ? null : ActivityFilterTypeEnum.getByType(activityFilterType));
        engineContextDTO.setNeedUnableReceiveReason(context.getBizParams().getNeedUnableReceiveReason());
        engineContextDTO.setActivitySendTypeFromReq(context.getBizParams().getActivitySendType());

        return engineContextDTO;
    }

    private SceneActionProcessResult convertToSceneActionProcessResultAndDot(SceneActionProcessContext context,
                                                                             List<SceneActivityCacheDto> consultActivityList,
                                                                             EngineResultContextDTO consultResultContextDTO) {
        String userId = context.getUserId();
        String sceneCode = context.getSceneCode();
        String reqNo = context.getReqNo();
        List<UnableReceiveBenefitDTO> unableReceiveBenefitDTOList = consultResultContextDTO.getUnableReceiveBenefitDTOList();

        if (CollectionUtils.isNotEmpty(consultActivityList)) {
            // 1 咨询成功，返回咨询活动列表
            SceneActionBizDataDTO bizDataDTO = SceneActionBizDataDTO.builder()
                    .consultActivityList(consultActivityList)
                    .build();
            return SceneActionProcessResult.success(bizDataDTO);
        } else if (CollectionUtils.isNotEmpty(unableReceiveBenefitDTOList)) {
            // 2 咨询失败，且有不可领取的权益列表，返回不可领取的权益列表
            log.info("sceneAction consultActivity failure haveUnableReceiveBenefitDTOList userId={}, sceneCode={}, reqNo={}" +
                            ", unableReceiveBenefitDTOList={}",
                    userId, sceneCode, reqNo, JSON.toJSONString(unableReceiveBenefitDTOList));
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_CONSULT_ACTIVITY, "haveUnableReceiveBenefitDTOList");

            SceneActionBizDataDTO bizDataDTO = SceneActionBizDataDTO.builder()
                    .unableReceiveBenefitDTOList(unableReceiveBenefitDTOList)
                    .build();
            return SceneActionProcessResult.failure("咨询失败，有不可领取的权益", bizDataDTO);
        } else {
            // 3 咨询失败，活动不存在或者活动状态异常
            log.info("sceneAction consultActivity failure activityNotExist userId={}, sceneCode={}, reqNo={},",
                    userId, sceneCode, reqNo);
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_CONSULT_ACTIVITY, "activityNotExist");

            SceneActionBizDataDTO bizDataDTO = SceneActionBizDataDTO.builder()
                    .consultResultErrorCode("activityNotExist")
                    .build();
            return SceneActionProcessResult.failure("咨询失败，活动不存在或者活动状态异常", bizDataDTO);
        }
    }
}
