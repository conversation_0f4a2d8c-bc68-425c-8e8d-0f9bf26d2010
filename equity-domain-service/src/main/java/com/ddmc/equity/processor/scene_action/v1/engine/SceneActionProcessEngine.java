package com.ddmc.equity.processor.scene_action.v1.engine;

import com.csoss.monitor.api.trace.Span;
import com.csoss.monitor.api.trace.Traces;
import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionBizDataDTO;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessContext;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessResult;
import com.ddmc.equity.processor.scene_action.v1.enums.SceneActionProcessTypeEnum;
import com.ddmc.equity.processor.scene_action.v1.processor.SceneActionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 场景动作流程引擎 - 负责编排和执行场景流程
 */
@Slf4j
@Component
public class SceneActionProcessEngine {

    /**
     * 处理器映射
     */
    private final Map<String, SceneActionProcessor> processorMap = new HashMap<>();

    @Autowired
    public SceneActionProcessEngine(List<SceneActionProcessor> processors) {
        // 初始化处理器映射
        processors.forEach(processor ->
                processorMap.put(getProcessorKey(processor.getSceneCode(), processor.getProcessTypeEnum()), processor)
        );
    }

    /**
     * 执行咨询流程
     */
    public SceneActionProcessResult executeConsultFlow(SceneActionProcessContext context, String consultFlowType) {
        SceneActionProcessResult processResult = null;
        try {
            log.info("sceneAction executeConsultFlow start sceneCode={}, userId={}, consultFlowType={}" +
                            ", externalType={}, externalIdList={}, strategyExternalIdList={}, benefitIdList={}",
                    context.getSceneCode(), context.getUserId(), consultFlowType,
                    context.getBizParams().getExternalType(), context.getBizParams().getExternalIdList(),
                    context.getBizParams().getStrategyExternalIdList(), context.getBizParams().getBenefitIdList());
            processResult = executeFlow(context, SceneActionProcessTypeEnum.getConsultFlows(consultFlowType));
        } finally {
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_EXECUTE_CONSULT_FLOW,
                    context.getSceneCode() + "_" + Optional.ofNullable(processResult)
                            .map(SceneActionProcessResult::getStatus)
                            .map(Enum::name).orElse("processResultIsNull"));
        }
        return processResult;
    }

    /**
     * 执行领取流程
     */
    public SceneActionProcessResult executeReceiveFlow(SceneActionProcessContext context) {
        SceneActionProcessResult processResult = null;
        try {
            log.info("sceneAction executeReceiveFlow start sceneCode={}, userId={}, reqNo={}" +
                            ", externalType={}, externalIdList={}, strategyExternalIdList={}, benefitIdList={}",
                    context.getSceneCode(), context.getUserId(), context.getReqNo(),
                    context.getBizParams().getExternalType(), context.getBizParams().getExternalIdList(),
                    context.getBizParams().getStrategyExternalIdList(), context.getBizParams().getBenefitIdList());
            processResult = executeFlow(context, SceneActionProcessTypeEnum.getReceiveFlows());
        } finally {
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_EXECUTE_RECEIVE_FLOW,
                    context.getSceneCode() + "_" + Optional.ofNullable(processResult)
                            .map(SceneActionProcessResult::getStatus)
                            .map(Enum::name).orElse("processResultIsNull"));
        }
        return processResult;
    }

    /**
     * 执行流程
     */
    private SceneActionProcessResult executeFlow(SceneActionProcessContext context,
                                                 List<SceneActionProcessTypeEnum> flow) {
        String userId = context.getUserId();
        String sceneCode = context.getSceneCode();
        String reqNo = context.getReqNo();

        String currentProcessType = null;
        SceneActionProcessResult finalStepResult = null;

        try {
            for (SceneActionProcessTypeEnum processTypeEnum : flow) {
                currentProcessType = processTypeEnum.getCode();

                // 1 获取处理器（优先场景特定，然后默认）
                SceneActionProcessor processor = getProcessor(sceneCode, processTypeEnum);

                // 2 检查是否启用该节点
                if (!processor.isEnabled(context)) {
                    log.debug("sceneAction executeFlow step skip userId={}, sceneCode={}, reqNo={} processType={}",
                            userId, sceneCode, reqNo, currentProcessType);
                    continue;
                }

                // 3 执行处理逻辑
                SceneActionProcessResult currentStepResult = executeProcessorLogic(context, currentProcessType, processor);

                // 4 步骤完成后，将当前步骤结果数据合并到上下文，供后续步骤使用
                context.mergeBizData(currentStepResult.getBizData());

                // 5 判断该节点执行完是否需要更新流程结果
                if (processor.shouldChangeResult(context)) {
                    // 需要更新流程结果，修改 finalStepResult
                    finalStepResult = currentStepResult;
                } else {
                    // 不更新流程结果，使用上一轮结果
                    log.debug("sceneAction executeFlow step ignore result userId={}, sceneCode={}, reqNo={}" +
                                    ", processType={}, status={}",
                            userId, sceneCode, reqNo, currentProcessType, currentStepResult.getStatus());
                }

                // 6 判断该节点执行完是否需要中断流程
                if (processor.shouldBreakAfterProcess(context, currentStepResult)) {
                    log.debug("sceneAction executeFlow step break userId={}, sceneCode={}, reqNo={}" +
                                    ", processType={}, status={}",
                            userId, sceneCode, reqNo, currentProcessType, currentStepResult.getStatus());
                    break;
                }
            }
        } catch (Exception e) {
            // 根据当前执行步骤构建异常信息
            String exceptionStep = currentProcessType != null
                    ? currentProcessType : SceneActionProcessTypeEnum.FLOW_GLOBAL.getCode();
            String detailMessage = currentProcessType != null
                    ? String.format("%s 步骤执行异常: %s", currentProcessType, e.getMessage())
                    : String.format("流程执行全局异常: %s", e.getMessage());

            log.error("sceneAction executeFlow exception userId={}, sceneCode={}, reqNo={}, exceptionStep={}, e=",
                    userId, sceneCode, reqNo, exceptionStep, e);

            // 具体异常步骤放到结果中
            SceneActionBizDataDTO bizDataDTO = SceneActionBizDataDTO.builder()
                    .exceptionStep(exceptionStep)
                    .build();
            finalStepResult = SceneActionProcessResult.failure(detailMessage, bizDataDTO);
        }

        // 构建最终结果，包含所有步骤合并后的数据
        if (finalStepResult == null) {
            // 最终执行结果为空，返回失败
            log.warn("sceneAction executeFlow finalStepResult is null userId={}, sceneCode={}, reqNo={}",
                    userId, sceneCode, reqNo);
            return SceneActionProcessResult.failure("最终执行结果为空", context.getBizData());
        } else {
            // 返回最后一步的结果，但包含所有步骤合并后的数据
            finalStepResult.mergeBizData(context.getBizData());
            return finalStepResult;
        }
    }

    /**
     * 执行处理器的业务逻辑
     *
     * @param context     处理上下文
     * @param processType 处理类型
     * @param processor   处理器
     * @return 处理结果
     */
    private SceneActionProcessResult executeProcessorLogic(SceneActionProcessContext context, String processType,
                                                           SceneActionProcessor processor) {
        String userId = context.getUserId();
        String sceneCode = context.getSceneCode();
        String reqNo = context.getReqNo();

        Span span = Traces.spanBuilder(processType).startSpan();

        SceneActionProcessResult process = null;
        try {
            process = processor.process(context);
            return process;
        } catch (Exception e) {
            log.error("sceneAction executeProcessorLogic exception userId={}, sceneCode={}, reqNo={}, processType={}, e=",
                    userId, sceneCode, reqNo, processType, e);

            // 构建详细的异常信息，包含异常步骤和已执行步骤的数据
            String detailMessage = String.format("%s 处理逻辑执行异常: %s", processType, e.getMessage());
            // 具体异常步骤放到结果中
            SceneActionBizDataDTO bizDataDTO = SceneActionBizDataDTO.builder()
                    .exceptionStep(processType)
                    .build();
            process = SceneActionProcessResult.failure(detailMessage, bizDataDTO);
            return process;
        } finally {
            if (process != null) {
                span.setAttribute("status", Optional.ofNullable(process.getStatus()).map(Enum::name).orElse(StringUtils.EMPTY));
                span.setAttribute("message", process.getMessage());
            }
            span.end();
        }
    }

    /**
     * 获取处理器
     */
    private SceneActionProcessor getProcessor(String sceneCode, SceneActionProcessTypeEnum processTypeEnum) {
        // 1 优先查找场景特定处理器
        String processorKey = getProcessorKey(sceneCode, processTypeEnum);
        SceneActionProcessor processor = processorMap.get(processorKey);

        if (processor != null) {
            return processor;
        }

        // 2 回退到默认处理器
        processorKey = getProcessorKey("default", processTypeEnum);
        processor = processorMap.get(processorKey);

        if (processor != null) {
            return processor;
        }

        // 3 都没有则抛异常
        throw new IllegalStateException(String.format("未找到 %s 处理器", processorKey));
    }

    private String getProcessorKey(String sceneCode, SceneActionProcessTypeEnum processTypeEnum) {
        String processType = processTypeEnum == null ? StringUtils.EMPTY : processTypeEnum.getCode();
        return sceneCode + "_" + processType;
    }
}
