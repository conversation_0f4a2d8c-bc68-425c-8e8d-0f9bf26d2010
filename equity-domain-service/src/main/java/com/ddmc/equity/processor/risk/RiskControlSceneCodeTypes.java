package com.ddmc.equity.processor.risk;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/25 20:24
 * @description
 */
public interface RiskControlSceneCodeTypes {

    /**
     * 默认场景。风控不通过不发奖励
     */
    String DEFAULT_SCENE = "DEFAULT_SCENE";
    /**
     * CNY-抽奖活动（领取前）
     */
    String CNY_CHEST_FRONT = "cny_chest_front";
    /**
     * CNY-抽奖活动（领取后）
     */
    String CNY_CHEST_AFTER = "cny_chest_after";
    /**
     * CNY-领奖活动（领取前）
     */
    String CNY_BUSINESS_FRONT = "cny_business_front";
    /**
     * CNY-领奖活动（领取后）
     */
    String CNY_BUSINESS_AFTER = "cny_business_after";
    /**
     * 分享红包-返券（发奖前）
     */
    String ACTIVITY_BAG_LAUNCH_FRONT = "activity_bag_launch_front";
    /**
     * 分享红包-返券（发奖后）
     */
    String ACTIVITY_BAG_LAUNCH_AFTER = "activity_bag_launch_after";
    /**
     * 会员裂变奖励发放
     */
    String VIP_CARD_ACTIVITY_INVITE_RETURN = "vip_card_activity_invite_return";
    /**
     * 分享助力，发起人发奖。使用 DefaultSceneRiskControlProcessor
     */
    String ACTIVITY_ASSIST_RETURN = "activity_assist_return";
    /**
     * 分享助力，助力人发奖。使用 DefaultSceneRiskControlProcessor
     */
    String RETURN_TICKET_ACCEPT = "return_ticket_accept";
    /**
     * 赛事转盘抽奖活动
     */
    String EVENT_TURNTABLE_ACTIVITY = "event_turntable_activity";
    /**
     * 邀请有礼，邀请人发奖。使用 DefaultSceneRiskControlProcessor
     */
    String ACTIVITY_INVITE_RETURN = "activity_invite_return";
    /**
     * 邀请有礼，被邀请人发奖。使用 DefaultSceneRiskControlProcessor
     */
    String ACTIVITY_ORDERGIFT = "activity_ordergift";
}
