package com.ddmc.equity.processor.scene_action.v1.dto;

import com.ddmc.equity.common.enums.ProcessStatus;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.ddmc.equity.domain.dto.account.UniversalAccountRpcResponseExtDTO;
import com.ddmc.equity.dto.customer.RiskControlInterceptRuleDTO;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/7/18 16:30
 * @description
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class SceneActionBizDataDTO {

    // ========== 咨询时相关 ==========
    /**
     * 咨询活动列表
     */
    private List<SceneActivityCacheDto> consultActivityList;
    /**
     * 不可领取的权益列表
     */
    private List<UnableReceiveBenefitDTO> unableReceiveBenefitDTOList;
    /**
     * 咨询结果错误码
     */
    private String consultResultErrorCode;

    // ========== 风控相关 ==========
    /**
     * 风控拦截规则列表
     */
    private List<RiskControlInterceptRuleDTO> riskControlInterceptRuleDTOList;
    /**
     * 风控替换权益信息，部分活动场景风控不通过需要替换权益。如果不为空，则发放时取这个权益；如果为空，则发放时取活动权益；
     */
    private FullBenefitInfoDTO riskReplaceBenefit;

    // ========== 消耗权益相关 ==========
    /**
     * 消耗权益，用户可用权益数量数量
     */
    private String consumeBenefitAvailableAmount;
    /**
     * 消耗权益，冻结记录 ID
     */
    private String consumeBenefitFreezeLogId;

    // ========== 领取时相关 ==========
    /**
     * 实际领取的权益信息，风控不通过可能会替换成兜底奖励
     */
    private FullBenefitInfoDTO actualReceiveBenefit;
    /**
     * 已存在的冻结记录 accountRecord 的请求流水号
     */
    private String frozenRecordReqNo;
    /**
     * 已存在的冻结记录 accountRecord 的冻结记录 ID（消耗权益时使用）
     */
    private String frozenRecordConsumeBenefitFreezeLogId;
    /**
     * 执行权益结果状态
     */
    private ProcessStatus executeBftResultStatus;
    /**
     * 领取成功时的权益账户明细 ID
     */
    private Long accountDetailId;
    /**
     * 外部 rpc 调用请求出参拓展信息
     */
    private UniversalAccountRpcResponseExtDTO rpcResponseExtDTO;

    // ========== 异常相关 ==========
    /**
     * 异常步骤，对应 SceneActionProcessTypeEnum Code
     */
    private String exceptionStep;
}

