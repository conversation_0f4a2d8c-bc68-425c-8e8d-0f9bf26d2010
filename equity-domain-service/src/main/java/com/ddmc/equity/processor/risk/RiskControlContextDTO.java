package com.ddmc.equity.processor.risk;

import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.ddmc.equity.dto.customer.ReceiveExternalInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/25 15:21
 * @description
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class RiskControlContextDTO {

    @ApiModelProperty(value = "基础请求")
    @NotNull(message = "基础请求为空")
    private BaseRequestDTO baseRequestDTO;

    @ApiModelProperty(value = "调用风控 sceneCode")
    @NotBlank(message = "调用风控 sceneCode 为空")
    private String sceneCode;

    @ApiModelProperty(value = "活动 id")
    private String activityId;

    @ApiModelProperty(value = "活动名称")
    private String activityName;

    @ApiModelProperty(value = "领取的权益信息")
    private FullBenefitInfoDTO benefitInfoDTO;

    @ApiModelProperty(value = "调用风控场景特殊参数")
    private Map<String, Object> riskSceneStrategyDataMap;

    @ApiModelProperty(value = "领取时关联的外部信息")
    private ReceiveExternalInfoDTO receiveExternalInfoDTO;
}
