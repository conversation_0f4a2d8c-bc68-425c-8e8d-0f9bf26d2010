package com.ddmc.equity.processor.scene_action.v1.processor.default_processor.check_consume_bft;

import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.util.CsossUtils;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.ddmc.equity.domain.entity.benefit.BenefitConvertEntity;
import com.ddmc.equity.domain.entity.scene_action.SceneActionProcessConvertEntity;
import com.ddmc.equity.enums.BenefitUnableReceiveReasonType;
import com.ddmc.equity.processor.consume_benefit.dto.ConsumeBenefitContext;
import com.ddmc.equity.processor.consume_benefit.dto.ConsumeBenefitResult;
import com.ddmc.equity.processor.consume_benefit.strategy.ConsumeBenefitStrategy;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessContext;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessResult;
import com.ddmc.equity.processor.scene_action.v1.enums.SceneActionProcessTypeEnum;
import com.ddmc.equity.processor.scene_action.v1.processor.AbstractConsumeBenefitProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 默认检查用户可用权益（消耗权益）数量是否足够处理器
 */
@Slf4j
@Component
public class DefaultCheckConsumeBftProcessor extends AbstractConsumeBenefitProcessor {

    @Override
    public SceneActionProcessTypeEnum getProcessTypeEnum() {
        return SceneActionProcessTypeEnum.CHECK_CONSUME_BFT;
    }

    @Override
    public boolean isEnabled(SceneActionProcessContext context) {
        // 如果需要消耗权益，启用该节点
        return super.isConsumeBenefitRequired(context.getSceneCode());
    }

    @Override
    public SceneActionProcessResult process(SceneActionProcessContext context) {
        // 1 检查消耗权益基础参数有效性
        SceneActionProcessResult checkResult = checkConsumeBenefit(context);
        if (checkResult != null && !checkResult.isSuccess()) {
            log.warn("sceneAction checkConsumeBft checkConsumeBenefitFailure userId={}, sceneCode={}, reqNo={}" +
                    ", checkResult={}", context.getUserId(), context.getSceneCode(), context.getReqNo(), checkResult);
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_CHECK_CONSUME_BFT, "checkConsumeBenefitFailure");
            return checkResult;
        }

        // 2 创建消耗权益上下文
        ConsumeBenefitContext benefitContext = ConsumeBenefitContext.buildConsumeBenefitContext(context);

        // 3 获取对应的权益策略
        Integer consumeBenefitType = context.getConsumeBenefitType();
        ConsumeBenefitStrategy strategy = strategyFactory.getStrategy(consumeBenefitType);
        if (strategy == null) {
            log.warn("sceneAction checkConsumeBft getStrategyFailure userId={}, sceneCode={}, reqNo={}" +
                    ", consumeBenefitType={}", context.getUserId(), context.getSceneCode(), context.getReqNo(), consumeBenefitType);
            CsossUtils.logEventWithSpan(MonitorConstants.SCENE_ACTION_CHECK_CONSUME_BFT, "getStrategyFailure");
            return SceneActionProcessResult.failure("未找到对应的消耗权益策略，消耗权益类型：" + consumeBenefitType, null);
        }

        // 4 执行消耗权益检查（消耗权益检查结果，里面有统一打点并配置告警，无需重复打点）
        ConsumeBenefitResult consumeBenefitResult = strategy.checkConsumeBenefit(benefitContext);

        // 5 转换为场景处理结果
        List<UnableReceiveBenefitDTO> unableReceiveBenefitDTOList = buildUnableReceiveBenefitDTOS(context, consumeBenefitResult);
        return ConsumeBenefitResult.convertToSceneActionProcessResult(consumeBenefitResult, unableReceiveBenefitDTOList);
    }

    private List<UnableReceiveBenefitDTO> buildUnableReceiveBenefitDTOS(SceneActionProcessContext context,
                                                                        ConsumeBenefitResult consumeBenefitResult) {
        List<FullBenefitInfoDTO> fullBenefitInfoDTOList = BenefitConvertEntity
                .convertToFullBenefitInfoDTOList(context.getBizData().getConsultActivityList());
        ConsumeBenefitResult.ConsumeBenefitResultCode consumeBenefitResultCode = consumeBenefitResult.getCode();
        String unableReceiveReasonCode = convertToUnableReceiveReasonCode(consumeBenefitResultCode);
        return SceneActionProcessConvertEntity.buildUnableReceiveBenefitDTOList(fullBenefitInfoDTOList, unableReceiveReasonCode);
    }

    private String convertToUnableReceiveReasonCode(ConsumeBenefitResult.ConsumeBenefitResultCode consumeBenefitResultCode) {
        if (consumeBenefitResultCode == null) {
            return BenefitUnableReceiveReasonType.CHECK_CONSUME_BFT_EXCEPTION.getCode();
        }
        switch (consumeBenefitResultCode) {
            case CONSUME_BENEFIT_NOT_ENOUGH:
                return BenefitUnableReceiveReasonType.CHECK_CONSUME_BFT_NOT_ENOUGH.getCode();
            case PARAMS_ERROR:
            case TOO_FREQUENT:
            case FAILURE:
                return BenefitUnableReceiveReasonType.CHECK_CONSUME_BFT_FAILURE.getCode();
            default:
                return BenefitUnableReceiveReasonType.CHECK_CONSUME_BFT_EXCEPTION.getCode();
        }
    }
}
