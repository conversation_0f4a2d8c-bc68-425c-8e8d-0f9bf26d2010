package com.ddmc.equity.processor.consume_benefit.dto;

import com.ddmc.equity.common.enums.ProcessStatus;
import com.ddmc.equity.domain.dto.UnableReceiveBenefitDTO;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionBizDataDTO;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 消耗权益结果 - 权益策略执行结果
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class ConsumeBenefitResult {

    /**
     * 状态
     */
    private ProcessStatus status;

    /**
     * 返回结果码（失败时错误码）
     */
    private ConsumeBenefitResultCode code;

    /**
     * 消息
     */
    private String message;

    /**
     * 业务数据
     */
    @Builder.Default
    @NotNull
    private ConsumeBenefitBizDataDTO bizData = ConsumeBenefitBizDataDTO.builder().build();

    @AllArgsConstructor
    @Getter
    public enum ConsumeBenefitResultCode {

        /**
         * 参数错误
         */
        PARAMS_ERROR(),
        /**
         * 操作过于频繁
         */
        TOO_FREQUENT(),
        /**
         * 用户可用消耗权益数量不足
         */
        CONSUME_BENEFIT_NOT_ENOUGH(),
        /**
         * 失败。除了上述情况之外的失败
         */
        FAILURE(),
        /**
         * 未知异常。系统间调用超时
         */
        EXCEPTION(),
    }

    /**
     * 创建成功结果（带数据）
     *
     * @param bizData 业务数据
     * @return 成功结果
     */
    public static ConsumeBenefitResult success(ConsumeBenefitBizDataDTO bizData) {
        ConsumeBenefitResult result = ConsumeBenefitResult.builder()
                .status(ProcessStatus.SUCCESS)
                .build();
        if (bizData != null) {
            result.setBizData(bizData);
        }
        return result;
    }

    /**
     * 创建失败结果（参数错误，导致失败）
     *
     * @param message 消息
     * @param bizData 业务数据
     * @return 失败结果
     */
    public static ConsumeBenefitResult paramsErrorFailure(String message, ConsumeBenefitBizDataDTO bizData) {
        ConsumeBenefitResult result = ConsumeBenefitResult.builder()
                .status(ProcessStatus.FAILURE)
                .code(ConsumeBenefitResultCode.PARAMS_ERROR)
                .message(message)
                .build();
        if (bizData != null) {
            result.setBizData(bizData);
        }
        return result;
    }

    /**
     * 创建失败结果（操作过于频繁，导致失败）
     *
     * @param message 消息
     * @param bizData 业务数据
     * @return 失败结果
     */
    public static ConsumeBenefitResult tooFrequentFailure(String message, ConsumeBenefitBizDataDTO bizData) {
        ConsumeBenefitResult result = ConsumeBenefitResult.builder()
                .status(ProcessStatus.FAILURE)
                .code(ConsumeBenefitResultCode.TOO_FREQUENT)
                .message(message)
                .build();
        if (bizData != null) {
            result.setBizData(bizData);
        }
        return result;
    }

    /**
     * 创建失败结果（用户可用消耗权益数量不足，导致失败）
     *
     * @param message 消息
     * @param bizData 业务数据
     * @return 失败结果
     */
    public static ConsumeBenefitResult consumeBenefitNotEnoughFailure(String message, ConsumeBenefitBizDataDTO bizData) {
        ConsumeBenefitResult result = ConsumeBenefitResult.builder()
                .status(ProcessStatus.FAILURE)
                .code(ConsumeBenefitResultCode.CONSUME_BENEFIT_NOT_ENOUGH)
                .message(message)
                .build();
        if (bizData != null) {
            result.setBizData(bizData);
        }
        return result;
    }

    /**
     * 创建失败结果（未知失败）
     *
     * @param message 消息
     * @param bizData 业务数据
     * @return 失败结果
     */
    public static ConsumeBenefitResult failureFailure(String message, ConsumeBenefitBizDataDTO bizData) {
        ConsumeBenefitResult result = ConsumeBenefitResult.builder()
                .status(ProcessStatus.FAILURE)
                .code(ConsumeBenefitResultCode.FAILURE)
                .message(message)
                .build();
        if (bizData != null) {
            result.setBizData(bizData);
        }
        return result;
    }

    /**
     * 创建失败结果（未知异常，导致失败）
     *
     * @param message 消息
     * @param bizData 业务数据
     * @return 失败结果
     */
    public static ConsumeBenefitResult exceptionFailure(String message, ConsumeBenefitBizDataDTO bizData) {
        ConsumeBenefitResult result = ConsumeBenefitResult.builder()
                .status(ProcessStatus.FAILURE)
                .code(ConsumeBenefitResultCode.EXCEPTION)
                .message(message)
                .build();
        if (bizData != null) {
            result.setBizData(bizData);
        }
        return result;
    }

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return ProcessStatus.isSuccess(status);
    }

    /**
     * 是否失败
     */
    public boolean isFailure() {
        return ProcessStatus.isFailure(status);
    }

    /**
     * 是否处理完成（成功或失败，非处理中）
     */
    public boolean isCompleted() {
        return ProcessStatus.isCompleted(status);
    }

    /**
     * 将消耗权益结果转换为场景处理结果
     *
     * @param consumeBenefitResult 消耗权益结果，包含执行状态、错误信息和返回数据
     * @return 场景处理结果，格式统一的处理结果对象
     * - 成功时：返回成功状态的场景处理结果，携带返回数据
     * - 失败时：返回失败状态的场景处理结果，携带错误信息和返回数据
     * - 其他状态：返回处理中状态的场景处理结果，携带错误信息和返回数据
     */
    public static SceneActionProcessResult convertToSceneActionProcessResult(ConsumeBenefitResult consumeBenefitResult,
                                                                             List<UnableReceiveBenefitDTO> unableReceiveBenefitDTOList) {
        SceneActionBizDataDTO sceneActionBizDataDTO = SceneActionBizDataDTO.builder()
                .consumeBenefitAvailableAmount(consumeBenefitResult.getBizData().getAvailableAmount())
                .consumeBenefitFreezeLogId(consumeBenefitResult.getBizData().getFreezeLogId())
                .build();

        if (consumeBenefitResult.isSuccess()) {
            return SceneActionProcessResult.success(sceneActionBizDataDTO);
        }

        if (consumeBenefitResult.isFailure()) {
            sceneActionBizDataDTO.setUnableReceiveBenefitDTOList(unableReceiveBenefitDTOList);
            return SceneActionProcessResult.failure(consumeBenefitResult.getMessage(), sceneActionBizDataDTO);
        }

        sceneActionBizDataDTO.setUnableReceiveBenefitDTOList(unableReceiveBenefitDTOList);
        return SceneActionProcessResult.processing(consumeBenefitResult.getMessage(), sceneActionBizDataDTO);
    }
}
