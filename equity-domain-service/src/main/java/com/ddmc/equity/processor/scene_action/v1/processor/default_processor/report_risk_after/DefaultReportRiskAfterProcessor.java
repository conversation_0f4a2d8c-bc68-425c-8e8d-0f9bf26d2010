package com.ddmc.equity.processor.scene_action.v1.processor.default_processor.report_risk_after;

import com.ddmc.equity.common.constant.Constants;
import com.ddmc.equity.common.enums.ProcessStatus;
import com.ddmc.equity.common.util.ThreadsUtils;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.ddmc.equity.model.convert.BaseRequestConvert;
import com.ddmc.equity.processor.risk.RiskControlContextDTO;
import com.ddmc.equity.processor.risk.RiskControlFactory;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessContext;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessResult;
import com.ddmc.equity.processor.scene_action.v1.enums.SceneActionProcessTypeEnum;
import com.ddmc.equity.processor.scene_action.v1.processor.SceneActionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 默认上报后置风控处理器
 */
@Slf4j
@Component
public class DefaultReportRiskAfterProcessor implements SceneActionProcessor {

    @Resource
    private RiskControlFactory riskControlFactory;

    @Override
    public SceneActionProcessTypeEnum getProcessTypeEnum() {
        return SceneActionProcessTypeEnum.REPORT_RISK_AFTER;
    }

    @Override
    public boolean isEnabled(SceneActionProcessContext context) {
        // 只有执行权益（EXECUTE_BFT）成功时才启用该节点
        return ProcessStatus.SUCCESS.equals(context.getBizData().getExecuteBftResultStatus());
    }

    @Override
    public SceneActionProcessResult process(SceneActionProcessContext context) {
        ThreadsUtils.runAsync(() -> {
            RiskControlContextDTO riskControlContextDTO = buildAfterRiskControlContext(context);
            riskControlFactory.process(riskControlContextDTO);
        }, ThreadsUtils.getOrdinaryDiscardThreadPoll(), Constants.SCENE_RECEIVE_RISK_AFTER);

        return SceneActionProcessResult.success(null);
    }

    @Override
    public boolean shouldChangeResult(SceneActionProcessContext context) {
        // 该节点处理器执行完成，不需要更新流程结果
        return false;
    }

    private RiskControlContextDTO buildAfterRiskControlContext(SceneActionProcessContext context) {
        FullBenefitInfoDTO actualReceiveBenefit = context.getBizData().getActualReceiveBenefit();

        // 领取后调用风控无须再传 headers
        BaseRequestDTO baseRequestDTO = BaseRequestConvert.INSTANCE.convertToBaseRequestDTOIgnoreHeaders(context);
        return RiskControlContextDTO.builder()
                .baseRequestDTO(baseRequestDTO)
                .sceneCode(context.getBizParams().getRiskSceneCodeAfter())
                .activityId(String.valueOf(actualReceiveBenefit.getActivityId()))
                .activityName(actualReceiveBenefit.getActivityName())
                .benefitInfoDTO(actualReceiveBenefit)
                .riskSceneStrategyDataMap(context.getBizParams().getRiskSceneStrategyDataMap())
                .build();
    }
}
