package com.ddmc.equity.processor.risk.impl;

import com.alibaba.fastjson.JSONObject;
import com.ddmc.equity.common.interceptor.annotation.MonitorSpan;
import com.ddmc.equity.common.util.risk.RiskCheckUtils;
import com.ddmc.equity.processor.risk.RiskControlContextDTO;
import com.ddmc.equity.processor.risk.RiskControlSceneCodeTypes;
import com.ddmc.risk.engine.vo.ResponseVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/6/18 16:45
 * @description
 */
@Slf4j
@Service
public class EventTurntableRiskControlProcessor extends DefaultSceneRiskControlProcessor {

    @Override
    protected List<String> getSceneCodes() {
        return Lists.newArrayList(RiskControlSceneCodeTypes.EVENT_TURNTABLE_ACTIVITY);
    }

    @Override
    protected JSONObject buildSceneStrategyData(RiskControlContextDTO context) {
        return super.buildSceneStrategyData(context);
    }

    @Override
    @MonitorSpan(name = "eventTurntable.handleRiskResponse")
    public String handleRiskResponse(ResponseVO riskResponse) {
        if (RiskCheckUtils.rickException(riskResponse)) {
            return RiskCheckUtils.RISK_EXCEPTION;
        }
        if (RiskCheckUtils.riskPass(riskResponse)) {
            return RiskCheckUtils.RISK_PASS;
        }
        // 赛事转盘抽奖活动，风控不通过发放兜底奖励
        return RiskCheckUtils.RISK_FALLBACK;
    }
}
