package com.ddmc.equity.processor.consume_benefit.strategy;

import com.ddmc.equity.processor.consume_benefit.dto.ConsumeBenefitContext;
import com.ddmc.equity.processor.consume_benefit.dto.ConsumeBenefitResult;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 消耗权益策略接口 - 根据消耗权益类型执行不同的处理逻辑
 */
public interface ConsumeBenefitStrategy {

    /**
     * 获取支持的消耗权益类型
     *
     * @return 消耗权益类型
     */
    Integer getSupportedConsumeBenefitType();

    /**
     * 检查用户可用权益数量是否足够
     *
     * @param context 消耗权益上下文
     * @return 权益处理结果
     */
    ConsumeBenefitResult checkConsumeBenefit(ConsumeBenefitContext context);

    /**
     * 扣减用户可用权益数量（冻结或直接扣减）
     *
     * @param context 消耗权益上下文
     * @return 权益处理结果
     */
    ConsumeBenefitResult deductConsumeBenefit(ConsumeBenefitContext context);

    /**
     * 确认扣减用户可用权益数量（成功时确认扣减）
     *
     * @param context 消耗权益上下文
     * @return 权益处理结果
     */
    @SuppressWarnings("UnusedReturnValue")
    ConsumeBenefitResult confirmConsumeBenefit(ConsumeBenefitContext context);

    /**
     * 释放用户可用权益数量（失败时释放）
     *
     * @param context 消耗权益上下文
     * @return 权益处理结果
     */
    @SuppressWarnings("UnusedReturnValue")
    ConsumeBenefitResult releaseConsumeBenefit(ConsumeBenefitContext context);
}
