package com.ddmc.equity.processor.consume_benefit.strategy;

import com.ddmc.equity.common.constant.MonitorConstants;
import com.ddmc.equity.common.util.CsossUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 消耗权益策略工厂 - 根据消耗权益类型获取对应的策略实现
 */
@Slf4j
@Component
public class ConsumeBenefitStrategyFactory {

    @Resource
    private List<ConsumeBenefitStrategy> strategies;

    private final Map<Integer, ConsumeBenefitStrategy> strategyMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        strategies.forEach(strategy -> {
            Integer consumeBenefitType = strategy.getSupportedConsumeBenefitType();
            if (consumeBenefitType != null) {
                strategyMap.put(consumeBenefitType, strategy);
            }
        });
        log.info("ConsumeBenefitStrategyFactory initialized successfully, registered {} strategies", strategyMap.size());
    }

    /**
     * 根据消耗权益类型获取对应的策略实现
     * 注意：如果找不到对应的策略实现，将返回 null，调用方需要自行处理 null 的情况
     *
     * @param consumeBenefitType 消耗权益类型
     * @return 策略实现，如果类型为空或找不到对应策略则返回 null
     */
    public ConsumeBenefitStrategy getStrategy(Integer consumeBenefitType) {
        if (consumeBenefitType == null) {
            log.warn("ConsumeBenefitStrategyFactory getStrategy ConsumeBenefitType is null");
            CsossUtils.logEventWithSpan(MonitorConstants.GET_CONSUME_BENEFIT_STRATEGY, "consume_benefit_type_is_null");
            return null;
        }

        ConsumeBenefitStrategy strategy = strategyMap.get(consumeBenefitType);
        if (strategy == null) {
            log.warn("ConsumeBenefitStrategyFactory getStrategy strategy is null consumeBenefitType={}", consumeBenefitType);
            CsossUtils.logEventWithSpan(MonitorConstants.GET_CONSUME_BENEFIT_STRATEGY, "consume_benefit_strategy_is_null");
            return null;
        }

        return strategy;
    }
}
