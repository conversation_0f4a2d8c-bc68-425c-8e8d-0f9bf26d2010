package com.ddmc.equity.processor.scene_action.v1.dto;

import com.ddmc.equity.common.util.activity.ActivityUtils;
import com.ddmc.equity.domain.converter.scene_action.SceneActionBizDataConverter;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.domain.entity.benefit.BenefitConvertEntity;
import com.ddmc.equity.dto.customer.BaseRequestDTO;
import com.ddmc.equity.model.dto.SceneActivityCacheDto;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 场景动作流程上下文 - 存储流程执行过程中的数据
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class SceneActionProcessContext extends BaseRequestDTO {

    /**
     * 请求来源 AppId
     */
    private String appId;

    /**
     * 请求来源页面 ID
     */
    private String pageId;

    /**
     * 请求来源
     */
    private String source;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 活动场景码
     *
     * @see com.ddmc.equity.enums.SceneCodeEnum
     */
    private String sceneCode;

    /**
     * 请求参数
     * <p>
     * 存储流程开始时设置的初始参数
     */
    @Builder.Default
    @NotNull
    private SceneActionBizParamsDTO bizParams = SceneActionBizParamsDTO.builder().build();

    /**
     * 流程中间产生的业务数据
     * <p>
     * 注意：在步骤执行时只读，步骤完成后才合并步骤结果
     */
    @Builder.Default
    @NotNull
    private SceneActionBizDataDTO bizData = SceneActionBizDataDTO.builder().build();

    /**
     * 合并步骤结果到上下文的 bizData 中
     *
     * @param stepBizData 步骤执行结果数据，可能为 null
     */
    public void mergeBizData(SceneActionBizDataDTO stepBizData) {
        if (stepBizData != null) {
            SceneActionBizDataConverter.INSTANCE.merge(this.bizData, stepBizData);
        }
    }

    /**
     * 获取请求流水号
     */
    public String getReqNo() {
        return getBizParams().getReqNo();
    }

    /**
     * 获取咨询时可领取的活动信息
     * 从咨询活动列表中获取唯一的活动信息，确保有且仅有一个
     *
     * @return 活动信息
     * @throws IllegalArgumentException 当没有可领取的活动或有多个可领取的活动时抛出异常
     */
    private SceneActivityCacheDto getConsultAvailableActivity() {
        List<SceneActivityCacheDto> activityList = getBizData().getConsultActivityList();

        Assert.isTrue(CollectionUtils.isNotEmpty(activityList), "未咨询到活动信息");
        Assert.isTrue(CollectionUtils.size(activityList) == 1, "咨询到多个活动信息");

        return activityList.get(0);
    }

    /**
     * 获取咨询时可领取的权益信息
     * 从咨询活动列表中获取唯一的权益信息，确保有且仅有一个
     * <p>
     * 注意：当前系统设计只支持一次领取一个权益，不支持批量领取
     * 如果检测到多个可领取的权益会抛出异常，需要业务层面确保单次流程只处理一个权益
     *
     * @return 权益信息
     * @throws IllegalArgumentException 当没有可领取的权益或有多个可领取的权益时抛出异常
     */
    public FullBenefitInfoDTO getConsultAvailableBenefit() {
        SceneActivityCacheDto consultActivity = getConsultAvailableActivity();
        List<FullBenefitInfoDTO> fullBenefitInfoDTOList = BenefitConvertEntity
                .convertToFullBenefitInfoDTOList(consultActivity);

        Assert.isTrue(CollectionUtils.isNotEmpty(fullBenefitInfoDTOList), "没有可领取的权益");
        Assert.isTrue(CollectionUtils.size(fullBenefitInfoDTOList) == 1, "有多个可领取的权益");

        return fullBenefitInfoDTOList.get(0);
    }

    /**
     * 获取领取的权益信息
     * <p>
     * 优先使用风控替换权益信息，
     * 如果风控替换权益信息为空，则使用咨询时可领取的权益信息
     *
     * @return 权益信息
     */
    public FullBenefitInfoDTO getReceiveBenefitInfoDTO() {
        // 优先使用风控替换权益信息
        FullBenefitInfoDTO riskReplaceBenefit = getBizData().getRiskReplaceBenefit();
        if (riskReplaceBenefit != null) {
            return riskReplaceBenefit;
        }

        return getConsultAvailableBenefit();
    }

    /**
     * 获取消耗权益类型
     * 从咨询活动中获取消耗权益类型，用于权益消耗计算
     *
     * @return 消耗权益类型
     * @throws IllegalArgumentException 当未咨询到活动信息或咨询到多个活动信息时抛出异常
     */
    public Integer getConsumeBenefitType() {
        SceneActivityCacheDto consultActivity = getConsultAvailableActivity();
        return ActivityUtils.getConsumeBenefitType(consultActivity);
    }

    /**
     * 获取消耗权益数量
     * 从咨询活动中获取消耗权益数量，用于权益消耗计算
     *
     * @return 消耗权益数量
     * @throws IllegalArgumentException 当未咨询到活动信息或咨询到多个活动信息时抛出异常
     */
    public String getConsumeBenefitAmount() {
        SceneActivityCacheDto consultActivity = getConsultAvailableActivity();
        return ActivityUtils.getConsumeBenefitAmount(consultActivity);
    }
}
