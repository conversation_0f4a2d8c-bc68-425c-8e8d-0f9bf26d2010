package com.ddmc.equity.processor.risk.impl;

import com.alibaba.fastjson.JSONObject;
import com.ddmc.equity.common.util.NumberUtils;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.dto.customer.ReceiveExternalInfoDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.model.dto.risk.VipFissionRiskStrategyDataDTO;
import com.ddmc.equity.processor.risk.AbstractRiskControlProcessor;
import com.ddmc.equity.processor.risk.RiskControlContextDTO;
import com.ddmc.equity.processor.risk.RiskControlSceneCodeTypes;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/4/25 14:06
 * @description
 */
@Slf4j
@Service
public class VipFissionRiskControlProcessor extends AbstractRiskControlProcessor {

    @Override
    protected List<String> getSceneCodes() {
        return Lists.newArrayList(RiskControlSceneCodeTypes.VIP_CARD_ACTIVITY_INVITE_RETURN);
    }

    @Override
    protected JSONObject buildSceneStrategyData(RiskControlContextDTO context) {
        FullBenefitInfoDTO benefitInfoDTO = context.getBenefitInfoDTO();
        ReceiveExternalInfoDTO receiveExternalInfoDTO = context.getReceiveExternalInfoDTO();
        VipFissionRiskStrategyDataDTO riskStrategyDataDTO = VipFissionRiskStrategyDataDTO.builder()
                .activityId(Objects.isNull(benefitInfoDTO) ? null : benefitInfoDTO.getExternalId())
                .activityName(Objects.isNull(benefitInfoDTO) ? null : benefitInfoDTO.getActivityName())
                .strategyName(Objects.isNull(benefitInfoDTO) ? null : benefitInfoDTO.getStrategyName())
                .invtUid(Objects.isNull(receiveExternalInfoDTO) ? null : receiveExternalInfoDTO.getInviter())
                .build();
        fillRiskStrategyDataPrizeInfo(riskStrategyDataDTO, benefitInfoDTO);
        return parseToJsonObject(riskStrategyDataDTO);
    }

    private void fillRiskStrategyDataPrizeInfo(VipFissionRiskStrategyDataDTO riskStrategyDataDTO,
                                               FullBenefitInfoDTO benefitInfoDTO) {
        if (Objects.isNull(benefitInfoDTO)) {
            return;
        }
        Integer benefitType = benefitInfoDTO.getBenefitType();
        String benefitValue = benefitInfoDTO.getBenefitValue();
        if (Objects.equals(benefitType, BenefitTypeEnum.BALANCE.getId())
                || Objects.equals(benefitType, BenefitTypeEnum.RANDOM_BALANCE.getId())) {
            riskStrategyDataDTO.setBalanceMoney(benefitValue);
            return;
        }
        if (Objects.equals(benefitType, BenefitTypeEnum.VIP_DAYS.getId())) {
            riskStrategyDataDTO.setVipDays(NumberUtils.convertToInteger(benefitValue));
        }
    }
}
