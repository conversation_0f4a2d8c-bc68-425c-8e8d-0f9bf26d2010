package com.ddmc.equity.processor.risk.impl;

import com.alibaba.fastjson.JSONObject;
import com.ddmc.equity.domain.dto.FullBenefitInfoDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.model.dto.risk.CnyStreetAfterRiskStrategyDataDTO;
import com.ddmc.equity.processor.risk.AbstractRiskControlProcessor;
import com.ddmc.equity.processor.risk.RiskControlContextDTO;
import com.ddmc.equity.processor.risk.RiskControlSceneCodeTypes;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2023/12/25 20:07
 * @description
 */
@Slf4j
@Service
public class CnyStreetAfterRiskControlProcessor extends AbstractRiskControlProcessor {

    @Override
    protected List<String> getSceneCodes() {
        return Lists.newArrayList(RiskControlSceneCodeTypes.CNY_CHEST_AFTER, RiskControlSceneCodeTypes.CNY_BUSINESS_AFTER);
    }

    @Override
    protected JSONObject buildSceneStrategyData(RiskControlContextDTO context) {
        FullBenefitInfoDTO benefitInfoDTO = context.getBenefitInfoDTO();
        String awardType = null;
        CnyStreetAfterRiskStrategyDataDTO.AwardDTO award = null;
        if (Objects.nonNull(benefitInfoDTO)) {
            awardType = Optional.of(benefitInfoDTO.getBenefitType())
                    .map(BenefitTypeEnum::getById).map(Enum::name).map(StringUtils::lowerCase)
                    .orElse(null);
            award = CnyStreetAfterRiskStrategyDataDTO.AwardDTO.builder()
                    .value(benefitInfoDTO.getBenefitValue())
                    .name(benefitInfoDTO.getBenefitName())
                    .build();
        }
        CnyStreetAfterRiskStrategyDataDTO riskStrategyDataDTO = CnyStreetAfterRiskStrategyDataDTO.builder()
                .activityId(context.getActivityId())
                .activityName(context.getActivityName())
                .awardType(awardType)
                .award(award)
                .build();
        return parseToJsonObject(riskStrategyDataDTO);
    }
}
