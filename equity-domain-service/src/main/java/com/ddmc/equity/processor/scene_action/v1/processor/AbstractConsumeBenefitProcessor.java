package com.ddmc.equity.processor.scene_action.v1.processor;

import com.ddmc.equity.processor.consume_benefit.strategy.ConsumeBenefitStrategyFactory;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessContext;
import com.ddmc.equity.processor.scene_action.v1.dto.SceneActionProcessResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Set;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/7/16 15:52
 * @description 消耗权益处理器抽象类
 */
public abstract class AbstractConsumeBenefitProcessor implements SceneActionProcessor {

    /**
     * 需要消耗权益的活动场景列表。默认为 SPIN_LOTTERY-转盘抽奖、POINT_EXCHANGE-积分兑换
     */
    @Value("${scene.action.consume.benefit.required.scene.codes:SPIN_LOTTERY,POINT_EXCHANGE}")
    private Set<String> consumeBenefitRequiredSceneCodes;

    /**
     * 支持的消耗权益类型列表。默认为 1-积分、16-活动次数、21-金豆
     */
    @Value("${scene.action.consume.benefit.supported.types:1,16,21}")
    private Set<Integer> consumeBenefitSupportedTypes;

    @Resource
    protected ConsumeBenefitStrategyFactory strategyFactory;

    /**
     * 判断指定活动场景是否需要消耗权益
     *
     * @param sceneCode 活动场景
     * @return true - 需要消耗权益，false - 不需要消耗权益
     */
    protected boolean isConsumeBenefitRequired(String sceneCode) {
        return CollectionUtils.isNotEmpty(consumeBenefitRequiredSceneCodes) &&
                consumeBenefitRequiredSceneCodes.contains(sceneCode);
    }

    /**
     * 检查消耗权益的基础参数有效性
     *
     * @param context 场景处理上下文
     * @return 如果参数有效返回 null，否则返回失败结果
     */
    protected SceneActionProcessResult checkConsumeBenefit(SceneActionProcessContext context) {
        // 检查消耗权益类型
        Integer consumeBenefitType = context.getConsumeBenefitType();
        if (consumeBenefitType == null) {
            return SceneActionProcessResult.failure("消耗权益类型不能为空，请检查配置", null);
        }

        // 检查消耗权益类型是否支持
        if (CollectionUtils.isEmpty(consumeBenefitSupportedTypes) || !consumeBenefitSupportedTypes.contains(consumeBenefitType)) {
            return SceneActionProcessResult.failure("不支持的消耗权益类型：" + consumeBenefitType + "，请检查配置", null);
        }

        // 检查消耗权益数量
        String consumeBenefitAmount = context.getConsumeBenefitAmount();
        if (StringUtils.isBlank(consumeBenefitAmount)) {
            return SceneActionProcessResult.failure("消耗权益数量不能为空，请检查参数", null);
        }

        // 检查消耗权益数量格式
        if (!NumberUtils.isCreatable(consumeBenefitAmount)) {
            return SceneActionProcessResult.failure("消耗权益数量格式错误：" + consumeBenefitAmount + "，必须是有效数字", null);
        }

        // 检查消耗权益数量是否为正数
        try {
            BigDecimal amount = new BigDecimal(consumeBenefitAmount);
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return SceneActionProcessResult.failure("消耗权益数量必须大于 0，当前值：" + consumeBenefitAmount, null);
            }
        } catch (NumberFormatException e) {
            return SceneActionProcessResult.failure("消耗权益数量转换失败：" + consumeBenefitAmount, null);
        }

        // 所有检查通过，返回 null 表示验证成功
        return null;
    }
}
