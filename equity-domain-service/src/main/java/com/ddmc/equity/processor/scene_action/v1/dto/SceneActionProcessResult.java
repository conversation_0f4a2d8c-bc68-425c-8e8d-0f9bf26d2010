package com.ddmc.equity.processor.scene_action.v1.dto;

import com.ddmc.equity.common.enums.ProcessStatus;
import com.ddmc.equity.domain.converter.scene_action.SceneActionBizDataConverter;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2025/6/27 16:30
 * @description 场景动作流程节点处理结果
 */
@Data
@NoArgsConstructor
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
public class SceneActionProcessResult {

    /**
     * 状态
     */
    private ProcessStatus status;

    /**
     * 消息
     */
    private String message;

    /**
     * 业务数据
     */
    @Builder.Default
    @NotNull
    private SceneActionBizDataDTO bizData = SceneActionBizDataDTO.builder().build();

    /**
     * 合并步骤结果到 bizData 中
     *
     * @param stepBizData 步骤执行结果数据，可能为 null
     */
    public void mergeBizData(SceneActionBizDataDTO stepBizData) {
        if (stepBizData != null) {
            SceneActionBizDataConverter.INSTANCE.merge(this.bizData, stepBizData);
        }
    }

    /**
     * 成功
     */
    public static SceneActionProcessResult success(SceneActionBizDataDTO bizData) {
        SceneActionProcessResult result = SceneActionProcessResult.builder()
                .status(ProcessStatus.SUCCESS)
                .build();
        if (bizData != null) {
            result.setBizData(bizData);
        }
        return result;
    }

    /**
     * 失败
     */
    public static SceneActionProcessResult failure(String message, SceneActionBizDataDTO bizData) {
        SceneActionProcessResult result = SceneActionProcessResult.builder()
                .status(ProcessStatus.FAILURE)
                .message(message)
                .build();
        if (bizData != null) {
            result.setBizData(bizData);
        }
        return result;
    }

    /**
     * 处理中
     */
    public static SceneActionProcessResult processing(String message, SceneActionBizDataDTO bizData) {
        SceneActionProcessResult result = SceneActionProcessResult.builder()
                .status(ProcessStatus.PROCESSING)
                .message(message)
                .build();
        if (bizData != null) {
            result.setBizData(bizData);
        }
        return result;
    }

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return ProcessStatus.isSuccess(status);
    }

    /**
     * 是否失败
     */
    public boolean isFailure() {
        return ProcessStatus.isFailure(status);
    }

    /**
     * 是否处理完成（成功或失败，非处理中）
     */
    public boolean isCompleted() {
        return ProcessStatus.isCompleted(status);
    }
}
