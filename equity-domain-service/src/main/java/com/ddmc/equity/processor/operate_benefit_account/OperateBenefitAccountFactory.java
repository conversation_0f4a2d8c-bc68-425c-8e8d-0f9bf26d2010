package com.ddmc.equity.processor.operate_benefit_account;

import com.ddmc.equity.common.util.ThreadsUtils;
import com.ddmc.equity.domain.dto.account.UniversalAccountDTO;
import com.ddmc.equity.dto.customer.account.UniversalAccountsReqDTO;
import com.ddmc.equity.enums.BenefitTypeEnum;
import com.ddmc.equity.processor.operate_benefit_account.impl.DefaultOperateBenefitAccountProcessor;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2024/7/31 16:19
 * @description
 */
@Slf4j
@Component
public class OperateBenefitAccountFactory {

    /**
     * 获取用户权益账户列表等待时长（毫秒）
     */
    @Value("${equity.get.user.accounts.wait.time:500}")
    private Integer getUserAccountsWaitTime;

    @Autowired
    private List<OperateBenefitAccountProcessor> processors;
    @Autowired
    private DefaultOperateBenefitAccountProcessor defaultProcessor;

    public OperateBenefitAccountProcessor getOperateBenefitAccountProcessor(Integer benefitType) {
        return processors.stream()
                .filter(e -> Objects.equals(e.getBenefitType(), benefitType))
                .findFirst()
                .orElse(defaultProcessor);
    }

    private List<Integer> getSpecialProcessorsBenefitTypes() {
        return processors.stream()
                .map(OperateBenefitAccountProcessor::getBenefitType)
                .filter(Objects::nonNull)
                .filter(e -> Objects.nonNull(BenefitTypeEnum.getById(e)))
                .collect(Collectors.toList());
    }

    private Pair<List<Integer>, List<Integer>> getProcessorsBenefitTypes(List<Integer> benefitTypes) {
        List<Integer> currDefaultProcessorsBenefitTypes = Lists.newArrayList();
        List<Integer> currSpecialProcessorsBenefitTypes = Lists.newArrayList();
        if (CollectionUtils.isEmpty(benefitTypes)) {
            return Pair.of(currDefaultProcessorsBenefitTypes, currSpecialProcessorsBenefitTypes);
        }

        List<Integer> specialProcessorsBenefitTypes = getSpecialProcessorsBenefitTypes();
        benefitTypes.forEach(benefitType -> {
            if (specialProcessorsBenefitTypes.contains(benefitType)) {
                currSpecialProcessorsBenefitTypes.add(benefitType);
                return;
            }
            currDefaultProcessorsBenefitTypes.add(benefitType);
        });
        return Pair.of(currDefaultProcessorsBenefitTypes, currSpecialProcessorsBenefitTypes);
    }

    public List<UniversalAccountDTO> getAllUserAccounts(UniversalAccountsReqDTO req) {
        String userId = req.getBaseRequestDTO().getUserId();
        Long useActivityId = req.getUseActivityId();
        Pair<List<Integer>, List<Integer>> processorsBenefitTypes = getProcessorsBenefitTypes(req.getBenefitTypes());
        if (needAsyncGetAllUserAccounts(processorsBenefitTypes)) {
            // 异步查询不同权益类型的权益账户列表
            return asyncGetAllUserAccounts(userId, useActivityId, processorsBenefitTypes);
        }
        // 同步查询不同权益类型的权益账户列表
        return syncGetAllUserAccounts(userId, useActivityId, processorsBenefitTypes);
    }

    private List<UniversalAccountDTO> asyncGetAllUserAccounts(String userId, Long useActivityId,
                                                              Pair<List<Integer>, List<Integer>> processorsBenefitTypes) {
        List<Integer> defaultProcessorsBenefitTypes = processorsBenefitTypes.getFirst();
        List<Integer> specialProcessorsBenefitTypes = processorsBenefitTypes.getSecond();

        // 异步查询不同权益类型的权益账户列表，并聚合
        List<CompletableFuture<Void>> asyncTasks = Lists.newArrayList();
        List<UniversalAccountDTO> allUserAccounts = Lists.newCopyOnWriteArrayList();
        if (CollectionUtils.isNotEmpty(defaultProcessorsBenefitTypes)) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() ->
                    getAndAddUserAccounts(userId, useActivityId, defaultProcessorsBenefitTypes, allUserAccounts,
                            defaultProcessor), ThreadsUtils.getCoreThreadPoll()
            );
            asyncTasks.add(future);
        }
        if (CollectionUtils.isNotEmpty(specialProcessorsBenefitTypes)) {
            specialProcessorsBenefitTypes.forEach(benefitType -> {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() ->
                        getAndAddUserAccounts(userId, useActivityId, Lists.newArrayList(benefitType), allUserAccounts,
                                getOperateBenefitAccountProcessor(benefitType)), ThreadsUtils.getCoreThreadPoll()
                );
                asyncTasks.add(future);
            });
        }

        // 等待异步处理结果
        if (CollectionUtils.isNotEmpty(asyncTasks)) {
            ThreadsUtils.getCompletableFutureList(asyncTasks, getUserAccountsWaitTime, TimeUnit.MILLISECONDS);
        }
        return allUserAccounts;
    }

    private List<UniversalAccountDTO> syncGetAllUserAccounts(String userId, Long useActivityId,
                                                             Pair<List<Integer>, List<Integer>> processorsBenefitTypes) {
        List<Integer> defaultProcessorsBenefitTypes = processorsBenefitTypes.getFirst();
        List<Integer> specialProcessorsBenefitTypes = processorsBenefitTypes.getSecond();

        List<UniversalAccountDTO> allUserAccounts = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(defaultProcessorsBenefitTypes)) {
            getAndAddUserAccounts(userId, useActivityId, defaultProcessorsBenefitTypes, allUserAccounts, defaultProcessor);
        }
        if (CollectionUtils.isNotEmpty(specialProcessorsBenefitTypes)) {
            specialProcessorsBenefitTypes.forEach(benefitType ->
                    getAndAddUserAccounts(userId, useActivityId, Lists.newArrayList(benefitType), allUserAccounts,
                            getOperateBenefitAccountProcessor(benefitType))
            );
        }
        return allUserAccounts;
    }

    private boolean needAsyncGetAllUserAccounts(Pair<List<Integer>, List<Integer>> processorsBenefitTypes) {
        List<Integer> defaultProcessorsBenefitTypes = processorsBenefitTypes.getFirst();
        List<Integer> specialProcessorsBenefitTypes = processorsBenefitTypes.getSecond();
        int getTimes = CollectionUtils.size(specialProcessorsBenefitTypes);
        getTimes = CollectionUtils.isEmpty(defaultProcessorsBenefitTypes) ? getTimes : getTimes + 1;
        return getTimes >= 2;
    }

    private void getAndAddUserAccounts(String userId, Long useActivityId, List<Integer> benefitTypes,
                                       List<UniversalAccountDTO> allUserAccounts,
                                       OperateBenefitAccountProcessor processor) {
        List<UniversalAccountDTO> userAccounts = processor.getUserAccounts(userId, useActivityId, benefitTypes);
        if (CollectionUtils.isNotEmpty(userAccounts)) {
            allUserAccounts.addAll(userAccounts);
        }
    }
}
