package com.ddmc.equity.account;

import com.alibaba.fastjson.JSON;
import com.ddmc.equity.common.enums.ExceptionEnum;
import com.ddmc.equity.common.enums.OperateTypeEnum;
import com.ddmc.equity.common.enums.StatusEnum;
import com.ddmc.equity.domain.dto.EquityRpcDto;
import com.ddmc.equity.domain.dto.ReceiveBenefitResDTO;
import com.ddmc.equity.domain.dto.SubAccountRecordDTO;
import com.ddmc.equity.infra.repository.dao.UniversalAccountRecordDO;
import com.ddmc.equity.domain.service.equityAccount.UniversalAccountRecordDomainService;
import com.ddmc.equity.account.BenefitReceiveRuleContext;

import com.ddmc.equity.infra.cache.redis.RedisCache;

import com.ddmc.equity.common.util.CatMonitorUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AbstractEquityAccountOperate 单元测试类
 * 
 * 测试覆盖范围：
 * 1. 抽象方法的默认实现测试
 * 2. 具体方法的业务逻辑测试
 * 3. 异常处理和边界条件测试
 * 4. 复杂业务流程测试
 * 
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({JSON.class})
public class AbstractEquityAccountOperateTest {

    @InjectMocks
    private TestableAbstractEquityAccountOperate abstractEquityAccountOperate;

    @Mock
    private UniversalAccountRecordDomainService universalAccountRecordDomainService;





    @Mock
    private RedisCache redisCache;



    @Mock
    private CatMonitorUtil catMonitorUtil;

    private EquityAccountContext testContext;
    private Map<String, List<String>> sceneMethod;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(JSON.class);
        
        // 初始化测试上下文
        testContext = createTestEquityAccountContext();
        
        // 初始化场景方法映射
        sceneMethod = Maps.newHashMap();
        sceneMethod.put("test_scene", Arrays.asList("provideEquity", "useEquity", "fallbackEquity"));
        abstractEquityAccountOperate.setSceneMethod(sceneMethod);
        
        // 设置默认 Mock 行为
        when(JSON.toJSONString(any())).thenReturn("{\"test\":\"data\"}");
    }

    /**
     * 创建测试用的 EquityAccountContext
     */
    private EquityAccountContext createTestEquityAccountContext() {
        EquityAccountContext context = new EquityAccountContext();
        context.setAppId("test_app");
        context.setPageId("test_page");
        context.setSource("test_source");
        context.setCityCode("test_city");
        context.setStationId("test_station");
        context.setLongitude("116.123456");
        context.setLatitude("39.123456");
        context.setSerialNumber("test_serial_123");
        context.setSceneCode("test_scene");
        context.setUid("test_user_123");
        context.setAccountType(1);
        context.setOperateType(OperateTypeEnum.PROVIDE.getCode());
        context.setOperateCount(100);
        context.setOperator("test_operator");
        context.setActivityId(1001L);
        context.setActivityName("测试活动");
        context.setExternalType("external_type");
        context.setExternalId("external_id_123");
        context.setStrategyId(2001L);
        context.setBenefitId(3001L);
        context.setEquityValue("100");
        context.setSendAmount(100);
        context.setBalanceMoney(1000L);
        context.setStatus(StatusEnum.INIT.getCode());
        context.setAccountId(4001L);
        context.setAccountDetailId(5001L);
        context.setAccountRecordId(6001L);
        context.setDoRpcUniqueSerialNumber("rpc_serial_123");
        return context;
    }

    /**
     * 创建测试用的 UniversalAccountRecordDO
     */
    private UniversalAccountRecordDO createTestUniversalAccountRecord() {
        UniversalAccountRecordDO record = new UniversalAccountRecordDO();
        record.setId(1L);
        record.setUserId("test_user_123");
        record.setSceneCode("test_scene");
        record.setOperateType(OperateTypeEnum.PROVIDE.getCode());
        record.setReqNo("test_req_123");
        record.setAuxKey("test_aux_123");
        record.setStatus(StatusEnum.INIT.getCode());
        record.setStrategyId(2001L);
        record.setBenefitId(3001L);
        record.setFreezeReceiveLimitId(7001L);
        return record;
    }

    /**
     * 创建测试用的 BenefitReceiveRuleContext
     */
    private BenefitReceiveRuleContext createTestBenefitReceiveRuleContext() {
        BenefitReceiveRuleContext context = new BenefitReceiveRuleContext();
        context.setUserId("test_user_123");
        context.setStrategyId(2001L);
        context.setBenefitId(3001L);
        context.setSceneCode("test_scene");
        context.setReqNo("test_req_123");
        return context;
    }

    // ==================== 抽象方法测试 ====================

    @Test
    public void testInitRecord() {
        Long result = abstractEquityAccountOperate.initRecord(testContext);
        assertNull("initRecord 默认实现应该返回 null", result);
    }

    @Test
    public void testCheckUseEquity() {
        boolean result = abstractEquityAccountOperate.checkUseEquity(testContext);
        assertTrue("checkUseEquity 默认实现应该返回 true", result);
    }

    @Test
    public void testDoUseEquity() {
        EquityRpcDto result = abstractEquityAccountOperate.doUseEquity(testContext);
        assertNotNull("doUseEquity 默认实现应该返回非空对象", result);
        assertEquals("状态应该为 SUCCESS", StatusEnum.SUCCESS, result.getStatusEnum());
        assertEquals("代码应该为 SUCCESS", ExceptionEnum.SUCCESS.getCode(), result.getCode());
        assertEquals("消息应该为 SUCCESS", ExceptionEnum.SUCCESS.getMessage(), result.getMessage());
    }

    @Test
    public void testCheckFallbackEquity() {
        boolean result = abstractEquityAccountOperate.checkFallbackEquity(testContext);
        assertTrue("checkFallbackEquity 默认实现应该返回 true", result);
    }

    @Test
    public void testDoProvideEquity() {
        EquityRpcDto result = abstractEquityAccountOperate.doProvideEquity(testContext);
        assertNotNull("doProvideEquity 默认实现应该返回非空对象", result);
        assertEquals("状态应该为 SUCCESS", StatusEnum.SUCCESS, result.getStatusEnum());
        assertEquals("代码应该为 SUCCESS", ExceptionEnum.SUCCESS.getCode(), result.getCode());
        assertEquals("消息应该为 SUCCESS", ExceptionEnum.SUCCESS.getMessage(), result.getMessage());
    }

    @Test
    public void testQueryEquityBenefitId() {
        Long result = abstractEquityAccountOperate.queryEquityBenefitId(testContext);
        assertNull("queryEquityBenefitId 默认实现应该返回 null", result);
    }

    @Test
    public void testQueryEquityAccountInfo() {
        SubAccountRecordDTO result = abstractEquityAccountOperate.queryEquityAccountInfo(testContext);
        assertNull("queryEquityAccountInfo 默认实现应该返回 null", result);
    }

    @Test
    public void testQueryEquityAccountInfos() {
        List<SubAccountRecordDTO> result = abstractEquityAccountOperate.queryEquityAccountInfos(testContext);
        assertNull("queryEquityAccountInfos 默认实现应该返回 null", result);
    }

    @Test
    public void testQueryEquityAccountInfosByPage() {
        List<SubAccountRecordDTO> result = abstractEquityAccountOperate.queryEquityAccountInfosByPage(testContext);
        assertNull("queryEquityAccountInfosByPage 默认实现应该返回 null", result);
    }

    @Test
    public void testHasSucceededByUniqueKey() {
        Boolean result = abstractEquityAccountOperate.hasSucceededByUniqueKey("test_user", 1, "test_req");
        assertNull("hasSucceededByUniqueKey 默认实现应该返回 null", result);
    }

    // ==================== 具体方法测试 ====================

    @Test
    public void testProvideEquity_Success() {
        // 准备测试数据
        Long recordId = 1L;
        abstractEquityAccountOperate.setInitRecordResult(recordId);
        abstractEquityAccountOperate.setDoProvideEquityResult(createSuccessEquityRpcDto());
        
        // 模拟依赖调用
        when(universalAccountRecordDomainService.queryExistAccountRecords(anyString(), anyLong(), anyLong(), anyInt(), anyString(), anyString(), anyList()))
            .thenReturn(Lists.newArrayList());
        
        // 执行测试
        boolean result = abstractEquityAccountOperate.provideEquity(testContext);
        
        // 验证结果
        assertTrue("provideEquity 应该返回 true", result);
        verify(catMonitorUtil, times(1)).logEventWithSpan(anyString(), anyString(), anyString(), anyString());
    }

    @Test
    public void testProvideEquity_Exception() {
        // 准备测试数据 - 模拟异常
        abstractEquityAccountOperate.setInitRecordThrowException(true);
        
        // 执行测试
        boolean result = abstractEquityAccountOperate.provideEquity(testContext);
        
        // 验证结果
        assertFalse("provideEquity 遇到异常应该返回 false", result);
    }

    @Test
    public void testUseEquity_Success() {
        // 准备测试数据
        abstractEquityAccountOperate.setCheckUseEquityResult(true);
        abstractEquityAccountOperate.setDoUseEquityResult(createSuccessEquityRpcDto());
        
        // 执行测试
        boolean result = abstractEquityAccountOperate.useEquity(testContext);
        
        // 验证结果
        assertTrue("useEquity 应该返回 true", result);
    }

    @Test
    public void testUseEquity_CheckFailed() {
        // 准备测试数据
        abstractEquityAccountOperate.setCheckUseEquityResult(false);
        
        // 执行测试
        boolean result = abstractEquityAccountOperate.useEquity(testContext);
        
        // 验证结果
        assertFalse("useEquity 检查失败应该返回 false", result);
    }

    @Test
    public void testFallbackEquity_Success() {
        // 准备测试数据
        Long recordId = 1L;
        abstractEquityAccountOperate.setInitRecordResult(recordId);
        abstractEquityAccountOperate.setCheckFallbackEquityResult(true);
        abstractEquityAccountOperate.setDoFallbackEquityResult(createSuccessEquityRpcDto());
        
        // 模拟依赖调用
        when(universalAccountRecordDomainService.queryExistAccountRecords(anyString(), anyLong(), anyLong(), anyInt(), anyString(), anyString(), anyList()))
            .thenReturn(Lists.newArrayList());
        
        // 执行测试
        boolean result = abstractEquityAccountOperate.fallbackEquity(testContext);
        
        // 验证结果
        assertTrue("fallbackEquity 应该返回 true", result);
    }

    @Test
    public void testGetExceptionStatus_SceneMethodExists() {
        // 准备测试数据
        testContext.setSceneCode("test_scene");
        
        // 执行测试
        int result = abstractEquityAccountOperate.getExceptionStatus(testContext, "useEquity");
        
        // 验证结果
        assertEquals("应该返回 PROCESSING 状态", StatusEnum.PROCESSING.getCode(), result);
    }

    @Test
    public void testGetExceptionStatus_SceneMethodNotExists() {
        // 准备测试数据
        testContext.setSceneCode("unknown_scene");
        
        // 执行测试
        int result = abstractEquityAccountOperate.getExceptionStatus(testContext, "provideEquity");
        
        // 验证结果
        assertEquals("应该返回 FAIL 状态", StatusEnum.FAIL.getCode(), result);
    }

    @Test
    public void testGetExceptionStatus_FirstMethod() {
        // 准备测试数据
        testContext.setSceneCode("test_scene");
        
        // 执行测试
        int result = abstractEquityAccountOperate.getExceptionStatus(testContext, "provideEquity");
        
        // 验证结果
        assertEquals("第一个方法应该返回 FAIL 状态", StatusEnum.FAIL.getCode(), result);
    }

    // ==================== 复杂业务流程测试 ====================

    @Test
    public void testReceiveSceneBenefit_Success() {
        // 准备测试数据
        BenefitReceiveRuleContext ruleContext = createTestBenefitReceiveRuleContext();
        UniversalAccountRecordDO record = createTestUniversalAccountRecord();
        
        // 模拟依赖调用
        when(redisCache.lock(anyString(), anyLong(), any(TimeUnit.class))).thenReturn(true);

        when(universalAccountRecordDomainService.queryOneByUniqueKey(anyString(), anyString(), anyInt(), anyString(), anyString())).thenReturn(null);
        when(universalAccountRecordDomainService.save(any(UniversalAccountRecordDO.class))).thenReturn(1L);

        
        abstractEquityAccountOperate.setRpcAndHandlerResultSuccess(true);
        
        // 执行测试
        ReceiveBenefitResDTO result = abstractEquityAccountOperate.receiveSceneBenefit(testContext);
        
        // 验证结果
        assertNotNull("receiveSceneBenefit 应该返回非空结果", result);
        assertEquals("状态应该为 SUCCESS", StatusEnum.SUCCESS, result.getStatusEnum());
        
        // 验证关键方法调用
        verify(redisCache, times(1)).lock(anyString(), anyLong(), any(TimeUnit.class));


    }

    @Test
    public void testReceiveSceneBenefit_LockFailed() {
        // 模拟加锁失败
        when(redisCache.lock(anyString(), anyLong(), any(TimeUnit.class))).thenReturn(false);
        
        // 执行测试
        ReceiveBenefitResDTO result = abstractEquityAccountOperate.receiveSceneBenefit(testContext);
        
        // 验证结果
        assertNotNull("receiveSceneBenefit 应该返回非空结果", result);
        assertEquals("状态应该为 PROCESSING", StatusEnum.PROCESSING, result.getStatusEnum());
        assertEquals("错误码应该为 TOO_FREQUENTING", ExceptionEnum.TOO_FREQUENTING.getCode(), result.getCode());
    }

    @Test
    public void testReceiveSceneBenefit_ExistingRecord() {
        // 准备测试数据
        UniversalAccountRecordDO existingRecord = createTestUniversalAccountRecord();
        existingRecord.setStatus(StatusEnum.SUCCESS.getCode());
        
        // 模拟依赖调用
        when(redisCache.lock(anyString(), anyLong(), any(TimeUnit.class))).thenReturn(true);

        when(universalAccountRecordDomainService.queryOneByUniqueKey(anyString(), anyString(), anyInt(), anyString(), anyString())).thenReturn(existingRecord);
        
        // 执行测试
        ReceiveBenefitResDTO result = abstractEquityAccountOperate.receiveSceneBenefit(testContext);
        
        // 验证结果
        assertNotNull("receiveSceneBenefit 应该返回非空结果", result);
        assertEquals("状态应该为 SUCCESS", StatusEnum.SUCCESS, result.getStatusEnum());
        
        // 验证不会重复处理
        verify(universalAccountRecordDomainService, never()).save(any(UniversalAccountRecordDO.class));
    }

    @Test
    public void testReceiveSceneBenefit_FreezeLimitFailed() {
        // 准备测试数据
        BenefitReceiveRuleContext ruleContext = createTestBenefitReceiveRuleContext();
        
        // 模拟依赖调用
        when(redisCache.lock(anyString(), anyLong(), any(TimeUnit.class))).thenReturn(true);

        when(universalAccountRecordDomainService.queryOneByUniqueKey(anyString(), anyString(), anyInt(), anyString(), anyString())).thenReturn(null);
        when(universalAccountRecordDomainService.save(any(UniversalAccountRecordDO.class))).thenReturn(1L);

        
        // 执行测试
        ReceiveBenefitResDTO result = abstractEquityAccountOperate.receiveSceneBenefit(testContext);
        
        // 验证结果
        assertNotNull("receiveSceneBenefit 应该返回非空结果", result);
        assertEquals("状态应该为 PROCESSING", StatusEnum.PROCESSING, result.getStatusEnum());
        assertEquals("错误码应该为 RECEIVE_OVER_LIMIT", ExceptionEnum.RECEIVE_OVER_LIMIT.getCode(), result.getCode());
    }

    @Test
    public void testReceiveSceneBenefit_StockLimitFailed() {
        // 准备测试数据
        BenefitReceiveRuleContext ruleContext = createTestBenefitReceiveRuleContext();
        
        // 模拟依赖调用
        when(redisCache.lock(anyString(), anyLong(), any(TimeUnit.class))).thenReturn(true);

        when(universalAccountRecordDomainService.queryOneByUniqueKey(anyString(), anyString(), anyInt(), anyString(), anyString())).thenReturn(null);
        when(universalAccountRecordDomainService.save(any(UniversalAccountRecordDO.class))).thenReturn(1L);

        
        // 执行测试
        ReceiveBenefitResDTO result = abstractEquityAccountOperate.receiveSceneBenefit(testContext);
        
        // 验证结果
        assertNotNull("receiveSceneBenefit 应该返回非空结果", result);
        assertEquals("状态应该为 PROCESSING", StatusEnum.PROCESSING, result.getStatusEnum());
        assertEquals("错误码应该为 STOCK_OVER_LIMIT", ExceptionEnum.STOCK_OVER_LIMIT.getCode(), result.getCode());
    }

    @Test
    public void testReceiveSceneBenefit_ProcessingRecord() {
        // 准备测试数据
        UniversalAccountRecordDO processingRecord = createTestUniversalAccountRecord();
        processingRecord.setStatus(StatusEnum.PROCESSING.getCode());
        
        // 模拟依赖调用
        when(redisCache.lock(anyString(), anyLong(), any(TimeUnit.class))).thenReturn(true);

        when(universalAccountRecordDomainService.queryOneByUniqueKey(anyString(), anyString(), anyInt(), anyString(), anyString())).thenReturn(processingRecord);
        
        // 执行测试
        ReceiveBenefitResDTO result = abstractEquityAccountOperate.receiveSceneBenefit(testContext);
        
        // 验证结果
        assertNotNull("receiveSceneBenefit 应该返回非空结果", result);
        assertEquals("状态应该为 PROCESSING", StatusEnum.PROCESSING, result.getStatusEnum());
        assertEquals("错误码应该为 RECEIVE_PROCESSING", ExceptionEnum.RECEIVE_PROCESSING.getCode(), result.getCode());
    }

    // ==================== 通用账户记录相关测试 ====================

    @Test
    public void testInitUniversalAccountRecord() {
        // 准备测试数据
        BenefitReceiveRuleContext ruleContext = createTestBenefitReceiveRuleContext();
        
        // 执行测试
        UniversalAccountRecordDO result = abstractEquityAccountOperate.initUniversalAccountRecord(testContext, ruleContext);
        
        // 验证结果
        assertNotNull("initUniversalAccountRecord 应该返回非空对象", result);
        assertEquals("用户ID应该匹配", testContext.getUid(), result.getUserId());
        assertEquals("场景代码应该匹配", testContext.getSceneCode(), result.getSceneCode());
        assertEquals("操作类型应该匹配", testContext.getOperateType(), result.getOperateType());
        assertEquals("请求号应该匹配", testContext.getSerialNumber(), result.getReqNo());
        assertEquals("辅助键应该匹配", testContext.getSerialNumber(), result.getAuxKey());
        assertEquals("状态应该为 INIT", StatusEnum.INIT.getCode(), result.getStatus());
        assertEquals("策略ID应该匹配", ruleContext.getStrategyId(), result.getStrategyId());
        assertEquals("权益ID应该匹配", ruleContext.getBenefitId(), result.getBenefitId());
    }

    @Test
    public void testQueryExistUniversalAccountRecord() {
        // 准备测试数据
        UniversalAccountRecordDO expectedRecord = createTestUniversalAccountRecord();
        
        // 模拟依赖调用
        when(universalAccountRecordDomainService.queryOneByUniqueKey(anyString(), anyString(), anyInt(), anyString(), anyString()))
            .thenReturn(expectedRecord);
        
        // 执行测试 - 使用反射调用私有方法
        UniversalAccountRecordDO result = abstractEquityAccountOperate.queryExistUniversalAccountRecordPublic(
            "test_user", "test_scene", 1, "test_req", "test_aux");
        
        // 验证结果
        assertNotNull("queryExistUniversalAccountRecord 应该返回非空对象", result);
        assertEquals("返回的记录应该匹配", expectedRecord.getId(), result.getId());
        
        // 验证方法调用
        verify(universalAccountRecordDomainService, times(1))
            .queryOneByUniqueKey("test_user", "test_scene", 1, "test_req", "test_aux");
    }

    @Test
    public void testUpdateUniversalAccountRecordProcessing() {
        // 准备测试数据
        Long recordId = 1L;
        
        // 模拟依赖调用
        when(universalAccountRecordDomainService.updateStatusById(anyLong(), anyInt())).thenReturn(true);
        
        // 执行测试
        boolean result = abstractEquityAccountOperate.updateUniversalAccountRecordProcessing(recordId, testContext);
        
        // 验证结果
        assertTrue("updateUniversalAccountRecordProcessing 应该返回 true", result);
        
        // 验证方法调用
        verify(universalAccountRecordDomainService, times(1))
            .updateStatusById(recordId, StatusEnum.PROCESSING.getCode());
    }

    @Test
    public void testUpdateUniversalAccountRecordFailure() {
        // 准备测试数据
        Long recordId = 1L;
        
        // 模拟依赖调用
        when(universalAccountRecordDomainService.updateStatusById(anyLong(), anyInt())).thenReturn(true);
        
        // 执行测试
        boolean result = abstractEquityAccountOperate.updateUniversalAccountRecordFailure(recordId, testContext);
        
        // 验证结果
        assertTrue("updateUniversalAccountRecordFailure 应该返回 true", result);
        
        // 验证方法调用
        verify(universalAccountRecordDomainService, times(1))
            .updateStatusById(recordId, StatusEnum.FAIL.getCode());
    }

    // ==================== 边界条件和异常测试 ====================

    @Test
    public void testProvideEquity_NullContext() {
        // 执行测试
        boolean result = abstractEquityAccountOperate.provideEquity(null);
        
        // 验证结果
        assertFalse("provideEquity 传入 null 应该返回 false", result);
    }

    @Test
    public void testUseEquity_NullContext() {
        // 执行测试
        boolean result = abstractEquityAccountOperate.useEquity(null);
        
        // 验证结果
        assertFalse("useEquity 传入 null 应该返回 false", result);
    }

    @Test
    public void testFallbackEquity_NullContext() {
        // 执行测试
        boolean result = abstractEquityAccountOperate.fallbackEquity(null);
        
        // 验证结果
        assertFalse("fallbackEquity 传入 null 应该返回 false", result);
    }

    @Test
    public void testGetExceptionStatus_NullSceneCode() {
        // 准备测试数据
        testContext.setSceneCode(null);
        
        // 执行测试
        int result = abstractEquityAccountOperate.getExceptionStatus(testContext, "provideEquity");
        
        // 验证结果
        assertEquals("场景代码为空应该返回 FAIL 状态", StatusEnum.FAIL.getCode(), result);
    }

    @Test
    public void testGetExceptionStatus_EmptySceneCode() {
        // 准备测试数据
        testContext.setSceneCode("");
        
        // 执行测试
        int result = abstractEquityAccountOperate.getExceptionStatus(testContext, "provideEquity");
        
        // 验证结果
        assertEquals("场景代码为空应该返回 FAIL 状态", StatusEnum.FAIL.getCode(), result);
    }

    @Test
    public void testReceiveSceneBenefit_NullContext() {
        // 执行测试
        ReceiveBenefitResDTO result = abstractEquityAccountOperate.receiveSceneBenefit(null);
        
        // 验证结果
        assertNotNull("receiveSceneBenefit 应该返回非空结果", result);
        assertEquals("状态应该为 PROCESSING", StatusEnum.PROCESSING, result.getStatusEnum());
        assertEquals("错误码应该为 PARAMS_ERROR", ExceptionEnum.PARAMS_ERROR.getCode(), result.getCode());
    }

    @Test
    public void testReceiveSceneBenefit_Exception() {
        // 模拟异常
        when(redisCache.lock(anyString(), anyLong(), any(TimeUnit.class))).thenThrow(new RuntimeException("Test exception"));
        
        // 执行测试
        ReceiveBenefitResDTO result = abstractEquityAccountOperate.receiveSceneBenefit(testContext);
        
        // 验证结果
        assertNotNull("receiveSceneBenefit 应该返回非空结果", result);
        assertEquals("状态应该为 PROCESSING", StatusEnum.PROCESSING, result.getStatusEnum());
        assertEquals("错误码应该为 COMMON_ERROR", ExceptionEnum.COMMON_ERROR.getCode(), result.getCode());
    }

    // ==================== 辅助方法 ====================

    private EquityRpcDto createSuccessEquityRpcDto() {
        EquityRpcDto dto = new EquityRpcDto();
        dto.setStatusEnum(StatusEnum.SUCCESS);
        dto.setCode(ExceptionEnum.SUCCESS.getCode());
        dto.setMessage(ExceptionEnum.SUCCESS.getMessage());
        return dto;
    }

    private EquityRpcDto createFailureEquityRpcDto() {
        EquityRpcDto dto = new EquityRpcDto();
        dto.setStatusEnum(StatusEnum.FAIL);
        dto.setCode(ExceptionEnum.COMMON_ERROR.getCode());
        dto.setMessage(ExceptionEnum.COMMON_ERROR.getMessage());
        return dto;
    }

    /**
     * 可测试的 AbstractEquityAccountOperate 实现类
     * 用于测试抽象类的具体方法
     */
    private static class TestableAbstractEquityAccountOperate extends AbstractEquityAccountOperate {
        
        private Long initRecordResult = null;
        private boolean initRecordThrowException = false;
        private boolean checkUseEquityResult = true;
        private EquityRpcDto doUseEquityResult = null;
        private boolean checkFallbackEquityResult = true;
        private EquityRpcDto doProvideEquityResult = null;
        private EquityRpcDto doFallbackEquityResult = null;
        private boolean rpcAndHandlerResultSuccess = false;
        
        // Setter 方法用于控制测试行为
        public void setInitRecordResult(Long result) {
            this.initRecordResult = result;
        }
        
        public void setInitRecordThrowException(boolean throwException) {
            this.initRecordThrowException = throwException;
        }
        
        public void setCheckUseEquityResult(boolean result) {
            this.checkUseEquityResult = result;
        }
        
        public void setDoUseEquityResult(EquityRpcDto result) {
            this.doUseEquityResult = result;
        }
        
        public void setCheckFallbackEquityResult(boolean result) {
            this.checkFallbackEquityResult = result;
        }
        
        public void setDoProvideEquityResult(EquityRpcDto result) {
            this.doProvideEquityResult = result;
        }
        
        public void setDoFallbackEquityResult(EquityRpcDto result) {
            this.doFallbackEquityResult = result;
        }
        
        public void setRpcAndHandlerResultSuccess(boolean success) {
            this.rpcAndHandlerResultSuccess = success;
        }
        
        public void setSceneMethod(Map<String, List<String>> sceneMethod) {
            super.sceneMethod = sceneMethod;
        }
        
        // 重写抽象方法以支持测试
        @Override
        public Long initRecord(EquityAccountContext equityAccountContext) {
            if (initRecordThrowException) {
                throw new RuntimeException("Test exception");
            }
            return initRecordResult != null ? initRecordResult : super.initRecord(equityAccountContext);
        }
        
        @Override
        public boolean checkUseEquity(EquityAccountContext equityAccountContext) {
            return checkUseEquityResult;
        }
        
        @Override
        public EquityRpcDto doUseEquity(EquityAccountContext equityAccountContext) {
            return doUseEquityResult != null ? doUseEquityResult : super.doUseEquity(equityAccountContext);
        }
        
        @Override
        public boolean checkFallbackEquity(EquityAccountContext equityAccountContext) {
            return checkFallbackEquityResult;
        }
        
        @Override
        public EquityRpcDto doProvideEquity(EquityAccountContext equityAccountContext) {
            return doProvideEquityResult != null ? doProvideEquityResult : super.doProvideEquity(equityAccountContext);
        }
        
        @Override
        public EquityRpcDto doFallbackEquity(EquityAccountContext equityAccountContext) {
            return doFallbackEquityResult != null ? doFallbackEquityResult : super.doFallbackEquity(equityAccountContext);
        }
        
        @Override
        protected ReceiveBenefitResDTO rpcAndHandlerResult(EquityAccountContext equityAccountContext, UniversalAccountRecordDO record) {
            ReceiveBenefitResDTO result = new ReceiveBenefitResDTO();
            if (rpcAndHandlerResultSuccess) {
                result.setStatusEnum(StatusEnum.SUCCESS);
                result.setCode(ExceptionEnum.SUCCESS.getCode());
                result.setMessage(ExceptionEnum.SUCCESS.getMessage());
            } else {
                result.setStatusEnum(StatusEnum.FAIL);
                result.setCode(ExceptionEnum.COMMON_ERROR.getCode());
                result.setMessage(ExceptionEnum.COMMON_ERROR.getMessage());
            }
            return result;
        }
    }
}