version = '1.0.0-SNAPSHOT'
jar.enabled = true
bootJar.enabled = false
dependencies {
    // log

    compile('net.logstash.logback:logstash-logback-encoder:7.0.1')
    compile('com.github.danielwegener:logback-kafka-appender:0.2.0-RC2')
    compile 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
    // ide
    compile('org.jetbrains:annotations:23.0.0')
    // coding 简化
    compileOnly('org.projectlombok:lombok:1.18.22')
    testCompileOnly('org.projectlombok:lombok:1.18.22')
    // mapstruct
    compile('org.mapstruct:mapstruct:1.4.2.Final')
    // mapstruct-processor
    compile('org.mapstruct:mapstruct-processor:1.4.2.Final')

    compile('com.alibaba:transmittable-thread-local:2.12.6')

    // B端SSO
    compile('com.ddmc:gateway-starter:1.7.4-SNAPSHOT')
    // C端SSO
    compile('com.ddmc:gateway-api-starter:1.0.2-SNAPSHOT')
//    compile('csoss.daas:spring-data-redis-core:0.0.3-RELEASE')
    // spring cloud
    compile('org.springframework.boot:spring-boot-starter-web') {
        exclude group: 'org.apache.logging.log4j', module: 'log4j-to-slf4j'
        exclude group: 'org.apache.logging.log4j', module: 'log4j-api'
    }
    compile('org.apache.logging.log4j:log4j-to-slf4j:2.20.0')
    compile('org.apache.logging.log4j:log4j-api:2.20.0')
    compile('org.apache.logging.log4j:log4j-core:2.20.0')
    compile('org.apache.commons:commons-pool2:2.6.0')
    compile("org.springframework.cloud:spring-cloud-context")
    compile('org.springframework.boot:spring-boot-starter-actuator')
    compile('org.springframework.cloud:spring-cloud-starter-openfeign')
    compile('org.springframework.cloud:spring-cloud-starter-zookeeper-discovery') {
        exclude group: 'org.apache.zookeeper', module: 'zookeeper'
        exclude group: 'com.google.guava', module: 'guava'
    }
    // 不能升级到最新的zk，3.7.0接上OPM去会报Zookeeper EndOfStreamException ?
    //noinspection GradlePackageUpdate
    compile('org.apache.zookeeper:zookeeper:3.4.9') {
        exclude group: 'org.slf4j', module: 'slf4j-log4j12'
    }
    // 配置中心
    //noinspection GradlePackageUpdate
    compile('com.ctrip.framework.apollo:apollo-client:1.9.1')
    compile 'com.ctrip.framework.apollo:apollo-core:1.9.1'
    compile('com.ctrip.framework.apollo:apollo-openapi:1.1.0')

    // mybatis and s mysql
    compile('org.springframework.boot:spring-boot-starter-jdbc')
    compile('org.springframework:spring-tx:5.1.5.RELEASE')
    compile group: 'com.baomidou', name: 'mybatis-plus-boot-starter', version: '3.4.2'
    compile('com.github.pagehelper:pagehelper-spring-boot-starter:1.2.3')
    // DAL
    compile('com.ddmc:ddmc-jdbc-pool') {
        exclude group: 'org.apache.zookeeper', module: 'zookeeper'
        exclude group: 'mysql', module: 'mysql-connector-java'
    }
    compile('com.mysql:mysql-connector-j')

    // 站点服务
//    compile('com.ddmc:station-client:1.2.3-SNAPSHOT')

    // https://mvnrepository.com/artifact/cglib/cglib
    implementation 'cglib:cglib:3.3.0'

    // redis
//    implementation('org.springframework.data:spring-data-redis:2.7.2')
//    implementation('io.lettuce:lettuce-core:5.1.3.RELEASE')
    //redis 客户端
    implementation group: 'org.springframework.boot', name: 'spring-boot-starter-data-redis', version: '2.4.5'
    implementation group: 'org.apache.commons', name: 'commons-pool2', version: '2.9.0'
    compile 'io.lettuce:lettuce-core:6.3.0.RELEASE'
    //redis 监控
    // compile 'csoss.daas:spring-data-redis-core:0.0.8-RELEASE'

    // Redisson
   // compile('org.redisson:redisson-spring-boot-starter:3.19.3')
    // swagger
    compile 'io.springfox:springfox-swagger2'
    compile 'io.springfox:springfox-swagger-ui'

    // log
    compile('log4j:log4j:1.2.17') { force = true }

    compile('org.springframework.kafka:spring-kafka:2.2.3.RELEASE')

    // rocketmq
//    compile 'org.apache.rocketmq:rocketmq-spring-boot-starter:2.2.1'

    // 磐石日志组件
    compile('com.ddmc:ddmc-log-ext:1.7.5-RELEASE')

    compile('com.github.danielwegener:logback-kafka-appender:0.2.0-RC2')

    compile('org.slf4j:slf4j-api:1.7.36')
    compile('org.slf4j:log4j-over-slf4j:1.7.36')
    // soa
    compile('com.ddmc.soa:spring-cloud-ddmc') {
        exclude group: 'org.apache.zookeeper', module: 'zookeeper'
        exclude group: 'com.google.guava', module: 'guava'
    }
    // Guava
    compile('com.google.guava:guava:31.1-jre')

    // util
    compile('com.ddmc:core:1.1.4-SNAPSHOT')
    // 三方库
    compile('org.apache.httpcomponents:httpclient:4.5.13')
    compile('com.alibaba:fastjson:1.2.83')
    compile('cn.hutool:hutool-all:5.7.22')
    compile('commons-validator:commons-validator:1.7')
    compile('commons-codec:commons-codec:1.15')
    compile('io.github.openfeign.form:feign-form:3.8.0')
    compile('io.github.openfeign.form:feign-form-spring:3.8.0')
    compile('io.github.openfeign:feign-gson:11.8')
    compile('io.github.openfeign:feign-okhttp')
    // 测试相关
    // Spring Boot 测试基础依赖
    testImplementation('org.springframework.boot:spring-boot-starter-test')
    // JUnit 依赖
    testImplementation('junit:junit:4.13.2')
    // Mockito 相关依赖
    testImplementation('org.mockito:mockito-core:3.11.2')
    testImplementation('org.mockito:mockito-junit-jupiter:3.11.2')
    // PowerMock 相关依赖
    testImplementation('org.powermock:powermock-core:2.0.9')
    testImplementation('org.powermock:powermock-module-junit4:2.0.9')
    testImplementation('org.powermock:powermock-api-mockito2:2.0.9')

    // 标签系统 @杨洋
    compile('com.ddmc:tag-manage-client:1.3.6-RELEASE')

    // 商品服务 类目
    // compile('com.ddmc.greenhouse:carbon-admin-third-api-client:1.3.2-RELEASE')


    // 商品上下架 + 赠品 + 大客户品 @邹旭
    //compile('com.ddmc.greenhouse:carbon-admin-third-api-client:TE-SNAPSHOT')

    // 商品详情接口 @邹旭不推荐用
//    compile('com.ddmc.greenhouse:carbon-core-api-client:1.0.16-RELEASE');

    compile('com.lmax:disruptor:4.0.0.RC1')

//    compile('com.ddmc.abtest:abtest-client:1.1.3-RELEASE'){
//        exclude "com/ddmc/abtest/dto/LayerDTO.class"
//    }

    // 虚拟库存 @王辉武 @钟杨
    // compile('com.ddmc:inventory-center-client:1.0.14-RELEASE')

    // 新站点服务 @赵来源
//    compile('com.ddmc:station-center-client:1.0.2-RELEASE')

    // 店仓分离  @邓建爱 @赵来源
    // compile('com.ddmc.store:store-client:1.0.6-RELEASE')

    // 动态feign超时设置 @莫湘林
    compile('com.ddmc:ddmc-feign-ext:1.0.9-RELEASE')


    //user 用户中心
    compile("com.ddmc:usercenter-open-client:2.0.1")

    //比较版本
    compile('com.vdurmont:semver4j:3.1.0')

    //noah
    // compile("com.ddmc:noah-api-client:1.0.17-RELEASE")
    //会员服务
    compile('com.ddmc:vip-client:1.6.3-RELEASE')
    //标签服务
    compile("com.ddmc:tag-api-client:1.4.0-RELEASE")
    compile("com.ddmc:tag-api-sdk:1.0.4-RELEASE") {
        exclude group: "com.ddmc", module: "tag-api-client"
    }
    //余额
    compile('com.ddmc:trade-balance-client:2.0.19-RELEASE')
    // 积分服务
    compile('com.ddmc:op-userpoint-api:1.2.0-RELEASE')

    //订单服务
    compile('com.ddmc:trade-order-client:2.0.6-RELEASE')

    compile('com.csoss:monitor-agent')

    //用户中心
    compile('com.ddmc:usercenter-open-client:3.4.4-RELEASE')

    compile('com.ddmc:columbus-client:1.4.7-RELEASE')

    compile project(':equity-client')
    compile 'com.ddmc.pulsar:pulsar-java-spring-boot-starter'

    compile('com.ddmc:promocore-client:1.4.0-RELEASE') {
        exclude group: "com.ddmc", module: "promo-guide-client"
    }

    compile('com.ddmc:promoequity-client:1.0.3-RELEASE')

    compile('com.ddmc:station-client:1.2.4-SNAPSHOT')

    compile('com.ddmc:voucherprod-client:1.1.2-RELEASE')

    compile('com.ddmc:vouchercore-client:1.4.2-RELEASE')

    compile 'org.apache.rocketmq:rocketmq-spring-boot-starter:2.1.1'

    // excel 处理
    compile('com.alibaba:easyexcel:3.3.2')
//    compile('com.ddmc:vouchercore-client:1.4.1-RELEASE')

    //站点
    compile('com.ddmc.store:store-client:1.0.7-RELEASE')
    //算价服务
    compile('com.ddmc:pricing-center-engine-client:1.1.0-SNAPSHOT')

    // 订单详情 @林旭
    compile('com.ddmc:trade-order-client:2.0.14-RELEASE')

    // 商品上下架 + 赠品 + 大客户品 @邹旭
    compile('com.ddmc.greenhouse:carbon-admin-third-api-client:1.3.80-RELEASE')

    // 店仓分离  @邓建爱 @赵来源
    compile('com.ddmc.store:store-client:1.0.6-RELEASE')

    // 虚拟库存 @王辉武 @钟杨
    compile('com.ddmc:inventory-center-client:1.0.20-RELEASE')

    // 站、区域、城市价格　
    compile('com.ddmc:price-base-client:1.3.19-RELEASE')

    // 滞销活动品 @王润润
    compile('com.ddmc:pricing-center-engine-client:1.1.0-SNAPSHOT')

    // 查询场景营销活动品 如避让0.99购和绿卡专享品时用 @罗欢 @王凡
    compile('com.ddmc:activitycore-client:1.7.2-RELEASE') {
        exclude group: 'com.ddmc', module: 'activitycore-base'
        exclude group: 'org.mongodb', module: 'mongodb-driver-core'
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-data-mongodb'
        exclude group: 'org.springframework.data', module: 'spring-data-redis'
        exclude group: 'org.springframework.data', module: 'spring-data-keyvalue'
        exclude group: 'org.springframework.data', module: 'spring-data-commons'
        exclude group: 'io.springfox', module: 'springfox-swagger'
        exclude group: 'io.springfox', module: 'springfox-swagger2'
        exclude group: 'io.springfox', module: 'springfox-swagger-common'
        exclude group: 'io.springfox', module: 'springfox-swagger-ui'
        exclude group: 'com.ddmc', module: 'ddmc-monitor'
        exclude group: 'com.ddmc', module: 'ddmc-monitor-mysql6'
        exclude group: 'com.ddmc', module: 'ddmc-monitor-mysql8'
        exclude group: 'io.lettuce', module: 'lettuce-core'
        exclude group: 'com.ddmc.greenhouse', module: 'carbon-admin-third-api-client'
    }

    // 查询元宇宙商品 @毕国平
    compile('com.ddmc.greenhouse:product-op-client-np-intro:1.0.5-RELEASE')

    // 查询汰换品  @毕国平
    compile('com.ddmc.greenhouse:product-op-client-replace:0.0.6-SNAPSHOT')

    // 供应链预测调拨 安全库存 @谢海勤
    compile('com.ddmc.algo.ppm:plan-parameter-manager-client:0.0.5')

    // 供应链预测调拨 阈值 @谢海勤
    compile('com.ddmc.scplan:plan-demand-manager-client:1.0.9.jdk8')

    // 皮秀拉新品 @刘浩
    compile('com.ddmc:op-pixiu-manage-client:1.0.2-RELEASE')

    //C端商品原价 于雷
    compile('com.ddmc:price-core-client:1.0.6-RELEASE')

    // 风控
    compile("com.ddmc:engine-feign-api:1.2.7-RELEASE")

    // 营销活动和选品包
    compile('com.ddmc:promo-guide-client:1.1.45-RELEASE')

    // 算法服务
    compile('com.ddmc.transformers:smart-sdk:3.2.10-RELEASE') {
        exclude group: 'com.ddmc.abtest', module: 'abtest-sdk'
        exclude group: 'csoss.daas', module: 'spring-data-redis-core'

    }

    compile('com.ddmc:gotone-client:1.2.7-RELEASE')

    implementation('csoss.redis:spring-data-redis-client:1.0.2-RELEASE')

    implementation('io.projectreactor:reactor-core:3.6.0')

    compile('com.ddmc.sdk:ddmc-sdk:1.0.2-RELEASE')

    // 社群
    compile('com.ddmc:op-wx-business-client:1.0.7-RELEASE') {
        exclude group: 'com.ddmc', module: 'solar-base-utils'
    }
    // 工作台消息
    implementation('com.ddmc:product-operation-workbench-client:1.0.20-RELEASE')
    // 鱼塘
    implementation('com.ddmc:farm-client:1.0.13-RELEASE') {
        exclude group: 'com.ddmc.guide', module: 'biz-service-template-client'
    }
}
