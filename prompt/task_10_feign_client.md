# 任务十：Feign 客户端实现

## 任务目标
实现 Feign 客户端接口，供其他微服务调用，定义与 Controller 一致的接口契约，支持服务发现和负载均衡。

## 前置依赖
- 任务一：基础 DTO 和枚举类已完成
- 任务九：REST API 控制器已完成
- SceneActionConsultReqDTO、SceneActionReceiveReqDTO 等已创建

## 需要创建的类

### 1. Feign 客户端接口
**路径**: `com.ddmc.equity.client.customer`

#### CustomerSceneActionClient.java
```java
/**
 * 客户场景动作 Feign 客户端
 * 供其他微服务调用场景动作相关接口
 */
@FeignClient(
    name = "equity-service",
    path = "/api/v1/customer/scene-action",
    fallback = CustomerSceneActionClientFallback.class
)
public interface CustomerSceneActionClient {
    
    /**
     * 咨询可领取权益
     * @param request 咨询请求
     * @return 咨询响应
     */
    @PostMapping("/consult")
    WebResp<SceneActionConsultRespDTO> consultForReceive(@RequestBody SceneActionConsultReqDTO request);
    
    /**
     * 领取权益
     * @param request 领取请求
     * @return 领取响应
     */
    @PostMapping("/receive")
    WebResp<SceneActionReceiveRespDTO> receive(@RequestBody SceneActionReceiveReqDTO request);
}
```

### 2. Feign 客户端降级处理
**路径**: `com.ddmc.equity.client.customer.fallback`

#### CustomerSceneActionClientFallback.java
```java
/**
 * 客户场景动作 Feign 客户端降级处理
 */
@Component
@Slf4j
public class CustomerSceneActionClientFallback implements CustomerSceneActionClient {
    
    @Override
    public WebResp<SceneActionConsultRespDTO> consultForReceive(SceneActionConsultReqDTO request) {
        log.error("场景动作咨询接口调用失败，触发降级，用户ID：{}", request.getUserId());
        
        SceneActionConsultRespDTO response = new SceneActionConsultRespDTO();
        response.setResultCode("SERVICE_UNAVAILABLE");
        response.setResultMessage("服务暂时不可用，请稍后重试");
        response.setReceivableBenefits(Collections.emptyList());
        response.setUnreceivableBenefits(Collections.emptyList());
        
        return WebResp.error("SERVICE_UNAVAILABLE", "服务暂时不可用", response);
    }
    
    @Override
    public WebResp<SceneActionReceiveRespDTO> receive(SceneActionReceiveReqDTO request) {
        log.error("场景动作领取接口调用失败，触发降级，用户ID：{}", request.getUserId());
        
        SceneActionReceiveRespDTO response = new SceneActionReceiveRespDTO();
        response.setResultCode("SERVICE_UNAVAILABLE");
        response.setResultMessage("服务暂时不可用，请稍后重试");
        response.setProcessId("FALLBACK_" + System.currentTimeMillis());
        
        return WebResp.error("SERVICE_UNAVAILABLE", "服务暂时不可用", response);
    }
}
```

### 3. Feign 配置类
**路径**: `com.ddmc.equity.config`

#### FeignConfig.java
```java
/**
 * Feign 客户端配置
 */
@Configuration
@EnableFeignClients(basePackages = "com.ddmc.equity.client")
@Slf4j
public class FeignConfig {
    
    /**
     * Feign 请求拦截器
     * 用于添加通用请求头、链路追踪等
     */
    @Bean
    public RequestInterceptor requestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                // 添加请求头
                template.header("Content-Type", "application/json");
                template.header("User-Agent", "equity-service-feign-client");
                
                // 传递链路追踪信息（如果有）
                String traceId = getTraceId();
                if (StringUtils.isNotBlank(traceId)) {
                    template.header("X-Trace-Id", traceId);
                }
                
                log.debug("Feign 请求拦截器添加请求头完成");
            }
        };
    }
    
    /**
     * Feign 错误解码器
     * 用于处理 HTTP 错误响应
     */
    @Bean
    public ErrorDecoder errorDecoder() {
        return new ErrorDecoder() {
            @Override
            public Exception decode(String methodKey, Response response) {
                log.error("Feign 调用失败，方法：{}，状态码：{}", methodKey, response.status());
                
                switch (response.status()) {
                    case 400:
                        return new IllegalArgumentException("请求参数错误");
                    case 404:
                        return new RuntimeException("接口不存在");
                    case 500:
                        return new RuntimeException("服务内部错误");
                    case 503:
                        return new RuntimeException("服务暂时不可用");
                    default:
                        return new RuntimeException("未知错误，状态码：" + response.status());
                }
            }
        };
    }
    
    /**
     * Feign 重试器
     * 配置重试策略
     */
    @Bean
    public Retryer retryer() {
        // 最大重试次数3次，初始间隔100ms，最大间隔1000ms
        return new Retryer.Default(100, 1000, 3);
    }
    
    /**
     * Feign 日志级别
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.BASIC;
    }
    
    /**
     * 获取链路追踪ID
     */
    private String getTraceId() {
        try {
            // 这里可以集成实际的链路追踪框架
            // 例如：Sleuth、SkyWalking 等
            return MDC.get("traceId");
        } catch (Exception e) {
            log.debug("获取链路追踪ID失败", e);
            return null;
        }
    }
}
```

### 4. 客户端使用示例
**路径**: `com.ddmc.equity.example`

#### SceneActionClientExample.java
```java
/**
 * 场景动作客户端使用示例
 * 展示如何在其他服务中使用 Feign 客户端
 */
@Component
@Slf4j
public class SceneActionClientExample {
    
    @Resource
    private CustomerSceneActionClient sceneActionClient;
    
    /**
     * 示例：咨询用户可领取权益
     */
    public void consultExample(Long userId, String sceneCode) {
        try {
            log.info("开始咨询用户可领取权益，用户ID：{}，场景：{}", userId, sceneCode);
            
            // 构建请求
            SceneActionConsultReqDTO request = new SceneActionConsultReqDTO();
            request.setUserId(userId);
            request.setSceneCode(sceneCode);
            
            // 调用 Feign 客户端
            WebResp<SceneActionConsultRespDTO> response = sceneActionClient.consultForReceive(request);
            
            if (response.getSuccess()) {
                SceneActionConsultRespDTO data = response.getData();
                log.info("咨询成功，可领取权益数：{}，不可领取权益数：{}", 
                        data.getReceivableBenefits().size(), 
                        data.getUnreceivableBenefits().size());
            } else {
                log.warn("咨询失败，错误码：{}，错误信息：{}", response.getCode(), response.getMessage());
            }
            
        } catch (Exception e) {
            log.error("咨询异常，用户ID：{}", userId, e);
        }
    }
    
    /**
     * 示例：用户领取权益
     */
    public void receiveExample(Long userId, String sceneCode, String activityId, String benefitId) {
        try {
            log.info("开始领取权益，用户ID：{}，场景：{}，权益ID：{}", userId, sceneCode, benefitId);
            
            // 构建请求
            SceneActionReceiveReqDTO request = new SceneActionReceiveReqDTO();
            request.setUserId(userId);
            request.setSceneCode(sceneCode);
            request.setActivityId(activityId);
            request.setBenefitId(benefitId);
            
            // 调用 Feign 客户端
            WebResp<SceneActionReceiveRespDTO> response = sceneActionClient.receive(request);
            
            if (response.getSuccess()) {
                SceneActionReceiveRespDTO data = response.getData();
                log.info("领取成功，流水号：{}", data.getProcessId());
            } else {
                log.warn("领取失败，错误码：{}，错误信息：{}", response.getCode(), response.getMessage());
            }
            
        } catch (Exception e) {
            log.error("领取异常，用户ID：{}", userId, e);
        }
    }
}
```

## 技术要求
- 使用 Spring Cloud OpenFeign 注解（@FeignClient、@PostMapping、@RequestBody）
- 支持服务发现和负载均衡
- 实现降级处理（fallback）
- 配置重试策略和错误处理
- 添加请求拦截器和链路追踪
- 与 Controller 接口保持一致

## 验收标准
- 编译通过：`./gradlew compileJava`
- Feign 客户端接口与 Controller 一致
- 降级处理正确实现
- 配置类完整
- 支持服务发现
- 错误处理完善
- 日志记录完整
