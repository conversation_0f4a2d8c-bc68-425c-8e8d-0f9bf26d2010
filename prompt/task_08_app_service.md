# 任务八：应用服务层实现

## 任务目标
实现应用服务接口和实现类，负责业务流程编排和 DTO 转换，调用场景动作流程引擎完成具体业务逻辑。

## 前置依赖
- 任务一：基础 DTO 和枚举类已完成
- 任务四：场景动作流程引擎核心已完成
- SceneActionProcessEngine 已创建

## 需要创建的类

### 1. 应用服务接口
**路径**: `com.ddmc.equity.service`

#### CustomerSceneActionAppService.java
```java
/**
 * 客户场景动作应用服务接口
 */
public interface CustomerSceneActionAppService {
    
    /**
     * 咨询可领取权益
     * @param request 咨询请求
     * @return 咨询响应
     */
    SceneActionConsultRespDTO consultForReceive(SceneActionConsultReqDTO request);
    
    /**
     * 领取权益
     * @param request 领取请求
     * @return 领取响应
     */
    SceneActionReceiveRespDTO receive(SceneActionReceiveReqDTO request);
}
```

### 2. 应用服务实现类
**路径**: `com.ddmc.equity.service.impl`

#### CustomerSceneActionAppServiceImpl.java
```java
/**
 * 客户场景动作应用服务实现
 */
@Service
@Slf4j
public class CustomerSceneActionAppServiceImpl implements CustomerSceneActionAppService {
    
    @Resource
    private SceneActionProcessEngine processEngine;
    
    @Override
    public SceneActionConsultRespDTO consultForReceive(SceneActionConsultReqDTO request) {
        try {
            log.info("开始处理咨询请求，用户ID：{}，场景：{}", request.getUserId(), request.getSceneCode());
            
            // 参数校验
            validateConsultRequest(request);
            
            // 构建处理上下文
            SceneActionProcessContext context = buildConsultContext(request);
            
            // 执行咨询流程
            SceneActionProcessResult result = processEngine.executeConsultProcess(context);
            
            // 转换响应结果
            SceneActionConsultRespDTO response = convertToConsultResponse(result);
            
            log.info("咨询请求处理完成，用户ID：{}，结果：{}", request.getUserId(), result.getSuccess());
            
            return response;
            
        } catch (IllegalArgumentException e) {
            log.warn("咨询请求参数校验失败，用户ID：{}，原因：{}", request.getUserId(), e.getMessage());
            return buildConsultErrorResponse("PARAM_ERROR", e.getMessage());
        } catch (Exception e) {
            log.error("咨询请求处理异常，用户ID：{}", request.getUserId(), e);
            return buildConsultErrorResponse("SYSTEM_ERROR", "系统异常，请稍后重试");
        }
    }
    
    @Override
    public SceneActionReceiveRespDTO receive(SceneActionReceiveReqDTO request) {
        try {
            log.info("开始处理领取请求，用户ID：{}，场景：{}，权益ID：{}", 
                    request.getUserId(), request.getSceneCode(), request.getBenefitId());
            
            // 参数校验
            validateReceiveRequest(request);
            
            // 构建处理上下文
            SceneActionProcessContext context = buildReceiveContext(request);
            
            // 执行领取流程
            SceneActionProcessResult result = processEngine.executeReceiveProcess(context);
            
            // 转换响应结果
            SceneActionReceiveRespDTO response = convertToReceiveResponse(result, context);
            
            log.info("领取请求处理完成，用户ID：{}，结果：{}", request.getUserId(), result.getSuccess());
            
            return response;
            
        } catch (IllegalArgumentException e) {
            log.warn("领取请求参数校验失败，用户ID：{}，原因：{}", request.getUserId(), e.getMessage());
            return buildReceiveErrorResponse("PARAM_ERROR", e.getMessage());
        } catch (Exception e) {
            log.error("领取请求处理异常，用户ID：{}", request.getUserId(), e);
            return buildReceiveErrorResponse("SYSTEM_ERROR", "系统异常，请稍后重试");
        }
    }
    
    /**
     * 校验咨询请求参数
     */
    private void validateConsultRequest(SceneActionConsultReqDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (request.getUserId() == null || request.getUserId() <= 0) {
            throw new IllegalArgumentException("用户ID不能为空且必须大于0");
        }
        if (StringUtils.isBlank(request.getSceneCode())) {
            throw new IllegalArgumentException("场景编码不能为空");
        }
    }
    
    /**
     * 校验领取请求参数
     */
    private void validateReceiveRequest(SceneActionReceiveReqDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }
        if (request.getUserId() == null || request.getUserId() <= 0) {
            throw new IllegalArgumentException("用户ID不能为空且必须大于0");
        }
        if (StringUtils.isBlank(request.getSceneCode())) {
            throw new IllegalArgumentException("场景编码不能为空");
        }
        if (StringUtils.isBlank(request.getActivityId())) {
            throw new IllegalArgumentException("活动ID不能为空");
        }
        if (StringUtils.isBlank(request.getBenefitId())) {
            throw new IllegalArgumentException("权益ID不能为空");
        }
    }
    
    /**
     * 构建咨询处理上下文
     */
    private SceneActionProcessContext buildConsultContext(SceneActionConsultReqDTO request) {
        Map<String, Object> extParams = new HashMap<>();
        if (StringUtils.isNotBlank(request.getExtParams())) {
            try {
                // 解析扩展参数
                Map<String, Object> extParamsMap = JSON.parseObject(request.getExtParams(), Map.class);
                extParams.putAll(extParamsMap);
            } catch (Exception e) {
                log.warn("解析扩展参数失败，用户ID：{}", request.getUserId(), e);
            }
        }
        
        // 添加活动ID到扩展参数
        if (StringUtils.isNotBlank(request.getActivityId())) {
            extParams.put("activityId", request.getActivityId());
        }
        
        return SceneActionProcessContext.builder()
                .userId(request.getUserId())
                .sceneCode(request.getSceneCode())
                .processType(SceneActionProcessTypeEnum.CONSULT)
                .processId(generateProcessId())
                .extParams(extParams)
                .build();
    }
    
    /**
     * 构建领取处理上下文
     */
    private SceneActionProcessContext buildReceiveContext(SceneActionReceiveReqDTO request) {
        Map<String, Object> extParams = new HashMap<>();
        if (StringUtils.isNotBlank(request.getExtParams())) {
            try {
                Map<String, Object> extParamsMap = JSON.parseObject(request.getExtParams(), Map.class);
                extParams.putAll(extParamsMap);
            } catch (Exception e) {
                log.warn("解析扩展参数失败，用户ID：{}", request.getUserId(), e);
            }
        }
        
        // 添加必要参数
        extParams.put("activityId", request.getActivityId());
        extParams.put("benefitId", request.getBenefitId());
        
        return SceneActionProcessContext.builder()
                .userId(request.getUserId())
                .sceneCode(request.getSceneCode())
                .processType(SceneActionProcessTypeEnum.RECEIVE)
                .processId(generateProcessId())
                .extParams(extParams)
                .build();
    }
    
    /**
     * 转换咨询响应结果
     */
    private SceneActionConsultRespDTO convertToConsultResponse(SceneActionProcessResult result) {
        SceneActionConsultRespDTO response = new SceneActionConsultRespDTO();
        response.setResultCode(result.getResultCode());
        response.setResultMessage(result.getResultMessage());
        
        if (result.getSuccess() && result.getProcessData() != null) {
            // 从处理数据中提取权益列表
            List<ReceivableBenefitDTO> receivableBenefits = 
                    (List<ReceivableBenefitDTO>) result.getProcessData().get("receivableBenefits");
            List<UnreceivableBenefitDTO> unreceivableBenefits = 
                    (List<UnreceivableBenefitDTO>) result.getProcessData().get("unreceivableBenefits");
            
            response.setReceivableBenefits(receivableBenefits != null ? receivableBenefits : Collections.emptyList());
            response.setUnreceivableBenefits(unreceivableBenefits != null ? unreceivableBenefits : Collections.emptyList());
        } else {
            response.setReceivableBenefits(Collections.emptyList());
            response.setUnreceivableBenefits(Collections.emptyList());
        }
        
        return response;
    }
    
    /**
     * 转换领取响应结果
     */
    private SceneActionReceiveRespDTO convertToReceiveResponse(SceneActionProcessResult result, 
                                                              SceneActionProcessContext context) {
        SceneActionReceiveRespDTO response = new SceneActionReceiveRespDTO();
        response.setResultCode(result.getResultCode());
        response.setResultMessage(result.getResultMessage());
        response.setProcessId(context.getProcessId());
        
        if (result.getProcessData() != null) {
            // 提取权益执行结果
            Map<String, Object> executeResult = 
                    (Map<String, Object>) result.getProcessData().get("benefitExecuteResult");
            
            if (executeResult != null && Boolean.TRUE.equals(executeResult.get("success"))) {
                // 构建领取的权益信息
                ReceivedBenefitDTO receivedBenefit = buildReceivedBenefit(executeResult, context);
                response.setReceivedBenefit(receivedBenefit);
            }
            
            // 提取消耗权益信息
            ConsumeBenefitResult consumeResult = 
                    (ConsumeBenefitResult) result.getProcessData().get("consumeConfirmResult");
            
            if (consumeResult != null) {
                ConsumedBenefitDTO consumedBenefit = buildConsumedBenefit(consumeResult, context);
                response.setConsumedBenefit(consumedBenefit);
            }
        }
        
        return response;
    }
    
    /**
     * 构建领取的权益信息
     */
    private ReceivedBenefitDTO buildReceivedBenefit(Map<String, Object> executeResult, 
                                                   SceneActionProcessContext context) {
        ReceivedBenefitDTO receivedBenefit = new ReceivedBenefitDTO();
        receivedBenefit.setBenefitId((String) context.getExtParams().get("benefitId"));
        receivedBenefit.setGrantId((String) executeResult.get("grantId"));
        receivedBenefit.setAfterBalance((Integer) executeResult.get("afterBalance"));
        receivedBenefit.setGrantTime(System.currentTimeMillis());
        return receivedBenefit;
    }
    
    /**
     * 构建消耗的权益信息
     */
    private ConsumedBenefitDTO buildConsumedBenefit(ConsumeBenefitResult consumeResult, 
                                                   SceneActionProcessContext context) {
        ConsumedBenefitDTO consumedBenefit = new ConsumedBenefitDTO();
        consumedBenefit.setConsumeAmount(consumeResult.getActualConsumeAmount());
        consumedBenefit.setBeforeBalance(consumeResult.getBeforeBalance());
        consumedBenefit.setAfterBalance(consumeResult.getAfterBalance());
        consumedBenefit.setProcessId(consumeResult.getProcessId());
        return consumedBenefit;
    }
    
    /**
     * 构建咨询错误响应
     */
    private SceneActionConsultRespDTO buildConsultErrorResponse(String resultCode, String resultMessage) {
        SceneActionConsultRespDTO response = new SceneActionConsultRespDTO();
        response.setResultCode(resultCode);
        response.setResultMessage(resultMessage);
        response.setReceivableBenefits(Collections.emptyList());
        response.setUnreceivableBenefits(Collections.emptyList());
        return response;
    }
    
    /**
     * 构建领取错误响应
     */
    private SceneActionReceiveRespDTO buildReceiveErrorResponse(String resultCode, String resultMessage) {
        SceneActionReceiveRespDTO response = new SceneActionReceiveRespDTO();
        response.setResultCode(resultCode);
        response.setResultMessage(resultMessage);
        response.setProcessId(generateProcessId());
        return response;
    }
    
    /**
     * 生成处理流水号
     */
    private String generateProcessId() {
        return "SA_" + System.currentTimeMillis() + "_" + ThreadLocalRandom.current().nextInt(1000, 9999);
    }
}
```

## 技术要求
- 实现业务流程编排和 DTO 转换
- 调用场景动作流程引擎完成具体业务逻辑
- 完整的参数校验和异常处理
- 正确的请求响应转换
- 统一的错误处理和日志记录

## 验收标准
- 编译通过：`./gradlew compileJava`
- 接口和实现类正确对应
- 参数校验完整
- DTO 转换正确
- 异常处理统一
- 日志记录完整
- Spring 注解使用正确
