# 场景动作模块重构任务总览

## 任务拆分说明

基于原始的 `scene_action_refactor_prompt.md` 文件，我们将大型重构任务拆分为 10 个独立的子任务，每个任务专注于特定功能模块，避免生成中断和编译失败问题。

## 任务列表

### 第一阶段：基础设施层

#### 任务一：基础 DTO 和枚举类创建
**文件**: `task_01_basic_dto_enums.md`
**目标**: 创建所有基础 DTO 类和枚举类
**输出**: 
- 场景相关 DTO（ConsumeBenefitSceneDTO、SendBenefitSceneDTO）
- 接口请求响应 DTO（SceneActionConsultReqDTO、SceneActionReceiveReqDTO 等）
- 流程处理相关 DTO 和枚举（ProcessStatus、SceneActionProcessContext 等）
- 权益消耗相关 DTO（ConsumeBenefitContext、ConsumeBenefitResult 等）

### 第二阶段：领域层核心

#### 任务二：权益消耗策略模式实现
**文件**: `task_02_consume_benefit_strategy.md`
**目标**: 实现权益消耗的策略模式
**输出**:
- ConsumeBenefitStrategy 接口
- AbstractConsumeBenefitStrategy 抽象基类
- DefaultConsumeBenefitStrategy、PointConsumeBenefitStrategy 具体策略
- ConsumeBenefitStrategyFactory 策略工厂

#### 任务三：积分 RPC 客户端代理实现
**文件**: `task_03_point_rpc_client.md`
**目标**: 实现积分服务的 RPC 客户端代理
**输出**:
- PointUserClientProxy 接口
- PointUserClientProxyImpl 实现类
- 四个核心操作：发放、冻结、扣减、解冻

#### 任务四：场景动作流程引擎核心实现
**文件**: `task_04_scene_action_engine.md`
**目标**: 实现场景动作流程引擎框架
**输出**:
- SceneActionProcessor 处理器接口
- AbstractSceneActionProcessor 抽象处理器
- SceneActionProcessEngine 主流程引擎

### 第三阶段：业务处理器

#### 任务五：咨询流程处理器实现
**文件**: `task_05_consult_processors.md`
**目标**: 实现咨询活动处理器
**输出**:
- DefaultConsultActivityProcessor
- 参考 CustomerBenefitAppServiceImpl#sceneConsult 逻辑

#### 任务六：权益消耗相关处理器实现
**文件**: `task_06_consume_processors.md`
**目标**: 实现权益消耗相关的三个处理器
**输出**:
- DefaultCheckConsumeBftProcessor（检查消耗权益）
- DefaultDeductConsumeBftProcessor（扣减消耗权益）
- DefaultConfirmConsumeBftProcessor（确认消耗权益）

#### 任务七：权益执行和风控处理器实现
**文件**: `task_07_execute_risk_processors.md`
**目标**: 实现权益执行和风控处理器
**输出**:
- DefaultExecuteBftProcessor（权益执行）
- DefaultCheckRiskBeforeProcessor（前置风控）
- DefaultReportRiskAfterProcessor（后置风控）

### 第四阶段：应用和接口层

#### 任务八：应用服务层实现
**文件**: `task_08_app_service.md`
**目标**: 实现应用服务接口和实现类
**输出**:
- CustomerSceneActionAppService 接口
- CustomerSceneActionAppServiceImpl 实现类

#### 任务九：REST API 控制器实现
**文件**: `task_09_rest_controller.md`
**目标**: 实现 RESTful API 控制器
**输出**:
- CustomerSceneActionController
- WebResp 统一响应格式
- 补充的 DTO 类

#### 任务十：Feign 客户端实现
**文件**: `task_10_feign_client.md`
**目标**: 实现 Feign 客户端接口
**输出**:
- CustomerSceneActionClient 接口
- CustomerSceneActionClientFallback 降级处理
- FeignConfig 配置类

## 执行顺序和依赖关系

```
任务一 (基础 DTO)
    ↓
任务二 (消耗策略) ← 任务三 (RPC 客户端)
    ↓                    ↓
任务四 (流程引擎) ← ← ← ← ←
    ↓
任务五 (咨询处理器)
    ↓
任务六 (消耗处理器) ← 任务七 (执行风控处理器)
    ↓                    ↓
任务八 (应用服务) ← ← ← ← ←
    ↓
任务九 (REST API) ← 任务十 (Feign 客户端)
```

## 拆分优势

### 1. 避免生成中断
- **代码量控制**: 每个任务的代码量控制在 3-8 个类，复杂度可控
- **专注单一功能**: 每个任务专注于特定功能模块，降低认知负担
- **清晰的边界**: 任务间边界清晰，避免混乱

### 2. 避免编译失败
- **明确依赖关系**: 每个任务都明确列出前置依赖
- **渐进式构建**: 按顺序执行，确保所需类在前置任务中已创建
- **独立验证**: 每个任务完成后可以独立编译验证

### 3. 提高代码质量
- **具体实现指导**: 每个任务都包含具体的代码示例
- **技术要求明确**: 明确的设计模式和技术要求
- **验收标准清晰**: 每个任务都有明确的验收标准

## 使用建议

### 1. 按顺序执行
严格按照依赖关系顺序执行任务，不要跳跃执行。

### 2. 逐个验证
每完成一个任务，立即执行编译验证：
```bash
./gradlew compileJava
```

### 3. 问题处理
如果某个任务出现问题：
- 检查前置依赖是否完成
- 确认所有 import 的类都存在
- 检查包路径是否正确
- 验证注解使用是否正确

### 4. 代码审查
每个任务完成后进行代码审查：
- 检查代码规范
- 验证业务逻辑正确性
- 确认异常处理完整
- 检查日志记录合理

## 预期效果

通过这种任务拆分方式，预期能够：

1. **消除生成中断**: 单个任务复杂度可控，不会因为内容过多导致生成中断
2. **避免编译失败**: 渐进式构建，确保每个阶段都能编译通过
3. **提高代码质量**: 专注单一功能，代码更加清晰和可维护
4. **便于问题定位**: 问题范围限定在单个任务内，便于快速定位和解决
5. **支持并行开发**: 后期任务可以在依赖完成后并行进行

## 注意事项

1. **严格按顺序**: 必须严格按照依赖关系执行，不能跳跃
2. **及时验证**: 每个任务完成后立即验证，不要积累问题
3. **保持一致**: 确保各任务间的接口和数据结构保持一致
4. **文档同步**: 及时更新相关文档和注释
5. **测试覆盖**: 虽然这里没有包含测试任务，但建议在业务代码完成后补充测试
