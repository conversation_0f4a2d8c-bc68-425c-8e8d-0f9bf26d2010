# 任务七：权益执行和风控处理器实现

## 任务目标
实现权益执行处理器、前置风控处理器和后置风控处理器，完成权益发放和风控检查逻辑。

## 前置依赖
- 任务一：基础 DTO 和枚举类已完成
- 任务四：场景动作流程引擎核心已完成
- AbstractSceneActionProcessor 已创建

## 需要创建的类

### 1. 权益执行处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.execute_bft`

#### DefaultExecuteBftProcessor.java
```java
/**
 * 默认权益执行处理器
 * 调用权益账户策略发放权益给用户
 */
@Component
@Slf4j
public class DefaultExecuteBftProcessor extends AbstractSceneActionProcessor {
    
    private static final String PROCESSOR_NAME = "DefaultExecuteBftProcessor";
    
    @Resource
    private BenefitAccountService benefitAccountService;
    
    @Override
    public String getProcessorName() {
        return PROCESSOR_NAME;
    }
    
    @Override
    public List<SceneActionProcessTypeEnum> getSupportedProcessTypes() {
        return Arrays.asList(SceneActionProcessTypeEnum.RECEIVE);
    }
    
    @Override
    protected SceneActionProcessResult doProcess(SceneActionProcessContext context) {
        Long userId = context.getUserId();
        String sceneCode = context.getSceneCode();
        
        try {
            log.info("开始执行权益发放，用户ID：{}，场景：{}", userId, sceneCode);
            
            // 获取权益发放配置
            BenefitExecuteConfig executeConfig = getBenefitExecuteConfig(context);
            if (executeConfig == null) {
                log.warn("未找到权益发放配置，用户ID：{}，场景：{}", userId, sceneCode);
                return buildErrorResult("NO_BENEFIT_CONFIG", "未找到权益发放配置");
            }
            
            // 检查是否有风控替换权益
            BenefitExecuteConfig finalConfig = applyRiskReplacement(context, executeConfig);
            
            // 执行权益发放
            BenefitExecuteResult executeResult = executeBenefit(userId, finalConfig, context);
            
            if (executeResult.isSuccess()) {
                log.info("权益发放成功，用户ID：{}，权益类型：{}，数量：{}", 
                        userId, finalConfig.getBenefitType(), finalConfig.getBenefitAmount());
                
                // 保存执行结果到上下文
                Map<String, Object> processData = new HashMap<>();
                processData.put("benefitExecuteResult", executeResult.toMap());
                processData.put("executedBenefit", finalConfig);
                
                return SceneActionProcessResult.builder()
                        .success(true)
                        .resultCode("SUCCESS")
                        .resultMessage("权益发放成功")
                        .processStatus(ProcessStatus.SUCCESS)
                        .processData(processData)
                        .build();
            } else {
                log.error("权益发放失败，用户ID：{}，原因：{}", userId, executeResult.getErrorMessage());
                
                // 即使发放失败，也要保存结果供后续处理器使用
                Map<String, Object> processData = new HashMap<>();
                processData.put("benefitExecuteResult", executeResult.toMap());
                
                return SceneActionProcessResult.builder()
                        .success(false)
                        .resultCode(executeResult.getErrorCode())
                        .resultMessage(executeResult.getErrorMessage())
                        .processStatus(ProcessStatus.FAILED)
                        .processData(processData)
                        .build();
            }
            
        } catch (Exception e) {
            log.error("执行权益发放异常，用户ID：{}，场景：{}", userId, sceneCode, e);
            
            // 保存失败结果
            Map<String, Object> processData = new HashMap<>();
            processData.put("benefitExecuteResult", BenefitExecuteResult.failed("EXECUTE_EXCEPTION", e.getMessage()).toMap());
            
            return SceneActionProcessResult.builder()
                    .success(false)
                    .resultCode("EXECUTE_BFT_ERROR")
                    .resultMessage("权益发放异常：" + e.getMessage())
                    .processStatus(ProcessStatus.FAILED)
                    .processData(processData)
                    .build();
        }
    }
    
    /**
     * 获取权益发放配置
     */
    private BenefitExecuteConfig getBenefitExecuteConfig(SceneActionProcessContext context) {
        Map<String, Object> extParams = context.getExtParams();
        if (extParams == null) {
            return null;
        }
        
        // 从咨询结果中获取要发放的权益信息
        List<ReceivableBenefitDTO> receivableBenefits = 
                (List<ReceivableBenefitDTO>) extParams.get("receivableBenefits");
        
        if (CollectionUtils.isEmpty(receivableBenefits)) {
            return null;
        }
        
        // 简化处理，取第一个可领取权益
        ReceivableBenefitDTO benefit = receivableBenefits.get(0);
        
        return BenefitExecuteConfig.builder()
                .benefitId(benefit.getBenefitId())
                .benefitType(benefit.getBenefitType())
                .benefitAmount(benefit.getBenefitAmount())
                .benefitName(benefit.getBenefitName())
                .build();
    }
    
    /**
     * 应用风控替换权益
     */
    private BenefitExecuteConfig applyRiskReplacement(SceneActionProcessContext context, 
                                                     BenefitExecuteConfig originalConfig) {
        Map<String, Object> extParams = context.getExtParams();
        if (extParams == null) {
            return originalConfig;
        }
        
        // 检查是否有风控替换权益
        Object riskReplacement = extParams.get("riskReplacementBenefit");
        if (riskReplacement instanceof BenefitExecuteConfig) {
            log.info("应用风控替换权益，用户ID：{}", context.getUserId());
            return (BenefitExecuteConfig) riskReplacement;
        }
        
        return originalConfig;
    }
    
    /**
     * 执行权益发放
     */
    private BenefitExecuteResult executeBenefit(Long userId, BenefitExecuteConfig config, 
                                               SceneActionProcessContext context) {
        try {
            // 构建发放请求
            BenefitGrantRequest request = BenefitGrantRequest.builder()
                    .userId(userId)
                    .benefitType(config.getBenefitType())
                    .benefitAmount(config.getBenefitAmount())
                    .requestId(context.getProcessId())
                    .sceneCode(context.getSceneCode())
                    .bizType("SCENE_ACTION")
                    .build();
            
            // 调用权益账户服务发放权益
            BenefitGrantResponse response = benefitAccountService.grantBenefit(request);
            
            if (response.isSuccess()) {
                return BenefitExecuteResult.success(response.getGrantId(), response.getAfterBalance());
            } else {
                return BenefitExecuteResult.failed(response.getErrorCode(), response.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("调用权益账户服务异常，用户ID：{}", userId, e);
            return BenefitExecuteResult.failed("SERVICE_ERROR", "权益账户服务异常");
        }
    }
    
    // 内部配置和结果类
    @Data
    @Builder
    private static class BenefitExecuteConfig {
        private String benefitId;
        private String benefitType;
        private Integer benefitAmount;
        private String benefitName;
    }
    
    @Data
    @Builder
    private static class BenefitExecuteResult {
        private boolean success;
        private String grantId;
        private Integer afterBalance;
        private String errorCode;
        private String errorMessage;
        
        public static BenefitExecuteResult success(String grantId, Integer afterBalance) {
            return BenefitExecuteResult.builder()
                    .success(true)
                    .grantId(grantId)
                    .afterBalance(afterBalance)
                    .build();
        }
        
        public static BenefitExecuteResult failed(String errorCode, String errorMessage) {
            return BenefitExecuteResult.builder()
                    .success(false)
                    .errorCode(errorCode)
                    .errorMessage(errorMessage)
                    .build();
        }
        
        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>();
            map.put("success", success);
            map.put("grantId", grantId);
            map.put("afterBalance", afterBalance);
            map.put("errorCode", errorCode);
            map.put("errorMessage", errorMessage);
            return map;
        }
    }
}
```

### 2. 前置风控处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.check_risk_before`

#### DefaultCheckRiskBeforeProcessor.java
```java
/**
 * 默认前置风控检查处理器
 * 执行风险控制检查，可能返回替换权益
 */
@Component
@Slf4j
public class DefaultCheckRiskBeforeProcessor extends AbstractSceneActionProcessor {
    
    private static final String PROCESSOR_NAME = "DefaultCheckRiskBeforeProcessor";
    
    @Resource
    private RiskControlService riskControlService;
    
    @Override
    public String getProcessorName() {
        return PROCESSOR_NAME;
    }
    
    @Override
    public List<SceneActionProcessTypeEnum> getSupportedProcessTypes() {
        return Arrays.asList(SceneActionProcessTypeEnum.RECEIVE);
    }
    
    @Override
    public boolean isEnabled(SceneActionProcessContext context) {
        // 检查是否启用风控
        SceneActionBizParamsDTO bizParams = context.getBizParams();
        if (bizParams != null && bizParams.getEnableRiskCheck() != null) {
            return bizParams.getEnableRiskCheck();
        }
        
        // 默认启用风控
        return true;
    }
    
    @Override
    protected SceneActionProcessResult doProcess(SceneActionProcessContext context) {
        Long userId = context.getUserId();
        String sceneCode = context.getSceneCode();
        
        try {
            log.info("开始前置风控检查，用户ID：{}，场景：{}", userId, sceneCode);
            
            // 构建风控检查请求
            RiskCheckRequest riskRequest = buildRiskCheckRequest(context);
            
            // 执行风控检查
            RiskCheckResponse riskResponse = riskControlService.checkRisk(riskRequest);
            
            if (riskResponse.isPass()) {
                log.info("前置风控检查通过，用户ID：{}", userId);
                
                Map<String, Object> processData = new HashMap<>();
                processData.put("riskCheckResult", "PASS");
                
                // 检查是否有替换权益
                if (riskResponse.hasReplacementBenefit()) {
                    log.info("风控返回替换权益，用户ID：{}", userId);
                    processData.put("riskReplacementBenefit", riskResponse.getReplacementBenefit());
                }
                
                return SceneActionProcessResult.builder()
                        .success(true)
                        .resultCode("SUCCESS")
                        .resultMessage("前置风控检查通过")
                        .processStatus(ProcessStatus.SUCCESS)
                        .processData(processData)
                        .build();
            } else {
                log.warn("前置风控检查不通过，用户ID：{}，原因：{}", userId, riskResponse.getRejectReason());
                return buildErrorResult("RISK_CHECK_REJECT", "风控检查不通过：" + riskResponse.getRejectReason());
            }
            
        } catch (Exception e) {
            log.error("前置风控检查异常，用户ID：{}，场景：{}", userId, sceneCode, e);
            return buildErrorResult("RISK_CHECK_ERROR", "前置风控检查失败：" + e.getMessage());
        }
    }
    
    /**
     * 构建风控检查请求
     */
    private RiskCheckRequest buildRiskCheckRequest(SceneActionProcessContext context) {
        return RiskCheckRequest.builder()
                .userId(context.getUserId())
                .sceneCode(context.getSceneCode())
                .requestId(context.getProcessId())
                .checkType("BEFORE_GRANT")
                .extParams(context.getExtParams())
                .build();
    }
}
```

### 3. 后置风控处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.report_risk_after`

#### DefaultReportRiskAfterProcessor.java
```java
/**
 * 默认后置风控上报处理器
 * 仅在权益发放成功时异步上报风控信息
 */
@Component
@Slf4j
public class DefaultReportRiskAfterProcessor extends AbstractSceneActionProcessor {
    
    private static final String PROCESSOR_NAME = "DefaultReportRiskAfterProcessor";
    
    @Resource
    private RiskControlService riskControlService;
    
    @Resource
    private TaskExecutor taskExecutor;
    
    @Override
    public String getProcessorName() {
        return PROCESSOR_NAME;
    }
    
    @Override
    public List<SceneActionProcessTypeEnum> getSupportedProcessTypes() {
        return Arrays.asList(SceneActionProcessTypeEnum.RECEIVE);
    }
    
    @Override
    public boolean isEnabled(SceneActionProcessContext context) {
        // 只有在权益发放成功时才启用后置风控
        return getBenefitExecuteResult(context);
    }
    
    @Override
    protected SceneActionProcessResult doProcess(SceneActionProcessContext context) {
        Long userId = context.getUserId();
        String sceneCode = context.getSceneCode();
        
        try {
            log.info("开始后置风控上报，用户ID：{}，场景：{}", userId, sceneCode);
            
            // 异步上报风控信息
            taskExecutor.execute(() -> {
                try {
                    reportRiskAsync(context);
                } catch (Exception e) {
                    log.error("异步上报风控信息失败，用户ID：{}", userId, e);
                }
            });
            
            log.info("后置风控上报任务已提交，用户ID：{}", userId);
            
            return SceneActionProcessResult.builder()
                    .success(true)
                    .resultCode("SUCCESS")
                    .resultMessage("后置风控上报已提交")
                    .processStatus(ProcessStatus.SUCCESS)
                    .build();
            
        } catch (Exception e) {
            log.error("后置风控上报异常，用户ID：{}，场景：{}", userId, sceneCode, e);
            // 后置风控失败不影响主流程
            return buildSuccessResult("后置风控上报异常，但不影响主流程");
        }
    }
    
    /**
     * 获取权益执行结果
     */
    private boolean getBenefitExecuteResult(SceneActionProcessContext context) {
        Map<String, Object> extParams = context.getExtParams();
        if (extParams == null) {
            return false;
        }
        
        Object executeResult = extParams.get("benefitExecuteResult");
        if (executeResult instanceof Map) {
            Map<String, Object> resultMap = (Map<String, Object>) executeResult;
            return Boolean.TRUE.equals(resultMap.get("success"));
        }
        
        return false;
    }
    
    /**
     * 异步上报风控信息
     */
    private void reportRiskAsync(SceneActionProcessContext context) {
        try {
            Long userId = context.getUserId();
            log.info("开始异步上报风控信息，用户ID：{}", userId);
            
            // 构建风控上报请求
            RiskReportRequest reportRequest = buildRiskReportRequest(context);
            
            // 调用风控服务上报
            riskControlService.reportRisk(reportRequest);
            
            log.info("异步上报风控信息完成，用户ID：{}", userId);
            
        } catch (Exception e) {
            log.error("异步上报风控信息异常，用户ID：{}", context.getUserId(), e);
        }
    }
    
    /**
     * 构建风控上报请求
     */
    private RiskReportRequest buildRiskReportRequest(SceneActionProcessContext context) {
        return RiskReportRequest.builder()
                .userId(context.getUserId())
                .sceneCode(context.getSceneCode())
                .requestId(context.getProcessId())
                .reportType("AFTER_GRANT")
                .grantResult(getGrantResultFromContext(context))
                .extParams(context.getExtParams())
                .build();
    }
    
    /**
     * 从上下文获取发放结果
     */
    private Map<String, Object> getGrantResultFromContext(SceneActionProcessContext context) {
        Map<String, Object> extParams = context.getExtParams();
        if (extParams != null) {
            Object executeResult = extParams.get("benefitExecuteResult");
            if (executeResult instanceof Map) {
                return (Map<String, Object>) executeResult;
            }
        }
        return Collections.emptyMap();
    }
}
```

## 技术要求
- 权益执行处理器调用权益账户策略发放权益
- 前置风控可能返回替换权益
- 后置风控仅在发放成功时异步上报
- 完整的异常处理和结果保存
- 支持处理器启用/禁用控制

## 验收标准
- 编译通过：`./gradlew compileJava`
- 正确继承 AbstractSceneActionProcessor
- 只支持 RECEIVE 处理类型
- 权益执行结果正确保存到上下文
- 风控处理逻辑正确
- 异步处理实现正确
