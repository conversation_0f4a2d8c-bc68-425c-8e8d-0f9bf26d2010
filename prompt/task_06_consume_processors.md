# 任务六：权益消耗相关处理器实现

## 任务目标
实现权益消耗相关的三个处理器：检查消耗权益、扣减消耗权益、确认消耗权益处理器，集成权益消耗策略模式。

## 前置依赖
- 任务一：基础 DTO 和枚举类已完成
- 任务二：权益消耗策略模式已完成
- 任务四：场景动作流程引擎核心已完成
- ConsumeBenefitStrategyFactory 已创建

## 需要创建的类

### 1. 检查消耗权益处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.check_consume_bft`

#### DefaultCheckConsumeBftProcessor.java
```java
/**
 * 默认检查消耗权益处理器
 * 通过消耗策略检查用户消耗权益是否足够
 */
@Component
@Slf4j
public class DefaultCheckConsumeBftProcessor extends AbstractSceneActionProcessor {
    
    private static final String PROCESSOR_NAME = "DefaultCheckConsumeBftProcessor";
    
    @Resource
    private ConsumeBenefitStrategyFactory strategyFactory;
    
    @Override
    public String getProcessorName() {
        return PROCESSOR_NAME;
    }
    
    @Override
    public List<SceneActionProcessTypeEnum> getSupportedProcessTypes() {
        return Arrays.asList(SceneActionProcessTypeEnum.RECEIVE);
    }
    
    @Override
    protected SceneActionProcessResult doProcess(SceneActionProcessContext context) {
        Long userId = context.getUserId();
        String sceneCode = context.getSceneCode();
        
        try {
            log.info("开始检查消耗权益，用户ID：{}，场景：{}", userId, sceneCode);
            
            // 获取消耗权益配置
            ConsumeBenefitConfig consumeConfig = getConsumeBenefitConfig(context);
            if (consumeConfig == null || !consumeConfig.isNeedConsume()) {
                log.info("场景{}无需消耗权益，跳过检查", sceneCode);
                return buildSkippedResult("无需消耗权益");
            }
            
            // 构建消耗上下文
            ConsumeBenefitContext consumeContext = buildConsumeContext(context, consumeConfig);
            
            // 获取消耗策略
            ConsumeBenefitStrategy strategy = strategyFactory.getStrategy(consumeConfig.getBenefitType());
            
            // 执行检查
            ConsumeBenefitResult checkResult = strategy.check(consumeContext);
            
            if (checkResult.getSuccess()) {
                log.info("消耗权益检查通过，用户ID：{}，权益类型：{}", userId, consumeConfig.getBenefitType());
                
                // 将检查结果保存到上下文中
                Map<String, Object> processData = new HashMap<>();
                processData.put("consumeCheckResult", checkResult);
                processData.put("consumeContext", consumeContext);
                
                return SceneActionProcessResult.builder()
                        .success(true)
                        .resultCode("SUCCESS")
                        .resultMessage("消耗权益检查通过")
                        .processStatus(ProcessStatus.SUCCESS)
                        .processData(processData)
                        .build();
            } else {
                log.warn("消耗权益检查失败，用户ID：{}，原因：{}", userId, checkResult.getResultMessage());
                return buildErrorResult(checkResult.getResultCode(), checkResult.getResultMessage());
            }
            
        } catch (Exception e) {
            log.error("检查消耗权益异常，用户ID：{}，场景：{}", userId, sceneCode, e);
            return buildErrorResult("CHECK_CONSUME_ERROR", "检查消耗权益失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取消耗权益配置
     */
    private ConsumeBenefitConfig getConsumeBenefitConfig(SceneActionProcessContext context) {
        // 从上下文或配置服务中获取消耗权益配置
        // 这里简化处理，实际应该从配置中心或数据库获取
        
        String sceneCode = context.getSceneCode();
        Map<String, Object> extParams = context.getExtParams();
        
        // 检查是否需要消耗权益
        if (extParams != null && extParams.containsKey("consumeBenefitType")) {
            ConsumeBenefitConfig config = new ConsumeBenefitConfig();
            config.setNeedConsume(true);
            config.setBenefitType((String) extParams.get("consumeBenefitType"));
            config.setConsumeAmount((Integer) extParams.getOrDefault("consumeBenefitAmount", 1));
            return config;
        }
        
        // 默认不需要消耗权益
        return ConsumeBenefitConfig.noConsume();
    }
    
    /**
     * 构建消耗上下文
     */
    private ConsumeBenefitContext buildConsumeContext(SceneActionProcessContext context, 
                                                     ConsumeBenefitConfig config) {
        return ConsumeBenefitContext.builder()
                .userId(context.getUserId())
                .benefitType(config.getBenefitType())
                .consumeAmount(config.getConsumeAmount())
                .requestId(context.getProcessId())
                .processStage("CHECK")
                .extParams(context.getExtParams())
                .build();
    }
    
    // 内部配置类
    private static class ConsumeBenefitConfig {
        private boolean needConsume;
        private String benefitType;
        private Integer consumeAmount;
        
        public static ConsumeBenefitConfig noConsume() {
            ConsumeBenefitConfig config = new ConsumeBenefitConfig();
            config.needConsume = false;
            return config;
        }
        
        // getters and setters...
        public boolean isNeedConsume() { return needConsume; }
        public void setNeedConsume(boolean needConsume) { this.needConsume = needConsume; }
        public String getBenefitType() { return benefitType; }
        public void setBenefitType(String benefitType) { this.benefitType = benefitType; }
        public Integer getConsumeAmount() { return consumeAmount; }
        public void setConsumeAmount(Integer consumeAmount) { this.consumeAmount = consumeAmount; }
    }
}
```

### 2. 扣减消耗权益处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.deduct_consume_bft`

#### DefaultDeductConsumeBftProcessor.java
```java
/**
 * 默认扣减消耗权益处理器
 * 通过消耗策略扣减（冻结）用户相应的消耗权益
 */
@Component
@Slf4j
public class DefaultDeductConsumeBftProcessor extends AbstractSceneActionProcessor {
    
    private static final String PROCESSOR_NAME = "DefaultDeductConsumeBftProcessor";
    
    @Resource
    private ConsumeBenefitStrategyFactory strategyFactory;
    
    @Override
    public String getProcessorName() {
        return PROCESSOR_NAME;
    }
    
    @Override
    public List<SceneActionProcessTypeEnum> getSupportedProcessTypes() {
        return Arrays.asList(SceneActionProcessTypeEnum.RECEIVE);
    }
    
    @Override
    protected SceneActionProcessResult doProcess(SceneActionProcessContext context) {
        Long userId = context.getUserId();
        
        try {
            log.info("开始扣减消耗权益，用户ID：{}", userId);
            
            // 从上下文中获取消耗上下文
            ConsumeBenefitContext consumeContext = getConsumeContextFromProcessContext(context);
            if (consumeContext == null) {
                log.info("未找到消耗上下文，跳过扣减，用户ID：{}", userId);
                return buildSkippedResult("无需扣减消耗权益");
            }
            
            // 更新处理阶段
            consumeContext.setProcessStage("DEDUCT");
            
            // 获取消耗策略
            ConsumeBenefitStrategy strategy = strategyFactory.getStrategy(consumeContext.getBenefitType());
            
            // 执行扣减
            ConsumeBenefitResult deductResult = strategy.deduct(consumeContext);
            
            if (deductResult.getSuccess()) {
                log.info("消耗权益扣减成功，用户ID：{}，权益类型：{}", userId, consumeContext.getBenefitType());
                
                // 将扣减结果保存到上下文中
                Map<String, Object> processData = new HashMap<>();
                processData.put("consumeDeductResult", deductResult);
                processData.put("consumeContext", consumeContext);
                
                return SceneActionProcessResult.builder()
                        .success(true)
                        .resultCode("SUCCESS")
                        .resultMessage("消耗权益扣减成功")
                        .processStatus(ProcessStatus.SUCCESS)
                        .processData(processData)
                        .build();
            } else {
                log.error("消耗权益扣减失败，用户ID：{}，原因：{}", userId, deductResult.getResultMessage());
                return buildErrorResult(deductResult.getResultCode(), deductResult.getResultMessage());
            }
            
        } catch (Exception e) {
            log.error("扣减消耗权益异常，用户ID：{}", userId, e);
            return buildErrorResult("DEDUCT_CONSUME_ERROR", "扣减消耗权益失败：" + e.getMessage());
        }
    }
    
    /**
     * 从流程上下文中获取消耗上下文
     */
    private ConsumeBenefitContext getConsumeContextFromProcessContext(SceneActionProcessContext context) {
        Map<String, Object> extParams = context.getExtParams();
        if (extParams == null) {
            return null;
        }
        
        return (ConsumeBenefitContext) extParams.get("consumeContext");
    }
}
```

### 3. 确认消耗权益处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.confirm_consume_bft`

#### DefaultConfirmConsumeBftProcessor.java
```java
/**
 * 默认确认消耗权益处理器
 * 根据权益发放结果决定确认消耗（发放成功）或释放消耗（发放失败）
 */
@Component
@Slf4j
public class DefaultConfirmConsumeBftProcessor extends AbstractSceneActionProcessor {
    
    private static final String PROCESSOR_NAME = "DefaultConfirmConsumeBftProcessor";
    
    @Resource
    private ConsumeBenefitStrategyFactory strategyFactory;
    
    @Override
    public String getProcessorName() {
        return PROCESSOR_NAME;
    }
    
    @Override
    public List<SceneActionProcessTypeEnum> getSupportedProcessTypes() {
        return Arrays.asList(SceneActionProcessTypeEnum.RECEIVE);
    }
    
    @Override
    protected SceneActionProcessResult doProcess(SceneActionProcessContext context) {
        Long userId = context.getUserId();
        
        try {
            log.info("开始确认消耗权益，用户ID：{}", userId);
            
            // 从上下文中获取消耗上下文
            ConsumeBenefitContext consumeContext = getConsumeContextFromProcessContext(context);
            if (consumeContext == null) {
                log.info("未找到消耗上下文，跳过确认，用户ID：{}", userId);
                return buildSkippedResult("无需确认消耗权益");
            }
            
            // 获取权益执行结果
            boolean benefitExecuteSuccess = getBenefitExecuteResult(context);
            
            // 获取消耗策略
            ConsumeBenefitStrategy strategy = strategyFactory.getStrategy(consumeContext.getBenefitType());
            
            ConsumeBenefitResult confirmResult;
            
            if (benefitExecuteSuccess) {
                // 权益发放成功，确认消耗
                log.info("权益发放成功，确认消耗权益，用户ID：{}", userId);
                consumeContext.setProcessStage("CONFIRM");
                confirmResult = strategy.confirm(consumeContext);
            } else {
                // 权益发放失败，释放消耗
                log.info("权益发放失败，释放消耗权益，用户ID：{}", userId);
                consumeContext.setProcessStage("RELEASE");
                confirmResult = strategy.release(consumeContext);
            }
            
            if (confirmResult.getSuccess()) {
                String action = benefitExecuteSuccess ? "确认" : "释放";
                log.info("消耗权益{}成功，用户ID：{}，权益类型：{}", 
                        action, userId, consumeContext.getBenefitType());
                
                Map<String, Object> processData = new HashMap<>();
                processData.put("consumeConfirmResult", confirmResult);
                processData.put("consumeAction", action);
                
                return SceneActionProcessResult.builder()
                        .success(true)
                        .resultCode("SUCCESS")
                        .resultMessage("消耗权益" + action + "成功")
                        .processStatus(ProcessStatus.SUCCESS)
                        .processData(processData)
                        .build();
            } else {
                String action = benefitExecuteSuccess ? "确认" : "释放";
                log.error("消耗权益{}失败，用户ID：{}，原因：{}", action, userId, confirmResult.getResultMessage());
                return buildErrorResult(confirmResult.getResultCode(), confirmResult.getResultMessage());
            }
            
        } catch (Exception e) {
            log.error("确认消耗权益异常，用户ID：{}", userId, e);
            return buildErrorResult("CONFIRM_CONSUME_ERROR", "确认消耗权益失败：" + e.getMessage());
        }
    }
    
    /**
     * 从流程上下文中获取消耗上下文
     */
    private ConsumeBenefitContext getConsumeContextFromProcessContext(SceneActionProcessContext context) {
        Map<String, Object> extParams = context.getExtParams();
        if (extParams == null) {
            return null;
        }
        
        return (ConsumeBenefitContext) extParams.get("consumeContext");
    }
    
    /**
     * 获取权益执行结果
     */
    private boolean getBenefitExecuteResult(SceneActionProcessContext context) {
        Map<String, Object> extParams = context.getExtParams();
        if (extParams == null) {
            return false;
        }
        
        // 从权益执行处理器的结果中获取执行状态
        Object executeResult = extParams.get("benefitExecuteResult");
        if (executeResult instanceof Map) {
            Map<String, Object> resultMap = (Map<String, Object>) executeResult;
            return Boolean.TRUE.equals(resultMap.get("success"));
        }
        
        return false;
    }
}
```

## 技术要求
- 集成权益消耗策略工厂
- 通过策略模式调用相应的消耗操作
- 检查处理器调用 check 方法
- 扣减处理器调用 deduct 方法
- 确认处理器根据发放结果调用 confirm 或 release 方法
- 完整的上下文传递和结果保存

## 验收标准
- 编译通过：`./gradlew compileJava`
- 正确继承 AbstractSceneActionProcessor
- 只支持 RECEIVE 处理类型
- 正确集成消耗策略工厂
- 上下文传递正确
- 异常处理完善
