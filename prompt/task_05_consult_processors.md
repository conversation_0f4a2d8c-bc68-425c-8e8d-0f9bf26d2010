# 任务五：咨询流程处理器实现

## 任务目标
实现咨询活动处理器，参考现有业务逻辑 `CustomerBenefitAppServiceImpl#sceneConsult`，返回用户可领取和不可领取的权益列表。

## 前置依赖
- 任务一：基础 DTO 和枚举类已完成
- 任务四：场景动作流程引擎核心已完成
- SceneActionProcessor、AbstractSceneActionProcessor 已创建

## 需要创建的类

### 1. 咨询活动处理器
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor.default_processor.consult_activity`

#### DefaultConsultActivityProcessor.java
```java
/**
 * 默认咨询活动处理器
 * 参考 CustomerBenefitAppServiceImpl#sceneConsult 逻辑
 * 返回用户可领取和不可领取的权益列表
 */
@Component
@Slf4j
public class DefaultConsultActivityProcessor extends AbstractSceneActionProcessor {
    
    private static final String PROCESSOR_NAME = "DefaultConsultActivityProcessor";
    
    @Resource
    private SceneBenefitCoreService sceneBenefitCoreService;
    
    @Override
    public String getProcessorName() {
        return PROCESSOR_NAME;
    }
    
    @Override
    public List<SceneActionProcessTypeEnum> getSupportedProcessTypes() {
        return Arrays.asList(SceneActionProcessTypeEnum.CONSULT);
    }
    
    @Override
    protected SceneActionProcessResult doProcess(SceneActionProcessContext context) {
        Long userId = context.getUserId();
        String sceneCode = context.getSceneCode();
        
        try {
            log.info("开始咨询活动，用户ID：{}，场景：{}", userId, sceneCode);
            
            // 查询场景下的所有权益活动
            List<BenefitActivityDTO> allActivities = querySceneActivities(sceneCode);
            
            if (CollectionUtils.isEmpty(allActivities)) {
                log.info("场景{}下没有配置权益活动", sceneCode);
                return buildConsultResult(Collections.emptyList(), Collections.emptyList());
            }
            
            // 分别处理可领取和不可领取的权益
            List<ReceivableBenefitDTO> receivableBenefits = new ArrayList<>();
            List<UnreceivableBenefitDTO> unreceivableBenefits = new ArrayList<>();
            
            for (BenefitActivityDTO activity : allActivities) {
                processActivity(userId, activity, receivableBenefits, unreceivableBenefits);
            }
            
            log.info("咨询活动完成，用户ID：{}，可领取权益数：{}，不可领取权益数：{}", 
                    userId, receivableBenefits.size(), unreceivableBenefits.size());
            
            return buildConsultResult(receivableBenefits, unreceivableBenefits);
            
        } catch (Exception e) {
            log.error("咨询活动处理异常，用户ID：{}，场景：{}", userId, sceneCode, e);
            return buildErrorResult("CONSULT_ACTIVITY_ERROR", "咨询活动失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询场景下的权益活动
     */
    private List<BenefitActivityDTO> querySceneActivities(String sceneCode) {
        try {
            // 调用场景权益核心服务查询活动列表
            return sceneBenefitCoreService.queryActivitiesByScene(sceneCode);
        } catch (Exception e) {
            log.error("查询场景活动失败，场景：{}", sceneCode, e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 处理单个活动，判断是否可领取
     */
    private void processActivity(Long userId, BenefitActivityDTO activity, 
                                List<ReceivableBenefitDTO> receivableBenefits,
                                List<UnreceivableBenefitDTO> unreceivableBenefits) {
        
        try {
            // 检查活动状态
            if (!isActivityValid(activity)) {
                addUnreceivableBenefit(unreceivableBenefits, activity, "ACTIVITY_INVALID", "活动已失效");
                return;
            }
            
            // 检查用户领取资格
            EligibilityCheckResult eligibilityResult = checkUserEligibility(userId, activity);
            if (!eligibilityResult.isEligible()) {
                addUnreceivableBenefit(unreceivableBenefits, activity, 
                        eligibilityResult.getReasonCode(), eligibilityResult.getReasonMessage());
                return;
            }
            
            // 检查领取次数限制
            ReceiveTimesCheckResult timesResult = checkReceiveTimes(userId, activity);
            if (!timesResult.canReceive()) {
                addUnreceivableBenefit(unreceivableBenefits, activity, 
                        "RECEIVE_TIMES_LIMIT", "已达到领取次数上限");
                return;
            }
            
            // 初始化用户活动次数（如果需要）
            initUserActivityTimes(userId, activity);
            
            // 添加到可领取列表
            addReceivableBenefit(receivableBenefits, activity, timesResult.getAvailableTimes());
            
        } catch (Exception e) {
            log.error("处理活动异常，用户ID：{}，活动ID：{}", userId, activity.getActivityId(), e);
            addUnreceivableBenefit(unreceivableBenefits, activity, "PROCESS_ERROR", "处理异常");
        }
    }
    
    /**
     * 检查活动是否有效
     */
    private boolean isActivityValid(BenefitActivityDTO activity) {
        if (activity == null) {
            return false;
        }
        
        // 检查活动状态
        if (!"ACTIVE".equals(activity.getStatus())) {
            return false;
        }
        
        // 检查活动时间
        long currentTime = System.currentTimeMillis();
        if (activity.getStartTime() != null && currentTime < activity.getStartTime()) {
            return false;
        }
        if (activity.getEndTime() != null && currentTime > activity.getEndTime()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查用户领取资格
     */
    private EligibilityCheckResult checkUserEligibility(Long userId, BenefitActivityDTO activity) {
        try {
            // 这里可以调用用户资格检查服务
            // 例如：检查用户等级、标签、黑白名单等
            
            // 模拟检查逻辑
            if (userId <= 0) {
                return EligibilityCheckResult.notEligible("INVALID_USER", "无效用户");
            }
            
            return EligibilityCheckResult.eligible();
            
        } catch (Exception e) {
            log.error("检查用户资格异常，用户ID：{}，活动ID：{}", userId, activity.getActivityId(), e);
            return EligibilityCheckResult.notEligible("CHECK_ERROR", "资格检查异常");
        }
    }
    
    /**
     * 检查领取次数
     */
    private ReceiveTimesCheckResult checkReceiveTimes(Long userId, BenefitActivityDTO activity) {
        try {
            // 查询用户已领取次数
            int receivedTimes = getUserReceivedTimes(userId, activity.getActivityId());
            
            // 获取活动限制次数
            int limitTimes = activity.getLimitTimes() != null ? activity.getLimitTimes() : Integer.MAX_VALUE;
            
            if (receivedTimes >= limitTimes) {
                return ReceiveTimesCheckResult.cannotReceive();
            }
            
            int availableTimes = limitTimes - receivedTimes;
            return ReceiveTimesCheckResult.canReceive(availableTimes);
            
        } catch (Exception e) {
            log.error("检查领取次数异常，用户ID：{}，活动ID：{}", userId, activity.getActivityId(), e);
            return ReceiveTimesCheckResult.cannotReceive();
        }
    }
    
    /**
     * 获取用户已领取次数
     */
    private int getUserReceivedTimes(Long userId, String activityId) {
        // 这里应该调用实际的查询服务
        // 暂时返回0，表示未领取过
        return 0;
    }
    
    /**
     * 初始化用户活动次数
     */
    private void initUserActivityTimes(Long userId, BenefitActivityDTO activity) {
        try {
            // 为活动初始化用户的可用次数
            // 这里可以调用相关服务进行初始化
            log.debug("初始化用户活动次数，用户ID：{}，活动ID：{}", userId, activity.getActivityId());
        } catch (Exception e) {
            log.warn("初始化用户活动次数失败，用户ID：{}，活动ID：{}", userId, activity.getActivityId(), e);
        }
    }
    
    /**
     * 添加可领取权益
     */
    private void addReceivableBenefit(List<ReceivableBenefitDTO> receivableBenefits, 
                                     BenefitActivityDTO activity, int availableTimes) {
        ReceivableBenefitDTO benefit = new ReceivableBenefitDTO();
        benefit.setBenefitId(activity.getBenefitId());
        benefit.setBenefitName(activity.getBenefitName());
        benefit.setBenefitType(activity.getBenefitType());
        benefit.setBenefitAmount(activity.getBenefitAmount());
        benefit.setBenefitDesc(activity.getBenefitDesc());
        benefit.setAvailableTimes(availableTimes);
        
        receivableBenefits.add(benefit);
    }
    
    /**
     * 添加不可领取权益
     */
    private void addUnreceivableBenefit(List<UnreceivableBenefitDTO> unreceivableBenefits,
                                       BenefitActivityDTO activity, String reasonCode, String reasonMessage) {
        UnreceivableBenefitDTO benefit = new UnreceivableBenefitDTO();
        benefit.setBenefitId(activity.getBenefitId());
        benefit.setBenefitName(activity.getBenefitName());
        benefit.setReasonCode(reasonCode);
        benefit.setReasonMessage(reasonMessage);
        
        unreceivableBenefits.add(benefit);
    }
    
    /**
     * 构建咨询结果
     */
    private SceneActionProcessResult buildConsultResult(List<ReceivableBenefitDTO> receivableBenefits,
                                                       List<UnreceivableBenefitDTO> unreceivableBenefits) {
        Map<String, Object> processData = new HashMap<>();
        processData.put("receivableBenefits", receivableBenefits);
        processData.put("unreceivableBenefits", unreceivableBenefits);
        
        return SceneActionProcessResult.builder()
                .success(true)
                .resultCode("SUCCESS")
                .resultMessage("咨询活动处理成功")
                .processStatus(ProcessStatus.SUCCESS)
                .processData(processData)
                .build();
    }
    
    // 内部辅助类
    private static class EligibilityCheckResult {
        private boolean eligible;
        private String reasonCode;
        private String reasonMessage;
        
        public static EligibilityCheckResult eligible() {
            EligibilityCheckResult result = new EligibilityCheckResult();
            result.eligible = true;
            return result;
        }
        
        public static EligibilityCheckResult notEligible(String reasonCode, String reasonMessage) {
            EligibilityCheckResult result = new EligibilityCheckResult();
            result.eligible = false;
            result.reasonCode = reasonCode;
            result.reasonMessage = reasonMessage;
            return result;
        }
        
        // getters...
        public boolean isEligible() { return eligible; }
        public String getReasonCode() { return reasonCode; }
        public String getReasonMessage() { return reasonMessage; }
    }
    
    private static class ReceiveTimesCheckResult {
        private boolean canReceive;
        private int availableTimes;
        
        public static ReceiveTimesCheckResult canReceive(int availableTimes) {
            ReceiveTimesCheckResult result = new ReceiveTimesCheckResult();
            result.canReceive = true;
            result.availableTimes = availableTimes;
            return result;
        }
        
        public static ReceiveTimesCheckResult cannotReceive() {
            ReceiveTimesCheckResult result = new ReceiveTimesCheckResult();
            result.canReceive = false;
            result.availableTimes = 0;
            return result;
        }
        
        // getters...
        public boolean canReceive() { return canReceive; }
        public int getAvailableTimes() { return availableTimes; }
    }
}
```

## 技术要求
- 参考 CustomerBenefitAppServiceImpl#sceneConsult 方法逻辑
- 返回可领取权益列表和不可领取权益列表
- 包含活动有效性检查、用户资格检查、领取次数检查
- 为活动初始化用户的可用次数
- 完整的异常处理和日志记录

## 验收标准
- 编译通过：`./gradlew compileJava`
- 正确继承 AbstractSceneActionProcessor
- 只支持 CONSULT 处理类型
- 返回正确的咨询结果格式
- 异常处理完善
- 日志记录完整
