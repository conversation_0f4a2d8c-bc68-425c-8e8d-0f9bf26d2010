# 任务一：基础 DTO 和枚举类创建

## 任务目标
创建场景动作模块所需的所有基础 DTO 类和枚举类，为后续业务逻辑实现提供数据结构支撑。

## 需要创建的类

### 1. 场景相关 DTO
**路径**: `com.ddmc.equity.dto.scene`

#### ConsumeBenefitSceneDTO.java
```java
@Data
@NoArgsConstructor
@Builder
@ApiModel("权益消耗场景")
public class ConsumeBenefitSceneDTO {
    @ApiModelProperty("场景编码")
    private String sceneCode;
    
    @ApiModelProperty("场景名称")
    private String sceneName;
    
    @ApiModelProperty("消耗权益类型")
    private String consumeBenefitType;
    
    @ApiModelProperty("消耗权益数量")
    private Integer consumeBenefitAmount;
}
```

#### SendBenefitSceneDTO.java
```java
@Data
@NoArgsConstructor
@Builder
@ApiModel("权益发放场景")
public class SendBenefitSceneDTO {
    @ApiModelProperty("场景编码")
    private String sceneCode;
    
    @ApiModelProperty("场景名称")
    private String sceneName;
    
    @ApiModelProperty("发放权益类型")
    private String sendBenefitType;
    
    @ApiModelProperty("发放权益数量")
    private Integer sendBenefitAmount;
}
```

### 2. 接口请求响应 DTO
**路径**: `com.ddmc.equity.dto.customer.scene_action`

#### SceneActionConsultReqDTO.java
```java
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("场景动作咨询请求")
public class SceneActionConsultReqDTO extends CommonBaseRequestDTO {
    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;
    
    @ApiModelProperty("活动ID")
    private String activityId;
    
    @ApiModelProperty("扩展参数")
    private String extParams;
}
```

#### SceneActionConsultRespDTO.java
```java
@Data
@NoArgsConstructor
@ApiModel("场景动作咨询响应")
public class SceneActionConsultRespDTO {
    @ApiModelProperty("可领取权益列表")
    private List<ReceivableBenefitDTO> receivableBenefits;
    
    @ApiModelProperty("不可领取权益列表")
    private List<UnreceivableBenefitDTO> unreceivableBenefits;
    
    @ApiModelProperty("结果码")
    private String resultCode;
    
    @ApiModelProperty("结果描述")
    private String resultMessage;
}
```

#### SceneActionReceiveReqDTO.java
```java
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("场景动作领取请求")
public class SceneActionReceiveReqDTO extends CommonBaseRequestDTO {
    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    @ApiModelProperty(value = "场景编码", required = true)
    @NotBlank(message = "场景编码不能为空")
    private String sceneCode;
    
    @ApiModelProperty(value = "活动ID", required = true)
    @NotBlank(message = "活动ID不能为空")
    private String activityId;
    
    @ApiModelProperty(value = "权益ID", required = true)
    @NotBlank(message = "权益ID不能为空")
    private String benefitId;
    
    @ApiModelProperty("扩展参数")
    private String extParams;
}
```

#### SceneActionReceiveRespDTO.java
```java
@Data
@NoArgsConstructor
@ApiModel("场景动作领取响应")
public class SceneActionReceiveRespDTO {
    @ApiModelProperty("结果码")
    private String resultCode;
    
    @ApiModelProperty("结果描述")
    private String resultMessage;
    
    @ApiModelProperty("领取的权益信息")
    private ReceivedBenefitDTO receivedBenefit;
    
    @ApiModelProperty("消耗的权益信息")
    private ConsumedBenefitDTO consumedBenefit;
    
    @ApiModelProperty("处理流水号")
    private String processId;
}
```

### 3. 流程处理相关
**路径**: `com.ddmc.equity.processor.scene_action.v1`

#### ProcessStatus.java (枚举)
```java
public enum ProcessStatus {
    NOT_STARTED("NOT_STARTED", "未开始"),
    IN_PROGRESS("IN_PROGRESS", "进行中"),
    SUCCESS("SUCCESS", "成功"),
    FAILED("FAILED", "失败"),
    SKIPPED("SKIPPED", "跳过");
    
    private final String code;
    private final String desc;
    
    ProcessStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static ProcessStatus getByCode(String code) {
        for (ProcessStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    // getters...
}
```

#### SceneActionProcessTypeEnum.java (枚举)
```java
public enum SceneActionProcessTypeEnum {
    CONSULT("CONSULT", "咨询流程"),
    RECEIVE("RECEIVE", "领取流程");
    
    private final String code;
    private final String desc;
    
    SceneActionProcessTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static SceneActionProcessTypeEnum getByCode(String code) {
        for (SceneActionProcessTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
    
    // getters...
}
```

### 4. 业务数据和上下文 DTO

#### SceneActionBizDataDTO.java
```java
@Data
@NoArgsConstructor
@Builder
public class SceneActionBizDataDTO {
    private Map<String, Object> activityInfo;
    private Map<String, Object> benefitInfo;
    private Map<String, Object> userInfo;
    private Map<String, Object> riskInfo;
}
```

#### SceneActionBizParamsDTO.java
```java
@Data
@NoArgsConstructor
@Builder
public class SceneActionBizParamsDTO {
    private Boolean enableRiskCheck;
    private Boolean enableConsumeBenefit;
    private Integer timeoutSeconds;
    private Integer retryTimes;
}
```

#### SceneActionProcessContext.java
```java
@Data
@NoArgsConstructor
@Builder
public class SceneActionProcessContext {
    private Long userId;
    private String sceneCode;
    private SceneActionProcessTypeEnum processType;
    private SceneActionBizParamsDTO bizParams;
    private SceneActionBizDataDTO bizData;
    private String processId;
    private Map<String, Object> extParams;
}
```

#### SceneActionProcessResult.java
```java
@Data
@NoArgsConstructor
@Builder
public class SceneActionProcessResult {
    private Boolean success;
    private String resultCode;
    private String resultMessage;
    private ProcessStatus processStatus;
    private Map<String, Object> processData;
    private Map<String, Object> extData;
}
```

### 5. 权益消耗相关 DTO
**路径**: `com.ddmc.equity.processor.consume_benefit.dto`

#### ConsumeBenefitBizDataDTO.java
```java
@Data
@NoArgsConstructor
@Builder
public class ConsumeBenefitBizDataDTO {
    private Integer currentBalance;
    private Integer frozenAmount;
    private Integer availableAmount;
    private String consumeRecord;
}
```

#### ConsumeBenefitBizParamsDTO.java
```java
@Data
@NoArgsConstructor
@Builder
public class ConsumeBenefitBizParamsDTO {
    private Boolean enableBalanceCheck;
    private Boolean enableFrozen;
    private Integer timeoutSeconds;
    private Integer maxRetryTimes;
}
```

#### ConsumeBenefitContext.java
```java
@Data
@NoArgsConstructor
@Builder
public class ConsumeBenefitContext {
    private Long userId;
    private String benefitType;
    private Integer consumeAmount;
    private String requestId;
    private ConsumeBenefitBizParamsDTO bizParams;
    private ConsumeBenefitBizDataDTO bizData;
    private String processStage;
    private Map<String, Object> extParams;
}
```

#### ConsumeBenefitResult.java
```java
@Data
@NoArgsConstructor
@Builder
public class ConsumeBenefitResult {
    private Boolean success;
    private String resultCode;
    private String resultMessage;
    private Integer beforeBalance;
    private Integer afterBalance;
    private Integer actualConsumeAmount;
    private String processId;
    private Map<String, Object> extData;
}
```

#### RpcRespDTO.java
```java
@Data
@NoArgsConstructor
@Builder
public class RpcRespDTO<T> {
    private Boolean success;
    private String code;
    private String message;
    private T data;
    private Long timestamp;
}
```

## 技术要求
- 使用 Java 8 语法
- 使用 Lombok 注解：@Data、@NoArgsConstructor、@Builder
- 使用 JSR-303 校验注解：@NotNull、@NotBlank
- 使用 Swagger 注解：@ApiModel、@ApiModelProperty
- 枚举类提供 getByCode() 静态方法

## 验收标准
- 编译通过：`./gradlew compileJava`
- 所有类在正确的包路径下
- 注解使用正确
- 继承关系正确（请求 DTO 继承 CommonBaseRequestDTO）
