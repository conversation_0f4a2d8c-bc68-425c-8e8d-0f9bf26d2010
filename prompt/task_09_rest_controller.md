# 任务九：REST API 控制器实现

## 任务目标
实现 RESTful API 控制器，提供场景动作的 HTTP 接口，集成 Swagger 文档和参数校验，实现统一的异常处理。

## 前置依赖
- 任务一：基础 DTO 和枚举类已完成
- 任务八：应用服务层已完成
- CustomerSceneActionAppService 已创建

## 需要创建的类

### 1. REST API 控制器
**路径**: `com.ddmc.equity.controller`

#### CustomerSceneActionController.java
```java
/**
 * 客户场景动作控制器
 * 提供场景动作相关的 RESTful API
 */
@RestController
@RequestMapping("/api/v1/customer/scene-action")
@Api(tags = "客户场景动作接口")
@Slf4j
public class CustomerSceneActionController {
    
    @Resource
    private CustomerSceneActionAppService sceneActionAppService;
    
    /**
     * 咨询可领取权益
     */
    @PostMapping("/consult")
    @ApiOperation(value = "咨询可领取权益", notes = "查询用户在指定场景下可领取的权益列表")
    public WebResp<SceneActionConsultRespDTO> consultForReceive(
            @Valid @RequestBody SceneActionConsultReqDTO request) {
        
        try {
            log.info("收到咨询请求，用户ID：{}，场景：{}", request.getUserId(), request.getSceneCode());
            
            SceneActionConsultRespDTO response = sceneActionAppService.consultForReceive(request);
            
            if ("SUCCESS".equals(response.getResultCode())) {
                log.info("咨询请求处理成功，用户ID：{}，可领取权益数：{}", 
                        request.getUserId(), response.getReceivableBenefits().size());
                return WebResp.success(response);
            } else {
                log.warn("咨询请求处理失败，用户ID：{}，原因：{}", 
                        request.getUserId(), response.getResultMessage());
                return WebResp.error(response.getResultCode(), response.getResultMessage(), response);
            }
            
        } catch (Exception e) {
            log.error("咨询请求处理异常，用户ID：{}", request.getUserId(), e);
            return WebResp.error("SYSTEM_ERROR", "系统异常，请稍后重试");
        }
    }
    
    /**
     * 领取权益
     */
    @PostMapping("/receive")
    @ApiOperation(value = "领取权益", notes = "用户领取指定的权益")
    public WebResp<SceneActionReceiveRespDTO> receive(
            @Valid @RequestBody SceneActionReceiveReqDTO request) {
        
        try {
            log.info("收到领取请求，用户ID：{}，场景：{}，权益ID：{}", 
                    request.getUserId(), request.getSceneCode(), request.getBenefitId());
            
            SceneActionReceiveRespDTO response = sceneActionAppService.receive(request);
            
            if ("SUCCESS".equals(response.getResultCode())) {
                log.info("领取请求处理成功，用户ID：{}，流水号：{}", 
                        request.getUserId(), response.getProcessId());
                return WebResp.success(response);
            } else {
                log.warn("领取请求处理失败，用户ID：{}，原因：{}", 
                        request.getUserId(), response.getResultMessage());
                return WebResp.error(response.getResultCode(), response.getResultMessage(), response);
            }
            
        } catch (Exception e) {
            log.error("领取请求处理异常，用户ID：{}", request.getUserId(), e);
            return WebResp.error("SYSTEM_ERROR", "系统异常，请稍后重试");
        }
    }
    
    /**
     * 全局异常处理
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public WebResp<Void> handleValidationException(MethodArgumentNotValidException e) {
        log.warn("参数校验失败：{}", e.getMessage());
        
        StringBuilder errorMsg = new StringBuilder("参数校验失败：");
        e.getBindingResult().getFieldErrors().forEach(error -> {
            errorMsg.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
        });
        
        return WebResp.error("PARAM_VALIDATION_ERROR", errorMsg.toString());
    }
    
    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public WebResp<Void> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常：{}", e.getMessage());
        return WebResp.error("ILLEGAL_ARGUMENT", e.getMessage());
    }
    
    /**
     * 处理系统异常
     */
    @ExceptionHandler(Exception.class)
    public WebResp<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return WebResp.error("SYSTEM_ERROR", "系统异常，请稍后重试");
    }
}
```

### 2. 统一响应格式（如果不存在）
**路径**: `com.ddmc.equity.dto.common`

#### WebResp.java
```java
/**
 * 统一 Web 响应格式
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("统一响应格式")
public class WebResp<T> {
    
    @ApiModelProperty("是否成功")
    private Boolean success;
    
    @ApiModelProperty("响应码")
    private String code;
    
    @ApiModelProperty("响应消息")
    private String message;
    
    @ApiModelProperty("响应数据")
    private T data;
    
    @ApiModelProperty("时间戳")
    private Long timestamp;
    
    /**
     * 成功响应
     */
    public static <T> WebResp<T> success(T data) {
        WebResp<T> response = new WebResp<>();
        response.setSuccess(true);
        response.setCode("SUCCESS");
        response.setMessage("操作成功");
        response.setData(data);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
    
    /**
     * 成功响应（无数据）
     */
    public static WebResp<Void> success() {
        return success(null);
    }
    
    /**
     * 错误响应
     */
    public static <T> WebResp<T> error(String code, String message) {
        WebResp<T> response = new WebResp<>();
        response.setSuccess(false);
        response.setCode(code);
        response.setMessage(message);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
    
    /**
     * 错误响应（带数据）
     */
    public static <T> WebResp<T> error(String code, String message, T data) {
        WebResp<T> response = new WebResp<>();
        response.setSuccess(false);
        response.setCode(code);
        response.setMessage(message);
        response.setData(data);
        response.setTimestamp(System.currentTimeMillis());
        return response;
    }
}
```

### 3. 补充的 DTO 类（如果不存在）
**路径**: `com.ddmc.equity.dto.customer.scene_action`

#### ReceivedBenefitDTO.java
```java
/**
 * 已领取权益 DTO
 */
@Data
@NoArgsConstructor
@ApiModel("已领取权益")
public class ReceivedBenefitDTO {
    
    @ApiModelProperty("权益ID")
    private String benefitId;
    
    @ApiModelProperty("发放流水号")
    private String grantId;
    
    @ApiModelProperty("发放后余额")
    private Integer afterBalance;
    
    @ApiModelProperty("领取时间")
    private Long grantTime;
}
```

#### ConsumedBenefitDTO.java
```java
/**
 * 已消耗权益 DTO
 */
@Data
@NoArgsConstructor
@ApiModel("已消耗权益")
public class ConsumedBenefitDTO {
    
    @ApiModelProperty("消耗数量")
    private Integer consumeAmount;
    
    @ApiModelProperty("消耗前余额")
    private Integer beforeBalance;
    
    @ApiModelProperty("消耗后余额")
    private Integer afterBalance;
    
    @ApiModelProperty("处理流水号")
    private String processId;
}
```

#### ReceivableBenefitDTO.java
```java
/**
 * 可领取权益 DTO
 */
@Data
@NoArgsConstructor
@ApiModel("可领取权益")
public class ReceivableBenefitDTO {
    
    @ApiModelProperty("权益ID")
    private String benefitId;
    
    @ApiModelProperty("权益名称")
    private String benefitName;
    
    @ApiModelProperty("权益类型")
    private String benefitType;
    
    @ApiModelProperty("权益数量")
    private Integer benefitAmount;
    
    @ApiModelProperty("权益描述")
    private String benefitDesc;
    
    @ApiModelProperty("可领取次数")
    private Integer availableTimes;
}
```

#### UnreceivableBenefitDTO.java
```java
/**
 * 不可领取权益 DTO
 */
@Data
@NoArgsConstructor
@ApiModel("不可领取权益")
public class UnreceivableBenefitDTO {
    
    @ApiModelProperty("权益ID")
    private String benefitId;
    
    @ApiModelProperty("权益名称")
    private String benefitName;
    
    @ApiModelProperty("不可领取原因码")
    private String reasonCode;
    
    @ApiModelProperty("不可领取原因描述")
    private String reasonMessage;
}
```

## 技术要求
- 使用 Spring MVC 注解（@RestController、@RequestMapping、@PostMapping）
- 集成 Swagger API 文档（@Api、@ApiOperation）
- 使用 JSR-303 参数校验（@Valid、@RequestBody）
- 统一的异常处理（@ExceptionHandler）
- 统一的响应格式（WebResp）
- 完整的日志记录

## 验收标准
- 编译通过：`./gradlew compileJava`
- REST API 接口正确实现
- Swagger 文档注解完整
- 参数校验正确工作
- 异常处理统一
- 响应格式统一
- 日志记录完整
