# 任务二：权益消耗策略模式实现

## 任务目标
实现权益消耗的策略模式，支持不同权益类型的消耗逻辑（积分、活动次数等），包括检查、扣减、确认、释放四个操作阶段。

## 前置依赖
- 任务一：基础 DTO 和枚举类已完成
- ConsumeBenefitContext 和 ConsumeBenefitResult 已创建

## 需要创建的类

### 1. 策略接口
**路径**: `com.ddmc.equity.processor.consume_benefit.strategy`

#### ConsumeBenefitStrategy.java
```java
/**
 * 权益消耗策略接口
 */
public interface ConsumeBenefitStrategy {
    
    /**
     * 检查权益是否足够消耗
     */
    ConsumeBenefitResult check(ConsumeBenefitContext context);
    
    /**
     * 扣减（冻结）权益
     */
    ConsumeBenefitResult deduct(ConsumeBenefitContext context);
    
    /**
     * 确认消耗权益
     */
    ConsumeBenefitResult confirm(ConsumeBenefitContext context);
    
    /**
     * 释放（解冻）权益
     */
    ConsumeBenefitResult release(ConsumeBenefitContext context);
    
    /**
     * 获取策略支持的权益类型
     */
    String getSupportedBenefitType();
}
```

### 2. 抽象策略基类
**路径**: `com.ddmc.equity.processor.consume_benefit.strategy`

#### AbstractConsumeBenefitStrategy.java
```java
/**
 * 权益消耗策略抽象基类
 * 提供通用的参数校验和模板方法
 */
@Slf4j
public abstract class AbstractConsumeBenefitStrategy implements ConsumeBenefitStrategy {
    
    @Override
    public final ConsumeBenefitResult check(ConsumeBenefitContext context) {
        try {
            validateContext(context, "check");
            log.info("开始检查权益消耗，用户ID：{}，权益类型：{}，消耗数量：{}", 
                    context.getUserId(), context.getBenefitType(), context.getConsumeAmount());
            
            ConsumeBenefitResult result = doCheck(context);
            
            log.info("权益消耗检查完成，用户ID：{}，结果：{}", 
                    context.getUserId(), result.getSuccess());
            return result;
        } catch (Exception e) {
            log.error("权益消耗检查异常，用户ID：{}", context.getUserId(), e);
            return buildErrorResult("CONSUME_CHECK_ERROR", "权益消耗检查失败：" + e.getMessage());
        }
    }
    
    @Override
    public final ConsumeBenefitResult deduct(ConsumeBenefitContext context) {
        try {
            validateContext(context, "deduct");
            log.info("开始扣减权益，用户ID：{}，权益类型：{}，消耗数量：{}", 
                    context.getUserId(), context.getBenefitType(), context.getConsumeAmount());
            
            ConsumeBenefitResult result = doDeduct(context);
            
            log.info("权益扣减完成，用户ID：{}，结果：{}", 
                    context.getUserId(), result.getSuccess());
            return result;
        } catch (Exception e) {
            log.error("权益扣减异常，用户ID：{}", context.getUserId(), e);
            return buildErrorResult("CONSUME_DEDUCT_ERROR", "权益扣减失败：" + e.getMessage());
        }
    }
    
    @Override
    public final ConsumeBenefitResult confirm(ConsumeBenefitContext context) {
        try {
            validateContext(context, "confirm");
            log.info("开始确认权益消耗，用户ID：{}，权益类型：{}", 
                    context.getUserId(), context.getBenefitType());
            
            ConsumeBenefitResult result = doConfirm(context);
            
            log.info("权益消耗确认完成，用户ID：{}，结果：{}", 
                    context.getUserId(), result.getSuccess());
            return result;
        } catch (Exception e) {
            log.error("权益消耗确认异常，用户ID：{}", context.getUserId(), e);
            return buildErrorResult("CONSUME_CONFIRM_ERROR", "权益消耗确认失败：" + e.getMessage());
        }
    }
    
    @Override
    public final ConsumeBenefitResult release(ConsumeBenefitContext context) {
        try {
            validateContext(context, "release");
            log.info("开始释放权益，用户ID：{}，权益类型：{}", 
                    context.getUserId(), context.getBenefitType());
            
            ConsumeBenefitResult result = doRelease(context);
            
            log.info("权益释放完成，用户ID：{}，结果：{}", 
                    context.getUserId(), result.getSuccess());
            return result;
        } catch (Exception e) {
            log.error("权益释放异常，用户ID：{}", context.getUserId(), e);
            return buildErrorResult("CONSUME_RELEASE_ERROR", "权益释放失败：" + e.getMessage());
        }
    }
    
    /**
     * 参数校验
     */
    protected void validateContext(ConsumeBenefitContext context, String operation) {
        if (context == null) {
            throw new IllegalArgumentException("消耗上下文不能为空");
        }
        if (context.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (StringUtils.isBlank(context.getBenefitType())) {
            throw new IllegalArgumentException("权益类型不能为空");
        }
        if (context.getConsumeAmount() == null || context.getConsumeAmount() <= 0) {
            throw new IllegalArgumentException("消耗数量必须大于0");
        }
        if (StringUtils.isBlank(context.getRequestId())) {
            throw new IllegalArgumentException("请求号不能为空");
        }
    }
    
    /**
     * 构建错误结果
     */
    protected ConsumeBenefitResult buildErrorResult(String resultCode, String resultMessage) {
        return ConsumeBenefitResult.builder()
                .success(false)
                .resultCode(resultCode)
                .resultMessage(resultMessage)
                .build();
    }
    
    /**
     * 构建成功结果
     */
    protected ConsumeBenefitResult buildSuccessResult(String resultMessage) {
        return ConsumeBenefitResult.builder()
                .success(true)
                .resultCode("SUCCESS")
                .resultMessage(resultMessage)
                .build();
    }
    
    // 抽象方法，由具体策略实现
    protected abstract ConsumeBenefitResult doCheck(ConsumeBenefitContext context);
    protected abstract ConsumeBenefitResult doDeduct(ConsumeBenefitContext context);
    protected abstract ConsumeBenefitResult doConfirm(ConsumeBenefitContext context);
    protected abstract ConsumeBenefitResult doRelease(ConsumeBenefitContext context);
}
```

### 3. 具体策略实现
**路径**: `com.ddmc.equity.processor.consume_benefit.strategy.impl`

#### DefaultConsumeBenefitStrategy.java
```java
/**
 * 默认权益消耗策略
 * 用于不需要实际消耗权益的场景
 */
@Component
@Slf4j
public class DefaultConsumeBenefitStrategy extends AbstractConsumeBenefitStrategy {
    
    private static final String SUPPORTED_BENEFIT_TYPE = "DEFAULT";
    
    @Override
    protected ConsumeBenefitResult doCheck(ConsumeBenefitContext context) {
        log.info("默认策略检查权益，用户ID：{}，无需实际检查", context.getUserId());
        return buildSuccessResult("默认策略检查通过");
    }
    
    @Override
    protected ConsumeBenefitResult doDeduct(ConsumeBenefitContext context) {
        log.info("默认策略扣减权益，用户ID：{}，无需实际扣减", context.getUserId());
        return buildSuccessResult("默认策略扣减成功");
    }
    
    @Override
    protected ConsumeBenefitResult doConfirm(ConsumeBenefitContext context) {
        log.info("默认策略确认消耗，用户ID：{}，无需实际确认", context.getUserId());
        return buildSuccessResult("默认策略确认成功");
    }
    
    @Override
    protected ConsumeBenefitResult doRelease(ConsumeBenefitContext context) {
        log.info("默认策略释放权益，用户ID：{}，无需实际释放", context.getUserId());
        return buildSuccessResult("默认策略释放成功");
    }
    
    @Override
    public String getSupportedBenefitType() {
        return SUPPORTED_BENEFIT_TYPE;
    }
}
```

#### PointConsumeBenefitStrategy.java
```java
/**
 * 积分权益消耗策略
 * 调用积分服务进行积分相关操作
 */
@Component
@Slf4j
public class PointConsumeBenefitStrategy extends AbstractConsumeBenefitStrategy {
    
    private static final String SUPPORTED_BENEFIT_TYPE = "POINT";
    
    @Resource
    private UserPointProxy userPointProxy;
    
    @Override
    protected ConsumeBenefitResult doCheck(ConsumeBenefitContext context) {
        // 调用积分服务检查用户积分余额
        Integer totalPoint = userPointProxy.getUserTotalPoint(context.getUserId());
        
        if (totalPoint >= context.getConsumeAmount()) {
            return ConsumeBenefitResult.builder()
                    .success(true)
                    .resultCode("SUCCESS")
                    .resultMessage("积分余额充足")
                    .beforeBalance(totalPoint)
                    .build();
        } else {
            return ConsumeBenefitResult.builder()
                    .success(false)
                    .resultCode("INSUFFICIENT_BALANCE")
                    .resultMessage("积分余额不足")
                    .beforeBalance(totalPoint)
                    .build();
        }
    }
    
    @Override
    protected ConsumeBenefitResult doDeduct(ConsumeBenefitContext context) {
        // 冻结积分
        // 实际实现需要调用积分服务的冻结接口
        log.info("冻结用户积分，用户ID：{}，数量：{}", context.getUserId(), context.getConsumeAmount());
        return buildSuccessResult("积分冻结成功");
    }
    
    @Override
    protected ConsumeBenefitResult doConfirm(ConsumeBenefitContext context) {
        // 确认扣减积分
        log.info("确认扣减用户积分，用户ID：{}，数量：{}", context.getUserId(), context.getConsumeAmount());
        return buildSuccessResult("积分扣减确认成功");
    }
    
    @Override
    protected ConsumeBenefitResult doRelease(ConsumeBenefitContext context) {
        // 解冻积分
        log.info("解冻用户积分，用户ID：{}，数量：{}", context.getUserId(), context.getConsumeAmount());
        return buildSuccessResult("积分解冻成功");
    }
    
    @Override
    public String getSupportedBenefitType() {
        return SUPPORTED_BENEFIT_TYPE;
    }
}
```

### 4. 策略工厂
**路径**: `com.ddmc.equity.processor.consume_benefit.strategy`

#### ConsumeBenefitStrategyFactory.java
```java
/**
 * 权益消耗策略工厂
 * 根据权益类型获取对应的消耗策略
 */
@Component
@Slf4j
public class ConsumeBenefitStrategyFactory {
    
    private final Map<String, ConsumeBenefitStrategy> strategyMap = new ConcurrentHashMap<>();
    
    @Resource
    private List<ConsumeBenefitStrategy> strategies;
    
    @PostConstruct
    public void init() {
        if (CollectionUtils.isNotEmpty(strategies)) {
            for (ConsumeBenefitStrategy strategy : strategies) {
                String benefitType = strategy.getSupportedBenefitType();
                strategyMap.put(benefitType, strategy);
                log.info("注册权益消耗策略：{} -> {}", benefitType, strategy.getClass().getSimpleName());
            }
        }
        log.info("权益消耗策略工厂初始化完成，共注册{}个策略", strategyMap.size());
    }
    
    /**
     * 根据权益类型获取策略
     */
    public ConsumeBenefitStrategy getStrategy(String benefitType) {
        if (StringUtils.isBlank(benefitType)) {
            throw new IllegalArgumentException("权益类型不能为空");
        }
        
        ConsumeBenefitStrategy strategy = strategyMap.get(benefitType);
        if (strategy == null) {
            log.warn("未找到权益类型{}对应的消耗策略，使用默认策略", benefitType);
            strategy = strategyMap.get("DEFAULT");
        }
        
        if (strategy == null) {
            throw new IllegalStateException("未找到可用的权益消耗策略");
        }
        
        return strategy;
    }
    
    /**
     * 获取所有支持的权益类型
     */
    public Set<String> getSupportedBenefitTypes() {
        return new HashSet<>(strategyMap.keySet());
    }
}
```

## 技术要求
- 使用策略模式和模板方法模式
- 支持 Spring IoC 容器管理
- 完整的参数校验和异常处理
- 统一的日志记录

## 验收标准
- 编译通过：`./gradlew compileJava`
- 策略模式正确实现
- 模板方法使用 final 修饰
- Spring 注解使用正确
- 参数校验完整
- 异常处理统一
