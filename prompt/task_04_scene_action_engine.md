# 任务四：场景动作流程引擎核心实现

## 任务目标
实现场景动作流程引擎，支持咨询流程和领取流程，包括处理器接口、抽象基类和主流程引擎，实现责任链模式的节点链式处理。

## 前置依赖
- 任务一：基础 DTO 和枚举类已完成
- SceneActionProcessContext、SceneActionProcessResult、SceneActionProcessTypeEnum 已创建

## 需要创建的类

### 1. 处理器接口
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor`

#### SceneActionProcessor.java
```java
/**
 * 场景动作处理器接口
 */
public interface SceneActionProcessor {

    /**
     * 处理场景动作
     */
    SceneActionProcessResult process(SceneActionProcessContext context);

    /**
     * 获取处理器名称
     */
    String getProcessorName();

    /**
     * 获取支持的处理类型
     */
    List<SceneActionProcessTypeEnum> getSupportedProcessTypes();

    /**
     * 是否启用该处理器
     */
    boolean isEnabled(SceneActionProcessContext context);
}
```

### 2. 抽象处理器基类
**路径**: `com.ddmc.equity.processor.scene_action.v1.processor`

#### AbstractSceneActionProcessor.java
```java
/**
 * 场景动作处理器抽象基类
 */
@Slf4j
public abstract class AbstractSceneActionProcessor implements SceneActionProcessor {
    
    @Override
    public final SceneActionProcessResult process(SceneActionProcessContext context) {
        String processorName = getProcessorName();
        
        try {
            // 检查是否启用
            if (!isEnabled(context)) {
                log.info("处理器{}已禁用，跳过处理，用户ID：{}", processorName, context.getUserId());
                return buildSkippedResult("处理器已禁用");
            }
            
            // 参数校验
            validateContext(context);
            
            log.info("开始执行处理器{}，用户ID：{}，场景：{}", 
                    processorName, context.getUserId(), context.getSceneCode());
            
            // 执行具体处理逻辑
            SceneActionProcessResult result = doProcess(context);
            
            log.info("处理器{}执行完成，用户ID：{}，结果：{}", 
                    processorName, context.getUserId(), result.getSuccess());
            
            return result;
            
        } catch (Exception e) {
            log.error("处理器{}执行异常，用户ID：{}", processorName, context.getUserId(), e);
            return buildErrorResult("PROCESSOR_ERROR", 
                    String.format("处理器%s执行失败：%s", processorName, e.getMessage()));
        }
    }
    
    @Override
    public boolean isEnabled(SceneActionProcessContext context) {
        // 默认启用，子类可以重写
        return true;
    }
    
    /**
     * 参数校验
     */
    protected void validateContext(SceneActionProcessContext context) {
        if (context == null) {
            throw new IllegalArgumentException("处理上下文不能为空");
        }
        if (context.getUserId() == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (StringUtils.isBlank(context.getSceneCode())) {
            throw new IllegalArgumentException("场景编码不能为空");
        }
        if (context.getProcessType() == null) {
            throw new IllegalArgumentException("处理类型不能为空");
        }
    }
    
    /**
     * 构建成功结果
     */
    protected SceneActionProcessResult buildSuccessResult(String message) {
        return SceneActionProcessResult.builder()
                .success(true)
                .resultCode("SUCCESS")
                .resultMessage(message)
                .processStatus(ProcessStatus.SUCCESS)
                .build();
    }
    
    /**
     * 构建错误结果
     */
    protected SceneActionProcessResult buildErrorResult(String code, String message) {
        return SceneActionProcessResult.builder()
                .success(false)
                .resultCode(code)
                .resultMessage(message)
                .processStatus(ProcessStatus.FAILED)
                .build();
    }
    
    /**
     * 构建跳过结果
     */
    protected SceneActionProcessResult buildSkippedResult(String message) {
        return SceneActionProcessResult.builder()
                .success(true)
                .resultCode("SKIPPED")
                .resultMessage(message)
                .processStatus(ProcessStatus.SKIPPED)
                .build();
    }
    
    /**
     * 具体处理逻辑，由子类实现
     */
    protected abstract SceneActionProcessResult doProcess(SceneActionProcessContext context);
}
```

### 3. 主流程引擎
**路径**: `com.ddmc.equity.processor.scene_action.v1.engine`

#### SceneActionProcessEngine.java
```java
/**
 * 场景动作流程引擎
 * 负责编排和执行场景动作处理流程
 */
@Component
@Slf4j
public class SceneActionProcessEngine {
    
    @Resource
    private List<SceneActionProcessor> processors;
    
    /**
     * 执行咨询流程
     */
    public SceneActionProcessResult executeConsultProcess(SceneActionProcessContext context) {
        context.setProcessType(SceneActionProcessTypeEnum.CONSULT);
        
        log.info("开始执行咨询流程，用户ID：{}，场景：{}", context.getUserId(), context.getSceneCode());
        
        List<SceneActionProcessor> consultProcessors = getProcessorsForType(SceneActionProcessTypeEnum.CONSULT);
        
        return executeProcessChain(context, consultProcessors);
    }
    
    /**
     * 执行领取流程
     */
    public SceneActionProcessResult executeReceiveProcess(SceneActionProcessContext context) {
        context.setProcessType(SceneActionProcessTypeEnum.RECEIVE);
        
        log.info("开始执行领取流程，用户ID：{}，场景：{}", context.getUserId(), context.getSceneCode());
        
        List<SceneActionProcessor> receiveProcessors = getProcessorsForType(SceneActionProcessTypeEnum.RECEIVE);
        
        return executeProcessChain(context, receiveProcessors);
    }
    
    /**
     * 执行处理器链
     */
    private SceneActionProcessResult executeProcessChain(SceneActionProcessContext context, 
                                                        List<SceneActionProcessor> processorChain) {
        
        SceneActionProcessResult finalResult = SceneActionProcessResult.builder()
                .success(true)
                .resultCode("SUCCESS")
                .resultMessage("流程执行成功")
                .processStatus(ProcessStatus.SUCCESS)
                .build();
        
        for (SceneActionProcessor processor : processorChain) {
            try {
                log.info("执行处理器：{}，用户ID：{}", processor.getProcessorName(), context.getUserId());
                
                SceneActionProcessResult result = processor.process(context);
                
                // 更新上下文中的处理数据
                updateContextWithResult(context, result);
                
                // 如果处理失败且不是跳过状态，中断流程
                if (!result.getSuccess() && result.getProcessStatus() != ProcessStatus.SKIPPED) {
                    log.error("处理器{}执行失败，中断流程，用户ID：{}", 
                            processor.getProcessorName(), context.getUserId());
                    return result;
                }
                
                // 如果是跳过状态，继续执行下一个处理器
                if (result.getProcessStatus() == ProcessStatus.SKIPPED) {
                    log.info("处理器{}被跳过，继续执行下一个处理器", processor.getProcessorName());
                    continue;
                }
                
                log.info("处理器{}执行成功，用户ID：{}", processor.getProcessorName(), context.getUserId());
                
            } catch (Exception e) {
                log.error("处理器{}执行异常，中断流程，用户ID：{}", 
                        processor.getProcessorName(), context.getUserId(), e);
                
                return SceneActionProcessResult.builder()
                        .success(false)
                        .resultCode("PROCESSOR_EXCEPTION")
                        .resultMessage("处理器执行异常：" + e.getMessage())
                        .processStatus(ProcessStatus.FAILED)
                        .build();
            }
        }
        
        log.info("流程执行完成，用户ID：{}，场景：{}", context.getUserId(), context.getSceneCode());
        
        return finalResult;
    }
    
    /**
     * 根据处理类型获取处理器列表
     */
    private List<SceneActionProcessor> getProcessorsForType(SceneActionProcessTypeEnum processType) {
        if (CollectionUtils.isEmpty(processors)) {
            return Collections.emptyList();
        }
        
        return processors.stream()
                .filter(processor -> processor.getSupportedProcessTypes().contains(processType))
                .sorted(this::compareProcessors)
                .collect(Collectors.toList());
    }
    
    /**
     * 处理器排序比较器
     * 可以根据实际需求定义处理器的执行顺序
     */
    private int compareProcessors(SceneActionProcessor p1, SceneActionProcessor p2) {
        // 这里可以根据处理器名称或其他规则进行排序
        // 例如：咨询活动 -> 检查消耗 -> 前置风控 -> 扣减消耗 -> 执行权益 -> 确认消耗 -> 后置风控
        
        Map<String, Integer> orderMap = new HashMap<>();
        orderMap.put("DefaultConsultActivityProcessor", 1);
        orderMap.put("DefaultCheckConsumeBftProcessor", 2);
        orderMap.put("DefaultCheckRiskBeforeProcessor", 3);
        orderMap.put("DefaultDeductConsumeBftProcessor", 4);
        orderMap.put("DefaultExecuteBftProcessor", 5);
        orderMap.put("DefaultConfirmConsumeBftProcessor", 6);
        orderMap.put("DefaultReportRiskAfterProcessor", 7);
        
        Integer order1 = orderMap.getOrDefault(p1.getClass().getSimpleName(), 999);
        Integer order2 = orderMap.getOrDefault(p2.getClass().getSimpleName(), 999);
        
        return order1.compareTo(order2);
    }
    
    /**
     * 用处理结果更新上下文
     */
    private void updateContextWithResult(SceneActionProcessContext context, SceneActionProcessResult result) {
        if (result.getProcessData() != null) {
            if (context.getExtParams() == null) {
                context.setExtParams(new HashMap<>());
            }
            context.getExtParams().putAll(result.getProcessData());
        }
    }
}
```

## 技术要求
- 使用责任链模式实现节点链式处理
- 支持节点启用/禁用控制
- 支持流程结果更新和中断控制
- 异常处理和错误恢复机制
- 完整的日志记录

## 验收标准
- 编译通过：`./gradlew compileJava`
- 责任链模式正确实现
- 支持咨询和领取两种流程
- 处理器排序和执行顺序正确
- 异常处理完善
- 日志记录完整
